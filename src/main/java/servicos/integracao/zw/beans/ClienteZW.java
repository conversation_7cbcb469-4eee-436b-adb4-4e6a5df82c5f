/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.integracao.zw.beans;

import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.professor.ProfessorSintetico;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
@ApiModel(description = "Dados do cliente para sincronização entre sistemas externos e interno")
public class ClienteZW implements Serializable {

    @ApiModelProperty(value = "Código único do cliente no sistema interno", example = "1001")
    private Integer codigo;

    @ApiModelProperty(value = "Nome completo do cliente", example = "Maria Silva Santos")
    private String nome;

    @ApiModelProperty(value = "Sexo do cliente (M=Masculino, F=Feminino)", example = "F")
    private String sexo;

    @ApiModelProperty(value = "Data de referência do cliente", example = "2024-01-15")
    private Date dia;

    @ApiModelProperty(value = "Código do cliente no sistema ZW", example = "2001")
    private Integer codigoCliente;

    @ApiModelProperty(value = "Código da pessoa no sistema ZW", example = "3001")
    private Integer codigoPessoa;

    @ApiModelProperty(value = "Número da matrícula do cliente", example = "202401001")
    private Integer matricula;

    @ApiModelProperty(value = "Data de nascimento do cliente", example = "1990-05-20")
    private Date dataNascimento;

    @ApiModelProperty(value = "Idade atual do cliente", example = "34")
    private Integer idade;

    @ApiModelProperty(value = "Profissão do cliente", example = "Engenheira")
    private String profissao;

    @ApiModelProperty(value = "Lista de colaboradores associados ao cliente", example = "João Silva, Ana Costa")
    private String colaboradores;

    @ApiModelProperty(value = "Código do contrato ativo do cliente", example = "5001")
    private Integer codigoContrato;

    @ApiModelProperty(value = "Situação atual do cliente no sistema", example = "ATIVO")
    private String situacao;

    @ApiModelProperty(value = "Duração do contrato em meses", example = "12")
    private Integer duracaoContratoMeses;

    @ApiModelProperty(value = "Mnemônico identificador do contrato", example = "ANUAL2024")
    private String mnemonicoDoContrato;

    @ApiModelProperty(value = "Nome do plano contratado", example = "Plano Premium Anual")
    private String nomeplano;
    private Double valorfaturadocontrato;
    private Double valorPagoContrato;
    private Double valorParcAbertoContrato;
    private Double saldoContaCorrenteCliente;
    private Date dataVigenciaDe;
    private Date dataVigenciaAte;
    private Date dataVigenciaAteAjustada;
    private Date dataLancamentoContrato;
    private Date dataRenovacaoContrato;
    private Date dataRematriculaContrato;
    private Date dataUltimoBV;
    private Date dataMatricula;
    private Date dataUltimarematricula;
    private Integer diasAssiduidadeUltRematriculaAteHoje;
    private Integer diasFaltaSemAcesso;
    private Date dataUltimoacesso;
    private String faseAtualCRM;
    private Date dataUltimoContatoCRM;
    private String responsavelUltimoContatoCRM;
    private Integer codigoUltimoContatoCRM;
    private String situacaoContrato;
    private String tipoPeriodoAcesso;
    private Date dataInicioPeriodoAcesso;
    private Date dataFimPeriodoAcesso;
    private Integer diasAcessoSemanaPassada;
    private Integer diasAcessoSemana2;
    private Integer diasAcessoSemana3;
    private Integer diasAcessoSemana4;
    private Integer vezesPorSemana;
    private Integer diasAcessoUltimoMes;
    private Integer diasAcessoMes2;
    private Integer diasAcessoMes3;
    private Integer diasAcessoMes4;
    private Integer mediaDiasAcesso4Meses;
    private Integer empresa;
    private String email;
    private String telefones;
    private Integer pesoRisco;
    private Integer codigoColaboradorProfessor;
    private Integer nrTreinosRealizados;
    private Integer nrTreinosPrevistos;
    private ProfessorSintetico professorSintetico;
    private String situacaoMatriculaContrato = "";
    private String codigoAcesso;
    private String modalidades;
    private Boolean alunoParq;
    private String descricaoDuracao;
    
    private Integer saldoCreditoTreino;
    private Integer totalCreditoTreino;
    private String descricoesModalidades;
    private Boolean crossfit;
    private String nomeConsulta;
    private String situacaoContratoOperacao;

    private Date dataCadastro;
    private Date dataVencimentoTreino;
    private Boolean existeParcVencidaContrato = false;
    private Boolean empresausafreepass = false;

    private String CPF;
    private Date ultimaVisita;
    private String cargo;
    private boolean freePass;
    private String endereco;
    private String cidade;
    private String bairro;
    private String estadoCivil;
    private String RG;
    private String UF;
    private Date freePassInicio;
    private Date freePassFim;

    public Date getUltimaVisita() {
        return ultimaVisita;
    }

    public void setUltimaVisita(Date ultimaVisita) {
        this.ultimaVisita = ultimaVisita;
    }

    public String getCargo() {
        return cargo;
    }

    public void setCargo(String cargo) {
        this.cargo = cargo;
    }

    public boolean isFreePass() {
        return freePass;
    }

    public void setFreePass(boolean freePass) {
        this.freePass = freePass;
    }

    public String getEndereco() {
        return endereco;
    }

    public void setEndereco(String endereco) {
        this.endereco = endereco;
    }

    public String getCidade() {
        return cidade;
    }

    public void setCidade(String cidade) {
        this.cidade = cidade;
    }

    public String getBairro() {
        return bairro;
    }

    public void setBairro(String bairro) {
        this.bairro = bairro;
    }


    public String getEstadoCivil() {
        return estadoCivil;
    }

    public void setEstadoCivil(String estadoCivil) {
        this.estadoCivil = estadoCivil;
    }

    public String getCPF() {
        return CPF;
    }

    public void setCPF(String CPF) {
        this.CPF = CPF;
    }

    public String getRG() {
        return RG;
    }

    public void setRG(String RG) {
        this.RG = RG;
    }

    public String getUF() {
        return UF;
    }

    public void setUF(String UF) {
        this.UF = UF;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getSexo() {
        return sexo;
    }

    public void setSexo(String sexo) {
        this.sexo = sexo;
    }

    public Date getDia() {
        return dia;
    }

    public void setDia(Date dia) {
        this.dia = dia;
    }

    public Integer getCodigoCliente() {
        return codigoCliente;
    }

    public void setCodigoCliente(Integer codigoCliente) {
        this.codigoCliente = codigoCliente;
    }

    public Integer getCodigoPessoa() {
        return codigoPessoa;
    }

    public void setCodigoPessoa(Integer codigoPessoa) {
        this.codigoPessoa = codigoPessoa;
    }

    public Integer getMatricula() {
        return matricula;
    }

    public void setMatricula(Integer matricula) {
        this.matricula = matricula;
    }

    public Date getDataNascimento() {
        return dataNascimento;
    }

    public void setDataNascimento(Date dataNascimento) {
        this.dataNascimento = dataNascimento;
    }

    public Integer getIdade() {
        return idade;
    }

    public void setIdade(Integer idade) {
        this.idade = idade;
    }

    public String getProfissao() {
        return profissao;
    }

    public void setProfissao(String profissao) {
        this.profissao = profissao;
    }

    public String getColaboradores() {
        return colaboradores;
    }

    public void setColaboradores(String colaboradores) {
        this.colaboradores = colaboradores;
    }

    public Integer getCodigoContrato() {
        return codigoContrato;
    }

    public void setCodigoContrato(Integer codigoContrato) {
        this.codigoContrato = codigoContrato;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public Integer getDuracaoContratoMeses() {
        return duracaoContratoMeses;
    }

    public void setDuracaoContratoMeses(Integer duracaoContratoMeses) {
        this.duracaoContratoMeses = duracaoContratoMeses;
    }

    public String getMnemonicoDoContrato() {
        return mnemonicoDoContrato;
    }

    public void setMnemonicoDoContrato(String mnemonicoDoContrato) {
        this.mnemonicoDoContrato = mnemonicoDoContrato;
    }

    public String getNomeplano() {
        return nomeplano;
    }

    public void setNomeplano(String nomeplano) {
        this.nomeplano = nomeplano;
    }

    public Double getValorfaturadocontrato() {
        return valorfaturadocontrato;
    }

    public void setValorfaturadocontrato(Double valorfaturadocontrato) {
        this.valorfaturadocontrato = valorfaturadocontrato;
    }

    public Double getValorPagoContrato() {
        return valorPagoContrato;
    }

    public void setValorPagoContrato(Double valorPagoContrato) {
        this.valorPagoContrato = valorPagoContrato;
    }

    public Double getValorParcAbertoContrato() {
        return valorParcAbertoContrato;
    }

    public void setValorParcAbertoContrato(Double valorParcAbertoContrato) {
        this.valorParcAbertoContrato = valorParcAbertoContrato;
    }

    public Double getSaldoContaCorrenteCliente() {
        return saldoContaCorrenteCliente;
    }

    public void setSaldoContaCorrenteCliente(Double saldoContaCorrenteCliente) {
        this.saldoContaCorrenteCliente = saldoContaCorrenteCliente;
    }

    public Date getDataVigenciaDe() {
        return dataVigenciaDe;
    }

    public void setDataVigenciaDe(Date dataVigenciaDe) {
        this.dataVigenciaDe = dataVigenciaDe;
    }

    public Date getDataVigenciaAte() {
        return dataVigenciaAte;
    }

    public void setDataVigenciaAte(Date dataVigenciaAte) {
        this.dataVigenciaAte = dataVigenciaAte;
    }

    public Date getDataVigenciaAteAjustada() {
        return dataVigenciaAteAjustada;
    }

    public void setDataVigenciaAteAjustada(Date dataVigenciaAteAjustada) {
        this.dataVigenciaAteAjustada = dataVigenciaAteAjustada;
    }

    public Date getDataLancamentoContrato() {
        return dataLancamentoContrato;
    }

    public void setDataLancamentoContrato(Date dataLancamentoContrato) {
        this.dataLancamentoContrato = dataLancamentoContrato;
    }

    public Date getDataRenovacaoContrato() {
        return dataRenovacaoContrato;
    }

    public void setDataRenovacaoContrato(Date dataRenovacaoContrato) {
        this.dataRenovacaoContrato = dataRenovacaoContrato;
    }

    public Date getDataRematriculaContrato() {
        return dataRematriculaContrato;
    }

    public void setDataRematriculaContrato(Date dataRematriculaContrato) {
        this.dataRematriculaContrato = dataRematriculaContrato;
    }

    public Date getDataUltimoBV() {
        return dataUltimoBV;
    }

    public void setDataUltimoBV(Date dataUltimoBV) {
        this.dataUltimoBV = dataUltimoBV;
    }

    public Date getDataMatricula() {
        return dataMatricula;
    }

    public void setDataMatricula(Date dataMatricula) {
        this.dataMatricula = dataMatricula;
    }

    public Date getDataUltimarematricula() {
        return dataUltimarematricula;
    }

    public void setDataUltimarematricula(Date dataUltimarematricula) {
        this.dataUltimarematricula = dataUltimarematricula;
    }

    public Integer getDiasAssiduidadeUltRematriculaAteHoje() {
        return diasAssiduidadeUltRematriculaAteHoje;
    }

    public void setDiasAssiduidadeUltRematriculaAteHoje(Integer diasAssiduidadeUltRematriculaAteHoje) {
        this.diasAssiduidadeUltRematriculaAteHoje = diasAssiduidadeUltRematriculaAteHoje;
    }

    public Integer getDiasFaltaSemAcesso() {
        return diasFaltaSemAcesso;
    }

    public void setDiasFaltaSemAcesso(Integer diasFaltaSemAcesso) {
        this.diasFaltaSemAcesso = diasFaltaSemAcesso;
    }

    public Date getDataUltimoacesso() {
        return dataUltimoacesso;
    }

    public void setDataUltimoacesso(Date dataUltimoacesso) {
        this.dataUltimoacesso = dataUltimoacesso;
    }

    public String getFaseAtualCRM() {
        return faseAtualCRM;
    }

    public void setFaseAtualCRM(String faseAtualCRM) {
        this.faseAtualCRM = faseAtualCRM;
    }

    public Date getDataUltimoContatoCRM() {
        return dataUltimoContatoCRM;
    }

    public void setDataUltimoContatoCRM(Date dataUltimoContatoCRM) {
        this.dataUltimoContatoCRM = dataUltimoContatoCRM;
    }

    public String getResponsavelUltimoContatoCRM() {
        return responsavelUltimoContatoCRM;
    }

    public void setResponsavelUltimoContatoCRM(String responsavelUltimoContatoCRM) {
        this.responsavelUltimoContatoCRM = responsavelUltimoContatoCRM;
    }

    public Integer getCodigoUltimoContatoCRM() {
        return codigoUltimoContatoCRM;
    }

    public void setCodigoUltimoContatoCRM(Integer codigoUltimoContatoCRM) {
        this.codigoUltimoContatoCRM = codigoUltimoContatoCRM;
    }

    public String getSituacaoContrato() {
        return situacaoContrato;
    }

    public void setSituacaoContrato(String situacaoContrato) {
        this.situacaoContrato = situacaoContrato;
    }

    public String getTipoPeriodoAcesso() {
        return tipoPeriodoAcesso;
    }

    public void setTipoPeriodoAcesso(String tipoPeriodoAcesso) {
        this.tipoPeriodoAcesso = tipoPeriodoAcesso;
    }

    public Date getDataInicioPeriodoAcesso() {
        return dataInicioPeriodoAcesso;
    }

    public void setDataInicioPeriodoAcesso(Date dataInicioPeriodoAcesso) {
        this.dataInicioPeriodoAcesso = dataInicioPeriodoAcesso;
    }

    public Date getDataFimPeriodoAcesso() {
        return dataFimPeriodoAcesso;
    }

    public void setDataFimPeriodoAcesso(Date dataFimPeriodoAcesso) {
        this.dataFimPeriodoAcesso = dataFimPeriodoAcesso;
    }

    public Integer getDiasAcessoSemanaPassada() {
        return diasAcessoSemanaPassada;
    }

    public void setDiasAcessoSemanaPassada(Integer diasAcessoSemanaPassada) {
        this.diasAcessoSemanaPassada = diasAcessoSemanaPassada;
    }

    public Integer getDiasAcessoSemana2() {
        return diasAcessoSemana2;
    }

    public void setDiasAcessoSemana2(Integer diasAcessoSemana2) {
        this.diasAcessoSemana2 = diasAcessoSemana2;
    }

    public Integer getDiasAcessoSemana3() {
        return diasAcessoSemana3;
    }

    public void setDiasAcessoSemana3(Integer diasAcessoSemana3) {
        this.diasAcessoSemana3 = diasAcessoSemana3;
    }

    public Integer getDiasAcessoSemana4() {
        return diasAcessoSemana4;
    }

    public void setDiasAcessoSemana4(Integer diasAcessoSemana4) {
        this.diasAcessoSemana4 = diasAcessoSemana4;
    }

    public Integer getVezesPorSemana() {
        return vezesPorSemana;
    }

    public void setVezesPorSemana(Integer vezesPorSemana) {
        this.vezesPorSemana = vezesPorSemana;
    }

    public Integer getDiasAcessoUltimoMes() {
        return diasAcessoUltimoMes;
    }

    public void setDiasAcessoUltimoMes(Integer diasAcessoUltimoMes) {
        this.diasAcessoUltimoMes = diasAcessoUltimoMes;
    }

    public Integer getDiasAcessoMes2() {
        return diasAcessoMes2;
    }

    public void setDiasAcessoMes2(Integer diasAcessoMes2) {
        this.diasAcessoMes2 = diasAcessoMes2;
    }

    public Integer getDiasAcessoMes3() {
        return diasAcessoMes3;
    }

    public void setDiasAcessoMes3(Integer diasAcessoMes3) {
        this.diasAcessoMes3 = diasAcessoMes3;
    }

    public Integer getDiasAcessoMes4() {
        return diasAcessoMes4;
    }

    public void setDiasAcessoMes4(Integer diasAcessoMes4) {
        this.diasAcessoMes4 = diasAcessoMes4;
    }

    public Integer getMediaDiasAcesso4Meses() {
        return mediaDiasAcesso4Meses;
    }

    public void setMediaDiasAcesso4Meses(Integer mediaDiasAcesso4Meses) {
        this.mediaDiasAcesso4Meses = mediaDiasAcesso4Meses;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getTelefones() {
        return telefones;
    }

    public void setTelefones(String telefones) {
        this.telefones = telefones;
    }

    public Integer getPesoRisco() {
        return pesoRisco;
    }

    public void setPesoRisco(Integer pesoRisco) {
        this.pesoRisco = pesoRisco;
    }

    public Integer getCodigoColaboradorProfessor() {
        return codigoColaboradorProfessor;
    }

    public void setCodigoColaboradorProfessor(Integer codigoColaboradorProfessor) {
        this.codigoColaboradorProfessor = codigoColaboradorProfessor;
    }

    public Integer getNrTreinosRealizados() {
        return nrTreinosRealizados;
    }

    public void setNrTreinosRealizados(Integer nrTreinosRealizados) {
        this.nrTreinosRealizados = nrTreinosRealizados;
    }

    public Integer getNrTreinosPrevistos() {
        return nrTreinosPrevistos;
    }

    public void setNrTreinosPrevistos(Integer nrTreinosPrevistos) {
        this.nrTreinosPrevistos = nrTreinosPrevistos;
    }

    public ProfessorSintetico getProfessorSintetico() {
        return professorSintetico;
    }

    public void setProfessorSintetico(ProfessorSintetico professorSintetico) {
        this.professorSintetico = professorSintetico;
    }

    public Boolean getAlunoParq() {
        return alunoParq;
    }

    public void setAlunoParq(Boolean alunoParqPlus) {
        this.alunoParq = alunoParqPlus;
    }

    
    public static ClienteSintetico toClienteSintetico(ClienteZW zw) {
        ClienteSintetico cs = new ClienteSintetico();
        cs.setDia(zw.getDia());
        cs.setNome(zw.getNome());
        cs.setCodigoCliente(zw.getCodigoCliente());
        cs.setCodigoContrato(zw.getCodigoContrato());
        cs.setCodigoPessoa(zw.getCodigoPessoa());
        cs.setCodigoUltimoContatoCRM(zw.getCodigoUltimoContatoCRM());
        cs.setColaboradores(zw.getColaboradores());
        cs.setDataNascimento(zw.getDataNascimento());
        cs.setEmail(zw.getEmail());
        cs.setMatricula(zw.getMatricula());
        cs.setSexo(zw.getSexo());
        cs.setCodigoColaboradorProfessor(zw.getCodigoColaboradorProfessor());
        cs.setProfessorSintetico(zw.getProfessorSintetico());
        //        
        cs.setIdade(zw.getIdade());
        cs.setProfissao(zw.getProfissao());
        cs.setSituacao(zw.getSituacao());
        cs.setDuracaoContratoMeses(zw.getDuracaoContratoMeses());
        cs.setDescricaoDuracao(zw.getDescricaoDuracao());
        cs.setMnemonicoDoContrato(zw.getMnemonicoDoContrato());
        cs.setNomeplano(zw.getNomeplano());
        cs.setValorfaturadocontrato(zw.getValorfaturadocontrato());
        cs.setValorPagoContrato(zw.getValorfaturadocontrato());
        cs.setValorParcAbertoContrato(zw.getValorParcAbertoContrato());
        cs.setSaldoContaCorrenteCliente(zw.getSaldoContaCorrenteCliente());
        cs.setDataVigenciaDe(zw.getDataVigenciaDe());
        cs.setDataVigenciaAte(zw.getDataVigenciaAte());
        cs.setDataVigenciaAteAjustada(zw.getDataVigenciaAteAjustada());
        cs.setDataLancamentoContrato(zw.getDataLancamentoContrato());
        cs.setDataRenovacaoContrato(zw.getDataRenovacaoContrato());
        cs.setDataRematriculaContrato(zw.getDataRematriculaContrato());
        cs.setDataUltimoBV(zw.getDataUltimoBV());
        cs.setDataMatricula(zw.getDataMatricula());
        cs.setDataUltimarematricula(zw.getDataUltimarematricula());
        cs.setDiasAssiduidadeUltRematriculaAteHoje(zw.getDiasAssiduidadeUltRematriculaAteHoje());
        cs.setDiasFaltaSemAcesso(zw.getDiasFaltaSemAcesso());
        cs.setDataUltimoacesso(zw.getDataUltimoacesso());
        cs.setFaseAtualCRM(zw.getFaseAtualCRM());
        cs.setDataUltimoContatoCRM(zw.getDataUltimoContatoCRM());
        cs.setResponsavelUltimoContatoCRM(zw.getResponsavelUltimoContatoCRM());
        cs.setCodigoUltimoContatoCRM(zw.getCodigoUltimoContatoCRM());
        cs.setSituacaoContrato(zw.getSituacaoContrato());
        cs.setTipoPeriodoAcesso(zw.getTipoPeriodoAcesso());
        cs.setDataInicioPeriodoAcesso(zw.getDataInicioPeriodoAcesso());
        cs.setDataFimPeriodoAcesso(zw.getDataFimPeriodoAcesso());
        cs.setDiasAcessoSemanaPassada(zw.getDiasAcessoSemanaPassada());
        cs.setDiasAcessoSemana2(zw.getDiasAcessoSemana2());
        cs.setDiasAcessoSemana3(zw.getDiasAcessoSemana3());
        cs.setDiasAcessoSemana4(zw.getDiasAcessoSemana4());
        cs.setVezesPorSemana(zw.getVezesPorSemana());
        cs.setDiasAcessoUltimoMes(zw.getDiasAcessoUltimoMes());
        cs.setDiasAcessoMes2(zw.getDiasAcessoMes2());
        cs.setDiasAcessoMes3(zw.getDiasAcessoMes3());
        cs.setDiasAcessoMes4(zw.getDiasAcessoMes4());
        cs.setMediaDiasAcesso4Meses(zw.getMediaDiasAcesso4Meses());
        cs.setEmpresa(zw.getEmpresa());
        cs.setEmail(zw.getEmail());
        cs.setTelefones(zw.getTelefones());
        cs.setPesoRisco(zw.getPesoRisco());
        cs.setSituacaoMatriculaContrato(zw.getSituacaoMatriculaContrato());
        cs.setCodigoAcesso(zw.getCodigoAcesso());
        cs.setModalidades(zw.getModalidades());
        cs.setParq(zw.getAlunoParq());
        cs.setCrossfit(zw.getCrossfit());

        cs.setTotalCreditoTreino(zw.getTotalCreditoTreino());
        cs.setSaldoCreditoTreino(zw.getSaldoCreditoTreino());
        cs.setDescricoesModalidades(zw.getDescricoesModalidades());
        cs.setNomeConsulta(zw.getNomeConsulta());
        cs.setDataCadastro(zw.getDataCadastro());
        cs.setExisteParcVencidaContrato(zw.getExisteParcVencidaContrato());
        cs.setFreePassInicio(zw.getFreePassInicio());
        cs.setFreePassFim(zw.getFreePassFim());
        try {
            cs.setCPF(zw.getCPF());
        }catch (Exception e){

        }
        return cs;
    }

    public static ClienteZW fromClienteSintetico(ClienteSintetico cs) {
        ClienteZW zw = new ClienteZW();
        zw.setDia(cs.getDia());
        zw.setNome(cs.getNome());
        zw.setCodigoCliente(cs.getCodigoCliente());
        zw.setCodigoContrato(cs.getCodigoContrato());
        zw.setCodigoPessoa(cs.getCodigoPessoa());
        zw.setCodigoUltimoContatoCRM(cs.getCodigoUltimoContatoCRM());
        zw.setColaboradores(cs.getColaboradores());
        zw.setDataNascimento(cs.getDataNascimento());
        zw.setEmail(cs.getEmail());
        zw.setMatricula(cs.getMatricula());
        zw.setSexo(cs.getSexo());
        zw.setCodigoColaboradorProfessor(cs.getCodigoColaboradorProfessor());
        //
        zw.setIdade(cs.getIdade());
        zw.setProfissao(cs.getProfissao());
        zw.setColaboradores(cs.getColaboradores());
        zw.setCodigoContrato(cs.getCodigoContrato());
        zw.setSituacao(cs.getSituacao());
        zw.setDuracaoContratoMeses(cs.getDuracaoContratoMeses());
        zw.setDescricaoDuracao(cs.getDescricaoDuracao());
        zw.setMnemonicoDoContrato(cs.getMnemonicoDoContrato());
        zw.setNomeplano(cs.getNomeplano());
        zw.setValorfaturadocontrato(cs.getValorfaturadocontrato());
        zw.setValorPagoContrato(cs.getValorfaturadocontrato());
        zw.setValorParcAbertoContrato(cs.getValorParcAbertoContrato());
        zw.setSaldoContaCorrenteCliente(cs.getSaldoContaCorrenteCliente());
        zw.setDataVigenciaDe(cs.getDataVigenciaDe());
        zw.setDataVigenciaAte(cs.getDataVigenciaAte());
        zw.setDataVigenciaAteAjustada(cs.getDataVigenciaAteAjustada());
        zw.setDataLancamentoContrato(cs.getDataLancamentoContrato());
        zw.setDataRenovacaoContrato(cs.getDataRenovacaoContrato());
        zw.setDataRematriculaContrato(cs.getDataRematriculaContrato());
        zw.setDataUltimoBV(cs.getDataUltimoBV());
        zw.setDataMatricula(cs.getDataMatricula());
        zw.setDataUltimarematricula(cs.getDataUltimarematricula());
        zw.setDiasAssiduidadeUltRematriculaAteHoje(cs.getDiasAssiduidadeUltRematriculaAteHoje());
        zw.setDiasFaltaSemAcesso(cs.getDiasFaltaSemAcesso());
        zw.setDataUltimoacesso(cs.getDataUltimoacesso());
        zw.setFaseAtualCRM(cs.getFaseAtualCRM());
        zw.setDataUltimoContatoCRM(cs.getDataUltimoContatoCRM());
        zw.setResponsavelUltimoContatoCRM(cs.getResponsavelUltimoContatoCRM());
        zw.setCodigoUltimoContatoCRM(cs.getCodigoUltimoContatoCRM());
        zw.setSituacaoContrato(cs.getSituacaoContrato());
        zw.setTipoPeriodoAcesso(cs.getTipoPeriodoAcesso());
        zw.setDataInicioPeriodoAcesso(cs.getDataInicioPeriodoAcesso());
        zw.setDataFimPeriodoAcesso(cs.getDataFimPeriodoAcesso());
        zw.setDiasAcessoSemanaPassada(cs.getDiasAcessoSemanaPassada());
        zw.setDiasAcessoSemana2(cs.getDiasAcessoSemana2());
        zw.setDiasAcessoSemana3(cs.getDiasAcessoSemana3());
        zw.setDiasAcessoSemana4(cs.getDiasAcessoSemana4());
        zw.setVezesPorSemana(cs.getVezesPorSemana());
        zw.setDiasAcessoUltimoMes(cs.getDiasAcessoUltimoMes());
        zw.setDiasAcessoMes2(cs.getDiasAcessoMes2());
        zw.setDiasAcessoMes3(cs.getDiasAcessoMes3());
        zw.setDiasAcessoMes4(cs.getDiasAcessoMes4());
        zw.setMediaDiasAcesso4Meses(cs.getMediaDiasAcesso4Meses());
        zw.setEmpresa(cs.getEmpresa());
        zw.setEmail(cs.getEmail());
        zw.setTelefones(cs.getTelefones());
        zw.setPesoRisco(cs.getPesoRisco());
        zw.setSituacaoMatriculaContrato(cs.getSituacaoMatriculaContrato());
        zw.setModalidades(cs.getModalidades());
        zw.setAlunoParq(cs.getParq());
        zw.setCrossfit(cs.getCrossfit());

        zw.setSaldoCreditoTreino(cs.getSaldoCreditoTreino());
        zw.setTotalCreditoTreino(cs.getTotalCreditoTreino());
        zw.setDescricoesModalidades(cs.getDescricoesModalidades());
        zw.setNomeConsulta(cs.getNomeConsulta());
        zw.setDataCadastro(cs.getDataCadastro());
        zw.setExisteParcVencidaContrato(cs.getExisteParcVencidaContrato());
        zw.setEmpresausafreepass(cs.getEmpresaUsaFreePass());
        zw.setFreePassInicio(cs.getFreePassInicio());
        zw.setFreePassFim(cs.getFreePassFim());
        return zw;
    }

    public String getSituacaoMatriculaContrato() {
        return situacaoMatriculaContrato;
    }

    public void setSituacaoMatriculaContrato(String situacaoMatriculaContrato) {
        this.situacaoMatriculaContrato = situacaoMatriculaContrato;
    }

    public String getCodigoAcesso() {
        return codigoAcesso;
    }

    public void setCodigoAcesso(String codigoAcesso) {
        this.codigoAcesso = codigoAcesso;
    }

    public String getModalidades() {
        return modalidades;
    }

    public void setModalidades(String modalidades) {
        this.modalidades = modalidades;
    }

    public String getDescricaoDuracao() {
        return descricaoDuracao;
    }

    public void setDescricaoDuracao(String descricaoDuracao) {
        this.descricaoDuracao = descricaoDuracao;
    }

    public Integer getSaldoCreditoTreino() {
        return saldoCreditoTreino;
    }

    public void setSaldoCreditoTreino(Integer saldoCreditoTreino) {
        this.saldoCreditoTreino = saldoCreditoTreino;
    }

    public Integer getTotalCreditoTreino() {
        return totalCreditoTreino;
    }

    public void setTotalCreditoTreino(Integer totalCreditoTreino) {
        this.totalCreditoTreino = totalCreditoTreino;
    }


    public String getDescricoesModalidades() {
        return descricoesModalidades;
    }

    public void setDescricoesModalidades(String descricoesModalidades) {
        this.descricoesModalidades = descricoesModalidades;
    }

    public Boolean getCrossfit() {
        return crossfit;
    }

    public void setCrossfit(Boolean crossfit) {
        this.crossfit = crossfit;
    }

    public String getNomeConsulta() {
        return nomeConsulta;
    }

    public void setNomeConsulta(String nomeConsulta) {
        this.nomeConsulta = nomeConsulta;
    }

    public String getSituacaoContratoOperacao() {
        return situacaoContratoOperacao;
    }

    public void setSituacaoContratoOperacao(String situacaoContratoOperacao) {
        this.situacaoContratoOperacao = situacaoContratoOperacao;
    }

    public Date getDataCadastro() {
        return dataCadastro;
    }

    public void setDataCadastro(Date dataCadastro) {
        this.dataCadastro = dataCadastro;
    }

    public Date getDataVencimentoTreino() {
        return dataVencimentoTreino;
    }

    public void setDataVencimentoTreino(Date dataVencimentoTreino) {
        this.dataVencimentoTreino = dataVencimentoTreino;
    }

    public Boolean getExisteParcVencidaContrato() {
        return existeParcVencidaContrato;
    }

    public void setExisteParcVencidaContrato(Boolean existeParcVencidaContrato) {
        this.existeParcVencidaContrato = existeParcVencidaContrato;
    }

    public Boolean getEmpresausafreepass() {
        return empresausafreepass;
    }

    public void setEmpresausafreepass(Boolean empresausafreepass) {
        this.empresausafreepass = empresausafreepass;
    }

    public Date getFreePassInicio() {
        return freePassInicio;
    }

    public void setFreePassInicio(Date freePassInicio) {
        this.freePassInicio = freePassInicio;
    }

    public Date getFreePassFim() {
        return freePassFim;
    }

    public void setFreePassFim(Date freePassFim) {
        this.freePassFim = freePassFim;
    }
}
