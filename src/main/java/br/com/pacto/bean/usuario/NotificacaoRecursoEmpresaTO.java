package br.com.pacto.bean.usuario;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "DTO para notificação de uso de recursos específicos da empresa")
public class NotificacaoRecursoEmpresaTO {

    @ApiModelProperty(value = "Chave de identificação da empresa", example = "empresa123", required = true)
    private String chave;

    @ApiModelProperty(value = "Nome do usuário que está utilizando o recurso", example = "João Silva", required = true)
    private String nomeUsuario;

    @ApiModelProperty(value = "Nome do recurso que está sendo notificado", example = "AVALIACAO_FISICA", required = true)
    private String recursoNotificar;

    @ApiModelProperty(value = "Identificador da empresa", example = "1", required = true)
    private Integer empresaId;

    @ApiModelProperty(value = "Nome da empresa", example = "Academia Exemplo", required = true)
    private String nomeEmpresa;

    @ApiModelProperty(value = "Cidade onde a empresa está localizada", example = "São Paulo")
    private String cidade;

    @ApiModelProperty(value = "Estado onde a empresa está localizada", example = "SP")
    private String estado;

    @ApiModelProperty(value = "País onde a empresa está localizada", example = "Brasil")
    private String pais;

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public String getNomeUsuario() {
        return nomeUsuario;
    }

    public void setNomeUsuario(String nomeUsuario) {
        this.nomeUsuario = nomeUsuario;
    }

    public Integer getEmpresaId() {
        return empresaId;
    }

    public void setEmpresaId(Integer empresaId) {
        this.empresaId = empresaId;
    }

    public String getNomeEmpresa() {
        return nomeEmpresa;
    }

    public void setNomeEmpresa(String nomeEmpresa) {
        this.nomeEmpresa = nomeEmpresa;
    }

    public String getCidade() {
        return cidade;
    }

    public void setCidade(String cidade) {
        this.cidade = cidade;
    }

    public String getEstado() {
        return estado;
    }

    public void setEstado(String estado) {
        this.estado = estado;
    }

    public String getPais() {
        return pais;
    }

    public void setPais(String pais) {
        this.pais = pais;
    }

    public String getRecursoNotificar() {
        return recursoNotificar;
    }

    public void setRecursoNotificar(String recursoNotificar) {
        this.recursoNotificar = recursoNotificar;
    }
}
