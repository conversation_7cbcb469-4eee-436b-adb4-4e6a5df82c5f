/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.usuario;

import io.swagger.annotations.ApiModel;

/**
 *
 * <AUTHOR>
 */
@ApiModel(description = "Enumerador que representa os possíveis status de alcance de um objetivo do aluno")
public enum StatusObjetivoAlunoEnum {
    NAO_ALCANCOU(0, "Não Alcançou"),
    ALCANCOU_PARCIALMENTE(1, "Alcançou Parcialmente"),
    ALCANCOU(2, "Alcançou");

    private Integer id;
    private String descricao;

    StatusObjetivoAlunoEnum(Integer id, String descricao) {
        this.id = id;
        this.descricao = descricao;
    }

    public static StatusObjetivoAlunoEnum getStatusObjetivoEnum(Integer id){
        for(StatusObjetivoAlunoEnum status : StatusObjetivoAlunoEnum.values()){
            if(status.getId().equals(id)){
                return status;
            }
        }
        return null;
    }

    public static StatusObjetivoAlunoEnum getEnumById(Integer id){
        for(StatusObjetivoAlunoEnum status : StatusObjetivoAlunoEnum.values()){
            if(status.getId().equals(id)){
                return status;
            }
        }
        return null;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}
