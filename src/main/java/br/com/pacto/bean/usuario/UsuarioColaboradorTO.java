package br.com.pacto.bean.usuario;

import br.com.pacto.bean.colaborador.SituacaoColaboradorEnum;
import br.com.pacto.bean.colaborador.TipoUsuarioColaboradorEnum;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * paulo 19/10/2018
 */

@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "DTO para transferência de dados de usuário colaborador nas operações de cadastro e atualização")
public class UsuarioColaboradorTO {

    @ApiModelProperty(value = "Identificador único do usuário colaborador", example = "1")
    private Integer id;

    @ApiModelProperty(value = "Identificador do colaborador associado ao usuário", example = "10", required = true)
    private Integer colaboradorId;

    @ApiModelProperty(value = "Nome de usuário para acesso ao aplicativo", example = "joao.silva", required = true)
    private String appUserName;

    @ApiModelProperty(value = "Senha para acesso ao aplicativo", example = "senha123", required = true)
    private String appPassword;

    @ApiModelProperty(value = "Tipo de usuário colaborador no sistema. \n\n" +
            "<strong>Valores disponíveis</strong>\n" +
            "- PROFESSOR (Professor)\n" +
            "- COORDENADOR (Coordenador)\n" +
            "- CONSULTOR (Consultor)\n", example = "PROFESSOR")
    private TipoUsuarioColaboradorEnum tipoUsuario;

    @ApiModelProperty(value = "Código do perfil de permissões do usuário", example = "1")
    private Integer perfilUsuarioPermissoes;

    @ApiModelProperty(value = "Dados da imagem do usuário em formato Base64", example = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==")
    private String imagemData;

    @ApiModelProperty(value = "Extensão do arquivo de imagem", example = "jpg")
    private String extensaoImagem;

    @ApiModelProperty(value = "Situação do colaborador no sistema. \n\n" +
            "<strong>Valores disponíveis</strong>\n" +
            "- ATIVO (Ativo)\n" +
            "- INATIVO (Inativo)\n", example = "ATIVO")
    private SituacaoColaboradorEnum situacao;

    @ApiModelProperty(value = "Endereço de email do usuário colaborador", example = "<EMAIL>")
    private String email;

    @ApiModelProperty(value = "Número de celular do usuário colaborador", example = "(11) 99999-9999")
    private String celular;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getColaboradorId() {
        return colaboradorId;
    }

    public void setColaboradorId(Integer colaboradorId) {
        this.colaboradorId = colaboradorId;
    }

    public String getAppUserName() {
        return appUserName;
    }

    public void setAppUserName(String appUserName) {
        this.appUserName = appUserName;
    }

    public String getAppPassword() {
        return appPassword;
    }

    public void setAppPassword(String appPassword) {
        this.appPassword = appPassword;
    }

    public TipoUsuarioColaboradorEnum getTipoUsuario() {
        return tipoUsuario;
    }

    public void setTipoUsuario(TipoUsuarioColaboradorEnum tipoUsuario) {
        this.tipoUsuario = tipoUsuario;
    }

    public Integer getPerfilUsuarioPermissoes() {
        return perfilUsuarioPermissoes;
    }

    public void setPerfilUsuarioPermissoes(Integer perfilUsuarioPermissoes) {
        this.perfilUsuarioPermissoes = perfilUsuarioPermissoes;
    }

    public String getImagemData() {
        return imagemData;
    }

    public void setImagemData(String imagemData) {
        this.imagemData = imagemData;
    }

    public String getExtensaoImagem() {
        return extensaoImagem;
    }

    public void setExtensaoImagem(String extensaoImagem) {
        this.extensaoImagem = extensaoImagem;
    }

    public SituacaoColaboradorEnum getSituacao() {
        return situacao;
    }

    public void setSituacao(SituacaoColaboradorEnum situacao) {
        this.situacao = situacao;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getCelular() {
        return celular;
    }

    public void setCelular(String celular) {
        this.celular = celular;
    }
}
