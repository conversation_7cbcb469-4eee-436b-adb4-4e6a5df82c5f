package br.com.pacto.bean.usuario;

import br.com.pacto.bean.colaborador.ColaboradorSimplesTO;
import br.com.pacto.bean.colaborador.PerfilUsuarioResponseTO;
import br.com.pacto.bean.colaborador.SituacaoColaboradorEnum;
import br.com.pacto.bean.colaborador.TipoUsuarioColaboradorEnum;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "DTO de resposta contendo dados completos de um usuário colaborador")
public class UsuarioColaboradorResponseTO {

    @ApiModelProperty(value = "Endereço de email do usuário colaborador", example = "<EMAIL>")
    private String email;

    @ApiModelProperty(value = "Identificador único do usuário colaborador", example = "1")
    private Integer id;

    @ApiModelProperty(value = "Dados simplificados do colaborador associado ao usuário")
    private ColaboradorSimplesTO colaborador;

    @ApiModelProperty(value = "Nome de usuário para acesso ao aplicativo", example = "joao.silva")
    private String appUserName;

    @ApiModelProperty(value = "Senha para acesso ao aplicativo", example = "senha123")
    private String appPassword;

    @ApiModelProperty(value = "Lista de empresas associadas ao usuário", example = "Academia Exemplo | Filial Centro")
    private String empresas;

    @ApiModelProperty(value = "Tipo de usuário colaborador no sistema. \n\n" +
            "<strong>Valores disponíveis</strong>\n" +
            "- PROFESSOR (Professor)\n" +
            "- COORDENADOR (Coordenador)\n" +
            "- CONSULTOR (Consultor)\n", example = "PROFESSOR")
    private TipoUsuarioColaboradorEnum tipoUsuario;

    @ApiModelProperty(value = "Dados do perfil de permissões do usuário")
    private PerfilUsuarioResponseTO perfilUsuarioPermissoes;

    @ApiModelProperty(value = "Dados da imagem do usuário em formato Base64", example = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==")
    private String imagemData;

    @ApiModelProperty(value = "Tipo/extensão da imagem do usuário", example = "jpg")
    private String tipoImagem;

    @ApiModelProperty(value = "Situação do colaborador no sistema. \n\n" +
            "<strong>Valores disponíveis</strong>\n" +
            "- ATIVO (Ativo)\n" +
            "- INATIVO (Inativo)\n", example = "ATIVO")
    private SituacaoColaboradorEnum situacao;

    @ApiModelProperty(value = "Indica se o email do usuário foi verificado", example = "true")
    private Boolean emailVerificado;

    public UsuarioColaboradorResponseTO(Usuario usuario, Boolean treinoIndependente, String empresas) {
        this.id = usuario.getCodigo();
        this.empresas = empresas;
        this.colaborador = new ColaboradorSimplesTO(usuario.getProfessor(), treinoIndependente);
        this.appUserName = usuario.getUserName();
        this.tipoUsuario = usuario.getTipo().obterTipoUsuarioColaborador();
        this.perfilUsuarioPermissoes = new PerfilUsuarioResponseTO(usuario.getPerfil());
        this.email = usuario.getEmail();
        this.situacao = usuario.getStatus() == null ? SituacaoColaboradorEnum.INATIVO : SituacaoColaboradorEnum.valueOf(usuario.getStatus().name());
        this.emailVerificado = usuario.getUsuarioEmail() != null ? usuario.getUsuarioEmail().getVerificado() : false;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public ColaboradorSimplesTO getColaborador() {
        return colaborador;
    }

    public void setColaborador(ColaboradorSimplesTO colaborador) {
        this.colaborador = colaborador;
    }

    public String getAppUserName() {
        return appUserName;
    }

    public void setAppUserName(String appUserName) {
        this.appUserName = appUserName;
    }

    public String getAppPassword() {
        return appPassword;
    }

    public void setAppPassword(String appPassword) {
        this.appPassword = appPassword;
    }

    public TipoUsuarioColaboradorEnum getTipoUsuario() {
        return tipoUsuario;
    }

    public void setTipoUsuario(TipoUsuarioColaboradorEnum tipoUsuario) {
        this.tipoUsuario = tipoUsuario;
    }

    public PerfilUsuarioResponseTO getPerfilUsuarioPermissoes() {
        return perfilUsuarioPermissoes;
    }

    public void setPerfilUsuarioPermissoes(PerfilUsuarioResponseTO perfilUsuarioPermissoes) {
        this.perfilUsuarioPermissoes = perfilUsuarioPermissoes;
    }

    public String getImagemData() {
        return imagemData;
    }

    public void setImagemData(String imagemData) {
        this.imagemData = imagemData;
    }

    public String getTipoImagem() {
        return tipoImagem;
    }

    public void setTipoImagem(String tipoImagem) {
        this.tipoImagem = tipoImagem;
    }

    public SituacaoColaboradorEnum getSituacao() {
        return situacao;
    }

    public void setSituacao(SituacaoColaboradorEnum situacao) {
        this.situacao = situacao;
    }

    public String getEmpresas() {
        return empresas;
    }

    public void setEmpresas(String empresas) {
        this.empresas = empresas;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Boolean getEmailVerificado() {
        return emailVerificado;
    }

    public void setEmailVerificado(Boolean emailVerificado) {
        this.emailVerificado = emailVerificado;
    }
}
