package br.com.pacto.bean.avaliacao;

import br.com.pacto.util.impl.UtilReflection;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Informações sobre a avaliação postural com visão anterior do aluno.")
public class AnamnesePosturalAnteriorDTO {

    @ApiModelProperty(value = "Indica se há joelho valgo (desvio para dentro).", example = "true")
    private Boolean joelhoValgo;

    @ApiModelProperty(value = "Indica se há joelho varo (desvio para fora).", example = "false")
    private Boolean joelhoVaro;

    @ApiModelProperty(value = "Indica se o pé está em abdução (virado para fora).", example = "true")
    private Boolean peAbduto;

    @ApiModelProperty(value = "Indica se o pé está em adução (virado para dentro).", example = "false")
    private Boolean peAduto;

    public AnamnesePosturalAnteriorDTO() {
    }

    public AnamnesePosturalAnteriorDTO(List<ItemAvaliacaoPostural> itemsAvaliacaoPostural) throws Exception {
        List<String> attributes = UtilReflection.getListAttributes(AnamnesePosturalAnteriorDTO.class);
        for (ItemAvaliacaoPostural i : itemsAvaliacaoPostural) {
            if (attributes.contains(i.getItem().getField())) {
                UtilReflection.setValor(this, i.getSelecionado(), i.getItem().getField());
            }
        }
    }

    public Boolean getJoelhoValgo() {
        return joelhoValgo;
    }

    public void setJoelhoValgo(Boolean joelhoValgo) {
        this.joelhoValgo = joelhoValgo;
    }

    public Boolean getJoelhoVaro() {
        return joelhoVaro;
    }

    public void setJoelhoVaro(Boolean joelhoVaro) {
        this.joelhoVaro = joelhoVaro;
    }

    public Boolean getPeAbduto() {
        return peAbduto;
    }

    public void setPeAbduto(Boolean peAbduto) {
        this.peAbduto = peAbduto;
    }

    public Boolean getPeAduto() {
        return peAduto;
    }

    public void setPeAduto(Boolean peAduto) {
        this.peAduto = peAduto;
    }
}
