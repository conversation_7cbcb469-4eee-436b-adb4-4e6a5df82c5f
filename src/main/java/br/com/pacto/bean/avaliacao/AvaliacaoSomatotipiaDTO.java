package br.com.pacto.bean.avaliacao;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Dados da avaliação de somatotipia para classificação do tipo físico do aluno.")
public class AvaliacaoSomatotipiaDTO {

    @ApiModelProperty(value = "Componente endomorfia da somatotipia (tendência à adiposidade).", example = "3.2")
    private Double endomorfia;

    @ApiModelProperty(value = "Componente mesomorfia da somatotipia (desenvolvimento muscular).", example = "5.1")
    private Double mesomorfia;

    @ApiModelProperty(value = "Componente ectomorfia da somatotipia (linearidade corporal).", example = "2.8")
    private Double ectomorfia;

    @ApiModelProperty(value = "Medida da dobra cutânea do tríceps (mm).", example = "11.5")
    private Double dobraTriceps;

    @ApiModelProperty(value = "Medida da dobra cutânea supra-espinhal (mm).", example = "9.2")
    private Double dobraSupraEspinhal;

    @ApiModelProperty(value = "Medida da dobra cutânea subescapular (mm).", example = "10.8")
    private Double dobraSubescapular;

    @ApiModelProperty(value = "Medida da dobra cutânea da panturrilha (mm).", example = "8.5")
    private Double dobraPanturrilha;

    @ApiModelProperty(value = "Perímetro da panturrilha direita (cm).", example = "36.0")
    private Double perimetroPanturrilhaDireita;

    @ApiModelProperty(value = "Perímetro do braço contraído direito (cm).", example = "32.5")
    private Double perimetroBracoContraidoDireito;

    @ApiModelProperty(value = "Diâmetro do punho (cm).", example = "5.8")
    private Double diametroPunho;

    @ApiModelProperty(value = "Diâmetro do joelho (cm).", example = "9.2")
    private Double diametroJoelho;

    @ApiModelProperty(value = "Diâmetro do cotovelo (cm).", example = "6.5")
    private Double diametroCotovelo;

    @ApiModelProperty(value = "Diâmetro do tornozelo (cm).", example = "7.1")
    private Double diametroTornozelo;

    public AvaliacaoSomatotipiaDTO() {
    }

    public AvaliacaoSomatotipiaDTO(AvaliacaoFisica avaliacaoFisica, PesoOsseo po) {
        this.endomorfia = avaliacaoFisica.getEndomorfia();
        this.mesomorfia = avaliacaoFisica.getMesomorfia();
        this.ectomorfia = avaliacaoFisica.getEctomorfia();
        this.dobraTriceps = avaliacaoFisica.getTriceps();
        this.dobraSupraEspinhal = avaliacaoFisica.getSupraEspinhal();
        this.dobraSubescapular = avaliacaoFisica.getSubescapular();
        this.dobraPanturrilha = avaliacaoFisica.getPanturrilha();
        this.perimetroPanturrilhaDireita = avaliacaoFisica.getPanturrilhaDir();
        this.perimetroBracoContraidoDireito = avaliacaoFisica.getBracoContraidoDir();
        this.diametroPunho = po.getDiametroPunho();
        this.diametroJoelho = po.getDiametroFemur();
        this.diametroCotovelo = po.getDiametroCotovelo();
        this.diametroTornozelo = po.getDiametroTornozelo();
    }

    public Double getEndomorfia() {
        return endomorfia;
    }

    public void setEndomorfia(Double endomorfia) {
        this.endomorfia = endomorfia;
    }

    public Double getMesomorfia() {
        return mesomorfia;
    }

    public void setMesomorfia(Double mesomorfia) {
        this.mesomorfia = mesomorfia;
    }

    public Double getEctomorfia() {
        return ectomorfia;
    }

    public void setEctomorfia(Double ectomorfia) {
        this.ectomorfia = ectomorfia;
    }

    public Double getDobraTriceps() {
        return dobraTriceps;
    }

    public void setDobraTriceps(Double dobraTriceps) {
        this.dobraTriceps = dobraTriceps;
    }

    public Double getDobraSupraEspinhal() {
        return dobraSupraEspinhal;
    }

    public void setDobraSupraEspinhal(Double dobraSupraEspinhal) {
        this.dobraSupraEspinhal = dobraSupraEspinhal;
    }

    public Double getDobraSubescapular() {
        return dobraSubescapular;
    }

    public void setDobraSubescapular(Double dobraSubescapular) {
        this.dobraSubescapular = dobraSubescapular;
    }

    public Double getDobraPanturrilha() {
        return dobraPanturrilha;
    }

    public void setDobraPanturrilha(Double dobraPanturrilha) {
        this.dobraPanturrilha = dobraPanturrilha;
    }

    public Double getPerimetroPanturrilhaDireita() {
        return perimetroPanturrilhaDireita;
    }

    public void setPerimetroPanturrilhaDireita(Double perimetroPanturrilhaDireita) {
        this.perimetroPanturrilhaDireita = perimetroPanturrilhaDireita;
    }

    public Double getPerimetroBracoContraidoDireito() {
        return perimetroBracoContraidoDireito;
    }

    public void setPerimetroBracoContraidoDireito(Double perimetroBracoContraidoDireito) {
        this.perimetroBracoContraidoDireito = perimetroBracoContraidoDireito;
    }

    public Double getDiametroPunho() {
        return diametroPunho;
    }

    public void setDiametroPunho(Double diametroPunho) {
        this.diametroPunho = diametroPunho;
    }

    public Double getDiametroJoelho() {
        return diametroJoelho;
    }

    public void setDiametroJoelho(Double diametroJoelho) {
        this.diametroJoelho = diametroJoelho;
    }

    public Double getDiametroCotovelo() {
        return diametroCotovelo;
    }

    public void setDiametroCotovelo(Double diametroCotovelo) {
        this.diametroCotovelo = diametroCotovelo;
    }

    public Double getDiametroTornozelo() {
        return diametroTornozelo;
    }

    public void setDiametroTornozelo(Double diametroTornozelo) {
        this.diametroTornozelo = diametroTornozelo;
    }
}
