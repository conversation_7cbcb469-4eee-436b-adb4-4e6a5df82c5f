package br.com.pacto.bean.avaliacao.evolucao;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;

/**
 * <AUTHOR> 30/01/2019
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Dados de evolução dos grupos musculares trabalhados pelo aluno")
public class EvolucaoGrupoTrabalhadoDTO implements Serializable {

    @ApiModelProperty(value = "Lista dos grupos musculares trabalhados no programa de treino atual do aluno")
    private Collection<GrupoTrabalhadoItemDTO> programaAtual;

    @ApiModelProperty(value = "Lista dos grupos musculares trabalhados durante o período especificado na consulta")
    private Collection<GrupoTrabalhadoItemDTO> durantePeriodo;

    public EvolucaoGrupoTrabalhadoDTO() {
        this.programaAtual = new ArrayList<GrupoTrabalhadoItemDTO>();
        this.durantePeriodo = new ArrayList<GrupoTrabalhadoItemDTO>();
    }

    public Collection<GrupoTrabalhadoItemDTO> getProgramaAtual() {
        return programaAtual;
    }

    public void setProgramaAtual(Collection<GrupoTrabalhadoItemDTO> programaAtual) {
        this.programaAtual = programaAtual;
    }

    public Collection<GrupoTrabalhadoItemDTO> getDurantePeriodo() {
        return durantePeriodo;
    }

    public void setDurantePeriodo(Collection<GrupoTrabalhadoItemDTO> durantePeriodo) {
        this.durantePeriodo = durantePeriodo;
    }
}
