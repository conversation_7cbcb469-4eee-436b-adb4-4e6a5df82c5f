package br.com.pacto.bean.avaliacao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Dados da bioimpedância relacionados à avaliação das dobras cutâneas.")
public class AnamneseDobrasBioDTO {

    @ApiModelProperty(value = "Índice de Massa Corporal (IMC).", example = "24.7")
    private Double imc;

    @ApiModelProperty(value = "Massa gorda em kg.", example = "18.3")
    private Double massaGorda;

    @ApiModelProperty(value = "Percentual de massa magra (%).", example = "40.2")
    private Double percentMassaMagra;

    @ApiModelProperty(value = "Quantidade de resíduos em kg.", example = "2.1")
    private Double residuos;

    @ApiModelProperty(value = "Quantidade ideal de gordura em kg.", example = "15.0")
    private Double gorduraIdeal;

    @ApiModelProperty(value = "Reatância elétrica corporal.", example = "500.0")
    private Double reatancia;

    @ApiModelProperty(value = "Necessidade energética física (NEC Física).", example = "1500.0")
    private Double necFisica;

    @ApiModelProperty(value = "Necessidade energética calórica (NEC Calórica).", example = "2000.0")
    private Double necCalorica;

    @ApiModelProperty(value = "Quantidade de gordura visceral em kg.", example = "3.5")
    private Double gorduraVisceral;

    @ApiModelProperty(value = "Percentual de massa gorda (%).", example = "22.5")
    private Double percentMassaGorda;

    @ApiModelProperty(value = "Massa magra em kg.", example = "48.0")
    private Double massaMagra;

    @ApiModelProperty(value = "Massa óssea em kg.", example = "3.0")
    private Double ossos;

    @ApiModelProperty(value = "Massa muscular em kg.", example = "40.0")
    private Double musculos;

    @ApiModelProperty(value = "Resistência elétrica corporal.", example = "600.0")
    private Double resistencia;

    @ApiModelProperty(value = "Percentual de água corporal (%).", example = "55.0")
    private Double percentAgua;

    @ApiModelProperty(value = "Taxa Metabólica Basal (TMB).", example = "1600.0")
    private Double tmb;

    @ApiModelProperty(value = "Idade metabólica estimada em anos.", example = "30")
    private Double idadeMetabolica;

    public AnamneseDobrasBioDTO() {
    }

    public AnamneseDobrasBioDTO(AvaliacaoFisica avaliacaoFisica) {
        this.imc = avaliacaoFisica.getImc();
        this.massaGorda = avaliacaoFisica.getMassaGorda();
        this.percentMassaMagra = avaliacaoFisica.getPercentualMassaMagra();
        this.residuos = avaliacaoFisica.getResidual();
        this.gorduraIdeal = avaliacaoFisica.getGorduraIdeal();
        this.reatancia = avaliacaoFisica.getReatancia();
        this.necFisica = avaliacaoFisica.getNecessidadeFisica();
        this.necCalorica = avaliacaoFisica.getNecessidadeCalorica();
        this.gorduraVisceral = avaliacaoFisica.getGorduraVisceral();
        this.percentMassaGorda = avaliacaoFisica.getPercentualGordura();
        this.massaMagra = avaliacaoFisica.getMassaMagra();
        this.ossos = avaliacaoFisica.getPesoOsseo();
        this.musculos = avaliacaoFisica.getPesoMuscular();
        this.resistencia = avaliacaoFisica.getResistencia();
        this.percentAgua = avaliacaoFisica.getPercentualAgua();
        this.tmb = avaliacaoFisica.getTmb();
        this.idadeMetabolica = avaliacaoFisica.getIdadeMetabolica();
    }

    public Double getImc() {
        return imc;
    }

    public void setImc(Double imc) {
        this.imc = imc;
    }

    public Double getMassaGorda() {
        return massaGorda;
    }

    public void setMassaGorda(Double massaGorda) {
        this.massaGorda = massaGorda;
    }

    public Double getPercentMassaMagra() {
        return percentMassaMagra;
    }

    public void setPercentMassaMagra(Double percentMassaMagra) {
        this.percentMassaMagra = percentMassaMagra;
    }

    public Double getResiduos() {
        return residuos;
    }

    public void setResiduos(Double residuos) {
        this.residuos = residuos;
    }

    public Double getGorduraIdeal() {
        return gorduraIdeal;
    }

    public void setGorduraIdeal(Double gorduraIdeal) {
        this.gorduraIdeal = gorduraIdeal;
    }

    public Double getReatancia() {
        return reatancia;
    }

    public void setReatancia(Double reatancia) {
        this.reatancia = reatancia;
    }

    public Double getNecFisica() {
        return necFisica;
    }

    public void setNecFisica(Double necFisica) {
        this.necFisica = necFisica;
    }

    public Double getNecCalorica() {
        return necCalorica;
    }

    public void setNecCalorica(Double necCalorica) {
        this.necCalorica = necCalorica;
    }

    public Double getGorduraVisceral() {
        return gorduraVisceral;
    }

    public void setGorduraVisceral(Double gorduraVisceral) {
        this.gorduraVisceral = gorduraVisceral;
    }

    public Double getPercentMassaGorda() {
        return percentMassaGorda;
    }

    public void setPercentMassaGorda(Double percentMassaGorda) {
        this.percentMassaGorda = percentMassaGorda;
    }

    public Double getMassaMagra() {
        return massaMagra;
    }

    public void setMassaMagra(Double massaMagra) {
        this.massaMagra = massaMagra;
    }

    public Double getOssos() {
        return ossos;
    }

    public void setOssos(Double ossos) {
        this.ossos = ossos;
    }

    public Double getMusculos() {
        return musculos;
    }

    public void setMusculos(Double musculos) {
        this.musculos = musculos;
    }

    public Double getResistencia() {
        return resistencia;
    }

    public void setResistencia(Double resistencia) {
        this.resistencia = resistencia;
    }

    public Double getPercentAgua() {
        return percentAgua;
    }

    public void setPercentAgua(Double percentAgua) {
        this.percentAgua = percentAgua;
    }

    public Double getTmb() {
        return tmb;
    }

    public void setTmb(Double tmb) {
        this.tmb = tmb;
    }

    public Double getIdadeMetabolica() {
        return idadeMetabolica;
    }

    public void setIdadeMetabolica(Double idadeMetabolica) {
        this.idadeMetabolica = idadeMetabolica;
    }
}
