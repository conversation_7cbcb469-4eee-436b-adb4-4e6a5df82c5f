package br.com.pacto.bean.avaliacao;

import br.com.pacto.util.impl.UtilReflection;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Informações sobre a avaliação postural com visão lateral do aluno.")
public class AnamnesePosturaLateralDTO {

    @ApiModelProperty(value = "Indica se há anteversão do quadril.", example = "false")
    private Boolean anterversaoQuadril;

    @ApiModelProperty(value = "Indica se há hipercifose torácica.", example = "true")
    private Boolean hipercifoseToracica;

    @ApiModelProperty(value = "Indica se há hiperlordose cervical.", example = "false")
    private Boolean hiperlordoseCervical;

    @ApiModelProperty(value = "Indica se há hiperlordose lombar.", example = "true")
    private Boolean hiperlordoseLombar;

    @ApiModelProperty(value = "Indica se o joelho está em flexo.", example = "false")
    private Boolean joelhoFlexo;

    @ApiModelProperty(value = "Indica se o joelho está recurvado.", example = "true")
    private Boolean joelhoRecurvado;

    @ApiModelProperty(value = "Indica se há protusão abdominal.", example = "false")
    private Boolean protusaoAbdominal;

    @ApiModelProperty(value = "Indica se o pé está em posição de calcâneo.", example = "false")
    private Boolean peCalcaneo;

    @ApiModelProperty(value = "Indica se o pé é cavo.", example = "true")
    private Boolean peCavo;

    @ApiModelProperty(value = "Indica se o pé é equino.", example = "false")
    private Boolean peEquino;

    @ApiModelProperty(value = "Indica se o pé é plano.", example = "false")
    private Boolean pePlano;

    @ApiModelProperty(value = "Indica se há retificação da cervical.", example = "true")
    private Boolean retificacaoCervical;

    @ApiModelProperty(value = "Indica se há retificação da lombar.", example = "false")
    private Boolean retificacaoLombar;

    @ApiModelProperty(value = "Indica se há retroversão do quadril.", example = "false")
    private Boolean retroversaoQuadril;

    @ApiModelProperty(value = "Indica se há rotação interna dos ombros.", example = "true")
    private Boolean rotacaoInternaOmbros;

    public AnamnesePosturaLateralDTO() {
    }

    public AnamnesePosturaLateralDTO(List<ItemAvaliacaoPostural> itemsAvaliacaoPostural) throws Exception {
        List<String> attributes = UtilReflection.getListAttributes(AnamnesePosturaLateralDTO.class);
        for (ItemAvaliacaoPostural i : itemsAvaliacaoPostural) {
            if (attributes.contains(i.getItem().getField())) {
                UtilReflection.setValor(this, i.getSelecionado(), i.getItem().getField());
            }
        }
    }

    public Boolean getAnterversaoQuadril() {
        return anterversaoQuadril;
    }

    public void setAnterversaoQuadril(Boolean anterversaoQuadril) {
        this.anterversaoQuadril = anterversaoQuadril;
    }

    public Boolean getHipercifoseToracica() {
        return hipercifoseToracica;
    }

    public void setHipercifoseToracica(Boolean hipercifoseToracica) {
        this.hipercifoseToracica = hipercifoseToracica;
    }

    public Boolean getHiperlordoseCervical() {
        return hiperlordoseCervical;
    }

    public void setHiperlordoseCervical(Boolean hiperlordoseCervical) {
        this.hiperlordoseCervical = hiperlordoseCervical;
    }

    public Boolean getHiperlordoseLombar() {
        return hiperlordoseLombar;
    }

    public void setHiperlordoseLombar(Boolean hiperlordoseLombar) {
        this.hiperlordoseLombar = hiperlordoseLombar;
    }

    public Boolean getJoelhoFlexo() {
        return joelhoFlexo;
    }

    public void setJoelhoFlexo(Boolean joelhoFlexo) {
        this.joelhoFlexo = joelhoFlexo;
    }

    public Boolean getJoelhoRecurvado() {
        return joelhoRecurvado;
    }

    public void setJoelhoRecurvado(Boolean joelhoRecurvado) {
        this.joelhoRecurvado = joelhoRecurvado;
    }

    public Boolean getProtusaoAbdominal() {
        return protusaoAbdominal;
    }

    public void setProtusaoAbdominal(Boolean protusaoAbdominal) {
        this.protusaoAbdominal = protusaoAbdominal;
    }

    public Boolean getPeCalcaneo() {
        return peCalcaneo;
    }

    public void setPeCalcaneo(Boolean peCalcaneo) {
        this.peCalcaneo = peCalcaneo;
    }

    public Boolean getPeCavo() {
        return peCavo;
    }

    public void setPeCavo(Boolean peCavo) {
        this.peCavo = peCavo;
    }

    public Boolean getPeEquino() {
        return peEquino;
    }

    public void setPeEquino(Boolean peEquino) {
        this.peEquino = peEquino;
    }

    public Boolean getPePlano() {
        return pePlano;
    }

    public void setPePlano(Boolean pePlano) {
        this.pePlano = pePlano;
    }

    public Boolean getRetificacaoCervical() {
        return retificacaoCervical;
    }

    public void setRetificacaoCervical(Boolean retificacaoCervical) {
        this.retificacaoCervical = retificacaoCervical;
    }

    public Boolean getRetificacaoLombar() {
        return retificacaoLombar;
    }

    public void setRetificacaoLombar(Boolean retificacaoLombar) {
        this.retificacaoLombar = retificacaoLombar;
    }

    public Boolean getRetroversaoQuadril() {
        return retroversaoQuadril;
    }

    public void setRetroversaoQuadril(Boolean retroversaoQuadril) {
        this.retroversaoQuadril = retroversaoQuadril;
    }

    public Boolean getRotacaoInternaOmbros() {
        return rotacaoInternaOmbros;
    }

    public void setRotacaoInternaOmbros(Boolean rotacaoInternaOmbros) {
        this.rotacaoInternaOmbros = rotacaoInternaOmbros;
    }
}
