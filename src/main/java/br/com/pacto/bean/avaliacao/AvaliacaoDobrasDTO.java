package br.com.pacto.bean.avaliacao;

import br.com.pacto.util.enumeradores.ProtocolosAvaliacaoFisicaEnum;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Dados das medidas de dobras cutâneas para avaliação física.")
public class AvaliacaoDobrasDTO {


    @ApiModelProperty(
            value = "Protocolo utilizado para avaliação das dobras cutâneas.\n\n" +
                    "Valores possíveis:\n" +
                    "- POLLOCK_3_DOBRAS: Avaliação com 3 dobras (abdominal, coxa, tórax)\n" +
                    "- POLLOCK_7_DOBRAS: Avaliação com 7 dobras (abdominal, coxa, tórax, triceps, axilar média, supra-ilíaca, subescapular)\n" +
                    "- GUEDES: Avaliação Guedes com triceps, supra-ilíaca, abdominal, subescapular, coxa\n" +
                    "- FAULKNER_DOBRAS: Avaliação Faulkner com triceps, subescapular, supra-ilíaca, abdominal\n" +
                    "- BIOIMPEDANCIA: Avaliação por bioimpedância\n" +
                    "- WELTMAN_OBESO: Avaliação Weltman para obesos (peso, altura)\n" +
                    "- POLLOCK_ADOLESCENTE: Avaliação Pollock para adolescentes (coxa, triceps)\n" +
                    "- SLAUGHTER: Avaliação Slaughter (panturrilha, triceps)\n" +
                    "- YUHASZ: Avaliação Yuhasz (abdominal, coxa, tórax, triceps, supra-ilíaca, subescapular)\n" +
                    "- TG_LOHMAN: Avaliação Lohman (coxa, triceps)",
            example = "POLLOCK_3_DOBRAS"
    )
    private ProtocolosAvaliacaoFisicaEnum protocolo;

    @ApiModelProperty(value = "Dobras abdominal em mm.", example = "12.5")
    private Double abdominal;

    @ApiModelProperty(value = "Dobras peitoral em mm.", example = "8.3")
    private Double peitoral;

    @ApiModelProperty(value = "Dobras da coxa medial em mm.", example = "15.2")
    private Double coxaMedial;

    @ApiModelProperty(value = "Dobras subescapular em mm.", example = "10.1")
    private Double subescapular;

    @ApiModelProperty(value = "Dobras supraespinhal em mm.", example = "9.7")
    private Double supraEspinhal;

    @ApiModelProperty(value = "Dobras supra-ilíaca em mm.", example = "14.2")
    private Double supraIliaca;

    @ApiModelProperty(value = "Dobras triceps em mm.", example = "11.6")
    private Double triceps;

    @ApiModelProperty(value = "Dobras biceps em mm.", example = "7.9")
    private Double biceps;

    @ApiModelProperty(value = "Dobras axilar média em mm.", example = "13.3")
    private Double axilarMedia;

    @ApiModelProperty(value = "Dobras panturrilha em mm.", example = "10.8")
    private Double panturrilha;

    @ApiModelProperty(value = "Dados de bioimpedância relacionados à avaliação de dobras.")
    private AnamneseDobrasBioDTO bioimpedancia;

    @ApiModelProperty(value = "Log de balança para registros adicionais.", example = "Leitura realizada em balança XYZ")
    private String logBalanca;

    public AvaliacaoDobrasDTO() {
    }

    public AvaliacaoDobrasDTO(AvaliacaoFisica avaliacaoFisica) {
        this.protocolo = avaliacaoFisica.getProtocolo();
        this.abdominal = avaliacaoFisica.getAbdominal();
        this.peitoral = avaliacaoFisica.getPeitoral();
        this.coxaMedial = avaliacaoFisica.getCoxaMedial();
        this.subescapular = avaliacaoFisica.getSubescapular();
        this.supraEspinhal = avaliacaoFisica.getSupraEspinhal();
        this.supraIliaca = avaliacaoFisica.getSupraIliaca();
        this.triceps = avaliacaoFisica.getTriceps();
        this.biceps = avaliacaoFisica.getBiceps();
        this.axilarMedia = avaliacaoFisica.getAxilarMedia();
        this.panturrilha = avaliacaoFisica.getPanturrilha();
        this.logBalanca = avaliacaoFisica.getLogBalanca();
        if(ProtocolosAvaliacaoFisicaEnum.BIOIMPEDANCIA.equals(this.protocolo)){
            this.bioimpedancia = new AnamneseDobrasBioDTO(avaliacaoFisica);
        }

    }

    public AnamneseDobrasBioDTO getBioimpedancia() {
        return bioimpedancia;
    }

    public void setBioimpedancia(AnamneseDobrasBioDTO bioimpedancia) {
        this.bioimpedancia = bioimpedancia;
    }

    public ProtocolosAvaliacaoFisicaEnum getProtocolo() {
        return protocolo;
    }

    public void setProtocolo(ProtocolosAvaliacaoFisicaEnum protocolo) {
        this.protocolo = protocolo;
    }

    public Double getAbdominal() {
        return abdominal;
    }

    public void setAbdominal(Double abdominal) {
        this.abdominal = abdominal;
    }

    public Double getPeitoral() {
        return peitoral;
    }

    public void setPeitoral(Double peitoral) {
        this.peitoral = peitoral;
    }

    public Double getCoxaMedial() {
        return coxaMedial;
    }

    public void setCoxaMedial(Double coxaMedial) {
        this.coxaMedial = coxaMedial;
    }

    public Double getSubescapular() {
        return subescapular;
    }

    public void setSubescapular(Double subescapular) {
        this.subescapular = subescapular;
    }

    public Double getSupraEspinhal() {
        return supraEspinhal;
    }

    public void setSupraEspinhal(Double supraEspinhal) {
        this.supraEspinhal = supraEspinhal;
    }

    public Double getSupraIliaca() {
        return supraIliaca;
    }

    public void setSupraIliaca(Double supraIliaca) {
        this.supraIliaca = supraIliaca;
    }

    public Double getTriceps() {
        return triceps;
    }

    public void setTriceps(Double triceps) {
        this.triceps = triceps;
    }

    public Double getBiceps() {
        return biceps;
    }

    public void setBiceps(Double biceps) {
        this.biceps = biceps;
    }

    public Double getAxilarMedia() {
        return axilarMedia;
    }

    public void setAxilarMedia(Double axilarMedia) {
        this.axilarMedia = axilarMedia;
    }

    public Double getPanturrilha() {
        return panturrilha;
    }

    public void setPanturrilha(Double panturrilha) {
        this.panturrilha = panturrilha;
    }

    public String getLogBalanca() {
        return logBalanca;
    }

    public void setLogBalanca(String logBalanca) {
        this.logBalanca = logBalanca;
    }
}
