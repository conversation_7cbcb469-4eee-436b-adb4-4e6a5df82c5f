package br.com.pacto.bean.replicarEmpresa;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Id;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import java.io.Serializable;
import java.util.Date;

@ApiModel(description = "Entidade que representa a configuração de replicação entre empresas da rede")
@Entity
@Table
public class ConfiguracaoRedeEmpresa implements Serializable {

    @ApiModelProperty(value = "Código único da configuração de replicação", example = "1")
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;

    @ApiModelProperty(value = "Chave da empresa de origem da replicação", example = "empresa_origem_123")
    private String chaveOrigem;

    @ApiModelProperty(value = "Chave da empresa de destino da replicação", example = "empresa_destino_456")
    private String chaveDestino;

    @ApiModelProperty(value = "Data de cadastro da configuração de replicação", example = "2024-01-15T10:30:00")
    private Date dataCadastro;

    @ApiModelProperty(value = "Data da última atualização da configuração de replicação", example = "2024-01-20T14:45:00")
    private Date dataAtualizacao;

    @ApiModelProperty(value = "Nome da unidade de destino da replicação", example = "Academia Pacto - Unidade Centro")
    private String nomeUnidade;

    @ApiModelProperty(value = "Mensagem com o status atual da replicação", example = "REPLICADO EM 20/01/2024 14:45. As configurações foram replicadas para a chave empresa_destino_456 com sucesso!")
    private String mensagemSituacao;

    public ConfiguracaoRedeEmpresa() {
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getChaveOrigem() {
        return chaveOrigem;
    }

    public void setChaveOrigem(String chaveOrigem) {
        this.chaveOrigem = chaveOrigem;
    }

    public String getChaveDestino() {
        return chaveDestino;
    }

    public void setChaveDestino(String chaveDestino) {
        this.chaveDestino = chaveDestino;
    }

    public Date getDataCadastro() {
        return dataCadastro;
    }

    public void setDataCadastro(Date dataCadastro) {
        this.dataCadastro = dataCadastro;
    }

    public Date getDataAtualizacao() {
        return dataAtualizacao;
    }

    public void setDataAtualizacao(Date dataAtualizacao) {
        this.dataAtualizacao = dataAtualizacao;
    }

    public String getNomeUnidade() {
        return nomeUnidade;
    }

    public void setNomeUnidade(String nomeUnidade) {
        this.nomeUnidade = nomeUnidade;
    }

    public String getMensagemSituacao() {
        return mensagemSituacao;
    }

    public void setMensagemSituacao(String mensagemSituacao) {
        this.mensagemSituacao = mensagemSituacao;
    }
}
