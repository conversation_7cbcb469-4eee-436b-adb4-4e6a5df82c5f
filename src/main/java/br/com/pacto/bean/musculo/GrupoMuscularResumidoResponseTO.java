package br.com.pacto.bean.musculo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Created by ulisses on 03/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Dados resumidos de um grupo muscular")
public class GrupoMuscularResumidoResponseTO {

    @ApiModelProperty(value = "Identificador único do grupo muscular", example = "1")
    private Integer id;

    @ApiModelProperty(value = "Nome do grupo muscular", example = "Membros superiores")
    private String nome;

    public GrupoMuscularResumidoResponseTO(){

    }

    public GrupoMuscularResumidoResponseTO(GrupoMuscular grupoMuscular){
        this.id = grupoMuscular.getCodigo();
        this.nome = grupoMuscular.getNome();
    }

    public GrupoMuscularResumidoResponseTO(Integer codigo, String nome) {
        this.id = codigo;
        this.nome = nome;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }
}
