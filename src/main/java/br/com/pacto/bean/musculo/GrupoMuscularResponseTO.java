package br.com.pacto.bean.musculo;

import br.com.pacto.bean.atividade.AtividadeGrupoMuscular;
import br.com.pacto.bean.atividade.AtividadeGrupoMuscularResponseTO;
import br.com.pacto.bean.avaliacao.PerimetriaEnum;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by ulisses on 03/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Grupos musculares da atividade")
public class GrupoMuscularResponseTO {

    @ApiModelProperty(value = "Identificador único do grupo muscular", example = "1")
    private Integer id;

    @ApiModelProperty(value = "Nome do grupo muscular", example = "Membros superiores")
    private String nome;

    @ApiModelProperty(value = "Lista de perimetrias associadas ao grupo muscular. \n\n" +
            "<strong>Valores disponíveis</strong>\n" +
            "- ANTEBRACO_ESQ (Antebraço esquerdo)\n" +
            "- ANTEBRACO_DIR (Antebraço direito)\n" +
            "- BRACO_RELAXADO_ESQ (Braço relaxado esquerdo)\n" +
            "- BRACO_RELAXADO_DIR (Braço relaxado direito)\n" +
            "- BRACO_CONTRAIDO_ESQ (Braço contraído esquerdo)\n" +
            "- BRACO_CONTRAIDO_DIR (Braço contraído direito)\n" +
            "- COXA_DISTAL_ESQ (Coxa distal esquerda)\n" +
            "- COXA_DISTAL_DIR (Coxa distal direita)\n" +
            "- COXA_MEDIAL_DIR (Coxa medial direita)\n" +
            "- COXA_MEDIAL_ESQ (Coxa medial esquerda)\n" +
            "- COXA_PROXIMAL_DIR (Coxa proximal direita)\n" +
            "- COXA_PROXIMAL_ESQ (Coxa proximal esquerda)\n" +
            "- PANTURRILHA_DIR (Panturrilha direita)\n" +
            "- PANTURRILHA_ESQ (Panturrilha esquerda)\n" +
            "- PESCOCO (Pescoço)\n" +
            "- OMBRO (Ombro)\n" +
            "- TORAX (Tórax/Busto)\n" +
            "- QUADRIL (Quadril)\n" +
            "- CINTURA (Cintura)\n" +
            "- CIRCUNFERENCIA_ABDOMINAL (Circunferência abdominal)\n" +
            "- GLUTEO (Glúteo)", example = "[\"TORAX\", \"OMBRO\"]")
    private List<PerimetriaEnum> perimetros = new ArrayList<>();

    @ApiModelProperty(value = "Lista de atividades físicas que exercitam este grupo muscular")
    private List<AtividadeGrupoMuscularResponseTO> atividades = new ArrayList<>();

    @ApiModelProperty(value = "Lista de músculos que pertencem a este grupo muscular")
    private List<MusculoGrupoResponseTO> musculos = new ArrayList<>();


    public GrupoMuscularResponseTO(){

    }

    public GrupoMuscularResponseTO(GrupoMuscular grupoMuscular){
        this.id = grupoMuscular.getCodigo();
        this.nome = grupoMuscular.getNome();
        if (grupoMuscular.getPerimetros() != null && grupoMuscular.getPerimetros().size() > 0){
            List<PerimetriaEnum> perimetros = new ArrayList<>();
            for (PerimetriaEnum perimetriaEnum : grupoMuscular.getPerimetros()) {
                perimetros.add(perimetriaEnum);
            }
            this.perimetros = perimetros;
        }
        if (grupoMuscular.getAtividades() != null){
            for (AtividadeGrupoMuscular atividadeGrupoMuscular: grupoMuscular.getAtividades()){
                getAtividades().add(new AtividadeGrupoMuscularResponseTO(atividadeGrupoMuscular.getAtividade().getCodigo(), atividadeGrupoMuscular.getAtividade().getNome()));
            }
        }
        if (grupoMuscular.getMusculos() != null){
            for (MusculoGrupoMuscular musculoGrupoMuscular: grupoMuscular.getMusculos()){
                getMusculos().add(new MusculoGrupoResponseTO(musculoGrupoMuscular.getMusculo().getCodigo(), musculoGrupoMuscular.getMusculo().getNome()));
            }
        }
    }

    public GrupoMuscularResponseTO(GrupoMuscular grupoMuscular, boolean resumido) {
        if (resumido) {
            this.id = grupoMuscular.getCodigo();
            this.nome = grupoMuscular.getNome();
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public List<PerimetriaEnum> getPerimetros() {
        return perimetros;
    }

    public void setPerimetros(List<PerimetriaEnum> perimetros) {
        this.perimetros = perimetros;
    }

    public List<AtividadeGrupoMuscularResponseTO> getAtividades() {
        return atividades;
    }

    public void setAtividades(List<AtividadeGrupoMuscularResponseTO> atividades) {
        this.atividades = atividades;
    }

    public List<MusculoGrupoResponseTO> getMusculos() {
        return musculos;
    }

    public void setMusculos(List<MusculoGrupoResponseTO> musculos) {
        this.musculos = musculos;
    }
}
