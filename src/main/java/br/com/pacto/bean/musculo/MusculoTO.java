package br.com.pacto.bean.musculo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Dados para cadastro e alteração de músculo")
public class MusculoTO {

    @ApiModelProperty(value = "Identificador único do músculo (usado apenas para alteração)", example = "1")
    private Integer id;

    @ApiModelProperty(value = "Nome do músculo", example = "Bíceps braquial", required = true)
    private String nome;

    @ApiModelProperty(value = "Lista de IDs das atividades que exercitam este músculo", example = "[1, 2, 3]")
    private List<Integer> atividadeIds;

    @ApiModelProperty(value = "Lista de IDs dos grupos musculares aos quais o músculo pertence", example = "[1, 2]")
    private List<Integer> grupoMuscularIds;


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public List<Integer> getAtividadeIds() {
        return atividadeIds;
    }

    public void setAtividadeIds(List<Integer> atividadeIds) {
        this.atividadeIds = atividadeIds;
    }

    public List<Integer> getGrupoMuscularIds() { return grupoMuscularIds; }

    public void setGrupoMuscularIds(List<Integer> grupoMuscularIds) { this.grupoMuscularIds = grupoMuscularIds; }
}
