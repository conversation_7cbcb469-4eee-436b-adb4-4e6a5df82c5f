package br.com.pacto.bean.musculo;

import br.com.pacto.bean.avaliacao.PerimetriaEnum;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * Created by ulisses on 07/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Dados para cadastro e alteração de grupo muscular")
public class GrupoMuscularTO {

    @ApiModelProperty(value = "Identificador único do grupo muscular (usado apenas para alteração)", example = "1")
    private Integer id;

    @ApiModelProperty(value = "Nome do grupo muscular", example = "Membros superiores", required = true)
    private String nome;

    @ApiModelProperty(value = "Lista de IDs das atividades que exercitam este grupo muscular", example = "[1, 2, 3]")
    private List<Integer> atividadeIds;

    @ApiModelProperty(value = "Lista de perimetrias associadas ao grupo muscular. \n\n" +
            "<strong>Valores disponíveis</strong>\n" +
            "- ANTEBRACO_ESQ (Antebraço esquerdo)\n" +
            "- ANTEBRACO_DIR (Antebraço direito)\n" +
            "- BRACO_RELAXADO_ESQ (Braço relaxado esquerdo)\n" +
            "- BRACO_RELAXADO_DIR (Braço relaxado direito)\n" +
            "- BRACO_CONTRAIDO_ESQ (Braço contraído esquerdo)\n" +
            "- BRACO_CONTRAIDO_DIR (Braço contraído direito)\n" +
            "- COXA_DISTAL_ESQ (Coxa distal esquerda)\n" +
            "- COXA_DISTAL_DIR (Coxa distal direita)\n" +
            "- COXA_MEDIAL_DIR (Coxa medial direita)\n" +
            "- COXA_MEDIAL_ESQ (Coxa medial esquerda)\n" +
            "- COXA_PROXIMAL_DIR (Coxa proximal direita)\n" +
            "- COXA_PROXIMAL_ESQ (Coxa proximal esquerda)\n" +
            "- PANTURRILHA_DIR (Panturrilha direita)\n" +
            "- PANTURRILHA_ESQ (Panturrilha esquerda)\n" +
            "- PESCOCO (Pescoço)\n" +
            "- OMBRO (Ombro)\n" +
            "- TORAX (Tórax/Busto)\n" +
            "- QUADRIL (Quadril)\n" +
            "- CINTURA (Cintura)\n" +
            "- CIRCUNFERENCIA_ABDOMINAL (Circunferência abdominal)\n" +
            "- GLUTEO (Glúteo)", example = "[\"TORAX\", \"OMBRO\"]")
    private List<PerimetriaEnum> perimetros;

    @ApiModelProperty(value = "Lista de IDs dos músculos que pertencem a este grupo muscular", example = "[1, 2, 3]")
    private List<Integer> musculoIds;


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public List<Integer> getAtividadeIds() {
        return atividadeIds;
    }

    public void setAtividadeIds(List<Integer> atividadeIds) {
        this.atividadeIds = atividadeIds;
    }

    public List<PerimetriaEnum> getPerimetros() {
        return perimetros;
    }

    public void setPerimetros(List<PerimetriaEnum> perimetros) {
        this.perimetros = perimetros;
    }

    public List<Integer> getMusculoIds() {
        return musculoIds;
    }

    public void setMusculoIds(List<Integer> musculoIds) {
        this.musculoIds = musculoIds;
    }
}
