package br.com.pacto.bean.musculo;

import br.com.pacto.bean.atividade.AtividadeMusculo;
import br.com.pacto.bean.atividade.AtividadeMusculoResponseTO;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by ulisses on 24/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Dados de resposta de um músculo com suas associações")
public class MusculoResponseTO {

    @ApiModelProperty(value = "Identificador único do músculo", example = "1")
    private Integer id;

    @ApiModelProperty(value = "Nome do músculo", example = "Bíceps braquial")
    private String nome;

    @ApiModelProperty(value = "Lista de grupos musculares aos quais o músculo pertence")
    private List<GrupoMuscularResumidoResponseTO> grupoMuscular = new ArrayList<>();

    @ApiModelProperty(value = "Lista de atividades que exercitam este músculo")
    private List<AtividadeMusculoResponseTO> atividades = new ArrayList<>();

    public MusculoResponseTO(Musculo musculo) {
        this.id = musculo.getCodigo();
        this.nome = musculo.getNome();
        if (musculo.getGrupos() != null) {
            for (MusculoGrupoMuscular musculoGrupoMuscular : musculo.getGrupos()) {
                getgrupoMuscular().add(new GrupoMuscularResumidoResponseTO(musculoGrupoMuscular.getGrupoMuscular().getCodigo(), musculoGrupoMuscular.getGrupoMuscular().getNome()));
            }
        }
        if (musculo.getAtividades() != null) {
            for (AtividadeMusculo atividadeMusculo : musculo.getAtividades()) {
                getAtividades().add(new AtividadeMusculoResponseTO(atividadeMusculo.getAtividade().getCodigo(), atividadeMusculo.getAtividade().getNome()));
            }
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public List<GrupoMuscularResumidoResponseTO> getgrupoMuscular() {
        return grupoMuscular;
    }

    public void setgrupoMuscular(List<GrupoMuscularResumidoResponseTO> grupoMuscular) {
        this.grupoMuscular = grupoMuscular;
    }

    public List<AtividadeMusculoResponseTO> getAtividades() {
        return atividades;
    }

    public void setAtividades(List<AtividadeMusculoResponseTO> atividades) {
        this.atividades = atividades;
    }
}
