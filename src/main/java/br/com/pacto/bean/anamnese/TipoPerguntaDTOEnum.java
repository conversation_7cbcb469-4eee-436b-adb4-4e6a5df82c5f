package br.com.pacto.bean.anamnese;

import br.com.pacto.enumerador.anamnese.TiposPerguntaEnum;
import io.swagger.annotations.ApiModel;

/**
 * <AUTHOR> 31/01/2019
 */
@ApiModel(description = "Tipo de pergunta que define como deve ser respondida no questionário PAR-Q. \n\n" +
        "<strong>Valores disponíveis</strong>\n" +
        "- ESCOLHA_MULTIPLA (Permite selecionar múltiplas opções)\n" +
        "- ESCOLHA_UNICA (Permite selecionar apenas uma opção)\n" +
        "- SIM_NAO (Pergunta de sim ou não)\n" +
        "- TEXTO (Resposta em texto livre)")
public enum TipoPerguntaDTOEnum {

    ESCOLHA_MULTIPLA,
    ESCOLHA_UNICA,
    SIM_NAO,
    TEXTO;

    public TiposPerguntaEnum getTiposPerguntaEnum() {
        switch (this) {
            case ESCOLHA_MULTIPLA: return TiposPerguntaEnum.MULTIPLA_ESCOLHA;
            case ESCOLHA_UNICA: return TiposPerguntaEnum.SIMPLES_ESCOLHA;
            case SIM_NAO: return TiposPerguntaEnum.SIM_NAO;
            case TEXTO: return TiposPerguntaEnum.TEXTUAL;
            default: return TiposPerguntaEnum.TEXTUAL;
        }
    }
}
