package br.com.pacto.bean.anamnese;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Resposta do aluno para uma pergunta do questionário PAR-Q")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RespostaClienteTO {

    @ApiModelProperty(value = "Identificador único da resposta", example = "1001")
    private Integer codigo;

    @ApiModelProperty(value = "Código identificador do aluno que respondeu", example = "2001")
    private Integer codigoCliente;

    @ApiModelProperty(value = "Código da resposta PAR-Q associada ao aluno", example = "3001")
    private Integer codigoRespostaClienteParq;

    @ApiModelProperty(value = "Código da pergunta da anamnese que foi respondida", example = "101")
    private Integer codigoPergunta;

    @ApiModelProperty(value = "Resposta fornecida pelo aluno para a pergunta", example = "nao")
    private String resposta;

    @ApiModelProperty(value = "Observações adicionais sobre a resposta", example = "Nunca teve problemas cardíacos")
    private String obs;

    public RespostaClienteTO() {}

    public RespostaClienteTO(Integer codigo, Integer codigoCliente, Integer codigoRespostaClienteParq, Integer codigoPergunta, String resposta, String obs) {
        this.codigo = codigo;
        this.codigoCliente = codigoCliente;
        this.codigoRespostaClienteParq = codigoRespostaClienteParq;
        this.codigoPergunta = codigoPergunta;
        this.resposta = resposta;
        this.obs = obs;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getCodigoCliente() {
        return codigoCliente;
    }

    public void setCodigoCliente(Integer codigoCliente) {
        this.codigoCliente = codigoCliente;
    }

    public Integer getCodigoRespostaClienteParq() {
        return codigoRespostaClienteParq;
    }

    public void setCodigoRespostaClienteParq(Integer codigoRespostaClienteParq) {
        this.codigoRespostaClienteParq = codigoRespostaClienteParq;
    }

    public Integer getCodigoPergunta() {
        return codigoPergunta;
    }

    public void setCodigoPergunta(Integer codigoPergunta) {
        this.codigoPergunta = codigoPergunta;
    }

    public String getResposta() {
        return resposta;
    }

    public void setResposta(String resposta) {
        this.resposta = resposta;
    }

    public String getObs() {
        return obs;
    }

    public void setObs(String obs) {
        this.obs = obs;
    }
}
