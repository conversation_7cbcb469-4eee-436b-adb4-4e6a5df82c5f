package br.com.pacto.bean.anamnese;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Created by ulisses on 05/08/2018.
 */
@ApiModel(description = "Opção de resposta disponível para uma pergunta do questionário PAR-Q")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OpcaoPerguntaResponseTO {

    @ApiModelProperty(value = "Identificador único da opção de resposta", example = "1")
    private Integer id;

    @ApiModelProperty(value = "Texto da opção de resposta", example = "Sim")
    private String nome;

    public OpcaoPerguntaResponseTO(OpcaoPergunta opcao){
        this.id = opcao.getCodigo();
        this.nome = opcao.getOpcao();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }
}
