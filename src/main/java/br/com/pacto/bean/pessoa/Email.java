/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.pessoa;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.io.Serializable;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table
@ApiModel(description = "Entidade que representa um endereço de email associado a uma pessoa, permitindo múltiplos emails por pessoa.")
public class Email implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @ApiModelProperty(value = "Código identificador único do email no banco de dados.", example = "456")
    private Integer codigo;

    @ApiModelProperty(value = "Endereço de email da pessoa.", example = "<EMAIL>")
    private String email;

    @ManyToOne
    @JsonIgnore
    @ApiModelProperty(value = "Referência à pessoa proprietária deste email.")
    private Pessoa pessoa;
    
    public Email(String email, Pessoa pessoa){
        this.email = email;
        this.pessoa = pessoa;
    }
    public Email(){
        
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Pessoa getPessoa() {
        return pessoa;
    }

    public void setPessoa(Pessoa pessoa) {
        this.pessoa = pessoa;
    }
    
}
