/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.pessoa;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import javax.persistence.*;

import com.fasterxml.jackson.annotation.JsonIgnore;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table
@ApiModel(description = "Entidade que representa um número de telefone associado a uma pessoa, incluindo o tipo de telefone (residencial, celular, comercial).")
public class Telefone implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @ApiModelProperty(value = "Código identificador único do telefone no banco de dados.", example = "789")
    private Integer codigo;

    @ApiModelProperty(value = "Número do telefone com formatação.", example = "(11) 99999-9999")
    private String telefone;

    @ManyToOne
    @JsonIgnore
    @ApiModelProperty(value = "Referência à pessoa proprietária deste telefone.")
    private Pessoa pessoa;

    @ApiModelProperty(value = "Tipo do telefone (RESIDENCIAL, CELULAR, COMERCIAL, etc.).", example = "CELULAR")
    private TipoTelefoneEnum tipo;

    public Telefone(String telefone, Pessoa pessoa){
        this.telefone = telefone;
        this.pessoa = pessoa;
    }
    public Telefone(){
        
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Pessoa getPessoa() {
        return pessoa;
    }

    public void setPessoa(Pessoa pessoa) {
        this.pessoa = pessoa;
    }

    public String getTelefone() {
        return telefone;
    }

    public void setTelefone(String telefone) {
        this.telefone = telefone;
    }

    public TipoTelefoneEnum getTipo() {
        return tipo;
    }

    public void setTipo(TipoTelefoneEnum tipo) {
        this.tipo = tipo;
    }
}
