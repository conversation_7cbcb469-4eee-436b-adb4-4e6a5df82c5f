/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.tipowod;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(uniqueConstraints =
        @UniqueConstraint(columnNames = {"nome"}))
public class TipoWod implements Serializable {
 
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @ApiModelProperty(value = "Código único do tipo de WOD", example = "1")
    private Integer codigo;

    @ApiModelProperty(value = "Nome do tipo de WOD", example = "AMRAP")
    private String nome;

    @ApiModelProperty(value = "Cláusula ORDER BY para ordenação dos resultados", example = "ORDER BY tempo DESC, peso")
    private String orderBy;

    @ApiModelProperty(value = "Campos utilizados para cálculo do resultado do ranking", example = "tempo,peso,nivelCrossfit")
    private String camposResultado;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getOrderBy() {
        return orderBy;
    }

    public void setOrderBy(String orderBy) {
        this.orderBy = orderBy;
    }

    public String getCamposResultado() {
        return camposResultado;
    }

    public void setCamposResultado(String camposResultado) {
        this.camposResultado = camposResultado;
    }

}
