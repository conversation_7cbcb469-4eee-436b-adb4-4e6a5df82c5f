package br.com.pacto.bean.programa;

import br.com.pacto.bean.colaborador.ColaboradorSimplesTO;
import br.com.pacto.bean.ficha.FichaResponseTO;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.util.UteisValidacao;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by ulisses on 13/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Detalhes completos do programa de treino")
public class ProgramaTreinoResponseTO {

    @ApiModelProperty(value = "ID único do programa de treino", example = "1")
    private Integer id;
    @ApiModelProperty(value = "Nome do programa de treino", example = "Programa Hipertrofia - João Silva")
    private String nome;
    @ApiModelProperty(value = "ID do aluno vinculado ao programa", example = "123")
    private Integer alunoId;
    @ApiModelProperty(value = "Dados do professor responsável pelo programa")
    private ProfessorResponseTO professor;
    @ApiModelProperty(value = "Data de criação do programa", example = "2024-01-15T10:30:00.000Z")
    private Date dataLancamento;
    @ApiModelProperty(value = "Data de início do programa", example = "2024-01-20T00:00:00.000Z")
    private Date inicio;
    @ApiModelProperty(value = "Data prevista para término do programa", example = "2024-04-20T00:00:00.000Z")
    private Date termino;
    @ApiModelProperty(value = "Total de treinos previstos no programa", example = "36")
    private Integer totalTreinos;
    @ApiModelProperty(value = "Quantidade de dias por semana do programa", example = "3")
    private Integer qtdDiasSemana;
    @ApiModelProperty(value = "Data da próxima revisão do programa", example = "2024-02-20T00:00:00.000Z")
    private Date revisao;
    @ApiModelProperty(value = "Número de treinos já concluídos pelo aluno", example = "12")
    private Integer treinosConcluidos;
    @ApiModelProperty(value = "Lista de fichas que compõem o programa de treino")
    private List<FichaResponseTO> fichas = new ArrayList<FichaResponseTO>();
    @ApiModelProperty(value = "URI da foto do aluno", example = "aluno_123_foto.jpg")
    private String alunoImagiUri;
    @ApiModelProperty(value = "Dados do professor que montou o programa")
    private ColaboradorSimplesTO professorMontou;
    @ApiModelProperty(value = "Gênero do programa (masculino/feminino)", example = "M")
    private String genero;
    @ApiModelProperty(value = "Situação atual do programa. \n\n" +
            "<strong>Valores disponíveis</strong>\n" +
            "- ATIVO (Programa ativo e em uso)\n" +
            "- INATIVO (Programa inativo)", example = "ATIVO")
    private ProgramaSituacaoEnum situacao = ProgramaSituacaoEnum.ATIVO;
    @ApiModelProperty(value = "Indica se o programa é pré-definido", example = "false")
    private Boolean predefinido;
    @ApiModelProperty(value = "Chave de origem do programa", example = "MANUAL")
    private String chaveOrigem;
    @ApiModelProperty(value = "ID do colaborador responsável", example = "45")
    private Integer colaboradorId;
    @ApiModelProperty(value = "Indica se o programa foi gerado por Inteligência Artificial", example = "false")
    private Boolean isGeradoPorIA;
    @ApiModelProperty(value = "Indica se o programa está em revisão pelo professor", example = "false")
    private Boolean emRevisaoProfessor;

    public ProgramaTreinoResponseTO() {
    }

    public ProgramaTreinoResponseTO(ProgramaTreino pt, Usuario usuarioProfessor,
                                    List<ProgramaTreinoFicha> programaFichas,
                                    Boolean treinoIndependente) {
        this.id = pt.getCodigo();
        this.nome = pt.getNome();
        this.genero = pt.getGenero();
        this.situacao = pt.getSituacao();
        this.predefinido = pt.getPreDefinido();
        this.dataLancamento = pt.getDataLancamento();
        this.emRevisaoProfessor = pt.getEmRevisaoProfessor();
        this.isGeradoPorIA = pt.getGeradoPorIA();
        if (pt.getCliente() != null) {
            this.alunoId = pt.getCliente().getCodigo();
        }
        if (usuarioProfessor != null) {
            this.setProfessor(new ProfessorResponseTO(usuarioProfessor, treinoIndependente));
        }
        if (pt.getDataInicio() != null) {
            this.inicio = pt.getDataInicio();
        }
        if (pt.getDataTerminoPrevisto() != null) {
            this.termino = pt.getDataTerminoPrevisto();
        }
        this.totalTreinos = pt.getTotalAulasPrevistas();
        this.qtdDiasSemana = pt.getDiasPorSemana();
        if (pt.getDataProximaRevisao()  != null) {
            this.revisao = pt.getDataProximaRevisao();
        }
        if (pt.getNrTreinosRealizados() != null) {
            this.treinosConcluidos = pt.getNrTreinosRealizados();
        }
        if (!UteisValidacao.emptyList(programaFichas)) {
            for (ProgramaTreinoFicha ptf : programaFichas) {
                this.fichas.add(new FichaResponseTO(ptf.getFicha(), ptf.getDiaSemana()));
            }
        }
        if (pt.getCliente() != null) {
            if (pt.getCliente().getPessoa().getFotoKey() == null) {
                this.alunoImagiUri = null;
            } else {
                this.alunoImagiUri = pt.getCliente().getPessoa().getFotoKey();
            }
        }
        if (pt.getProfessorMontou() != null) {
            this.professorMontou = new ColaboradorSimplesTO(pt.getProfessorMontou(), treinoIndependente);
        }
        if (pt.getDataLancamento() != null) {
            this.dataLancamento = pt.getDataLancamento();
        }
        if (pt.getCodigoColaborador() != null) {
            this.colaboradorId = pt.getCodigoColaborador();
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getAlunoId() {
        return alunoId;
    }

    public void setAlunoId(Integer alunoId) {
        this.alunoId = alunoId;
    }

    public ProfessorResponseTO getProfessor() {
        return professor;
    }

    public void setProfessor(ProfessorResponseTO professor) {
        this.professor = professor;
    }

    public Date getInicio() {
        return inicio;
    }

    public void setInicio(Date inicio) {
        this.inicio = inicio;
    }

    public Date getTermino() {
        return termino;
    }

    public void setTermino(Date termino) {
        this.termino = termino;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public Integer getTotalTreinos() {
        return totalTreinos;
    }

    public void setTotalTreinos(Integer totalTreinos) {
        this.totalTreinos = totalTreinos;
    }

    public Integer getQtdDiasSemana() {
        return qtdDiasSemana;
    }

    public void setQtdDiasSemana(Integer qtdDiasSemana) {
        this.qtdDiasSemana = qtdDiasSemana;
    }

    public Boolean getGeradoPorIA() {
        return isGeradoPorIA;
    }

    public void setGeradoPorIA(Boolean geradoPorIA) {
        isGeradoPorIA = geradoPorIA;
    }

    public Boolean getEmRevisaoProfessor() {
        return emRevisaoProfessor;
    }

    public void setEmRevisaoProfessor(Boolean emRevisaoProfessor) {
        this.emRevisaoProfessor = emRevisaoProfessor;
    }

    public Date getRevisao() {
        return revisao;
    }

    public void setRevisao(Date revisao) {
        this.revisao = revisao;
    }

    public Integer getTreinosConcluidos() {
        return treinosConcluidos;
    }

    public void setTreinosConcluidos(Integer treinosConcluidos) {
        this.treinosConcluidos = treinosConcluidos;
    }

    public List<FichaResponseTO> getFichas() {
        return fichas;
    }

    public void setFichas(List<FichaResponseTO> fichas) {
        this.fichas = fichas;
    }

    public String getAlunoImagiUri() {
        return alunoImagiUri;
    }

    public void setAlunoImagiUri(String alunoImagiUri) {
        this.alunoImagiUri = alunoImagiUri;
    }

    public ColaboradorSimplesTO getProfessorMontou() {
        return professorMontou;
    }

    public void setProfessorMontou(ColaboradorSimplesTO professorMontou) {
        this.professorMontou = professorMontou;
    }

    public String getGenero() {
        return genero;
    }

    public void setGenero(String genero) {
        this.genero = genero;
    }

    public ProgramaSituacaoEnum getSituacao() {
        return situacao;
    }

    public void setSituacao(ProgramaSituacaoEnum situacao) {
        this.situacao = situacao;
    }

    public Boolean getPredefinido() {
        return predefinido;
    }

    public void setPredefinido(Boolean predefinido) {
        this.predefinido = predefinido;
    }

    public String getChaveOrigem() {
        return chaveOrigem;
    }

    public void setChaveOrigem(String chaveOrigem) {
        this.chaveOrigem = chaveOrigem;
    }

    public Integer getColaboradorId() { return colaboradorId; }

    public void setColaboradorId(Integer colaboradorId) { this.colaboradorId = colaboradorId; }
}
