/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.empresa;

import br.com.pacto.bean.configuracoes.TimeZoneEnum;
import servicos.integracao.adm.client.EmpresaWS;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Entity
@Table(uniqueConstraints =
@UniqueConstraint(columnNames = {"nome"}))
@ApiModel(description = "Entidade que representa uma empresa/academia no sistema")
public class Empresa implements Serializable {

    @ApiModelProperty(value = "Código único identificador da empresa", example = "123")
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;

    @ApiModelProperty(value = "Código da empresa no sistema ZillionWeb (ZW)", example = "456")
    private Integer codZW;

    @ApiModelProperty(value = "Nome da empresa/academia", example = "Academia Pacto Goiânia")
    private String nome;

    @ApiModelProperty(value = "Token para envio de SMS da empresa", example = "sms_token_abc123")
    private String tokenSMS;

    @ApiModelProperty(value = "URL do site oficial da empresa", example = "https://www.academiapacto.com.br")
    private String urlSite;

    @ApiModelProperty(value = "Fuso horário padrão da empresa", example = "Brazil/East")
    private String timeZoneDefault = TimeZoneEnum.Brazil_East.getId();

    @ApiModelProperty(value = "Indica se a empresa utiliza aplicativo mobile", example = "true")
    @Column(columnDefinition = "boolean DEFAULT true")
    private Boolean usaMobile = true;

    @ApiModelProperty(value = "E-mail principal da empresa", example = "<EMAIL>")
    private String email;

    @ApiModelProperty(value = "Idioma do banco de dados da empresa. \n\n" +
            "<strong>Valores disponíveis</strong>\n" +
            "- PT (Português)\n" +
            "- ES (Espanhol)\n" +
            "- EN (Inglês)", example = "PT")
    private IdiomaBancoEnum idiomaBanco;

    @ApiModelProperty(value = "Código da empresa no sistema financeiro", example = "789")
    @Column(name = "cod_empresafinanceiro")
    private Integer codFinanceiro;

    @ApiModelProperty(value = "Token SMS shortcode para envios específicos", example = "shortcode_token_xyz789")
    @Column(name = "tokensmsshortcode")
    private String tokenSMSShortcode;

    @ApiModelProperty(value = "Chave da imagem/logo da empresa ou URL completa", example = "https://cdn1.pactorian.net/logo_empresa_123.jpg")
    private String keyImgEmpresa;

    @ApiModelProperty(value = "Data de expiração da licença da empresa", example = "2024-12-31")
    @Column(name = "dataexpiracao")
    private Date dataExpiracao;

    @ApiModelProperty(value = "Token MQV (Message Queue Validator) para validações de mensagens", example = "mqv_token_def456")
    @Column(name = "tokenmqv")
    private String tokenMqv;

    public String getKeyImgEmpresa() { return keyImgEmpresa; }

    public void setKeyImgEmpresa(String keyImgEmpresa) { this.keyImgEmpresa = keyImgEmpresa; }

    public Empresa() {
    }

    public Empresa(Integer codigo) {
        this.codigo = codigo;
    }

    public Empresa(final String nome) {
        this.nome = nome;
    }

    public static Empresa copyAttributes(Empresa e, EmpresaWS eWS) {
        e.setCodZW(eWS.getCodigo());
        e.setNome(eWS.getNome());
        e.setTimeZoneDefault(eWS.getTimeZoneDefault());
        e.setTokenSMS(eWS.getTokenSMS());
        e.setUrlSite(eWS.getSite());
        e.setEmail(eWS.getEmail());
        e.setCodFinanceiro(eWS.getCodigoFinanceiro());
        e.setTokenSMSShortcode(eWS.getTokenShortcode());
        return e;
    }

    public String getTimeZoneDefault() {
        return timeZoneDefault;
    }

    public void setTimeZoneDefault(String timeZoneDefault) {
        this.timeZoneDefault = timeZoneDefault;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getCodZW() {
        return codZW;
    }

    public void setCodZW(Integer codZW) {
        this.codZW = codZW;
    }

    public String getTokenSMS() {
        return tokenSMS;
    }

    public void setTokenSMS(String tokenSMS) {
        this.tokenSMS = tokenSMS;
    }

    public Boolean getUsaMobile() {
        return usaMobile;
    }

    public void setUsaMobile(Boolean usaMobile) {
        this.usaMobile = usaMobile;
    }

    public String getNomeSufixo() {
        String nomeTmp = nome;
        if (nomeTmp != null) {
            if (nomeTmp.contains("-")) {
                try {
                    nomeTmp = nomeTmp.substring(nomeTmp.indexOf("-") + 1).trim();
                } catch (Exception ignored) {
                }
            }
        }
        return nomeTmp;
    }

    public String getUrlSite() {
        return urlSite;
    }

    public void setUrlSite(String urlSite) {
        this.urlSite = urlSite;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public IdiomaBancoEnum getIdiomaBanco() {
        return idiomaBanco;
    }

    public void setIdiomaBanco(IdiomaBancoEnum idiomaBanco) {
        this.idiomaBanco = idiomaBanco;
    }

    public Integer getCodFinanceiro() {
        return codFinanceiro;
    }

    public void setCodFinanceiro(Integer codFinanceiro) {
        this.codFinanceiro = codFinanceiro;
    }

    public String getTokenSMSShortcode() {
        return tokenSMSShortcode;
    }

    public void setTokenSMSShortcode(String tokenSMSShortcode) {
        this.tokenSMSShortcode = tokenSMSShortcode;
    }

    public Date getDataExpiracao() {
        return dataExpiracao;
    }

    public void setDataExpiracao(Date dataExpiracao) {
        this.dataExpiracao = dataExpiracao;
    }

    public String getTokenMqv() {
        return tokenMqv;
    }

    public void setTokenMqv(String tokenMqv) {
        this.tokenMqv = tokenMqv;
    }
}
