package br.com.pacto.bean.eventoUsuario;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.persistence.Entity;
import javax.persistence.Id;
import java.io.Serializable;
import java.util.Objects;

/**
 * <AUTHOR> 19/02/2019
 */
@Entity
@ApiModel(description = "Entidade que representa um evento personalizado do usuário, contendo identificação e descrição do evento.")
public class EventoUsuario implements Serializable {

    @Id
    @ApiModelProperty(value = "Código identificador único do evento do usuário.", example = "EVT001")
    private String codigo;

    @ApiModelProperty(value = "Descrição ou nome do evento personalizado do usuário.", example = "Evento de Treinamento Especial")
    private String evento;

    public EventoUsuario() {
    }

    public EventoUsuario(String codigo, String evento) {
        this.codigo = codigo;
        this.evento = evento;
    }

    public String getCodigo() {
        return codigo;
    }

    public void setCodigo(String codigo) {
        this.codigo = codigo;
    }

    public String getEvento() {
        return evento;
    }

    public void setEvento(String evento) {
        this.evento = evento;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        EventoUsuario that = (EventoUsuario) o;
        return Objects.equals(codigo, that.codigo);
    }

    @Override
    public int hashCode() {
        return Objects.hash(codigo);
    }
}
