/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.gestao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 *
 * <AUTHOR>
 */
@ApiModel(description = "Transfer Object para item de gráfico BI, contendo configurações de indicador e tipo de gráfico")
public class ItemGraficoTO {
    @ApiModelProperty(value = "Indicador de gráfico BI selecionado")
    private IndicadorGraficoEnum indicador;

    @ApiModelProperty(value = "Indica se o indicador está selecionado para exibição", example = "true")
    private boolean selecionado;

    @ApiModelProperty(value = "Indica se é o primeiro indicador da lista", example = "false")
    private boolean primeiro;

    @ApiModelProperty(value = "Tipo de gráfico para exibição do indicador")
    private TipoGraficoEnum tipo;

    public ItemGraficoTO(IndicadorGraficoEnum indicador, boolean selecionado, TipoGraficoEnum tipo) {
        this.indicador = indicador;
        this.selecionado = selecionado;
        this.tipo = tipo;
    }

    public String getName(){
        return indicador.name();
    }

    public int getOrdinal(){
        return indicador.ordinal();
    }
    
    public IndicadorGraficoEnum getIndicador() {
        return indicador;
    }

    public void setIndicador(IndicadorGraficoEnum indicador) {
        this.indicador = indicador;
    }

    public boolean isSelecionado() {
        return selecionado;
    }

    public void setSelecionado(boolean selecionado) {
        this.selecionado = selecionado;
    }

    public TipoGraficoEnum getTipo() {
        return tipo;
    }

    public void setTipo(TipoGraficoEnum tipo) {
        this.tipo = tipo;
    }

    public boolean isPrimeiro() {
        return primeiro;
    }

    public void setPrimeiro(boolean primeiro) {
        this.primeiro = primeiro;
    }
    
    
}
