/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.gestao;

import br.com.pacto.bean.programa.TipoExecucaoEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 *
 * <AUTHOR>
 */
@ApiModel(description = "Entidade que representa um item de grupo de indicadores de gráfico BI, contendo configurações de indicadores personalizados salvos pelo usuário")
@Entity
@Table
public class ItemGrupoIndicadores implements Serializable {
    @ApiModelProperty(value = "Código único do item de grupo de indicadores", example = "1")
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;

    @ApiModelProperty(value = "Nome do grupo de indicadores personalizado", example = "Minha View Personalizada")
    private String nome;

    @ApiModelProperty(value = "Lista de IDs dos professores separados por ponto e vírgula. Vazio ou '0' indica todos os professores", example = "1;2;3")
    private String professores;

    @ApiModelProperty(value = "Tipo de indicador de gráfico BI. \n\n" +
            "<strong>Valores disponíveis</strong>\n" +
            "- IND_TOTAL_ALUNOS (Total de alunos)\n" +
            "- IND_ATIVOS (Ativos)\n" +
            "- IND_INATIVOS (Inativos)\n" +
            "- IND_ATIVOS_TREINO (Ativos com treino)\n" +
            "- IND_EM_DIA (Treinos em dia)\n" +
            "- IND_VENCIDOS (Treinos vencidos)\n" +
            "- IND_RENOVAR (Treinos a renovar)\n" +
            "- IND_AGENDAMENTOS (Agendamentos)\n" +
            "- IND_AGENDAMENTOS_EXECUTARAM (Ag. executados)\n" +
            "- IND_AGENDAMENTOS_FALTARAM (Ag. faltaram)\n" +
            "- IND_AGENDAMENTOS_CANCELARAM (Cancelaram agendamento)\n" +
            "- IND_RENOVADOS (Renovados)\n" +
            "- IND_NAO_RENOVADOS (Não renovados)\n" +
            "- IND_A_VENCER (A vencer)\n" +
            "- IND_NOVOS_CARTEIRA (Novos na carteira)\n" +
            "- IND_TROCARAM_CARTEIRA (Trocaram de carteira)\n" +
            "- IND_TEMPO_MEDIO_CARTEIRA (Tempo médio na carteira)\n" +
            "- IND_SEM_TREINO (Sem treino)\n" +
            "- IND_PERC_TREINO_EM_DIA (Perc. treino em dia)\n" +
            "- IND_PERC_TREINO_VENCIDOS (Perc. treino vencidos)\n" +
            "- IND_TEMPO_MEDIO_PROGRAMA (Tempo médio programa)\n" +
            "- IND_NR_AVALIACOES (Nr. avaliações)\n" +
            "- IND_MEDIA_AVALIACOES (Média de avaliações)\n" +
            "- IND_ESTRELA_1 (1 estrela)\n" +
            "- IND_ESTRELA_2 (2 estrelas)\n" +
            "- IND_ESTRELA_3 (3 estrelas)\n" +
            "- IND_ESTRELA_4 (4 estrelas)\n" +
            "- IND_ESTRELA_5 (5 estrelas)\n" +
            "- IND_COM_AVALIACAO (Alunos com avaliação)\n" +
            "- IND_SEM_AVALIACAO (Alunos sem avaliação)\n" +
            "- IND_AGENDAMENTO_PROFESSORES (Professores da agenda)\n" +
            "- IND_HORAS_DISPONIBILIDADE (Horas de disponibilidade)\n" +
            "- IND_HORAS_EXECUTADAS (Horas executadas)\n" +
            "- IND_PERC_OCUPACAO (% de ocupação da agenda)\n" +
            "- IND_AGENDAMENTO_NOVOS_TREINOS (Agendamentos de novos treinos)\n" +
            "- IND_AGENDAMENTO_TREINO_RENOVADOS (Agendamentos de renovação de treino)\n" +
            "- IND_AGENDAMENTO_TREINO_REVISADOS (Agendamentos de revisão de treino)\n" +
            "- IND_AGENDAMENTO_AVALIACOES_FISICAS (Agendamentos de avaliação física)\n" +
            "- IND_PERCENTUAL_RENOVACAO (Percentual de renovação)\n", example = "IND_TOTAL_ALUNOS")
    @Enumerated(EnumType.ORDINAL)
    private IndicadorGraficoEnum indicador;

    @ApiModelProperty(value = "Tipo de gráfico para exibição do indicador. \n\n" +
            "<strong>Valores disponíveis</strong>\n" +
            "- LINHA (line)\n" +
            "- COLUNA (column)\n", example = "LINHA")
    @Enumerated(EnumType.ORDINAL)
    private TipoGraficoEnum tipo;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public IndicadorGraficoEnum getIndicador() {
        return indicador;
    }

    public void setIndicador(IndicadorGraficoEnum indicador) {
        this.indicador = indicador;
    }

    public TipoGraficoEnum getTipo() {
        return tipo;
    }

    public void setTipo(TipoGraficoEnum tipo) {
        this.tipo = tipo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getProfessores() {
        return professores;
    }

    public void setProfessores(String professores) {
        this.professores = professores;
    }
    
    
    
}
