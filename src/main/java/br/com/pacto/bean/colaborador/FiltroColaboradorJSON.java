package br.com.pacto.bean.colaborador;

import br.com.pacto.controller.json.base.SuperJSON;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.enumeradores.TipoColaboradorZWEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

@ApiModel(description = "Filtros para consulta de colaboradores, permitindo refinar os resultados por nome, situação, tipo e campos de busca específicos.")
public class FiltroColaboradorJSON extends SuperJSON {

    @ApiModelProperty(value = "Indica se a busca deve ser aplicada no campo nome do colaborador.", example = "true")
    private Boolean nome = false;

    @ApiModelProperty(value = "Indica se a busca deve ser aplicada no campo nome de usuário (userName).", example = "false")
    private Boolean userName = false;

    @ApiModelProperty(value = "Lista de situações do colaborador para filtro. Valores possíveis: 'ATIVO', 'INATIVO'.")
    private List<SituacaoColaboradorEnum> situacoes = new ArrayList<>();

    @ApiModelProperty(value = "Termo de busca rápida para filtrar colaboradores por nome ou userName.", example = "João Silva")
    private String parametros;

    @ApiModelProperty(value = "Tipo específico do colaborador. Valores possíveis: 'PR' (Professor), 'TW' (Professor TreinoWeb), 'PT' (Personal Trainer), 'OR' (Orientador), 'CO' (Consultor), 'PI' (Personal Interno), 'PE' (Personal Externo), 'TE' (Terceirizado), 'ES' (Estúdio), 'FO' (Fornecedor), 'CR' (Coordenador), 'MD' (Médico), 'FC' (Funcionário), 'AD' (Administrador).", example = "PR")
    private TipoColaboradorZWEnum tipoColaborador;

    public FiltroColaboradorJSON(JSONObject filters) throws JSONException {
        if (filters != null) {
            this.parametros = filters.optString("quicksearchValue");
            JSONArray situacoes = filters.optJSONArray("situacoes");
            if (situacoes != null) {
                for (int i = 0; i < situacoes.length(); i++) {
                    getSituacoes().add(SituacaoColaboradorEnum.valueOf(situacoes.get(i).toString()));
                }
            }
            //verifica se o tipo de colaborador informado é valido, se naõ for retorna erro
            this.tipoColaborador = TipoColaboradorZWEnum.getTipo(filters.optString("tipoColaborador"));
            if (this.tipoColaborador == null && !UteisValidacao.emptyString(filters.optString("tipoColaborador"))) {
                throw new JSONException("Tipo de colaborador inválido");
            }
            JSONArray colunasVisiveis = filters.optJSONArray("quicksearchFields");
            if (colunasVisiveis != null) {
                for (int i = 0; i < colunasVisiveis.length(); i++) {
                    if (colunasVisiveis.get(i).equals("nome")) {
                        this.nome = true;
                    }
                    if (colunasVisiveis.get(i).equals("userName")) {
                        this.userName = true;
                    }
                }

            }
        }
    }

    public Boolean getNome() {
        return nome;
    }

    public void setNome(Boolean nome) {
        this.nome = nome;
    }

    public Boolean getUserName() {
        return userName;
    }

    public void setUserName(Boolean userName) {
        this.userName = userName;
    }

    public List<SituacaoColaboradorEnum> getSituacoes() {
        return situacoes;
    }

    public void setSituacoes(List<SituacaoColaboradorEnum> situacoes) {
        this.situacoes = situacoes;
    }

    public String getParametros() {
        return parametros;
    }

    public void setParametros(String parametros) {
        this.parametros = parametros;
    }

    public TipoColaboradorZWEnum getTipoColaborador() {
        return tipoColaborador;
    }

    public void setTipoColaborador(TipoColaboradorZWEnum tipoColaborador) {
        this.tipoColaborador = tipoColaborador;
    }
}
