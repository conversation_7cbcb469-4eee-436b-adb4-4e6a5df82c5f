package br.com.pacto.bean.colaborador;

import br.com.pacto.bean.perfil.Perfil;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Created by ulisses on 27/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "DTO de resposta contendo dados básicos do perfil de usuário")
public class PerfilUsuarioResponseTO {

    @ApiModelProperty(value = "Identificador único do perfil de usuário", example = "1")
    private Integer id;

    @ApiModelProperty(value = "Nome do perfil de usuário", example = "Professor")
    private String nome;

    public PerfilUsuarioResponseTO() {
    }

    public PerfilUsuarioResponseTO(Perfil perfil) {
        if(perfil != null ) {
            this.id = perfil.getCodigo();
            this.nome = perfil.getNome();
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }
}
