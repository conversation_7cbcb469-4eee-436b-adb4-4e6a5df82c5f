package br.com.pacto.bean.colaborador;

import br.com.pacto.bean.pessoa.SexoEnum;
import br.com.pacto.bean.usuario.TipoUsuarioEnum;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by ulisses on 27/08/2018.
 */
@ApiModel(description = "Dados do colaborador para importação e criação de usuário no sistema")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ColaboradorTO {

    @ApiModelProperty(value = "Código único do colaborador", example = "12345")
    private Integer id;

    @ApiModelProperty(value = "Nome completo do colaborador", example = "<PERSON>", required = true)
    private String nome;

    @ApiModelProperty(value = "Situação atual do colaborador. Valores possíveis: 'ATIVO', 'INATIVO'", example = "ATIVO")
    private SituacaoColaboradorEnum situacao;

    @ApiModelProperty(value = "Data de nascimento do colaborador", example = "1990-05-15T00:00:00Z")
    private Date dataNascimento;

    @ApiModelProperty(value = "Sexo do colaborador. Valores possíveis: 'M' (Masculino), 'F' (Feminino), 'N' (Não Informado)", example = "M")
    private SexoEnum sexo;

    @ApiModelProperty(value = "Lista de emails do colaborador")
    private List<EmailTO> emails = new ArrayList<>();

    @ApiModelProperty(value = "Lista de telefones do colaborador")
    private List<TelefoneTO> fones = new ArrayList<>();

    @ApiModelProperty(value = "Indica se o colaborador pode usar o aplicativo móvel", example = "true")
    private Boolean usarApp;

    @ApiModelProperty(value = "Nome de usuário para acesso ao aplicativo", example = "joao.silva", required = true)
    private String appUserName;

    @ApiModelProperty(value = "Senha para acesso ao aplicativo", example = "senha123", required = true)
    private String appPassword;

    @ApiModelProperty(value = "Tipo de usuário do colaborador. Valores possíveis: 'PROFESSOR', 'COORDENADOR', 'CONSULTOR'", example = "PROFESSOR")
    private TipoUsuarioColaboradorEnum tipoUsuario;

    @ApiModelProperty(value = "Código do perfil de usuário associado", example = "1")
    private Integer perfilUsuarioId;

    @ApiModelProperty(value = "Dados da imagem do colaborador em Base64", example = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==")
    private String imagemData;

    @ApiModelProperty(value = "Extensão do arquivo de imagem", example = "jpg")
    private String extensaoImagem;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public SituacaoColaboradorEnum getSituacao() {
        return situacao;
    }

    public void setSituacao(SituacaoColaboradorEnum situacao) {
        this.situacao = situacao;
    }

    public Date getDataNascimento() {
        return dataNascimento;
    }

    public void setDataNascimento(Date dataNascimento) {
        this.dataNascimento = dataNascimento;
    }

    public SexoEnum getSexo() {
        return sexo;
    }

    public void setSexo(SexoEnum sexo) {
        this.sexo = sexo;
    }

    public List<EmailTO> getEmails() {
        if (emails == null) {
            emails = new ArrayList<>();
        }
        return emails;
    }

    public void setEmails(List<EmailTO> emails) {
        this.emails = emails;
    }

    public List<TelefoneTO> getFones() {
        if (fones == null) {
            fones = new ArrayList<>();
        }
        return fones;
    }

    public void setFones(List<TelefoneTO> fones) {
        this.fones = fones;
    }

    public Boolean getUsarApp() {
        return usarApp;
    }

    public void setUsarApp(Boolean usarApp) {
        this.usarApp = usarApp;
    }

    public String getAppUserName() {
        return appUserName;
    }

    public void setAppUserName(String appUserName) {
        this.appUserName = appUserName;
    }

    public String getAppPassword() {
        return appPassword;
    }

    public void setAppPassword(String appPassword) {
        this.appPassword = appPassword;
    }

    public TipoUsuarioColaboradorEnum getTipoUsuario() {
        return tipoUsuario;
    }

    public void setTipoUsuario(TipoUsuarioColaboradorEnum tipoUsuario) {
        this.tipoUsuario = tipoUsuario;
    }

    public Integer getPerfilUsuarioId() {
        return perfilUsuarioId;
    }

    public void setPerfilUsuarioID(Integer perfilUsuarioId) {
        this.perfilUsuarioId = perfilUsuarioId;
    }

    public String getImagemData() {
        return imagemData;
    }

    public void setImagemData(String imagemData) {
        this.imagemData = imagemData;
    }

    public String getExtensaoImagem() {
        return extensaoImagem;
    }

    public void setExtensaoImagem(String extensaoImagem) {
        this.extensaoImagem = extensaoImagem;
    }
}
