package br.com.pacto.bean.benchmark;

import br.com.pacto.controller.json.benchmark.BenchmarkTO;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Created by ulisses on 24/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Dados do tipo de benchmark para operações CRUD")
public class TipoBenchmarkTO {

    @ApiModelProperty(value = "ID único do tipo de benchmark", example = "1")
    private Integer id;

    @ApiModelProperty(value = "Nome do tipo de benchmark", example = "Hero WODs")
    private String nome;

    public TipoBenchmarkTO () {}

    public TipoBenchmarkTO (TipoBenchmark tipoBenchmark) {
        this.id = tipoBenchmark.getCodigo();
        this.nome = tipoBenchmark.getNome();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }
}
