package br.com.pacto.bean.lesao;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Dados de lesao de aluno para analise de Business Intelligence")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AlunoLesaoDTO {

    @ApiModelProperty(value = "Matricula do aluno", example = "12345")
    private Integer matricula;

    @ApiModelProperty(value = "Nome do aluno", example = "Joao Silva")
    private String nome;

    @ApiModelProperty(value = "Data da lesao no formato dd/MM/yyyy", example = "15/01/2024")
    private String dataLesao;

    @ApiModelProperty(value = "Nome do professor responsavel", example = "Carlos Santos")
    private String professor;

    @ApiModelProperty(value = "Regiao do corpo onde ocorreu a lesao", example = "Joelho direito")
    private String regiaoLesao;

    @ApiModelProperty(value = "Descricao da gravidade da lesao", example = "Leve")
    private String gravidade;

    @ApiModelProperty(value = "Observacoes sobre a lesao", example = "Lesao ocorrida durante treino de agachamento")
    private String observacao;

    public AlunoLesaoDTO() { }

    public AlunoLesaoDTO(Integer matricula, String nome, String dataLesao, String professor, String regiaoLesao, String gravidade, String observacao) {
        this.matricula = matricula;
        this.nome = nome;
        this.dataLesao = dataLesao;
        this.professor = professor;
        this.regiaoLesao = regiaoLesao;
        this.gravidade = gravidade;
        this.observacao = observacao;
    }

    public Integer getMatricula() {
        return matricula;
    }

    public void setMatricula(Integer matricula) {
        this.matricula = matricula;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getDataLesao() {
        return dataLesao;
    }

    public void setDataLesao(String dataLesao) {
        this.dataLesao = dataLesao;
    }

    public String getProfessor() {
        return professor;
    }

    public void setProfessor(String professor) {
        this.professor = professor;
    }

    public String getRegiaoLesao() {
        return regiaoLesao;
    }

    public void setRegiaoLesao(String regiaoLesao) {
        this.regiaoLesao = regiaoLesao;
    }

    public String getGravidade() {
        return gravidade;
    }

    public void setGravidade(String gravidade) {
        this.gravidade = gravidade;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

}
