package br.com.pacto.bean.lesao;

import br.com.pacto.objeto.Calendario;
import br.com.pacto.security.dto.UsuarioSimplesDTO;
import br.com.pacto.util.json.ClienteSintenticoJson;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.text.ParseException;

@ApiModel(description = "Dados de uma lesao registrada no sistema")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class LesaoDTO {
    @ApiModelProperty(value = "Identificador unico da lesao", example = "123")
    private Integer id;

    @ApiModelProperty(value = "Matricula do aluno que sofreu a lesao", example = "12345")
    private Integer matricula;

    @ApiModelProperty(value = "Dados do aluno que sofreu a lesao")
    private ClienteSintenticoJson aluno;

    @ApiModelProperty(value = "Observacoes sobre a lesao", example = "Lesao ocorrida durante treino de agachamento")
    private String observacao;

    @ApiModelProperty(value = "Regiao do corpo onde ocorreu a lesao", example = "Joelho direito")
    private String regiaoLesao;

    @ApiModelProperty(value = "Gravidade da lesao. \n\n" +
            "<strong>Valores disponiveis</strong>\n" +
            "- LEVE (Leve)\n" +
            "- MODERADA (Moderada)\n" +
            "- GRAVE (Grave)\n", example = "LEVE")
    private GravidadeLesaoEnum gravidade;

    @ApiModelProperty(value = "Status atual da lesao. \n\n" +
            "<strong>Valores disponiveis</strong>\n" +
            "- LEVE (Em recuperacao)\n" +
            "- MODERADA (Em recuperacao 2)\n" +
            "- GRAVE (Em recuperacao 3)\n" +
            "- RECUPERADA (Recuperada)\n", example = "LEVE")
    private StatusLesaoEnum status;

    @ApiModelProperty(value = "Descricao textual do status da lesao", example = "Em recuperacao")
    private String descricaoStatus;

    @ApiModelProperty(value = "Data e hora do registro da lesao no formato yyyy-MM-dd hh:mm:ss", example = "2024-01-15 14:30:00")
    private String dataRegistro; // "yyyy-MM-dd hh:mm:ss"

    @ApiModelProperty(value = "Data em que a lesao ocorreu no formato yyyy-MM-dd", example = "2024-01-15")
    private String dataLesao; // "yyyy-MM-dd "

    @ApiModelProperty(value = "Dados do usuario que registrou a lesao")
    private UsuarioSimplesDTO usuario;

    public LesaoDTO() {
    }

    public LesaoDTO(Lesao lesao, ClienteSintenticoJson clienteSintenticoJson, String username) throws ParseException {
        this.id = lesao.getCodigo();
        this.matricula = clienteSintenticoJson.getMatricula();
        this.aluno = clienteSintenticoJson;
        this.observacao = lesao.getObservacao();
        this.regiaoLesao = lesao.getRegiaoLesao();
        this.dataRegistro = Calendario.getData(lesao.getDataRegistro(), "yyyy-MM-dd hh:mm:ss");
        this.dataLesao = Calendario.getData(lesao.getDataLesao(), "yyyy-MM-dd");
        this.status = lesao.getStatus();
        this.gravidade = lesao.getGravidade();
        UsuarioSimplesDTO usuarioSimplesDTO = new UsuarioSimplesDTO();
        usuarioSimplesDTO.setId(lesao.getUsuarioLancou_codigo());
        usuarioSimplesDTO.setUsername(username);
        this.usuario = usuarioSimplesDTO;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getMatricula() {
        return matricula;
    }

    public void setMatricula(Integer matricula) {
        this.matricula = matricula;
    }

    public ClienteSintenticoJson getAluno() {
        return aluno;
    }

    public void setAluno(ClienteSintenticoJson aluno) {
        this.aluno = aluno;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public String getRegiaoLesao() {
        return regiaoLesao;
    }

    public void setRegiaoLesao(String regiaoLesao) {
        this.regiaoLesao = regiaoLesao;
    }

    public GravidadeLesaoEnum getGravidade() {
        return gravidade;
    }

    public void setGravidade(GravidadeLesaoEnum gravidade) {
        this.gravidade = gravidade;
    }

    public StatusLesaoEnum getStatus() {
        return status;
    }

    public void setStatus(StatusLesaoEnum status) {
        this.status = status;
    }

    public String getDescricaoStatus() {
        return descricaoStatus;
    }

    public void setDescricaoStatus(String descricaoStatus) {
        this.descricaoStatus = descricaoStatus;
    }

    public String getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(String dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public String getDataLesao() {
        return dataLesao;
    }

    public void setDataLesao(String dataLesao) {
        this.dataLesao = dataLesao;
    }

    public UsuarioSimplesDTO getUsuario() {
        return usuario;
    }

    public void setUsuario(UsuarioSimplesDTO usuario) {
        this.usuario = usuario;
    }

}
