package br.com.pacto.bean.lesao;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Indice de lesoes por gravidade em um determinado periodo")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class IndiceLesaoDTO {

    @ApiModelProperty(value = "Total de lesoes leves registradas", example = "15")
    private Integer totalLesoesLeves;

    @ApiModelProperty(value = "Total de lesoes moderadas registradas", example = "8")
    private Integer totalLesoesModeradas;

    @ApiModelProperty(value = "Total de lesoes graves registradas", example = "3")
    private Integer totalLesoesGraves;

    @ApiModelProperty(value = "Total geral de lesoes no ano", example = "26")
    private Integer totalLesoesAoAno;

    public IndiceLesaoDTO() { }

    public IndiceLesaoDTO(Integer totalLesoesLeves, Integer totalLesoesModeradas, Integer totalLesoesGraves, Integer totalLesoesAoAno) {
        this.totalLesoesLeves = totalLesoesLeves;
        this.totalLesoesModeradas = totalLesoesModeradas;
        this.totalLesoesGraves = totalLesoesGraves;
        this.totalLesoesAoAno = totalLesoesAoAno;
    }

    public Integer getTotalLesoesLeves() {
        return totalLesoesLeves;
    }

    public void setTotalLesoesLeves(Integer totalLesoesLeves) {
        this.totalLesoesLeves = totalLesoesLeves;
    }

    public Integer getTotalLesoesModeradas() {
        return totalLesoesModeradas;
    }

    public void setTotalLesoesModeradas(Integer totalLesoesModeradas) {
        this.totalLesoesModeradas = totalLesoesModeradas;
    }

    public Integer getTotalLesoesGraves() {
        return totalLesoesGraves;
    }

    public void setTotalLesoesGraves(Integer totalLesoesGraves) {
        this.totalLesoesGraves = totalLesoesGraves;
    }

    public Integer getTotalLesoesAoAno() {
        return totalLesoesAoAno;
    }

    public void setTotalLesoesAoAno(Integer totalLesoesAoAno) {
        this.totalLesoesAoAno = totalLesoesAoAno;
    }

}
