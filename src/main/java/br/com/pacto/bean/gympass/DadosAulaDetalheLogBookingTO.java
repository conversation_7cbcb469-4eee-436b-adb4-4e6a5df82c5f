package br.com.pacto.bean.gympass;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.json.JSONArray;
import org.json.JSONObject;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.ArrayList;
import java.util.List;

@JsonIgnoreProperties
@ApiModel(description = "Contém os dados detalhados de uma turma e suas aulas vinculadas para registro de log de booking")
public class DadosAulaDetalheLogBookingTO {
    @ApiModelProperty(value = "Identificador da turma", example = "123")
    private Integer idturma;
    @ApiModelProperty(value = "Nome ou descrição da turma", example = "Musculação - Segunda a Sexta 18h")
    private String turma;
    @ApiModelProperty(value = "Lista de aulas detalhadas vinculadas à turma")
    private List<AulaDetalheLogBookingTO> aulas;

    public DadosAulaDetalheLogBookingTO(JSONObject json) throws Exception{
        this.idturma = json.optInt("idturma");
        this.turma = json.optString("turma");
        this.aulas = new ArrayList(){{
            if(json.has("aulas")){
                JSONArray aulas = json.getJSONArray("aulas");
                for(int i = 0; i < aulas.length(); i++){
                    add(new AulaDetalheLogBookingTO(aulas.getJSONObject(i)));
                }
            }
        }};

    }

    public Integer getIdturma() {
        return idturma;
    }

    public void setIdturma(Integer idturma) {
        this.idturma = idturma;
    }

    public String getTurma() {
        return turma;
    }

    public void setTurma(String turma) {
        this.turma = turma;
    }

    public List<AulaDetalheLogBookingTO> getAulas() {
        return aulas;
    }

    public void setAulas(List<AulaDetalheLogBookingTO> aulas) {
        this.aulas = aulas;
    }
}
