/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.gympass;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 12/05/2020
 */
@Entity
@Table()
@ApiModel(description = "Representa um log da integração com o GymPass")
public class LogGymPass implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @ApiModelProperty(value = "Código único identificador do log do GymPass", example = "2")
    private Integer codigo;
    @Temporal(TemporalType.TIMESTAMP)
    @ApiModelProperty(value = "Data e hora do registro do log", example = "2025-06-09T10:30:00Z")
    private Date dataRegistro;
    @ApiModelProperty(value = "Identificador da turma relacionada ao log", example = "1")
    private String idTurma;
    @Column(columnDefinition = "text")
    @ApiModelProperty(value = "Conteúdo completo do log gerado pela integração com o GymPass", example = "Check-in realizado com sucesso.")
    private String log;

    public LogGymPass() {
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getIdTurma() {
        if (idTurma == null) {
            idTurma = "";
        }
        return idTurma;
    }

    public void setIdTurma(String idTurma) {
        this.idTurma = idTurma;
    }

    public Date getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public String getLog() {
        return log;
    }

    public void setLog(String log) {
        this.log = log;
    }
}
