/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.gympass;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.persistence.*;
import java.io.Serializable;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 06/04/2020
 */
@Entity
@Table()
@ApiModel(description = "Representa os dados de um agendamento (booking) recebido da integração com o GymPass")
public class BookingGymPass implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @ApiModelProperty(value = "Código único identificador do Booking GymPass", example = "1")
    private Integer codigo;

    @Column(columnDefinition = "TEXT")
    @ApiModelProperty(value = "Identificador do agendamento (booking)", example = "booking_abc123")
    private String booking;

    @ApiModelProperty(value = "Operação executada no contexto do booking", example = "CHECK_IN")
    private String operacao;

    @ApiModelProperty(value = "Número do agendamento (booking number)", example = "BN-987654")
    private String bookingNumber;

    @ApiModelProperty(value = "Código do cliente sintético associado ao agendamento", example = "1024")
    private Integer clienteSintetico;

    @Column(columnDefinition = "boolean DEFAULT false")
    @ApiModelProperty(value = "Indica se a operação foi concluída com sucesso", example = "false")
    private Boolean sucesso = false;


    public BookingGymPass() {
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getBooking() {
        return booking;
    }

    public void setBooking(String booking) {
        this.booking = booking;
    }

    public String getOperacao() {
        if (operacao == null) {
            operacao = "";
        }
        return operacao;
    }

    public void setOperacao(String operacao) {
        this.operacao = operacao;
    }

    public String getBookingNumber() {
        return bookingNumber;
    }

    public void setBookingNumber(String bookingNumber) {
        this.bookingNumber = bookingNumber;
    }

    public Integer getClienteSintetico() {
        return clienteSintetico;
    }

    public void setClienteSintetico(Integer clienteSintetico) {
        this.clienteSintetico = clienteSintetico;
    }

    public boolean isMarcarBooking() {
        return getOperacao().equalsIgnoreCase("booking-requested");
    }

    public boolean isCancelarBooking() {
        return getOperacao().equalsIgnoreCase("booking-canceled");
    }

    public boolean isCancelarBookingAposPeriodo() {
        return getOperacao().equalsIgnoreCase("booking-late-canceled");
    }

    public Boolean isSucesso() {
        return sucesso;
    }

    public void setSucesso(Boolean sucesso) {
        this.sucesso = sucesso;
    }
}
