package br.com.pacto.bean.gympass;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.json.JSONObject;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties
@ApiModel(description = "Representa os dados de uma aula específica vinculada ao log de booking")
public class AulaDetalheLogBookingTO {
    @ApiModelProperty(value = "Dia da aula representado como timestamp", example = "1750032000000")
    private Long dia;
    @ApiModelProperty(value = "Identificador do booking relacionado à aula", example = "booking_789xyz")
    private String bookingid;
    @ApiModelProperty(value = "Horário inicial da aula", example = "18:00")
    private String horainicial;
    @ApiModelProperty(value = "Identificador da turma associada à aula", example = "02")
    private String identificadorturma;

    public AulaDetalheLogBookingTO(JSONObject json) {
        this.dia = json.optLong("dia");
        this.bookingid = json.optString("bookingid");
        this.horainicial = json.optString("horainicial");
        this.identificadorturma = json.optString("identificadorturma");
    }

    public Long getDia() {
        return dia;
    }

    public void setDia(Long dia) {
        this.dia = dia;
    }

    public String getBookingid() {
        return bookingid;
    }

    public void setBookingid(String bookingid) {
        this.bookingid = bookingid;
    }

    public String getHorainicial() {
        return horainicial;
    }

    public void setHorainicial(String horainicial) {
        this.horainicial = horainicial;
    }

    public String getIdentificadorturma() {
        return identificadorturma;
    }

    public void setIdentificadorturma(String identificadorturma) {
        this.identificadorturma = identificadorturma;
    }
}
