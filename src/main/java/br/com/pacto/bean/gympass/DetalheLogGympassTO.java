package br.com.pacto.bean.gympass;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Detallhes do Log do GymPass")
public class DetalheLogGympassTO {
    @ApiModelProperty(value = "Informações do log do GymPass")
    private LogGymPassTO logGymPass;
    @ApiModelProperty(value = "Eventos relacionados ao log")
    private List<BookingGymPass> eventos;
    @ApiModelProperty(value = "Lista de logs do GymPass")
    private List<LogGymPass> logs;
    @ApiModelProperty(value = "Dados do Log do GymPass")
    private DadosAulaDetalheLogBookingTO dados;

    public LogGymPassTO getLogGymPass() {
        return logGymPass;
    }

    public void setLogGymPass(LogGymPassTO logGymPass) {
        this.logGymPass = logGymPass;
    }

    public List<BookingGymPass> getEventos() {
        return eventos;
    }

    public void setEventos(List<BookingGymPass> eventos) {
        this.eventos = eventos;
    }

    public List<LogGymPass> getLogs() {
        return logs;
    }

    public void setLogs(List<LogGymPass> logs) {
        this.logs = logs;
    }

    public DadosAulaDetalheLogBookingTO getDados() {
        return dados;
    }

    public void setDados(DadosAulaDetalheLogBookingTO dados) {
        this.dados = dados;
    }
}
