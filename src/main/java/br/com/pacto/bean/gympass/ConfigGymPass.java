/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.gympass;

import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.gympass.ConfigGymPassDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 22/03/2020
 */
@ApiModel(description = "Entidade de configuração do GymPass")
@Entity
public class ConfigGymPass implements Serializable {

    @ApiModelProperty(value = "Código único identificador da configuração", example = "1")
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;

    @ApiModelProperty(value = "Referência à empresa vinculada à configuração")
    @ManyToOne
    private Empresa empresa;

    @ApiModelProperty(value = "Nome da empresa", example = "Academia Fitness Plus")
    private String nome;

    @ApiModelProperty(value = "Código identificador da empresa no sistema GymPass", example = "GYM123456")
    private String codigoGymPass;

    @ApiModelProperty(value = "Indica se a integração com GymPass Booking está ativa", example = "false")
    @Column(columnDefinition = "boolean DEFAULT false")
    private boolean usarGymPassBooking;

    @ApiModelProperty(value = "Código do usuário que realizou o lançamento", example = "10")
    private Integer usuarioLancou_codigo;

    @ApiModelProperty(value = "Indica se a configuração está ativa", example = "true")
    private Boolean ativo;

    @ApiModelProperty(value = "Indica se é permitido realizar WODs via GymPass", example = "false")
    private Boolean permitirWod;

    @ApiModelProperty(value = "Data e hora do lançamento da configuração", example = "2024-01-15T10:30:00.000Z")
    private Date dataLancamento;

    @ApiModelProperty(value = "Limite máximo de acessos por dia para usuários GymPass", example = "3")
    private Integer limiteDeAcessosPorDia;

    @ApiModelProperty(value = "Limite máximo de aulas por dia para usuários GymPass", example = "2")
    private Integer limiteDeAulasPorDia;

    public ConfigGymPass() {
    }

    public ConfigGymPass(ConfigGymPassDTO dto, Empresa empresa, Usuario usuario, Date dataLancamento) {
        this.codigo = dto.getCodigo();
        this.empresa = empresa;
        this.nome = empresa.getNome();
        this.codigoGymPass = dto.getCodigoGymPass();
        this.permitirWod = dto.isPermitirWod();
        this.usarGymPassBooking = dto.isUsarGymPassBooking();
        this.limiteDeAcessosPorDia = dto.getLimiteDeAcessosPorDia();
        this.limiteDeAulasPorDia = dto.getLimiteDeAulasPorDia();
        this.usuarioLancou_codigo = usuario.getCodigo();
        this.dataLancamento = dataLancamento;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getCodigoGymPass() {
        return codigoGymPass;
    }

    public void setCodigoGymPass(String codigoGymPass) {
        this.codigoGymPass = codigoGymPass;
    }

    public boolean isUsarGymPassBooking() {
        return usarGymPassBooking;
    }

    public void setUsarGymPassBooking(boolean usarGymPassBooking) {
        this.usarGymPassBooking = usarGymPassBooking;
    }

    public Integer getLimiteDeAcessosPorDia() {
        return limiteDeAcessosPorDia;
    }

    public void setLimiteDeAcessosPorDia(Integer limiteDeAcessosPorDia) {
        this.limiteDeAcessosPorDia = limiteDeAcessosPorDia;
    }

    public Integer getLimiteDeAulasPorDia() {
        return limiteDeAulasPorDia;
    }

    public void setLimiteDeAulasPorDia(Integer limiteDeAulasPorDia) {
        this.limiteDeAulasPorDia = limiteDeAulasPorDia;
    }

    public Integer getUsuarioLancou_codigo() {
        return usuarioLancou_codigo;
    }

    public void setUsuarioLancou_codigo(Integer usuarioLancou) {
        this.usuarioLancou_codigo = usuarioLancou;
    }

    public Boolean getAtivo() {
        return ativo;
    }

    public void setAtivo(Boolean ativo) {
        this.ativo = ativo;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public Boolean getPermitirWod() {
        if(permitirWod == null){
            permitirWod = false;
        }
        return permitirWod;
    }

    public void setPermitirWod(Boolean permitirWod) {
        this.permitirWod = permitirWod;
    }
}
