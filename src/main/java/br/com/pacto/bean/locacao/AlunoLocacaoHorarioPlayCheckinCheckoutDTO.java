package br.com.pacto.bean.locacao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

@ApiModel(description = "Dados para registrar check-in ou check-out de um aluno em uma locação do tipo play.")
public class AlunoLocacaoHorarioPlayCheckinCheckoutDTO implements Serializable {

    @ApiModelProperty(value = "Indica se é um check-in (true) ou check-out (false).", example = "true")
    private Boolean checkin;

    @ApiModelProperty(value = "Código do aluno na locação horário play.", example = "1")
    private Integer alunoLocacaoHorarioPlay;

    @ApiModelProperty(value = "Data e hora do check-in/check-out no formato yyyy/MM/dd HH:mm:ss.", example = "2024/01/15 08:30:00")
    private String dataHora;

    public Boolean getCheckin() {
        return checkin;
    }

    public void setCheckin(Boolean checkin) {
        this.checkin = checkin;
    }

    public Integer getAlunoLocacaoHorarioPlay() {
        return alunoLocacaoHorarioPlay;
    }

    public void setAlunoLocacaoHorarioPlay(Integer alunoLocacaoHorarioPlay) {
        this.alunoLocacaoHorarioPlay = alunoLocacaoHorarioPlay;
    }

    public String getDataHora() {
        return dataHora;
    }

    public void setDataHora(String dataHora) {
        this.dataHora = dataHora;
    }
}
