package br.com.pacto.bean.locacao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

@ApiModel(description = "Dados para cancelar ou finalizar uma locação do tipo play.")
public class LocacaoPlayCanceladaFinalizadaDTO implements Serializable {

    @ApiModelProperty(value = "Código do responsável pelo lançamento.", example = "10")
    private Integer responsavelLancamento;

    @ApiModelProperty(value = "Código do ambiente.", example = "2")
    private Integer ambiente;

    @ApiModelProperty(value = "Código do horário da locação.", example = "5")
    private Integer locacaoHorario;

    @ApiModelProperty(value = "Dia da locação no formato string.", example = "2024-01-15")
    private String dia;

    @ApiModelProperty(value = "Justificativa para o cancelamento ou finalização.", example = "Cliente solicitou cancelamento")
    private String justificativa;

    @ApiModelProperty(value = "Indica se a locação foi cancelada.", example = "true")
    private Boolean cancelada;

    @ApiModelProperty(value = "Indica se a locação foi finalizada.", example = "false")
    private Boolean finalizada;

    public Integer getResponsavelLancamento() {
        return responsavelLancamento;
    }

    public void setResponsavelLancamento(Integer responsavelLancamento) {
        this.responsavelLancamento = responsavelLancamento;
    }

    public Integer getAmbiente() {
        return ambiente;
    }

    public void setAmbiente(Integer ambiente) {
        this.ambiente = ambiente;
    }

    public Integer getLocacaoHorario() {
        return locacaoHorario;
    }

    public void setLocacaoHorario(Integer locacaoHorario) {
        this.locacaoHorario = locacaoHorario;
    }

    public String getDia() {
        return dia;
    }

    public void setDia(String dia) {
        this.dia = dia;
    }

    public String getJustificativa() {
        return justificativa;
    }

    public void setJustificativa(String justificativa) {
        this.justificativa = justificativa;
    }

    public Boolean getCancelada() {
        return cancelada;
    }

    public void setCancelada(Boolean cancelada) {
        this.cancelada = cancelada;
    }

    public Boolean getFinalizada() {
        return finalizada;
    }

    public void setFinalizada(Boolean finalizada) {
        this.finalizada = finalizada;
    }
}
