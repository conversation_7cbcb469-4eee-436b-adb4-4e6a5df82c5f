package br.com.pacto.bean.locacao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

@ApiModel(description = "Dados para adicionar um aluno a um horário de locação do tipo play.")
public class AlunoLocacaoHorarioPlayDTO implements Serializable {

    @ApiModelProperty(value = "Código único do registro.", example = "1")
    private Integer codigo;

    @ApiModelProperty(value = "Código do responsável pelo lançamento.", example = "10")
    private Integer responsavelLancamento;

    @ApiModelProperty(value = "Código da empresa.", example = "1")
    private Integer empresa;

    @ApiModelProperty(value = "Código do cliente/aluno.", example = "123")
    private Integer cliente;

    @ApiModelProperty(value = "Código do horário da locação.", example = "5")
    private Integer locacaoHorario;

    @ApiModelProperty(value = "Código do ambiente.", example = "2")
    private Integer ambiente;

    @ApiModelProperty(value = "Dia da locação no formato string.", example = "2024-01-15")
    private String dia;

    @ApiModelProperty(value = "Indica se o aluno fez check-in.", example = "false")
    private Boolean checkin;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getResponsavelLancamento() {
        return responsavelLancamento;
    }

    public void setResponsavelLancamento(Integer responsavelLancamento) {
        this.responsavelLancamento = responsavelLancamento;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public Integer getCliente() {
        return cliente;
    }

    public void setCliente(Integer cliente) {
        this.cliente = cliente;
    }

    public Integer getLocacaoHorario() {
        return locacaoHorario;
    }

    public void setLocacaoHorario(Integer locacaoHorario) {
        this.locacaoHorario = locacaoHorario;
    }

    public Integer getAmbiente() {
        return ambiente;
    }

    public void setAmbiente(Integer ambiente) {
        this.ambiente = ambiente;
    }

    public String getDia() {
        return dia;
    }

    public void setDia(String dia) {
        this.dia = dia;
    }

    public Boolean getCheckin() {
        return checkin;
    }

    public void setCheckin(Boolean checkin) {
        this.checkin = checkin;
    }
}
