package br.com.pacto.bean.perfil;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Created by ulisses on 24/08/2018.
 */
@ApiModel(description = "DTO para representar dados básicos de um perfil de acesso")
public class PerfilResponseTO {

    @ApiModelProperty(value = "Identificador único do perfil", example = "1")
    private Integer id;

    @ApiModelProperty(value = "Nome do perfil de acesso", example = "Professor")
    private String nome;

    @ApiModelProperty(value = "Tipo do perfil de acesso", example = "PROFESSOR")
    private String tipo;

    public PerfilResponseTO(Perfil perfil){
        this.id = perfil.getCodigo();
        this.nome = perfil.getNome();
        this.tipo = perfil.getTipo();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }
}
