package br.com.pacto.bean.perfil;

import br.com.pacto.bean.perfil.permissao.Permissao;
import br.com.pacto.bean.perfil.permissao.TipoRecurso;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by paulo on 22/07/2019.
 */
@ApiModel(description = "DTO para representar dados completos de um perfil de acesso incluindo recursos e funcionalidades")
public class PerfilDTO {

    @ApiModelProperty(value = "Identificador único do perfil", example = "1")
    private Integer id;

    @ApiModelProperty(value = "Nome do perfil de acesso", example = "Professor")
    private String nome;

    @ApiModelProperty(value = "Tipo do perfil de acesso", example = "PROFESSOR")
    private String tipo;

    @ApiModelProperty(value = "Recursos/entidades disponíveis para o perfil. Referência à classe RecursoEntidadeDTO com permissões específicas")
    private RecursoEntidadeDTO recursos;

    @ApiModelProperty(value = "Funcionalidades disponíveis para o perfil. Referência à classe RecursoFuncionalidadeDTO com funcionalidades habilitadas")
    private RecursoFuncionalidadeDTO funcionalidades;

    public PerfilDTO (Perfil perfil) {
        this.id = perfil.getCodigo();
        this.nome = perfil.getNome();
        this.tipo = perfil.getTipo();
        Map<String, List> recursoMap = new HashMap<>();
        Map<String, Boolean> funcionalidadeMap = new HashMap<>();
        ObjectMapper mapper = new ObjectMapper();
        for (Permissao permissao : perfil.getPermissoes()) {
            if (permissao.getRecurso().getTipo().equals(TipoRecurso.ENTIDADE)) {
                recursoMap.put(permissao.getRecurso().name().toLowerCase(), new ArrayList(permissao.getTipoPermissoes()));
            } else {
                funcionalidadeMap.put(permissao.getRecurso().name().toLowerCase(), (permissao.getTipoPermissoes() != null && permissao.getTipoPermissoes().size() > 0));
            }
        }
        this.recursos = mapper.convertValue(recursoMap, RecursoEntidadeDTO.class);
        this.funcionalidades = mapper.convertValue(funcionalidadeMap, RecursoFuncionalidadeDTO.class);
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public RecursoEntidadeDTO getRecursos() {
        return recursos;
    }

    public void setRecursos(RecursoEntidadeDTO recursos) {
        this.recursos = recursos;
    }

    public RecursoFuncionalidadeDTO getFuncionalidades() {
        return funcionalidades;
    }

    public void setFuncionalidades(RecursoFuncionalidadeDTO funcionalidades) {
        this.funcionalidades = funcionalidades;
    }

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }
}
