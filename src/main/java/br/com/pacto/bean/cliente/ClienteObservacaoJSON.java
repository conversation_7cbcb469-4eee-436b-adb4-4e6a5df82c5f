/**
 *
 * <AUTHOR>
 */

package br.com.pacto.bean.cliente;

import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.base.SuperJSON;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.sql.Timestamp;

@ApiModel(description = "Observação do cliente formatada para aplicativo móvel")
public class ClienteObservacaoJSON extends SuperJSON {

    @ApiModelProperty(value = "Código único da observação", example = "123")
    private Integer codigo;

    @ApiModelProperty(value = "Indica se a observação é marcada como importante", example = "true")
    private Boolean importante;

    @ApiModelProperty(value = "Nome de usuário do profissional que cadastrou a observação", example = "professor1")
    private String userName;

    @ApiModelProperty(value = "Nome do profissional que cadastrou a observação", example = "João Silva")
    private String nome;

    @ApiModelProperty(value = "Data da observação em timestamp", example = "1718467200000")
    private Timestamp dataLong;

    @ApiModelProperty(value = "Texto da observação", example = "Cliente apresentou boa evolução no treino de membros superiores")
    private String observacao;

    @ApiModelProperty(value = "URL da foto do profissional que cadastrou a observação", example = "https://exemplo.com/foto.jpg")
    private String foto;

    public ClienteObservacaoJSON(ClienteObservacao co, Usuario usu)
    {
        if(co != null) {
            this.codigo = co.getCodigo();
            this.importante = co.getImportante();
            this.observacao = co.getObservacao();
            this.dataLong = co.getDataObservacao() != null ? new  Timestamp(co.getDataObservacao().getTime()) : null;
            if(co.getUsuario_codigo() != null)
            {
                this.userName = usu.getUserName();
                if(usu.getProfessor() != null) {
                    this.nome = usu.getProfessor().getNome();
                }
            }
        }
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Boolean getImportante() {
        return importante;
    }

    public void setImportante(Boolean importante) {
        this.importante = importante;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public Timestamp getDataLong() {
        return dataLong;
    }

    public void setDataLong(Timestamp dataLong) {
        this.dataLong = dataLong;
    }

    public String getFoto() {
        return foto;
    }

    public void setFoto(String foto) {
        this.foto = foto;
    }
}
