/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.cliente;

import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.objeto.Uteis;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;


import org.hibernate.annotations.Type;

import javax.persistence.*;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table
@ApiModel(description = "Entidade que representa uma observação registrada sobre um cliente/aluno, incluindo anotações importantes, anexos e informações relacionadas a avaliações físicas.")
public class ClienteObservacao implements Serializable {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @ApiModelProperty(value = "Código identificador único da observação no banco de dados.", example = "1234")
    private Integer codigo;

    @ManyToOne
    @ApiModelProperty(value = "Referência ao cliente/aluno ao qual esta observação pertence.")
    private ClienteSintetico cliente;

    @ApiModelProperty(value = "Código do usuário que registrou esta observação.", example = "567")
    private Integer usuario_codigo;

    @Column(columnDefinition = "boolean DEFAULT false")
    @ApiModelProperty(value = "Indica se esta observação é marcada como importante ou prioritária.", example = "true")
    private Boolean importante = Boolean.FALSE;

    @Lob
    @Type(type="org.hibernate.type.StringClobType")
    @Column(columnDefinition = "text", length = 9999)
    @ApiModelProperty(value = "Texto da observação registrada sobre o cliente.", example = "Cliente demonstrou boa evolução nos exercícios de força. Recomendado aumento de carga na próxima semana.")
    private String observacao;

    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    @ApiModelProperty(value = "Data e hora em que a observação foi registrada.", example = "2024-06-15T14:30:00Z")
    private Date dataObservacao;

    @ApiModelProperty(value = "Indica se esta observação está relacionada a uma avaliação física.", example = "false")
    private Boolean avaliacaoFisica = Boolean.FALSE;

    @ApiModelProperty(value = "Chave identificadora do anexo associado à observação (se houver).", example = "anexo_cliente_1234_20240615")
    private String anexoKey;

    @Column(columnDefinition = "text")
    @ApiModelProperty(value = "Nome do arquivo anexado à observação.", example = "foto_evolucao_cliente.jpg")
    private String nomeArquivo;

    @Column(columnDefinition = "text")
    @ApiModelProperty(value = "Formato/extensão do arquivo anexado.", example = "jpg")
    private String formatoArquivo;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public ClienteSintetico getCliente() {
        return cliente;
    }

    public void setCliente(ClienteSintetico cliente) {
        this.cliente = cliente;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public Date getDataObservacao() {
        return dataObservacao;
    }

    public void setDataObservacao(Date dataObservacao) {
        this.dataObservacao = dataObservacao;
    }

    public Boolean getAvaliacaoFisica() {
        return avaliacaoFisica;
    }

    public void setAvaliacaoFisica(Boolean avaliacaoFisica) {
        this.avaliacaoFisica = avaliacaoFisica;
    }

    public Integer getUsuario_codigo() {
        return usuario_codigo;
    }

    public void setUsuario_codigo(Integer usuario) {
        this.usuario_codigo = usuario;
    }
    
    public String getDataObservacaoApresentar() {
        try {
            return Uteis.getDataAplicandoFormatacao(dataObservacao, "dd/MM/yyyy");
        } catch (Exception e) {
            return "";
        }

    }

    public String getHoraObservacaoApresentar(){
        try {
            return Uteis.getDataAplicandoFormatacao(dataObservacao, "HH:mm");
        } catch (Exception e) {
            return "";
        }
    }
    
    public String getAnexoKey() {
        return anexoKey;
    }

    public void setAnexoKey(String anexoKey) {
        this.anexoKey = anexoKey;
    }

    public Boolean getImportante() {
        return importante;
    }

    public void setImportante(Boolean importante) {
        this.importante = importante;
    }

    public String getNomeArquivo() {
        return nomeArquivo;
    }

    public void setNomeArquivo(String nomeArquivo) {
        this.nomeArquivo = nomeArquivo;
    }

    public String getFormatoArquivo() {
        return formatoArquivo;
    }

    public void setFormatoArquivo(String formatoArquivo) {
        this.formatoArquivo = formatoArquivo;
    }
}
