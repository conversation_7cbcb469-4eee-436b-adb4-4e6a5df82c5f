package br.com.pacto.bean.cliente;


import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Informações do aluno para exibição no catálogo de avaliações.")
public class AvaliacaoCatalogoAlunoDTO {

    @ApiModelProperty(value = "Código identificador do aluno.", example = "1234")
    private Integer id;

    @ApiModelProperty(value = "URL da imagem do aluno.", example = "https://cdn.imagens.com/alunos/foto123.jpg")
    private String imageUri;

    @ApiModelProperty(value = "Nome completo do aluno.", example = "Lucas Ferreira")
    private String nome;

    @ApiModelProperty(value = "Sexo do aluno. Valores possíveis: M (Masculino), F (Feminino), N (Não informado)", example = "M")
    private String sexo;

    @ApiModelProperty(value = "Nome do nível atual do aluno.", example = "Intermediário")
    private String nivelNome;

    @ApiModelProperty(value = "Data de nascimento do aluno.", example = "1992-04-18T00:00:00.000Z")
    private Date dataNascimento;

    @ApiModelProperty(value = "Indica se o aluno foi aprovado no PAR-Q.", example = "true")
    private Boolean resultadoParq;

    public AvaliacaoCatalogoAlunoDTO() {

    }
    public AvaliacaoCatalogoAlunoDTO(ClienteSintetico cliente, Boolean resultadoPositivoParq) {
        this.id = cliente.getCodigo();
        this.imageUri = cliente.getUrlFoto();
        this.nome = cliente.getNome();
        this.sexo = cliente.getSexo();
        this.nivelNome = cliente.getNivelAluno() == null ? null : cliente.getNivelAluno().getNome();
        this.dataNascimento = cliente.getDataNascimento();
        this.resultadoParq = resultadoPositivoParq;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getImageUri() {
        return imageUri;
    }

    public void setImageUri(String imageUri) {
        this.imageUri = imageUri;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getNivelNome() {
        return nivelNome;
    }

    public void setNivelNome(String nivelNome) {
        this.nivelNome = nivelNome;
    }

    public String getSexo() {
        return sexo;
    }

    public void setSexo(String sexo) {
        this.sexo = sexo;
    }

    public Date getDataNascimento() {
        return dataNascimento;
    }

    public void setDataNascimento(Date dataNascimento) {
        this.dataNascimento = dataNascimento;
    }

    public Boolean getResultadoParq() {
        return resultadoParq;
    }

    public void setResultadoParq(Boolean resultadoParq) {
        this.resultadoParq = resultadoParq;
    }
}
