package br.com.pacto.bean.cliente;

import br.com.pacto.bean.ficha.FichaResponseTO;
import br.com.pacto.bean.programa.ProgramaTreino;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@ApiModel(description = "Dados de prescrição de programa de treino para um cliente")
public class PessoaPrescricaoDTO {

    @ApiModelProperty(value = "Código único do cliente no sistema", example = "12345")
    private Integer codigoCliente;

    @ApiModelProperty(value = "Código único do programa de treino", example = "789")
    private Integer codigoPrograma;

    @ApiModelProperty(value = "Código do colaborador responsável", example = "456")
    private Integer codigoColaborador;

    @ApiModelProperty(value = "Tipo de pessoa (sempre 'Aluno' para prescrições)", example = "Aluno")
    private String tipo;

    @ApiModelProperty(value = "Nome completo do cliente", example = "João Silva Santos")
    private String nome;

    @ApiModelProperty(value = "Número da matrícula do cliente", example = "202301234")
    private String matricula;

    @ApiModelProperty(value = "Nome do programa de treino prescrito", example = "Programa Hipertrofia Iniciante")
    private String programa;

    @ApiModelProperty(value = "Situação atual do cliente", example = "ATIVO")
    private String situacao;

    @ApiModelProperty(value = "Situação do contrato do cliente", example = "VIGENTE")
    private String situacaoContrato;

    @ApiModelProperty(value = "Nome do professor responsável pelo programa", example = "Prof. Carlos Alberto")
    private String professor;

    @ApiModelProperty(value = "Percentual de andamento do programa (0-100)", example = "65")
    private Integer andamento;

    @ApiModelProperty(value = "Data de início do programa no formato string", example = "2024-01-15")
    private String inicio;

    @ApiModelProperty(value = "Data de fim prevista do programa no formato string", example = "2024-04-15")
    private String fim;

    @ApiModelProperty(value = "Situação atual do treino", example = "EM_ANDAMENTO")
    private String situacaoTreino;

    @ApiModelProperty(value = "Indicação médica especial para o treino", example = "Evitar exercícios de impacto")
    private String indicacaoMedica;

    @ApiModelProperty(value = "URL da foto do cliente", example = "https://exemplo.com/fotos/cliente123.jpg")
    private String urlFoto;

    @ApiModelProperty(value = "Indica se o usuário tem permissão para criar programas", example = "true")
    private boolean permissaoCriar;

    @ApiModelProperty(value = "Indica se o usuário tem permissão para revisar programas", example = "true")
    private boolean permissaoRevisar;

    @ApiModelProperty(value = "Indica se o usuário tem permissão para renovar programas", example = "false")
    private boolean permissaoRenovar;

    @ApiModelProperty(value = "Data e hora do último acesso do cliente", example = "2024-05-20 14:30:00")
    private String ultimoAcesso;

    @ApiModelProperty(value = "Indica se o cliente possui plano TotalPass", example = "false")
    private boolean totalpass;

    @ApiModelProperty(value = "Indica se o cliente possui plano GymPass", example = "true")
    private boolean gympass;

    @ApiModelProperty(value = "Indica se o cliente possui plano FreePass", example = "false")
    private boolean freepass;

    @ApiModelProperty(value = "Indica se o cliente é dependente", example = "false")
    private boolean dependente;

    @ApiModelProperty(value = "Indica se o cliente possui plano de diária", example = "false")
    private boolean diaria;

    @ApiModelProperty(value = "Indica se o cliente possui aula avulsa", example = "false")
    private boolean aulaAvulsa;

    @ApiModelProperty(value = "Indica se o cliente está com atestado médico", example = "false")
    private boolean atestado;

    @ApiModelProperty(value = "Indica se o contrato do cliente está a vencer", example = "false")
    private boolean avencer;

    @ApiModelProperty(value = "Indica se o cliente está cancelado", example = "false")
    private boolean cancelado;

    @ApiModelProperty(value = "Indica se o cliente é desistente", example = "false")
    private boolean desistente;

    @ApiModelProperty(value = "Indica se o cliente está de férias", example = "false")
    private boolean ferias;

    @ApiModelProperty(value = "Indica se o cliente está com matrícula trancada", example = "false")
    private boolean trancado;

    @ApiModelProperty(value = "Indica se o contrato do cliente está vencido", example = "false")
    private boolean vencido;

    @ApiModelProperty(value = "Indica se o programa foi gerado por Inteligência Artificial", example = "false")
    private boolean geradoPorIA;

    @ApiModelProperty(value = "Indica se o programa foi revisado pelo professor", example = "true")
    private boolean isRevisadoProfessor;

    @ApiModelProperty(value = "Data de lançamento do programa")
    @Temporal(TemporalType.TIMESTAMP)
    private Date datalancamento;

    public PessoaPrescricaoDTO() {
    }

    public PessoaPrescricaoDTO(Integer codigoCliente, Integer codigoColaborador,
                               String tipo, String nome, String matricula, String programa,
                               Integer andamento, String inicio, String fim, String situacaoTreino,
                               String indicacaoMedica, String urlFoto, String ultimoAcesso) {
        this.codigoCliente = codigoCliente;
        this.codigoColaborador = codigoColaborador;
        this.tipo = tipo;
        this.nome = nome;
        this.matricula = matricula;
        this.programa = programa;
        this.andamento = andamento;
        this.inicio = inicio;
        this.fim = fim;
        this.situacaoTreino = situacaoTreino;
        this.indicacaoMedica = indicacaoMedica;
        this.urlFoto = urlFoto;
        this.ultimoAcesso = ultimoAcesso;
    }

    public Integer getCodigoCliente() {
        return codigoCliente;
    }

    public void setCodigoCliente(Integer codigoCliente) {
        this.codigoCliente = codigoCliente;
    }

    public Integer getCodigoColaborador() {
        return codigoColaborador;
    }

    public void setCodigoColaborador(Integer codigoColaborador) {
        this.codigoColaborador = codigoColaborador;
    }

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public String getPrograma() {
        return programa;
    }

    public void setPrograma(String programa) {
        this.programa = programa;
    }

    public Integer getAndamento() {
        return andamento;
    }

    public void setAndamento(Integer andamento) {
        this.andamento = andamento;
    }

    public String getInicio() {
        return inicio;
    }

    public void setInicio(String inicio) {
        this.inicio = inicio;
    }

    public String getFim() {
        return fim;
    }

    public void setFim(String fim) {
        this.fim = fim;
    }

    public String getSituacaoTreino() {
        return situacaoTreino;
    }

    public void setSituacaoTreino(String situacaoTreino) {
        this.situacaoTreino = situacaoTreino;
    }

    public String getIndicacaoMedica() {
        return indicacaoMedica;
    }

    public void setIndicacaoMedica(String indicacaoMedica) {
        this.indicacaoMedica = indicacaoMedica;
    }

    public String getUrlFoto() {
        return urlFoto;
    }

    public void setUrlFoto(String urlFoto) {
        this.urlFoto = urlFoto;
    }

    public Integer getCodigoPrograma() {
        return codigoPrograma;
    }

    public void setCodigoPrograma(Integer codigoPrograma) {
        this.codigoPrograma = codigoPrograma;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public String getSituacaoContrato() {
        return situacaoContrato;
    }

    public void setSituacaoContrato(String situacaoContrato) {
        this.situacaoContrato = situacaoContrato;
    }

    public String getProfessor() {
        return professor;
    }

    public void setProfessor(String professor) {
        this.professor = professor;
    }

    public boolean isPermissaoCriar() {
        return permissaoCriar;
    }

    public void setPermissaoCriar(boolean permissaoCriar) {
        this.permissaoCriar = permissaoCriar;
    }

    public boolean isPermissaoRevisar() {
        return permissaoRevisar;
    }

    public void setPermissaoRevisar(boolean permissaoRevisar) {
        this.permissaoRevisar = permissaoRevisar;
    }

    public boolean isPermissaoRenovar() {
        return permissaoRenovar;
    }

    public void setPermissaoRenovar(boolean permissaoRenovar) {
        this.permissaoRenovar = permissaoRenovar;
    }

    public String getUltimoAcesso() {return ultimoAcesso; }

    public void setUltimoAcesso(String ultimoAcesso) {this.ultimoAcesso = ultimoAcesso;  }

    public boolean isGeradoPorIA() {
        return geradoPorIA;
    }

    public void setGeradoPorIA(boolean geradoPorIA) {
        this.geradoPorIA = geradoPorIA;
    }

    public boolean isRevisadoProfessor() {
        return isRevisadoProfessor;
    }

    public void setRevisadoProfessor(boolean revisadoProfessor) {
        isRevisadoProfessor = revisadoProfessor;
    }

    public Date getDatalancamento() {
        return datalancamento;
    }

    public void setDatalancamento(Date datalancamento) {
        this.datalancamento = datalancamento;
    }

    public boolean isTotalpass() {return totalpass;}

    public void setTotalpass(boolean totalpass) {this.totalpass = totalpass;}

    public boolean isGympass() {return gympass;}

    public void setGympass(boolean gympass) {this.gympass = gympass;}

    public boolean isFreepass() {return freepass;}

    public void setFreepass(boolean freepass) {this.freepass = freepass;}

    public boolean isDependente() {return dependente;}

    public void setDependente(boolean dependente) {this.dependente = dependente;}

    public boolean isDiaria() {return diaria;}

    public void setDiaria(boolean diaria) {this.diaria = diaria;}

    public boolean isAulaAvulsa() {
        return aulaAvulsa;
    }
    public void setAulaAvulsa(boolean aulaAvulsa) {
        this.aulaAvulsa = aulaAvulsa;
    }

    public boolean isAtestado() {return atestado;}

    public void setAtestado(boolean atestado) {this.atestado = atestado;}

    public boolean isAvencer() {
        return avencer;
    }

    public void setAvencer(boolean avencer) {
        this.avencer = avencer;
    }

    public boolean isCancelado() {
        return cancelado;
    }

    public void setCancelado(boolean cancelado) {
        this.cancelado = cancelado;
    }

    public boolean isDesistente() {
        return desistente;
    }

    public void setDesistente(boolean desistente) {
        this.desistente = desistente;
    }
    public boolean isFerias() {
        return ferias;
    }
    public void setFerias(boolean ferias) {
        this.ferias = ferias;
    }

    public boolean isTrancado() {
        return trancado;
    }

    public void setTrancado(boolean trancado) {
        this.trancado = trancado;
    }

    public boolean isVencido() {
        return vencido;
    }

    public void setVencido(boolean vencido) {
        this.vencido = vencido;
    }
}
