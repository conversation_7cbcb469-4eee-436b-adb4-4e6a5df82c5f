package br.com.pacto.bean.cliente;

import br.com.pacto.controller.json.base.SuperJSON;
import br.com.pacto.controller.json.cliente.ClienteJSON;
import br.com.pacto.controller.json.professor.ProfessorJSON;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.sql.Timestamp;

@ApiModel(description = "Dados do acompanhamento de cliente em formato JSON")
public class ClienteAcompanhamentoJSON extends SuperJSON {

    @ApiModelProperty(value = "Código único do acompanhamento", example = "123")
    private Integer codigo;

    @ApiModelProperty(value = "Dados do cliente em acompanhamento")
    private ClienteJSON cliente;

    @ApiModelProperty(value = "Dados do professor responsável pelo acompanhamento")
    private ProfessorJSON professor;

    @ApiModelProperty(value = "Data e hora de início do acompanhamento", example = "2024-06-20 08:00:00")
    private Timestamp inicio;

    @ApiModelProperty(value = "Data e hora de fim do acompanhamento", example = "2024-06-20 09:00:00")
    private Timestamp fim;

    public ClienteAcompanhamentoJSON(ClienteAcompanhamento obj) {
        if(obj != null) {
            this.codigo = obj.getCodigo();
            this.cliente = obj.getCliente() != null ? new ClienteJSON(obj.getCliente()) : null;
            this.professor = obj.getProfessor() != null ? new ProfessorJSON(obj.getProfessor()) : null;
            this.inicio = obj.getInicio() != null ? new  Timestamp(obj.getInicio().getTime()) : null;
            this.fim = obj.getFim() != null ? new  Timestamp(obj.getFim().getTime()) : null;
        }
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public ClienteJSON getCliente() {
        return cliente;
    }

    public void setCliente(ClienteJSON cliente) {
        this.cliente = cliente;
    }

    public ProfessorJSON getProfessor() {
        return professor;
    }

    public void setProfessor(ProfessorJSON professor) {
        this.professor = professor;
    }

    public Timestamp getInicio() {
        return inicio;
    }

    public void setInicio(Timestamp inicio) {
        this.inicio = inicio;
    }

    public Timestamp getFim() {
        return fim;
    }

    public void setFim(Timestamp fim) {
        this.fim = fim;
    }
}
