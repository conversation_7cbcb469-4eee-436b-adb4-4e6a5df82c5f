package br.com.pacto.bean.imagem;

import br.com.pacto.bean.animacao.Animacao;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Created by ulisses on 29/08/2018.
 */
@ApiModel(description = "Dados de resposta de uma imagem do catálogo")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ImagemResponseTO {

    @ApiModelProperty(value = "Identificador único da imagem no catálogo", example = "imagem_exercicio_001.jpg")
    private String id;

    @ApiModelProperty(value = "Nome descritivo da imagem", example = "Exercício de Flexão de Braços")
    private String nome;

    @ApiModelProperty(value = "URI completa para acesso à imagem", example = "http://app.pactosolucoes.com.br/midias/DEFAULT/Large/Square/imagem_exercicio_001.jpg")
    private String uri;

    public ImagemResponseTO(Animacao animacao) {
        this.id = animacao.getUrl();
        this.nome = animacao.getTitulo();
        this.uri = "http://app.pactosolucoes.com.br/midias/DEFAULT/Large/Square/" + animacao.getUrl();
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getUri() {
        return uri;
    }

    public void setUri(String uri) {
        this.uri = uri;
    }
}
