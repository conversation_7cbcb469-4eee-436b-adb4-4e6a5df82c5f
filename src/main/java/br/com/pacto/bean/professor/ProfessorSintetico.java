/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.professor;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.pessoa.Pessoa;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Uteis;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.persistence.UniqueConstraint;
import java.io.Serializable;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(uniqueConstraints={@UniqueConstraint(name = "professorsintetico_codigocolaborador_key",columnNames={"codigoColaborador"})})
@ApiModel(description = "Entidade que representa um professor/instrutor da academia, incluindo informações profissionais, dados de contato, especialidades e configurações de trabalho.")
public class ProfessorSintetico implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @ApiModelProperty(value = "Código identificador único do professor no banco de dados.", example = "456")
    private Integer codigo;

    @ApiModelProperty(value = "Nome completo do professor/instrutor.", example = "Maria Silva Santos")
    private String nome;

    @ApiModelProperty(value = "Indica se o professor está ativo no sistema.", example = "true")
    private boolean ativo = true;

    @ApiModelProperty(value = "Código da pessoa física associada ao professor.", example = "789")
    private Integer codigoPessoa;

    @ApiModelProperty(value = "Código do colaborador no sistema de RH.", example = "123")
    private Integer codigoColaborador;

    @ManyToOne(cascade = CascadeType.ALL)
    @JoinColumn(name = "empresa_codigo")
    @ApiModelProperty(value = "Referência à empresa/unidade onde o professor trabalha.")
    private Empresa empresa;

    @Transient
    @ApiModelProperty(value = "URL do avatar/foto do professor (campo transiente).", example = "https://exemplo.com/avatar/prof123.jpg")
    private String avatar;

    @ApiModelProperty(value = "Indica se o professor atua como personal trainer.", example = "false")
    private Boolean personal;

    @ApiModelProperty(value = "Endereço de email do professor.", example = "<EMAIL>")
    private String email;
    @ApiModelProperty(value = "Indica se o professor trabalha no modelo pós-pago (recebe após prestação do serviço).", example = "false")
    private Boolean posPago = Boolean.FALSE;

    @ApiModelProperty(value = "Indica se é um personal trainer interno da academia.", example = "false")
    private Boolean personaInterno = Boolean.FALSE;

    @ApiModelProperty(value = "Indica se é um colaborador simples (não professor).", example = "false")
    private Boolean colaboradorSimples;

    @ApiModelProperty(value = "Indica se deve cobrar créditos dos clientes pelos serviços.", example = "true")
    private Boolean cobrarCreditos;

    @Column(columnDefinition = "boolean DEFAULT false")
    @ApiModelProperty(value = "Indica se o professor utiliza o sistema TW (Training Workout).", example = "false")
    private Boolean professorTW = Boolean.FALSE;

    @OneToOne(cascade = {CascadeType.PERSIST, CascadeType.MERGE, CascadeType.REMOVE})
    @ApiModelProperty(value = "Referência aos dados pessoais completos do professor.")
    private Pessoa pessoa;

    @Column(columnDefinition ="INTEGER default 1")
    @ApiModelProperty(value = "Versão do registro para controle de concorrência.", example = "1")
    private Integer versao;

    @ApiModelProperty(value = "Número do registro no Conselho Regional de Educação Física (CREF).", example = "123456-G/SP")
    private String cref;

    @ApiModelProperty(value = "URI da imagem/foto do professor.", example = "https://exemplo.com/fotos/prof123.jpg")
    private String uriImagem;

    @ApiModelProperty(value = "Código de acesso especial do professor para sistemas integrados.", example = "PROF123")
    private String codigoAcesso;

    @Transient
    private String fotoKey;
    
    @Transient
    private Double total;
    @Transient
    private String tipoColaborador;
    @Transient
    private Date validadeCREF;

    public ProfessorSintetico() {
    }
    
    public ProfessorSintetico(String nome) {
        this.nome = nome;
    }

    public ProfessorSintetico(String nome, String uriImagem) {
        this.nome = nome;
        this.uriImagem = uriImagem;
    }

    public ProfessorSintetico(Integer codigo) {
        this.codigo = codigo;
    }

    

    public Boolean getPersonal() {
        if(personal == null){
            personal = Boolean.FALSE;
        }
        return personal;
    }

    public ProfessorSintetico(Integer codigo, Pessoa pessoa) {
        this.codigo = codigo;
        this.pessoa = pessoa;
    }

    public ProfessorSintetico(String nome, Integer codigo) {
        this.nome = nome;
        this.codigo = codigo;

    }

    public String getUriImagem() {
        if(uriImagem == null){
            uriImagem = "";
        }
        return uriImagem;
}

    public void setUriImagem(String uriImagem) {
        this.uriImagem = uriImagem;
    }

    public void setPersonal(Boolean personal) {
        this.personal = personal;
    }

    public void setPosPago(Boolean posPago) {
        this.posPago = posPago;
    }

    public Boolean getPersonaInterno() {
        return personaInterno;
    }

    public void setPersonaInterno(Boolean personaInterno) {
        this.personaInterno = personaInterno;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public boolean getPosPago() {
        if (posPago == null) {
            posPago = Boolean.FALSE;
        }
        return posPago;
    }

    public void setPosPago(boolean posPago) {
        this.posPago = posPago;
    }

    


    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public String getNomeMinusculo() {
        return nome.toLowerCase();
    }

    public String getNomeAbreviadoMinusculo() {
        try {
            return Uteis.getNomeAbreviandoSomentePrimeiroSobrenome(nome).toLowerCase();
        } catch (Exception e) {
            return "";
        }

    }

    public String getNomeAbreviado() {
        try {
            return Uteis.getNomeAbreviandoSomentePrimeiroSobrenome(nome);
        } catch (Exception e) {
            return "";
        }

    }
    
    public String getPrimeiroNome() {
        try {
            return Uteis.getPrimeiroNome(nome);
        } catch (Exception e) {
            return "";
        }

    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public boolean isAtivo() {
        return ativo;
    }

    public void setAtivo(boolean ativo) {
        this.ativo = ativo;
    }

    public Integer getCodigoPessoa() {
        return codigoPessoa;
    }

    public void setCodigoPessoa(Integer codigoPessoa) {
        this.codigoPessoa = codigoPessoa;
    }

    public Integer getCodigoColaborador() {
        return codigoColaborador;
    }

    public void setCodigoColaborador(Integer codigoColaborador) {
        this.codigoColaborador = codigoColaborador;
    }

    public Empresa getEmpresa() {
        if(empresa == null){
            empresa = new Empresa();
        }
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }


    @Override
    public String toString() {
        return this.hashCode() + " - " + this.nome;
    }

    @Override
    public int hashCode() {
        return this.getCodigo() != null && this.getCodigo() > 0
                ? this.getCodigo().hashCode() : super.hashCode();
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null) {
            return false;
        }
        if (!(obj instanceof ProfessorSintetico)) {
            return false;
        }
        final ProfessorSintetico other = (ProfessorSintetico) obj;
        if (this.getCodigo() != other.getCodigo() && (this.getCodigo() == null || !this.getCodigo().equals(other.getCodigo()))) {
            return false;
        }
        return true;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getDescricaoTipo() {
        return posPago != null && posPago ? "Pós-pago" : "Pré-pago";
    }
    public String getDescricaoTipoPersonal() {
        return personaInterno != null && personaInterno ? "Personal interno" : "Personal externo";
    }

    public Boolean getColaboradorSimples() {
        if(colaboradorSimples == null){
            colaboradorSimples = Boolean.FALSE;
        }
        return colaboradorSimples;
    }

    public void setColaboradorSimples(Boolean colaboradorSimples) {
        this.colaboradorSimples = colaboradorSimples;
    }
    
    public String getDescricaoTipoColaborador(){
        return getPersonal() ? "Personal" : tipoColaborador == null ? (getColaboradorSimples() ? "Colaborador" : "Professor") : tipoColaborador;
    }

    public Boolean getCobrarCreditos() {
        if(cobrarCreditos == null){
           cobrarCreditos = Boolean.TRUE;
        }
        return cobrarCreditos;
    }

    public void setCobrarCreditos(Boolean cobrarCreditos) {
        this.cobrarCreditos = cobrarCreditos;
    }

    public Double getTotal() {
        if(total == null){
            total = 0.0;
        }
        return total;
    }
    
    public String getTotalApresentar(){
        return Uteis.arrendondarForcando2CadasDecimaisComVirgula(getTotal());
    }

    public void setTotal(Double total) {
        this.total = total;
    }
    
    public String getAtivoApresentar(){
        return ativo ? "Sim" : "Não";
    }

    public Boolean getProfessorTW() {
        return professorTW;
    }

    public void setProfessorTW(Boolean professorTW) {
        this.professorTW = professorTW;
    }

    public Pessoa getPessoa() {
        if(pessoa == null){
            pessoa = new Pessoa();
        }
        return pessoa;
    }

    public void setPessoa(Pessoa pessoa) {
        this.pessoa = pessoa;
    }

    public Integer getVersao() {
        return versao;
    }

    public void setVersao(Integer versao) {
        this.versao = versao;
    }

    public void setCref(String cref) {
        this.cref = cref;
    }

    public String getCref() {
        if (cref == null) {
            cref = "";
        }
        return cref;
    }

    public String getFotoKey() {
        if (uriImagem != null) {
            fotoKey = uriImagem.replace(Aplicacao.getProp(Aplicacao.urlFotosNuvem), "");

            int questionMarkIndex = uriImagem.indexOf("?");
            if (questionMarkIndex != -1) {
                String remover = uriImagem.substring(questionMarkIndex);
                fotoKey = fotoKey.replace(remover, "");
            }
            if (!fotoKey.isEmpty()) {
                fotoKey = fotoKey.substring(1);
            }
            return fotoKey;
        }
        return "";
    }

    public void setFotoKey(String fotoKey) {
        this.fotoKey = fotoKey;
    }

    public String getTipoColaborador() {
        return tipoColaborador;
    }

    public void setTipoColaborador(String tipoColaborador) {
        this.tipoColaborador = tipoColaborador;
    }

    public String getCodigoAcesso() {
        return codigoAcesso;
    }

    public void setCodigoAcesso(String codigoAcesso) {
        this.codigoAcesso = codigoAcesso;
    }

    public Date getValidadeCREF() {
        return validadeCREF;
    }

    public void setValidadeCREF(Date validadeCREF) {
        this.validadeCREF = validadeCREF;
    }
}
