package br.com.pacto.bean.professor;

import br.com.pacto.bean.usuario.Usuario;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToOne;
import java.io.Serializable;

@ApiModel(description = "Configuração personalizada de relatórios por usuário")
@Entity
public class ConfigRelatorio implements Serializable{

    @ApiModelProperty(value = "Código único da configuração", example = "1")
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;

    @ApiModelProperty(value = "Usuário proprietário da configuração")
    @OneToOne
    private Usuario usuario;

    @ApiModelProperty(value = "Identificador da tabela/relatório configurado", example = "relatorio-professores-001")
    private String tableId;

    @ApiModelProperty(value = "Configuração em formato JSON contendo definições de colunas, filtros e ordenação",
                      example = "{\"columns\":[\"nome\",\"email\",\"telefone\"], \"filters\":{\"ativo\":true}, \"sorting\":{\"field\":\"nome\",\"order\":\"asc\"}}")
    private String configTable;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Usuario getUsuario() {
        return usuario;
    }

    public void setUsuario(Usuario usuario) {
        this.usuario = usuario;
    }

    public String getTableId() {
        return tableId;
    }

    public void setTableId(String tableId) {
        this.tableId = tableId;
    }

    public String getConfigTable() {
        return configTable;
    }

    public void setConfigTable(String configTable) {
        this.configTable = configTable;
    }
}



