package br.com.pacto.bean.professor;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.LinkedHashMap;
import java.util.Map;

@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Indicadores acumulados de atividade de um professor")
public class IndicadorAtividadeProfessorAcumuladoVO {

    @ApiModelProperty(value = "Identificador único do professor", example = "123")
    private Integer professorId;

    @ApiModelProperty(value = "Nome completo do professor", example = "<PERSON> Silva")
    private String nome;

    @ApiModelProperty(value = "Mapa contendo os indicadores acumulados organizados por tipo de atividade",
                     example = "{\"treinos_novos\": 15, \"treinos_renovados\": 8, \"acompanhamentos\": 25}")
    private LinkedHashMap<String, Integer> indicadores;

    public Integer getProfessorId() {
        return professorId;
    }

    public void setProfessorId(Integer professorId) {
        this.professorId = professorId;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public LinkedHashMap<String, Integer> getIndicadores() {
        return indicadores;
    }

    public void setIndicadores(LinkedHashMap<String, Integer> indicadores) {
        this.indicadores = indicadores;
    }
}
