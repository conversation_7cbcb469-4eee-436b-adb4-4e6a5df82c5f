package br.com.pacto.security.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * DTO de Login no sistema
 *
 * <AUTHOR>
 * @since 19/07/2018
 */
@ApiModel(description = "Dados necessários para autenticação versão 2 do usuário no sistema")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UsuarioLoginV2DTO {

    @ApiModelProperty(value = "Chave de identificação da empresa no sistema", example = "ACADEMIA123", required = true)
    private String chave;

    @ApiModelProperty(value = "Código do usuário no sistema ZW (ZillionWeb)", example = "12345")
    private Integer usuario_zw;

    @ApiModelProperty(value = "Código do usuário no sistema TW (TreinoWeb)", example = "67890")
    private Integer usuario_tw;

    @ApiModelProperty(value = "Nome de usuário para autenticação", example = "<EMAIL>")
    private String usuario_username;

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public Integer getUsuario_zw() {
        return usuario_zw;
    }

    public void setUsuario_zw(Integer usuario_zw) {
        this.usuario_zw = usuario_zw;
    }

    public Integer getUsuario_tw() {
        return usuario_tw;
    }

    public void setUsuario_tw(Integer usuario_tw) {
        this.usuario_tw = usuario_tw;
    }

    public String getUsuario_username() {
        return usuario_username;
    }

    public void setUsuario_username(String usuario_username) {
        this.usuario_username = usuario_username;
    }
}
