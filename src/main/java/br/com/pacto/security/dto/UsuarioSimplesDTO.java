package br.com.pacto.security.dto;

import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.util.UteisValidacao;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.*;

/**
 * DTO de usuário simples, um DTO apenas com os dados de acesso de um usuário, objeto bem menor que a entidade
 * {@link br.com.pacto.bean.usuario.Usuario} para permitir criar caches em memória e trafegar menos dados a cada
 * requisição, eliminando a necessidade da IDA ao banco a cada requisição.
 *
 * <AUTHOR>
 * @since 19/07/2018
 */
@ApiModel(description = "Dados basicos do usuario para autenticacao e identificacao")
public class UsuarioSimplesDTO {

    @ApiModelProperty(value = "Identificador unico do usuario", example = "123")
    private Integer id;

    @ApiModelProperty(value = "Token de autenticacao do usuario", example = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...")
    private String token;

    @ApiModelProperty(value = "Chave de identificacao do usuario", example = "abc123def456")
    private String chave;

    @ApiModelProperty(value = "Permissoes de APIs do usuario")
    private String permissoesApis;

    @ApiModelProperty(value = "Nome de usuario para login", example = "carlos.santos")
    private String username;

    @ApiModelProperty(value = "Conjunto de recursos/permissões do usuário no sistema de academia/crossfit. \n\n" +
            "<strong>Valores disponíveis</strong>\n" +
            "- MUSCULOS (Cadastro de Músculos)\n" +
            "- GRUPOS_MUSCULARES (Grupos Musculares)\n" +
            "- APARELHOS (Aparelhos)\n" +
            "- ATIVIDADES (Atividades)\n" +
            "- FICHAS_PRE_DEFINIDAS (Fichas pré-definidas)\n" +
            "- CATEGORIA_ATIVIDADE (Categoria Atividade)\n" +
            "- CATEGORIA_FICHAS (Categoria Ficha)\n" +
            "- NIVEIS (Níveis)\n" +
            "- OBJETIVOS (Objetivos pré-definidos)\n" +
            "- IMAGENS (Imagens)\n" +
            "- ALUNOS (Alunos)\n" +
            "- GESTAO (Gestão)\n" +
            "- NOTIFICACOES (Notificações)\n" +
            "- ACOMPANHAR (Acompanhar)\n" +
            "- CONFIGURACOES_EMPRESA (Configurações Empresa)\n" +
            "- BADGES (Badges)\n" +
            "- ADD_ALUNO (Adicionar aluno)\n" +
            "- TIPO_EVENTO (Tipo de evento)\n" +
            "- PERFIL_USUARIO (Perfis de usuário)\n" +
            "- AGENDA_DISPONIBILIDADE (Disponibilidade)\n" +
            "- CONTATO_INTERPESSOAL (Contato Interpessoal)\n" +
            "- PRESCRICAO_TREINO (Prescrição de Treino)\n" +
            "- REVISAO_TREINO (Revisão de Treino)\n" +
            "- RENOVAR_TREINO (Renovar Treino)\n" +
            "- AVALIACAO_FISICA (Avaliação Física)\n" +
            "- ALTERAR_PROFESSOR_TREINO (Alterar professor Montou Treino)\n" +
            "- USUARIOS (Usuários)\n" +
            "- VISUALIZAR_DADOS_CONTATO_ALUNO (Visualizar dados de contato do aluno)\n" +
            "- PROGRAMA_TREINO (Programa de treino)\n" +
            "- VER_ALUNOS_OUTRAS_CARTEIRAS (Visualizar alunos de outras carteiras)\n" +
            "- VER_AGENDA_OUTROS_PROFESSORES (Visualizar a Agenda de outros professores)\n" +
            "- GESTAO_PERSONAL (Gestão de Personal)\n" +
            "- LIBERAR_CHECK_IN (Forçar Check-In)\n" +
            "- ACOMPANHAR_PERSONAL (Acompanhar personal)\n" +
            "- GESTAO_CREDITOS_PERSONAL (Visualizar a gestão de créditos)\n" +
            "- CADASTRO_AULAS (Cadastrar aulas)\n" +
            "- EXCLUIR_AULAS_DIA (Excluir aula diária)\n" +
            "- INSERIR_ALUNO (Inserir aluno na aula)\n" +
            "- EXCLUIR_ALUNO (Excluir aluno da aula)\n" +
            "- AGENDA (Agenda de aulas)\n" +
            "- EMPRESA (Empresa)\n" +
            "- VER_GESTAO_GERAL (Visualizar gestão geral)\n" +
            "- TROCAR_PROFESSOR (Trocar professor do aluno)\n" +
            "- ALTERAR_SENHA_USUARIOS (Alterar senha Usuários)\n" +
            "- PERMISSAO_EXCLUIR_ALUNO (Excluir Cadastro do Aluno)\n" +
            "- BENCHMARK (Benchmark)\n" +
            "- TIPO_BENCHMARK (Tipo Benchmark)\n" +
            "- WOD (Wod)\n" +
            "- RANKING_WOD (Ranking Crossfit)\n" +
            "- ALTERAR_HORARIO_INICIO_CHECK_PERSONAL (Alterar data/horário de início/fim do check-in/checkout)\n" +
            "- ANAMNESE (Anamnese)\n" +
            "- LANCAR_AVALICAO_RETROATIVA (Alterar Avaliação Fisica)\n" +
            "- CADASTRAR_TIPO_WOD (Cadastrar Tipo Wod)\n" +
            "- EXCLUIR_ANEXO_ALUNO (Permitir excluir anexo da avaliação física)\n" +
            "- ATIVIDADES_WOD (Cadastrar Atividades Wod)\n" +
            "- APARELHOS_WOD (Cadastrar Aparelho Wod)\n" +
            "- PERMISSAO_EXCLUIR_ALUNO_VINCULADO (Excluir Cadastro do Aluno ignorando vínculo)\n" +
            "- TROCAR_METODO_EXECUCAO (Método de execução)\n" +
            "- MODALIDADES (Cadastrar modalidades)\n" +
            "- SERIE (Cadastrar series)\n" +
            "- AMBIENTES (Cadastrar ambientes)\n" +
            "- LOCAL_RETIRA_FICHA (Cadastrar Local Retira Ficha)\n" +
            "- VISUALIZAR_TODAS_AULAS_APP (Visualizar aulas e turmas de todos os professores no APP)\n" +
            "- USUARIO_MARCAR_ANTECEDENCIA (Marcar e desmarcar aula com antecedência)\n" +
            "- PROGRAMAS_PREDEFINIDOS (Programas predefinidos)\n" +
            "- TORNAR_PROGRAMA_PREDEFINIDO (Tornar um Programa Predefinido)\n" +
            "- ATRIBUIR_PROGRAMA_TREINO_PRE_DEFINIDO (Atribuir Programa Treino Pré Definido)\n" +
            "- DESVINCULAR_USUARIO (Desvincular Usuário da Academia)\n" +
            "- TELA_PRESCRICAO_TREINO (Tela de prescrição de treino)\n" +
            "- AVALIACAO_MEDIA_ALUNOS (Visualizar detalhes de Avaliação média do treino dos alunos)\n" +
            "- VISUALIZAR_WOD_OUTRAS_UNIDADES (Visualizar WOD de outras unidades)\n" +
            "- BI_CROSS (BI Cross)\n" +
            "- MONITOR (Monitor)\n" +
            "- AGENDA_DE_AULAS (Agenda de Aulas)\n" +
            "- AGENDA_DE_SERVICOS (Agenda de Serviços)\n" +
            "- RELATORIOS_AGENDA (Relatórios Agenda)\n" +
            "- BI_AVALIACAO_FISICA (BI Avaliação Física)\n" +
            "- UTILIZAR_MODULO_CROSS (Utilizar Módulo Cross)\n" +
            "- UTILIZAR_MODULO_GRADUACAO (Utilizar Módulo Graduação)\n" +
            "- UTILIZAR_MODULO_AGENDA (Utilizar Módulo Agenda)\n" +
            "- UTILIZAR_MODULO_AVALIACAO_FISICA (Utilizar Módulo Avaliação Física)\n" +
            "- CADASTROS_AGENDA (Cadastros Agenda)\n" +
            "- ENVIAR_TREINO_EM_MASSA (Enviar treino em massa)\n" +
            "- TREINO_EM_CASA (Habilitar treino em casa)\n" +
            "- PRESCRICAO_DE_TREINO (Habilitar prescrição de treino)\n" +
            "- CADASTROS_TREINO (Cadastros treino)\n" +
            "- PRESCRICAO_DE_TREINO_POR_IA (Habilitar prescrição de treino POR I.A.)\n" +
            "- EDITAR_AULAS_DIA (Permitir editar aulas)\n" +
            "- CONFIGURACOES_DO_RANKING (Permitir acessar as configurações do Ranking)",
            example = "[\"ALUNOS\", \"GESTAO\", \"PROGRAMA_TREINO\", \"AVALIACAO_FISICA\", \"AGENDA_DISPONIBILIDADE\", \"WOD\", \"RANKING_WOD\"]")
    private Set<RecursoEnum> recursos;

    @ApiModelProperty(value = "Identificador do colaborador associado", example = "456")
    private Integer colaboradorId;

    @ApiModelProperty(value = "Provedor de autenticacao", example = "local")
    private String provider;

    @ApiModelProperty(value = "Identificador da empresa atual", example = "1")
    private Integer empresaAtual;

    @ApiModelProperty(value = "Chave da foto do usuario", example = "foto_123.jpg")
    private String fotoKey;

    /**
     * Inicializa o array de recursos
     */
    public UsuarioSimplesDTO() {
        this.recursos = new HashSet<>();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public Set<RecursoEnum> getRecursos() {
        return recursos;
    }

    public void setRecursos(Set<RecursoEnum> recursos) {
        this.recursos = recursos;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }


    public List<String> getPermissoesApis() {
        return UteisValidacao.emptyString(permissoesApis) ? new ArrayList<>() : Arrays.asList(permissoesApis.split(","));
    }

    public String getFotoKey() {
        return fotoKey;
    }

    public void setFotoKey(String fotoKey) {
        this.fotoKey = fotoKey;
    }

    public void setPermissoes(String permissoesApis) {
        this.permissoesApis = permissoesApis;
    }

    public Integer getColaboradorId() {
        return colaboradorId;
    }

    public void setColaboradorId(Integer colaboradorId) {
        this.colaboradorId = colaboradorId;
    }

    public String getProvider() {
        return provider;
    }

    public void setProvider(String provider) {
        this.provider = provider;
    }

    public Integer getEmpresaAtual() {return empresaAtual;}

    public void setEmpresaAtual(Integer empresaAtual) {this.empresaAtual = empresaAtual;}
}
