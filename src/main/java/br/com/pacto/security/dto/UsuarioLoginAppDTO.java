package br.com.pacto.security.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * DTO de Login no sistema
 *
 * <AUTHOR>
 * @since 19/07/2018
 */
@ApiModel(description = "Dados necessários para autenticação do usuário em aplicativos móveis usando código do usuário")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class UsuarioLoginAppDTO {

    @ApiModelProperty(value = "Chave de identificação da empresa no sistema", example = "ACADEMIA123", required = true)
    private String chave;

    @ApiModelProperty(value = "Código único do usuário no sistema", example = "12345", required = true)
    private Integer codUsuario;

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public Integer getCodUsuario() {
        return codUsuario;
    }

    public void setCodUsuario(Integer codUsuario) {
        this.codUsuario = codUsuario;
    }
}
