package br.com.pacto.security.service;

import br.com.pacto.bean.perfil.permissao.Permissao;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.bean.usuario.Usuario;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.HashSet;
import java.util.Set;

@ApiModel(description = "Dados do usuário autenticado no sistema com suas permissões e token de acesso")
public class UsuarioAutenticadoDTO {

    @ApiModelProperty(value = "Código único do usuário no sistema", example = "12345")
    private Integer codigo;

    @ApiModelProperty(value = "Nome de usuário para identificação", example = "<EMAIL>")
    private String username;

    @ApiModelProperty(value = "Código do colaborador associado ao usuário", example = "67890")
    private Integer colaboradorId;

    @ApiModelProperty(value = "Lista de recursos/permissões do usuário no sistema. Valores possíveis incluem: ALUNOS, GESTAO, NOTIFICACOES, ACOMPANHAR, CONFIGURACOES_EMPRESA, USUARIOS, PROGRAMA_TREINO, AVALIACAO_FISICA, entre outros")
    private RecursoEnum[] recursos;

    @ApiModelProperty(value = "Token de autenticação do usuário", example = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ")
    private String token;

    public UsuarioAutenticadoDTO() {

    }
    public UsuarioAutenticadoDTO(Usuario usuario) {
        Set<RecursoEnum> recursos = new HashSet<>();
        if(usuario.getPerfil() != null){
            for (Permissao permissao : usuario.getPerfil().getPermissoes()) {
                recursos.add(permissao.getRecurso());
            }
        }
        this.codigo = usuario.getCodigo();
        this.username = usuario.getUserName();
        this.colaboradorId = usuario.getProfessor().getCodigoColaborador() == null ?
                usuario.getProfessor().getCodigo() : usuario.getProfessor().getCodigoColaborador();
        this.recursos = recursos.toArray(new RecursoEnum[recursos.size()]);
        this.token = "";
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public RecursoEnum[] getRecursos() {
        return recursos;
    }

    public void setRecursos(RecursoEnum[] recursos) {
        this.recursos = recursos;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getColaboradorId() {
        return colaboradorId;
    }

    public void setColaboradorId(Integer colaboradorId) {
        this.colaboradorId = colaboradorId;
    }

}
