package br.com.pacto.controller.json.agendamento;

import br.com.pacto.enumerador.agenda.StatusAgendamentoEnum;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@ApiModel(description = "Informações para agendar uma aula")
public class AgendamentoDTO {
    @ApiModelProperty(value = "Dia que ocorrerá a aula. Deve ser informado no formato yyyyMMdd", example = "20250604")
    private String data; //No formato yyyyMMdd
    @ApiModelProperty(value = "Duração da aula (em horas)", example = "1")
    private Integer duracao;
    @ApiModelProperty(value = "Hor<PERSON>rio inicial da aula", example = "19:00")
    private String horarioInicial;
    @ApiModelProperty(value = "Horário que a aula acaba", example = "20:00")
    private String horarioFinal;

    @ApiModelProperty(value = "Código do tipo do agendamento", example = "1")
    private Integer tipo;

    @ApiModelProperty(value = "Código do professor que irá ministrar a aula", example = "1")
    private Integer professor;

    @ApiModelProperty(value = "Código do aluno que irá participar da aula", example = "34")
    private Integer alunoId;

    @ApiModelProperty(value = "Status do agendamento da aula.<br/>" +
            "<strong>Valores disponíveis:<strong/>" +
            "<ul>" +
            "<li>0 - Aguardando confirmação (AGUARDANDO_CONFIRMACAO)</li>" +
            "<li>1 - Confirmado (CONFIRMADO)</li>" +
            "<li>2 - Executado (EXECUTADO)</li>" +
            "<li>3 - Cancelado (CANCELADO)</li>" +
            "<li>4 - Faltou (FALTOU)</li>" +
            "<li>5 - Reagendado (REAGENDADO)</li>" +
            "</ul>", example = "CONFIRMADO", allowableValues = "AGUARDANDO_CONFIRMACAO,CONFIRMADO,EXECUTADO,CANCELADO,FALTOU,REAGENDADO")
    private StatusAgendamentoEnum status;

    @ApiModelProperty(value = "Observação do agendamento", example = "Aula individual")
    private String observacao;

    @ApiModelProperty(value = "Indica se o horário foi alterado", example = "false")
    private Boolean horarioAlterado;

    @ApiModelProperty(value = "Indica se o horário é de um evento", example = "false")
    private Boolean tipoEvento;

    @ApiModelProperty(value = "Código do horário disponível", example = "1")
    private Integer horarioDisponibilidadeCod;

    @ApiModelProperty(value = "Indica se a aula é em uma segunda-feira", example = "false")
    private Boolean seg;

    @ApiModelProperty(value = "Indica se a aula é em uma terça-feira", example = "false")
    private Boolean ter;

    @ApiModelProperty(value = "Indica se a aula é em uma quarta-feira", example = "true")
    private Boolean qua;

    @ApiModelProperty(value = "Indica se a aula é em uma quinta-feira", example = "false")
    private Boolean qui;

    @ApiModelProperty(value = "Indica se a aula é em uma sexta-feira", example = "false")
    private Boolean sex;

    @ApiModelProperty(value = "Indica se a aula é em um sábado", example = "false")
    private Boolean sab;

    @ApiModelProperty(value = "Indica se a aula é em um domingo", example = "false")
    private Boolean dom;

    @ApiModelProperty(value = "Indica a quantidade de vezes que a aula é agendada por semana", example = "1")
    private Integer opcSemanaMes;

    @ApiModelProperty(value = "Opção de periodicidade para a aula", example = "5")
    private Integer opcPeriodicidade;

    @ApiModelProperty(value = "Número de vezes que a aula é agendada", example = "1")
    private Integer nrVezes;

    @ApiModelProperty(value = "Data de finalização dos agendamentos das aulas", example = "20250604")
    private Date dataFim;

    @ApiModelProperty(value = "Opção do agendamento recorrente se é por semana ou por mês", example = "1")
    private Integer opcSemanaOuMes;


    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public Integer getTipo() {
        return tipo;
    }

    public void setTipo(Integer tipo) {
        this.tipo = tipo;
    }

    public String getHorarioInicial() {
        return horarioInicial;
    }

    public void setHorarioInicial(String horarioInicial) {
        this.horarioInicial = horarioInicial;
    }

    public String getHorarioFinal() {
        return horarioFinal;
    }

    public void setHorarioFinal(String horarioFinal) {
        this.horarioFinal = horarioFinal;
    }

    public Integer getAlunoId() {
        return alunoId;
    }

    public void setAlunoId(Integer alunoId) {
        this.alunoId = alunoId;
    }

    public StatusAgendamentoEnum getStatus() {
        return status;
    }

    public void setStatus(StatusAgendamentoEnum status) {
        this.status = status;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public Integer getDuracao() {
        return duracao;
    }

    public void setDuracao(Integer duracao) {
        this.duracao = duracao;
    }

    public Integer getProfessor() {
        return professor;
    }

    public void setProfessor(Integer professor) {
        this.professor = professor;
    }

    public Boolean getHorarioAlterado() {
        return horarioAlterado;
    }

    public void setHorarioAlterado(Boolean horarioAlterado) {
        this.horarioAlterado = horarioAlterado;
    }

    public Boolean getTipoEvento() {
        return tipoEvento;
    }

    public void setTipoEvento(Boolean tipoEvento) {
        this.tipoEvento = tipoEvento;
    }

    public Integer getHorarioDisponibilidadeCod() {
        return horarioDisponibilidadeCod;
    }

    public void setHorarioDisponibilidadeCod(Integer horarioDisponibilidadeCod) {
        this.horarioDisponibilidadeCod = horarioDisponibilidadeCod;
    }

    public Boolean getSeg() {
        return seg;
    }

    public void setSeg(Boolean seg) {
        this.seg = seg;
    }

    public Boolean getTer() {
        return ter;
    }

    public void setTer(Boolean ter) {
        this.ter = ter;
    }

    public Boolean getQua() {
        return qua;
    }

    public void setQua(Boolean qua) {
        this.qua = qua;
    }

    public Boolean getQui() {
        return qui;
    }

    public void setQui(Boolean qui) {
        this.qui = qui;
    }

    public Boolean getSex() {
        return sex;
    }

    public void setSex(Boolean sex) {
        this.sex = sex;
    }

    public Boolean getSab() {
        return sab;
    }

    public void setSab(Boolean sab) {
        this.sab = sab;
    }

    public Boolean getDom() {
        return dom;
    }

    public void setDom(Boolean dom) {
        this.dom = dom;
    }

    public Integer getOpcSemanaMes() {
        return opcSemanaMes;
    }

    public void setOpcSemanaMes(Integer opcSemanaMes) {
        this.opcSemanaMes = opcSemanaMes;
    }

    public Integer getOpcPeriodicidade() {
        return opcPeriodicidade;
    }

    public void setOpcPeriodicidade(Integer opcPeriodicidade) {
        this.opcPeriodicidade = opcPeriodicidade;
    }

    public Integer getNrVezes() {
        return nrVezes;
    }

    public void setNrVezes(Integer nrVezes) {
        this.nrVezes = nrVezes;
    }

    public Date getDataFim() {
        return dataFim;
    }

    public void setDataFim(Date dataFim) {
        this.dataFim = dataFim;
    }

    public Integer getOpcSemanaOuMes() {
        return opcSemanaOuMes;
    }

    public void setOpcSemanaOuMes(Integer opcSemanaOuMes) {
        this.opcSemanaOuMes = opcSemanaOuMes;
    }
}
