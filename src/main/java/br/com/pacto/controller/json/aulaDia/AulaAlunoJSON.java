/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.aulaDia;

import br.com.pacto.bean.aula.AulaAluno;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.controller.json.agendamento.AlunoVinculoAulaEnum;
import br.com.pacto.controller.json.agendamento.EventoAulaDTO;
import br.com.pacto.controller.json.base.SuperJSON;
import br.com.pacto.controller.json.cliente.ClienteJSON;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.util.DataUtils;
import br.com.pacto.util.json.AgendaTotalJSON;
import br.com.pacto.util.AgendadoJSON;
import br.com.pacto.util.json.ClienteSintenticoJson;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * Representa um aluno matriculado em uma aula específica com informações detalhadas
 * <AUTHOR>
 */
@ApiModel(description = "Informações detalhadas de um aluno matriculado em uma aula específica")
public class AulaAlunoJSON extends SuperJSON {

    @ApiModelProperty(value = "Número da matrícula do aluno na academia", example = "2024001")
    private String matricula;

    @ApiModelProperty(value = "Nome completo do aluno", example = "João Silva Santos")
    private String nome;

    @ApiModelProperty(value = "Mensagem ou observação relacionada à aula", example = "Aula de musculação para iniciantes")
    private String mensagem;

    @ApiModelProperty(value = "Número de telefone do aluno", example = "(11) 99999-9999")
    private String telefone;

    @ApiModelProperty(value = "Data de nascimento do aluno no formato dd/MM/yyyy", example = "15/03/1990")
    private String dataNascimento;

    @ApiModelProperty(value = "Número de aulas experimentais disponíveis para o aluno", example = "2")
    private Integer nrAulasExperimentais;

    @ApiModelProperty(value = "Informações detalhadas da aula na qual o aluno está matriculado")
    private AulaDiaJSON aula;

    @ApiModelProperty(value = "Indica se o aluno confirmou presença na aula", example = "true")
    private boolean confirmado = false;

    @ApiModelProperty(value = "Informações completas do cliente/aluno")
    private ClienteJSON cliente;

    @ApiModelProperty(value = "Tipo de vínculo do aluno com a aula. \n\n" +
            "<strong>Valores disponíveis</strong>\n" +
            "- MATRICULADO (Aluno regularmente matriculado)\n" +
            "- REPOSICAO (Aula de reposição)\n" +
            "- DESMARCADO (Aula desmarcada pelo aluno)\n" +
            "- AULA_EXPERIMENTAL (Aula experimental/teste)\n" +
            "- DIARIA (Diária avulsa)\n" +
            "- DIARIA_GYMPASS (Diária via GymPass)\n" +
            "- DIARIA_TOTALPASS (Diária via TotalPass)\n" +
            "- MARCACAO (Marcação de aula)\n" +
            "- VISITANTE (Aluno visitante)\n" +
            "- DEPENDENTE (Dependente de aluno)\n" +
            "- DESAFIO (Participação em desafio)\n" +
            "- INTEGRACAO (Via integração externa)\n" +
            "- PARTICIPANTE_FIXO (Participante fixo da turma)\n" +
            "- ESPERA (Lista de espera)", example = "MATRICULADO")
    private String vinculoComAula;



    public AulaAlunoJSON(AulaAluno aluno) {
        matricula = aluno.getCliente().getMatriculaString();
        nome = aluno.getCliente().getNome();
        mensagem = aluno.getAula().getAula().getMensagem();
        nrAulasExperimentais = aluno.getCliente().getNrAulasExperimentais();
        aula = new AulaDiaJSON(aluno.getAula(), aluno.getAula().getAlunos());
    }

    public AulaAlunoJSON(AgendadoJSON aluno) {
        matricula = aluno.getMatricula();
        nome = aluno.getNome();
        confirmado = aluno.getConfirmado();
        aula = new AulaDiaJSON(aluno);
    }

    public AulaAlunoJSON(AgendadoJSON aluno, ClienteSintetico cliente) {
        this(aluno);
        this.cliente = new ClienteJSON(cliente);
        this.dataNascimento = cliente.getDataNascimento() == null ? "" : Uteis.getData(cliente.getDataNascimento());
        this.telefone = Uteis.celulares(cliente.getTelefones());
    }

    public AulaAlunoJSON(AgendadoJSON aluno, ClienteSintenticoJson cliente) {
        this(aluno);
        this.cliente = new ClienteJSON(cliente);
    }

    public AulaAlunoJSON(AgendaTotalJSON aluno, ClienteSintetico cliente) throws Exception {
        this.matricula = cliente.getMatriculaString();
        this.nome = cliente.getNome();
        this.mensagem = aluno.getMensagem();
        this.aula = new AulaDiaJSON(aluno);
        this.nrAulasExperimentais = cliente.getNrAulasExperimentais();
    }

    public AulaAlunoJSON(AgendaTotalJSON aluno, String nomeEmpresa, String fotokey) throws Exception {
        matricula = aluno.getMatricula() == null ? "" : aluno.getMatricula().toString();
        nome = aluno.getNomeAluno();
        mensagem = aluno.getMensagem();
        nrAulasExperimentais = aluno.getAulasExperimentais();
        aluno.setNomeEmpresa(nomeEmpresa);
        aluno.setLogoEmpresa(fotokey);
        aula = new AulaDiaJSON(aluno);
    }


    public AulaAlunoJSON(ClienteSintetico cliente, EventoAulaDTO aula, List<AulaAluno> alunos) {
        matricula = cliente.getMatriculaString();
        nome = cliente.getNome();
        mensagem = aula.getAula().getMensagem();
        nrAulasExperimentais = null;
//        aula = new AulaDiaJSON(aula, alunos);
    }

    public AulaAlunoJSON(AgendadoJSON aluno, ClienteJSON cliente) {
        this.setMatricula(aluno.getMatricula());
        this.setNome(aluno.getNome());
        this.setConfirmado(aluno.getConfirmado());
        String dataStr = DataUtils.dateToString(cliente.getDataNascimento(), "yyyy-MM-dd");
        this.setDataNascimento(dataStr);
        this.aula = new AulaDiaJSON(aluno);
        this.setCliente(cliente);
        if(aluno.isDesafio()) {
            this.setVinculoComAula(AlunoVinculoAulaEnum.DESAFIO.toString());
        } else if (aluno.isGymPass()) {
            this.setVinculoComAula(AlunoVinculoAulaEnum.DIARIA_GYMPASS.toString());
        } else if(aluno.isExperimental()) {
            this.setVinculoComAula(AlunoVinculoAulaEnum.AULA_EXPERIMENTAL.toString());
        } else {
            this.setVinculoComAula("");
        }
    }

    public String getVinculoComAula() {
        return vinculoComAula;
    }

    public void setVinculoComAula(String vinculoComAula) {
        this.vinculoComAula = vinculoComAula;
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getMensagem() {
        return mensagem;
    }

    public void setMensagem(String mensagem) {
        this.mensagem = mensagem;
    }

    public Integer getNrAulasExperimentais() {
        return nrAulasExperimentais;
    }

    public void setNrAulasExperimentais(Integer nrAulasExperimentais) {
        this.nrAulasExperimentais = nrAulasExperimentais;
    }

    public AulaDiaJSON getAula() {
        return aula;
    }

    public void setAula(AulaDiaJSON aula) {
        this.aula = aula;
    }

    public boolean isConfirmado() {
        return confirmado;
    }

    public void setConfirmado(boolean confirmado) {
        this.confirmado = confirmado;
    }

    public ClienteJSON getCliente() {
        return cliente;
    }

    public void setCliente(ClienteJSON cliente) {
        this.cliente = cliente;
    }

    public String getTelefone() {
        return telefone;
    }

    public void setTelefone(String telefone) {
        this.telefone = telefone;
    }

    public String getDataNascimento() {
        return dataNascimento;
    }

    public void setDataNascimento(String dataNascimento) {
        this.dataNascimento = dataNascimento;
    }
}
