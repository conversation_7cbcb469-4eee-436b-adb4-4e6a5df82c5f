package br.com.pacto.controller.json.aulaExcluida;

import br.com.pacto.bean.aula.AulaDiaExclusao;
import br.com.pacto.bean.colaborador.ColaboradorSimplesTO;
import br.com.pacto.controller.json.agendamento.TurmaResponseDTO;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(value = "Informações da aula excluída")
public class AulaExcluidaDTO {
    @ApiModelProperty(value = "Código identificador da aula excluída", example = "513")
    private Integer codigo;
    @ApiModelProperty(value = "Código do horário de turma que a aula aconteceria", example = "343")
    private Integer codigoHorarioTurma;
    @ApiModelProperty(value = "Nome da aula", example = "Aula de Spinning")
    private String nome;
    @ApiModelProperty(value = "Professor responsável por ministrar a aula")
    private ColaboradorSimplesTO professor;
    @ApiModelProperty(value = "Professor substituto, caso o professor principal não consiga ministrar a aula")
    private ColaboradorSimplesTO professorSubstituto;
    @ApiModelProperty(value = "Nome de usuário responsável pela exclusão", example = "marcus.castro")
    private String nomeUsuario;
    @ApiModelProperty(value = "Dia da aula (Formato yyyyMMdd)", example = "20250611")
    private String dia;
    @ApiModelProperty(value = "Data que a aula aconteceria", example = "2025-12-15T15:00:00Z")
    private Date dataAulaDia;
    @ApiModelProperty(value = "Data de exclusão da aula", example = "2025-11-10T14:35:25Z")
    private Date dataExclusao;

    public AulaExcluidaDTO() {
    }

    public AulaExcluidaDTO(AulaDiaExclusao aulaDiaExclusao, TurmaResponseDTO aula) {
        this.codigo = aulaDiaExclusao.getCodigo();
        this.codigoHorarioTurma = aulaDiaExclusao.getCodigoHorarioTurma();
        this.nome = aula.getNome();
        this.professor = aula.getProfessor();
        this.professorSubstituto = aula.getProfessorSubstituto();
        this.dia = aula.getDia();
        this.dataAulaDia = aulaDiaExclusao.getDataAulaDia();
        this.dataExclusao = aulaDiaExclusao.getDataExclusao();
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getCodigoHorarioTurma() {
        return codigoHorarioTurma;
    }

    public void setCodigoHorarioTurma(Integer codigoHorarioTurma) {
        this.codigoHorarioTurma = codigoHorarioTurma;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public ColaboradorSimplesTO getProfessor() {
        return professor;
    }

    public void setProfessor(ColaboradorSimplesTO professor) {
        this.professor = professor;
    }

    public ColaboradorSimplesTO getProfessorSubstituto() {
        return professorSubstituto;
    }

    public void setProfessorSubstituto(ColaboradorSimplesTO professorSubstituto) {
        this.professorSubstituto = professorSubstituto;
    }

    public String getNomeUsuario() {
        return nomeUsuario;
    }

    public void setNomeUsuario(String nomeUsuario) {
        this.nomeUsuario = nomeUsuario;
    }

    public String getDia() {
        return dia;
    }

    public void setDia(String dia) {
        this.dia = dia;
    }

    public Date getDataAulaDia() {
        return dataAulaDia;
    }

    public void setDataAulaDia(Date dataAulaDia) {
        this.dataAulaDia = dataAulaDia;
    }

    public Date getDataExclusao() {
        return dataExclusao;
    }

    public void setDataExclusao(Date dataExclusao) {
        this.dataExclusao = dataExclusao;
    }
}
