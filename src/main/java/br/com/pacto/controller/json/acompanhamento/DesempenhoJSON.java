/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.acompanhamento;

import br.com.pacto.controller.json.base.SuperJSON;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
@ApiModel(description = "Dados de desempenho de uma ficha ou treino específico")
public class DesempenhoJSON extends SuperJSON {

    @ApiModelProperty(value = "Nome da ficha de treino ou exercício", example = "Treino de Peito e Tríceps")
    private String nome;

    @ApiModelProperty(value = "Lista de dados de desempenho das séries realizadas")
    private List<SerieDesempenhoJSON> dados = new ArrayList<SerieDesempenhoJSON>();

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public List<SerieDesempenhoJSON> getDados() {
        return dados;
    }

    public void setDados(List<SerieDesempenhoJSON> dados) {
        this.dados = dados;
    }
}
