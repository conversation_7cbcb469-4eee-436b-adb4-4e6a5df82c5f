package br.com.pacto.controller.json.agendamento;

import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.pessoa.Telefone;
import br.com.pacto.bean.pessoa.TipoTelefoneEnum;
import br.com.pacto.controller.json.aluno.SituacaoAlunoEnum;
import br.com.pacto.controller.json.aluno.SituacaoContratoZWEnum;
import br.com.pacto.controller.json.aluno.TelefoneDTO;
import br.com.pacto.objeto.Uteis;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by paulo 20/08/2019
 */
@ApiModel(description = "Informações do aluno em um agendamento")
public class AlunoAgendamentoDTO {
    @ApiModelProperty(value = "Código identificador do aluno agendamento", example = "1")
    private Integer id;
    @ApiModelProperty(value = "Código da matrícula do aluno no ZW", example = "123123")
    private Integer matriculaZW;
    @ApiModelProperty(value = "Nome do aluno no agendamento", example = "Renato Cariri Alves")
    private String nome;
    @ApiModelProperty(value = "URL da imagem doa aluno", example = "www.pactosolucoes.com.br/imagens/renato-cariri.jpg")
    private String imageUri;
    @ApiModelProperty(value = "Situação do aluno.\n\n " +
            "<strong>Valores disponíveis</strong>" +
            "- AT - Ativo\n" +
            "- IN - Inativo\n" +
            "- VI - Visitante\n" +
            "- TR - Trancado\n" +
            "- AE - Atestado\n" +
            "- CA - Cancelado\n" +
            "- CR - Carência\n" +
            "- DE - Desistente\n" +
            "- VE - Vencido\n" +
            "- OU - Outros", example = "AT", allowableValues = "AT,IN,VI,TR,AE,CA,CR,DE,VE,OU")
    private SituacaoAlunoEnum situacaoAluno;
    @ApiModelProperty(value = "Situação do contrato do aluno.\n\n Valores disponíveis" +
            "- NO - Normal\n" +
            "- AV - A vencer\n" +
            "- ATM - Atestado médico\n" +
            "- CR - Carência\n" +
            "- CA - Cancelado\n" +
            "- DE - Desistente\n" +
            "- VE - Contrato Inativo / Vencido\n" +
            "- TV - Contrato Trancado / Vencido\n" +
            "- DI - Contrato de diária\n" +
            "- PE - Free Pass\n" +
            "- AA - Aula Avulsa",
            example = "NO", allowableValues = "NO, AV, ATM, CR, CA, DE, VE, TV, DI, PE, AA")
    private SituacaoContratoZWEnum situacaoContrato;
    @ApiModelProperty(value = "Lista de telefones do cliente")
    private List<TelefoneDTO> telefones;

    public AlunoAgendamentoDTO(ClienteSintetico cs, Boolean treinoIndependente) {
        this.id = cs.getCodigo();
        this.matriculaZW = cs.getMatricula();
        this.nome = cs.getNome();
        this.imageUri = cs.getUrlFoto();
        this.situacaoAluno = SituacaoAlunoEnum.getInstance(cs.getSituacao());
        this.situacaoContrato = SituacaoContratoZWEnum.getInstance(cs.getSituacaoContrato());
        this.telefones = new ArrayList<>();
        if (treinoIndependente) {
            for (Telefone telefone : cs.getPessoa().getTelefones()) {
                TelefoneDTO telefoneDTO = new TelefoneDTO();
                telefoneDTO.setNumero(telefone.getTelefone());
                if (Uteis.validarTelefoneCelular(telefone.getTelefone())) {
                    telefoneDTO.setTipo(TipoTelefoneEnum.CELULAR);
                } else {
                    telefoneDTO.setTipo(TipoTelefoneEnum.FIXO);
                }
                this.telefones.add(telefoneDTO);
            }
        } else {
            for (String telefone : cs.getListaTelefones()) {
                TelefoneDTO telefoneDTO = new TelefoneDTO();
                telefoneDTO.setNumero(telefone);
                if (telefone.length() == 13) {
                    telefoneDTO.setTipo(TipoTelefoneEnum.CELULAR);
                } else {
                    telefoneDTO.setTipo(TipoTelefoneEnum.FIXO);
                }
                this.telefones.add(telefoneDTO);
            }
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getMatriculaZW() {
        return matriculaZW;
    }

    public void setMatriculaZW(Integer matriculaZW) {
        this.matriculaZW = matriculaZW;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getImageUri() {
        return imageUri;
    }

    public void setImageUri(String imageUri) {
        this.imageUri = imageUri;
    }

    public SituacaoAlunoEnum getSituacaoAluno() {
        return situacaoAluno;
    }

    public void setSituacaoAluno(SituacaoAlunoEnum situacaoAluno) {
        this.situacaoAluno = situacaoAluno;
    }

    public SituacaoContratoZWEnum getSituacaoContrato() {
        return situacaoContrato;
    }

    public void setSituacaoContrato(SituacaoContratoZWEnum situacaoContrato) {
        this.situacaoContrato = situacaoContrato;
    }

    public List<TelefoneDTO> gettelefones() {
        if (telefones == null) {
            telefones = new ArrayList<>();
        }
        return telefones;
    }

    public void settelefones(List<TelefoneDTO> telefones) {
        this.telefones = telefones;
    }
}
