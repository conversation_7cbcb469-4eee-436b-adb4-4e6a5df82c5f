package br.com.pacto.controller.json.avaliacao;

import br.com.pacto.controller.json.professor.ProfessorJSON;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Dados de um avaliador físico disponível para agendamento")
public class AvaliadorFisicoJSON extends ProfessorJSON {

    @ApiModelProperty(value = "Data do primeiro dia disponível do avaliador físico para agendamento de avaliações", example = "24/07/2025")
    private String primeiroDia;

    public String getPrimeiroDia() {
        return primeiroDia;
    }

    public void setPrimeiroDia(String primeiroDia) {
        this.primeiroDia = primeiroDia;
    }
}
