package br.com.pacto.controller.json.agendamento;

import br.com.pacto.bean.Agendamento;
import br.com.pacto.bean.colaborador.ColaboradorSimplesTO;
import br.com.pacto.controller.json.tipoEvento.TipoAgendamentoDTO;
import br.com.pacto.enumerador.agenda.StatusAgendamentoEnum;
import br.com.pacto.objeto.Calendario;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Informações do serviço de agendamento")
public class ServicoAgendamentoDTO {


    @ApiModelProperty(value = "Código identificador do serviço de agendamento", example = "1")
    private Integer id;

    @ApiModelProperty(value = "Data do agendamento (formato yyyyMMdd)", example = "202506204")
    private String data;

    @ApiModelProperty(value = "Status do agendamento da aula.<br/>" +
            "<strong>Valores disponíveis:<strong/>" +
            "<ul>" +
            "<li>0 - Aguardando confirmação (AGUARDANDO_CONFIRMACAO)</li>" +
            "<li>1 - Confirmado (CONFIRMADO)</li>" +
            "<li>2 - Executado (EXECUTADO)</li>" +
            "<li>3 - Cancelado (CANCELADO)</li>" +
            "<li>4 - Faltou (FALTOU)</li>" +
            "<li>5 - Reagendado (REAGENDADO)</li>" +
            "</ul>", example = "CONFIRMADO", allowableValues = "AGUARDANDO_CONFIRMACAO,CONFIRMADO,EXECUTADO,CANCELADO,FALTOU,REAGENDADO")
    private StatusAgendamentoEnum status;

    @ApiModelProperty(value = "Tipo do agendamento.")
    private TipoAgendamentoDTO tipoAgendamento;

    @ApiModelProperty(value = "Horário inicial que foi agendado", example = "19:00")
    private String horarioInicial;

    @ApiModelProperty(value = "Horário final do agendamento", example = "20:00")
    private String horarioFinal;

    @ApiModelProperty(value = "Informações do professor vinculado ao agendamento")
    private ColaboradorSimplesTO professor;

    @ApiModelProperty(value = "Informações do aluno vinculado ao agendamento")
    private AlunoAgendamentoDTO aluno;

    @ApiModelProperty(value = "Observação sobre o agendamento", example = "Aula Marcada")
    private String observacao;

    public ServicoAgendamentoDTO(Agendamento agendamento, Boolean treinoIndependente) {
        this.id = agendamento.getCodigo();
        this.data = Calendario.getData(agendamento.getInicio(), "yyyyMMdd");
        this.status = agendamento.getStatus();
        this.tipoAgendamento = (agendamento.getTipoEvento() != null && agendamento.getTipoEvento().getCodigo() != null && agendamento.getTipoEvento().getCodigo() > 0) ?
                new TipoAgendamentoDTO(agendamento.getTipoEvento()) : new TipoAgendamentoDTO(agendamento.getHorarioDisponibilidade().getDisponibilidade());
        this.horarioInicial = agendamento.getHoraInicioApresentar();
        this.horarioFinal = agendamento.getHoraFimApresentar();
        this.professor = new ColaboradorSimplesTO(agendamento.getProfessor(), treinoIndependente);
        this.aluno = new AlunoAgendamentoDTO(agendamento.getCliente(), treinoIndependente);
        this.observacao = agendamento.getObservacao();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public StatusAgendamentoEnum getStatus() {
        return status;
    }

    public void setStatus(StatusAgendamentoEnum status) {
        this.status = status;
    }

    public TipoAgendamentoDTO getTipoAgendamento() {
        return tipoAgendamento;
    }

    public void setTipoAgendamento(TipoAgendamentoDTO tipoAgendamento) {
        this.tipoAgendamento = tipoAgendamento;
    }

    public String getHorarioInicial() {
        return horarioInicial;
    }

    public void setHorarioInicial(String horarioInicial) {
        this.horarioInicial = horarioInicial;
    }

    public String getHorarioFinal() {
        return horarioFinal;
    }

    public void setHorarioFinal(String horarioFinal) {
        this.horarioFinal = horarioFinal;
    }

    public ColaboradorSimplesTO getProfessor() {
        return professor;
    }

    public void setProfessor(ColaboradorSimplesTO professor) {
        this.professor = professor;
    }

    public AlunoAgendamentoDTO getAluno() {
        return aluno;
    }

    public void setAluno(AlunoAgendamentoDTO aluno) {
        this.aluno = aluno;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }
}
