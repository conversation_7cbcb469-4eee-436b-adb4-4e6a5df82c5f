package br.com.pacto.controller.json.colaborador;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.colaborador.ColaboradorTO;
import br.com.pacto.bean.colaborador.FiltroColaboradorJSON;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.professor.ProfessorSinteticoService;
import br.com.pacto.swagger.respostas.ExemploRespostaVazia;
import br.com.pacto.util.UteisValidacao;
import io.swagger.annotations.*;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by Joao Moita on 26/09/2018.
 */

@Api(tags = "Colaborador")
@Controller
@RequestMapping("/psec/colaboradores")
public class ColaboradorController {

    private final ProfessorSinteticoService professorSinteticoService;

    @Autowired
    public ColaboradorController(ProfessorSinteticoService professorSinteticoService) {
        Assert.notNull(professorSinteticoService, "O serviço de professor sintético não foi injetado corretamente");
        this.professorSinteticoService = professorSinteticoService;
    }

    @ApiOperation(
            value = "Listar colaboradores com paginação",
            notes = "Consulta colaboradores da empresa com suporte a filtros e paginação. " +
                    "Permite filtrar por nome, situação (ATIVO/INATIVO), tipo de colaborador e outros critérios. " +
                    "Retorna uma lista paginada com os dados dos colaboradores encontrados.",
            tags = "Colaborador"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Número da página a ser retornada", dataType = "int", paramType = "query", defaultValue = "0"),
            @ApiImplicitParam(name = "size", value = "Número de itens por página", dataType = "int", paramType = "query", defaultValue = "20"),
            @ApiImplicitParam(name = "sort", value = "Campo para ordenação dos resultados, seguido de 'ASC' ou 'DESC' (ex: 'nome,ASC'). " +
                    "Campos disponíveis: <br/>" +
                    "<b>nome</b> - Nome do colaborador<br/>" +
                    "<b>situacao</b> - Situação do colaborador<br/>" +
                    "<b>tipoUsuario</b> - Tipo de usuário",
                    dataType = "string", paramType = "query", defaultValue = "nome,ASC")
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Lista paginada de colaboradores)", response = br.com.pacto.swagger.respostas.colaborador.ExemploRespostaListColaboradorResponseTO.class),
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.COLABORADORES)
    @RequestMapping(method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listaColaborador(
            @ApiParam(value = "Filtros de busca, deve ser informado como um JSON.<br/><br/>" +
                    "<strong>É necessário fazer o ENCODE na URL para realizar a requisição com os filtros.</strong><br/><br/>" +
                    "<strong>Filtros disponíveis:</strong><br/>" +
                    "- <strong>quicksearchValue:</strong> Termo de busca para nome do colaborador.<br/>" +
                    "- <strong>situacoes:</strong> Lista de situações do colaborador (Deve ser informado como uma lista ex: [\"ATIVO\", \"INATIVO\"]).<br/>" +
                    "- <strong>tipoColaborador:</strong> Tipo específico do colaborador (PR, TW, PT, OR, CO, PI, PE, TE, ES, FO, CR, MD, FC, AD).<br/>" +
                    "- <strong>quicksearchFields:</strong> Define em quais campos o 'quicksearchValue' será aplicado (Deve ser informado como uma lista ex: [\"nome\", \"userName\"]).",
                    defaultValue = "{\"quicksearchValue\":\"João\", \"situacoes\":[\"ATIVO\"], \"tipoColaborador\":\"PR\", \"quicksearchFields\":[\"nome\"]}")
            @RequestParam(value = "filters", required = false) JSONObject filters,
            @ApiIgnore
            PaginadorDTO paginadorDTO,
            @ApiParam(value = "Código da empresa para consulta dos colaboradores", defaultValue = "1", required = true)
            @RequestHeader(value = "empresaId", required = true) Integer empresaId,
            @ApiParam(value = "Incluir todos os tipos de colaboradores na consulta", defaultValue = "false")
            @RequestParam(value = "todosTipos", required = false) String todosTipos,
            HttpServletRequest request
    ) throws JSONException {
        try {
            FiltroColaboradorJSON filtros = new FiltroColaboradorJSON(filters);
            return ResponseEntityFactory.ok(
                    professorSinteticoService.listarColaboradores(request, filtros, paginadorDTO, empresaId,
                            todosTipos == null ? Boolean.FALSE : Boolean.valueOf(todosTipos)),
                    paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(ColaboradorController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar colaboradores", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @ApiOperation(
            value = "Listar dados básicos dos colaboradores professores",
            notes = "Consulta dados básicos (nome e código) dos colaboradores professores filtrados por quem montou o treino. " +
                    "Retorna informações simplificadas dos professores para uso em seleções e listagens.",
            tags = "Colaborador"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Lista de dados básicos dos professores)", response = br.com.pacto.swagger.respostas.colaborador.ExemploRespostaListColaboradorResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.COLABORADORES)
    @RequestMapping(value = "/professores-dados-basicos/{idProfessorMontou}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listaColaboradorDadosBasicos(
            @ApiParam(value = "ID do professor que montou o treino", defaultValue = "123", required = true)
            @PathVariable("idProfessorMontou") Integer idProfessorMontou,
            @ApiParam(value = "Filtros de busca, deve ser informado como um JSON.<br/><br/>" +
                    "<strong>É necessário fazer o ENCODE na URL para realizar a requisição com os filtros.</strong><br/><br/>" +
                    "<strong>Filtros disponíveis:</strong><br/>" +
                    "- <strong>quicksearchValue:</strong> Termo de busca para nome do colaborador professor.",
                    defaultValue = "{\"quicksearchValue\":\"Maria\"}")
            @RequestParam(value = "filters", required = false) JSONObject filters,
            @ApiParam(value = "Código da empresa ZW para consulta", defaultValue = "1", required = true)
            @RequestHeader(value = "empresaId", required = true) Integer empresaZwId
    ) throws JSONException {
        // retornar somente nome e codigo professor;
        try {
            FiltroColaboradorJSON filtros = new FiltroColaboradorJSON(filters);
            return ResponseEntityFactory.ok(professorSinteticoService.listaColaboradorDadosBasicos(idProfessorMontou, filtros, empresaZwId));
        } catch (ServiceException e) {
            Logger.getLogger(ColaboradorController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar colaboradores", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (JSONException e) {
            Logger.getLogger(ColaboradorController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar colaboradores", e);
            return ResponseEntityFactory.erroInterno("erro_colaboradores_", e.getMessage());
        }
    }

    @ApiOperation(
            value = "Listar todos os colaboradores de forma simplificada",
            notes = "Consulta todos os colaboradores da empresa retornando dados simplificados com suporte a paginação. " +
                    "Utilizado para listagens que necessitam de informações básicas dos colaboradores.",
            tags = "Colaborador"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Número da página a ser retornada", dataType = "int", paramType = "query", defaultValue = "0"),
            @ApiImplicitParam(name = "size", value = "Número de itens por página", dataType = "int", paramType = "query", defaultValue = "20"),
            @ApiImplicitParam(name = "sort", value = "Campo para ordenação dos resultados, seguido de 'ASC' ou 'DESC' (ex: 'nome,ASC'). " +
                    "Campos disponíveis: <br/>" +
                    "<b>nome</b> - Nome do colaborador<br/>" +
                    "<b>id</b> - ID do colaborador",
                    dataType = "string", paramType = "query", defaultValue = "nome,ASC")
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Lista simplificada de colaboradores)", response = br.com.pacto.swagger.respostas.colaborador.ExemploRespostaListColaboradorResponseTOPaginacao.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.COLABORADORES)
    @RequestMapping(value = "/all-simple", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> todosSimples(
            @ApiParam(value = "Filtros de busca, deve ser informado como um JSON.<br/><br/>" +
                    "<strong>É necessário fazer o ENCODE na URL para realizar a requisição com os filtros.</strong><br/><br/>" +
                    "<strong>Filtros disponíveis:</strong><br/>" +
                    "- <strong>quicksearchValue:</strong> Termo de busca para nome do colaborador.",
                    defaultValue = "{\"quicksearchValue\":\"João\"}")
            @RequestParam(value = "filters", required = false) JSONObject filters,
            @ApiIgnore
            PaginadorDTO paginadorDTO,
            @ApiParam(value = "Código da empresa para consulta dos colaboradores", defaultValue = "1", required = true)
            @RequestHeader(value = "empresaId", required = true) Integer empresaId,
            @ApiParam(value = "Incluir todos os tipos de colaboradores na consulta", defaultValue = "false")
            @RequestParam(value = "todosTipos", required = false) String todosTipos,
            HttpServletRequest request) {
        try {

            return ResponseEntityFactory.ok(professorSinteticoService.listarColaboradoresSimples(request, new FiltroColaboradorJSON(filters), paginadorDTO, empresaId));
        } catch (ServiceException e) {
            Logger.getLogger(ColaboradorController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os colaboradores", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            Logger.getLogger(ColaboradorController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os colaboradores", e);
            return ResponseEntityFactory.erroInterno("erro_colaboradores_", e.getMessage());
        }
    }

    @ApiOperation(
            value = "Cadastrar novo colaborador",
            notes = "Cadastra um novo colaborador no sistema com todos os dados pessoais, profissionais e de acesso. " +
                    "Inclui informações como nome, situação, dados de contato, configurações de app e perfil de usuário.",
            tags = "Colaborador"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Colaborador cadastrado com sucesso)", response = br.com.pacto.swagger.respostas.colaborador.ExemploRespostaColaboradorTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.COLABORADORES)
    @RequestMapping(method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cadastroColaborador(
            @ApiParam(value = "Dados completos do colaborador para cadastro", required = true)
            @RequestBody ColaboradorTO colaboradorTO,
            @ApiParam(value = "Código da empresa onde o colaborador será cadastrado", defaultValue = "1", required = true)
            @RequestHeader("empresaId") Integer empresaId,
            HttpServletRequest request) {
        try {
            return ResponseEntityFactory.ok(professorSinteticoService.cadastrarColaborador(colaboradorTO, empresaId, request));
        } catch (ServiceException e) {
            Logger.getLogger(ColaboradorController.class.getName()).log(Level.SEVERE, "Erro ao tentar cadastrar o colaborador", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ApiOperation(
            value = "Consultar detalhes de um colaborador específico",
            notes = "Retorna informações completas de um colaborador específico incluindo dados pessoais, contatos, " +
                    "configurações de aplicativo, perfil de usuário e situação. Permite especificar se a consulta " +
                    "é originada da tela de colaborador para ajustar o retorno de dados.",
            tags = "Colaborador"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Detalhes do colaborador)", response = br.com.pacto.swagger.respostas.colaborador.ExemploRespostaColaboradorResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.COLABORADORES)
    @RequestMapping(value = "/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> detalhesColaborador(
            @ApiParam(value = "ID único do colaborador a ser consultado", defaultValue = "123", required = true)
            @PathVariable("id") Integer id,
            HttpServletRequest request,
            @ApiParam(value = "Código da empresa para consulta do colaborador", defaultValue = "1", required = true)
            @RequestHeader(value = "empresaId") Integer empresaId,
            @ApiParam(value = "Indica se a consulta é originada da tela de colaborador (afeta dados retornados)", defaultValue = "false")
            @RequestParam(value = "origemIsTelaColaborador", required = false) boolean origemIsTelaColaboradorZWUI) {
        try {
            return ResponseEntityFactory.ok(professorSinteticoService.buscarColaborador(id, empresaId, request, origemIsTelaColaboradorZWUI));
        } catch (ServiceException e) {
            Logger.getLogger(ColaboradorController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter detalhes do colaborador", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Alterar situação de um colaborador",
            notes = "Alterna a situação de um colaborador entre ATIVO e INATIVO. Esta operação é utilizada para " +
                    "ativar ou desativar um colaborador no sistema sem excluir seus dados.",
            tags = "Colaborador"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Situação alterada com sucesso)", response = ExemploRespostaVazia.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.COLABORADORES)
    @RequestMapping(value = "edit-situacao/{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> editSituacaoColaborador(
            @ApiParam(value = "ID único do colaborador cuja situação será alterada", defaultValue = "123", required = true)
            @PathVariable("id") Integer id) {
        try {
            professorSinteticoService.editSituacaoColaborador(id);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(ColaboradorController.class.getName()).log(Level.SEVERE, "Erro ao tentar editar situação do colaborador", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Atualizar dados de um colaborador existente",
            notes = "Atualiza as informações de um colaborador existente incluindo dados pessoais, contatos, " +
                    "configurações de aplicativo e perfil de usuário. Todos os campos enviados serão atualizados.",
            tags = "Colaborador"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Colaborador atualizado com sucesso)", response = br.com.pacto.swagger.respostas.colaborador.ExemploRespostaColaboradorTO.class),
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.COLABORADORES)
    @RequestMapping(value = "/{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> atualizarColaborador(
            @ApiParam(value = "ID único do colaborador a ser atualizado", defaultValue = "123", required = true)
            @PathVariable("id") Integer id,
            @ApiParam(value = "Dados atualizados do colaborador", required = true)
            @RequestBody ColaboradorTO colaboradorTO,
            @ApiParam(value = "Código da empresa onde o colaborador será atualizado", defaultValue = "1", required = true)
            @RequestHeader("empresaId") Integer empresaId) {
        try {

            return ResponseEntityFactory.ok(professorSinteticoService.atualizarColaborador(id, empresaId, colaboradorTO));
        } catch (ServiceException e) {
            Logger.getLogger(ColaboradorController.class.getName()).log(Level.SEVERE, "Erro ao tentar alterar o colaborador", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Listar colaboradores sem usuário de sistema",
            notes = "Consulta todos os colaboradores que não possuem usuário de sistema associado. " +
                    "Útil para identificar colaboradores que precisam ter usuários criados para acesso ao sistema.",
            tags = "Colaborador"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Lista de colaboradores sem usuário)", response = br.com.pacto.swagger.respostas.colaborador.ExemploRespostaListColaboradorSimplesTO.class),
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.COLABORADORES)
    @RequestMapping(value = "/is-null-usuario/all", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> colaboradorIsNullUsuario(HttpServletRequest request) {
        try {

            return ResponseEntityFactory.ok(professorSinteticoService.colaboradorIsNullUsuario(request));
        } catch (ServiceException e) {
            Logger.getLogger(ColaboradorController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os colaboradores", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Listar todos os colaboradores da empresa",
            notes = "Consulta todos os colaboradores da empresa com opções para incluir inativos, todos os tipos " +
                    "de colaboradores e validação no sistema de treino. Retorna lista completa sem paginação.",
            tags = "Colaborador"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Lista completa de colaboradores)", response = br.com.pacto.swagger.respostas.colaborador.ExemploRespostaListProfessorResponseTO.class),
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.COLABORADORES)
    @RequestMapping(value = "/all", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarTodosColaborador(
            @ApiParam(value = "Código da empresa para consulta dos colaboradores", defaultValue = "1", required = true)
            @RequestHeader(value = "empresaId", required = true) Integer empresaId,
            @ApiParam(value = "Incluir colaboradores inativos na consulta", defaultValue = "false")
            @RequestParam(value = "incluirInativos", required = false) Boolean incluirInativos,
            @ApiParam(value = "Incluir todos os tipos de colaboradores na consulta", defaultValue = "false")
            @RequestParam(value = "todosTipos", required = false) String todosTipos,
            @ApiParam(value = "Validar colaboradores no sistema de treino", defaultValue = "false")
            @RequestParam(value = "validarNoTreino", required = false) Boolean validarNoTreino,
            HttpServletRequest request) {
        try {

            return ResponseEntityFactory.ok(professorSinteticoService.listarTodosColaborador(request, empresaId, incluirInativos,
                    todosTipos == null ? Boolean.FALSE : Boolean.valueOf(todosTipos),
                    validarNoTreino == null ? Boolean.FALSE : Boolean.valueOf(validarNoTreino)));
        } catch (ServiceException e) {
            Logger.getLogger(ColaboradorController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os colaboradores", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Listar todos os colaboradores ZW da empresa",
            notes = "Consulta todos os colaboradores do sistema ZW (TreinoWeb) da empresa com opções para incluir " +
                    "inativos e todos os tipos de colaboradores. Específico para integração com sistema ZW.",
            tags = "Colaborador"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Lista de colaboradores ZW)", response = br.com.pacto.swagger.respostas.colaborador.ExemploRespostaListColaboradorZWDTO.class),
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.COLABORADORES)
    @RequestMapping(value = "zw/all", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarTodosColaboradorZW(
            @ApiParam(value = "Código da empresa ZW para consulta dos colaboradores", defaultValue = "1", required = true)
            @RequestHeader(value = "empresaId", required = true) Integer empresaId,
            @ApiParam(value = "Incluir colaboradores inativos na consulta", defaultValue = "false")
            @RequestParam(value = "incluirInativos", required = false) Boolean incluirInativos,
            @ApiParam(value = "Incluir todos os tipos de colaboradores na consulta", defaultValue = "false")
            @RequestParam(value = "todosTipos", required = false) String todosTipos,
            HttpServletRequest request) {
        try {

            return ResponseEntityFactory.ok(professorSinteticoService.consultarTodosProfessoresZW(empresaId, incluirInativos,
                    todosTipos == null ? Boolean.FALSE : Boolean.valueOf(todosTipos)));
        } catch (ServiceException e) {
            Logger.getLogger(ColaboradorController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os colaboradores", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Listar colaboradores aptos a ministrar aulas",
            notes = "Consulta todos os colaboradores que estão aptos a ministrar aulas na empresa. " +
                    "Permite filtrar por nome e incluir colaboradores inativos. Específico para seleção de professores em aulas.",
            tags = "Colaborador"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Lista de colaboradores aptos a aulas)", response = br.com.pacto.swagger.respostas.colaborador.ExemploRespostaListColaboradorSimplesTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.COLABORADORES)
    @RequestMapping(value = "/professores-aulas/all", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarTodosColaboradorAptoAAula(
            @ApiParam(value = "Código da empresa para consulta dos colaboradores", defaultValue = "1", required = true)
            @RequestHeader(value = "empresaId") Integer empresaId,
            @ApiParam(value = "Filtros de busca, deve ser informado como um JSON.<br/><br/>" +
                    "<strong>Filtros disponíveis:</strong><br/>" +
                    "- <strong>quicksearchValue:</strong> Termo de busca para nome do colaborador.",
                    defaultValue = "{\"quicksearchValue\":\"João\"}")
            @RequestParam(value = "filters", required = false) JSONObject filters,
            @ApiParam(value = "Incluir colaboradores inativos na consulta", defaultValue = "false")
            @RequestParam(value = "incluirInativos", required = false) Boolean incluirInativos,
            HttpServletRequest request) {
        try {
            String nome = (filters != null && !UteisValidacao.emptyString(filters.optString("quicksearchValue"))) ? filters.optString("quicksearchValue") : null;

            return ResponseEntityFactory.ok(professorSinteticoService.listarTodosColaboradorAptoAAula(request, empresaId, incluirInativos, nome));
        } catch (ServiceException e) {
            Logger.getLogger(ColaboradorController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os colaboradores", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @ApiOperation(
            value = "Consultar colaborador por ID de usuário",
            notes = "Busca um colaborador específico através do ID do usuário de sistema associado. " +
                    "Útil para obter dados do colaborador quando se tem apenas o ID do usuário.",
            tags = "Colaborador"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Dados do colaborador)", response = br.com.pacto.swagger.respostas.colaborador.ExemploRespostaProfessorResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.COLABORADORES)
    @RequestMapping(value = "/usuario/{usuarioId}/colaborador", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarColaboradoradorPorUsuario(
            @ApiParam(value = "ID único do usuário para buscar o colaborador associado", defaultValue = "456", required = true)
            @PathVariable(value = "usuarioId") Integer usuarioId) {
        try {

            return ResponseEntityFactory.ok(professorSinteticoService.consultarColaboradorPorUsuario(usuarioId));
        } catch (ServiceException e) {
            Logger.getLogger(ColaboradorController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os colaboradores", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @ApiOperation(
            value = "Listar professores com vínculos ativos",
            notes = "Consulta todos os professores que possuem vínculos ativos com alunos na empresa. " +
                    "Retorna apenas professores que têm alunos associados em sua carteira.",
            tags = "Colaborador"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Lista de professores com vínculos)", response = br.com.pacto.swagger.respostas.colaborador.ExemploRespostaListProfessorResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.COLABORADORES)
    @RequestMapping(value = "/professores-vinculos", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> professoresComVinculos(
            @ApiParam(value = "Código da empresa para consulta dos professores", defaultValue = "1", required = true)
            @RequestHeader(value = "empresaId") Integer empresaId,
            HttpServletRequest request) {
        try {
            return ResponseEntityFactory.ok(professorSinteticoService.consultarProfessoresComVinculos(request, empresaId));
        } catch (ServiceException e) {
            Logger.getLogger(ColaboradorController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os colaboradores", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Listar professores com vínculos para Business Intelligence",
            notes = "Consulta todos os professores que possuem vínculos ativos com alunos para análise de Business Intelligence. " +
                    "Retorna dados específicos para relatórios e dashboards de acompanhamento de professores.",
            tags = "Colaborador"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Lista de professores para BI)", response = br.com.pacto.swagger.respostas.colaborador.ExemploRespostaListProfessorBIResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.COLABORADORES)
    @RequestMapping(value = "/bi-professores-vinculos", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> biProfessoresComVinculos(
            @ApiParam(value = "Código da empresa para consulta dos professores", defaultValue = "1", required = true)
            @RequestHeader(value = "empresaId") Integer empresaId,
            HttpServletRequest request) {
        try {
            return ResponseEntityFactory.ok(professorSinteticoService.consultarBiProfessoresComVinculos(request, empresaId));
        } catch (ServiceException e) {
            Logger.getLogger(ColaboradorController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os colaboradores", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Listar colaboradores ZW por empresa e IDs específicos",
            notes = "Consulta colaboradores do sistema ZW (TreinoWeb) de uma empresa específica, " +
                    "opcionalmente filtrados por uma lista de IDs. Utilizado para sincronização e integração entre sistemas.",
            tags = "Colaborador"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Lista de colaboradores ZW por empresa)", response = br.com.pacto.swagger.respostas.colaborador.ExemploRespostaListColaboradorZWDTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "zw/todos-por-empresa", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarTodosColaboradoresPorEmpresa(
            @ApiParam(value = "Código da empresa ZW para consulta dos colaboradores", defaultValue = "1", required = true)
            @RequestHeader(value = "empresaId", required = true) Integer empresaId,
            @ApiParam(value = "Lista de IDs específicos de colaboradores separados por vírgula (ex: '1,2,3')", defaultValue = "1,2,3")
            @RequestParam(value = "ids", required = false) String ids) {
        try {
            return ResponseEntityFactory.ok(professorSinteticoService.listarTodosColaboradoresPorEmpresa(ids, empresaId));
        } catch (ServiceException e) {
            Logger.getLogger(ColaboradorController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os colaboradores do zw", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
}
