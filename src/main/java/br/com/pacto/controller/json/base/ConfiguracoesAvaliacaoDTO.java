package br.com.pacto.controller.json.base;

import br.com.pacto.bean.avaliacao.DobrasEnum;
import br.com.pacto.bean.configuracoes.ConfiguracaoDobras;
import br.com.pacto.dao.intf.configuracoes.ConfiguracaoDobrasDao;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.impl.Ordenacao;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.beans.factory.annotation.Autowired;

import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> 07/02/2019
 */
@ApiModel(description = "DTO para configurações de avaliação física do sistema")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ConfiguracoesAvaliacaoDTO {

    @ApiModelProperty(value = "Define se deve incluir objetivos na anamnese", example = "true")
    private String cfg_objetivos_anamnese;

    @ApiModelProperty(value = "Define se deve incluir peso, altura, pressão arterial e frequência cardíaca", example = "true")
    private String cfg_peso_altura_pa_fc;

    @ApiModelProperty(value = "Define se deve incluir questionário PAR-Q", example = "true")
    private String cfg_parq;

    @ApiModelProperty(value = "Define se deve incluir medição de dobras cutâneas", example = "true")
    private String cfg_dobras_cutaneas;

    @ApiModelProperty(value = "Define se deve incluir perimetria (medidas corporais)", example = "true")
    private String cfg_perimetria;

    @ApiModelProperty(value = "Define se deve incluir composição corporal", example = "true")
    private String cfg_composicao_corporal;

    @ApiModelProperty(value = "Define se deve incluir teste de flexibilidade", example = "true")
    private String cfg_flexibilidade;

    @ApiModelProperty(value = "Define se deve incluir avaliação postural", example = "true")
    private String cfg_postura;

    @ApiModelProperty(value = "Define se deve incluir teste de resistência muscular localizada (RML)", example = "true")
    private String cfg_rml;

    @ApiModelProperty(value = "Define se deve incluir teste de VO2 máximo", example = "false")
    private String cfg_vo2max;

    @ApiModelProperty(value = "Define se deve incluir recomendações na avaliação", example = "true")
    private String cfg_recomendacoes;

    @ApiModelProperty(value = "Define se deve incluir ventilometria", example = "false")
    private String cfg_ventilometria;

    @ApiModelProperty(value = "Define se deve incluir testes de campo", example = "true")
    private String cfg_testes_campo;

    @ApiModelProperty(value = "Define se deve incluir teste de bike", example = "false")
    private String cfg_teste_bike;

    @ApiModelProperty(value = "Define se deve incluir somatotipia", example = "false")
    private String cfg_somatotipia;

    @ApiModelProperty(value = "Define se deve incluir teste de Queens", example = "false")
    private String cfg_teste_queens;

    @ApiModelProperty(value = "Define se deve permitir importação de dados do Biosanny", example = "true")
    private String cfg_importacao_biosanny;

    @ApiModelProperty(value = "Define se deve lançar agendamento para próxima avaliação", example = "true")
    private String lancar_agendamento_proxima_avaliacao;

    @ApiModelProperty(value = "Define se deve lançar produto da avaliação", example = "false")
    private String lancar_produto_avaliacao;

    @ApiModelProperty(value = "Define se deve validar produto da avaliação", example = "false")
    private String validar_produto_avaliacao;

    @ApiModelProperty(value = "Código do produto da avaliação", example = "123")
    private String produto_avaliacao;

    @ApiModelProperty(value = "Define se deve lançar produto da avaliação com data de vencimento", example = "false")
    private String lancar_produto_avaliacao_data_vencimento;

    @ApiModelProperty(value = "Define se deve usar pressão sistólica e diastólica separadamente", example = "true")
    private String usar_pressao_sistolica_diastolica;

    @ApiModelProperty(value = "Define se deve usar ordem personalizada para dobras cutâneas", example = "false")
    private String usar_ordem_dobras_cutaneas;

    @ApiModelProperty(value = "Define se deve obrigar preenchimento de campos de dobras e bioimpedância", example = "false")
    private String obrigar_campos_dobras_bioimpedancia;

    @ApiModelProperty(value = "Lista de produtos em formato JSON", example = "[{\"id\":1,\"nome\":\"Avaliação Completa\"}]")
    private String produtos;

    @ApiModelProperty(value = "Ordem das dobras cutâneas em formato JSON", example = "[{\"ordem\":1,\"dobra\":\"TRICEPS\"}]")
    private String ordens_dobras;

    @ApiModelProperty(value = "Modelo de evolução física utilizado", example = "PADRAO")
    private String modelo_evolucao_fisica;

    @ApiModelProperty(value = "Define se deve separar configuração de RML", example = "false")
    private String cfg_rml_separado;

    @ApiModelProperty(value = "Define se deve mostrar opções de RML", example = "true")
    private String cfg_rml_opcoes;

    public Boolean getCfg_objetivos_anamnese() {
        if (!UteisValidacao.emptyString(cfg_objetivos_anamnese)) {
            return cfg_objetivos_anamnese.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setCfg_objetivos_anamnese(String cfg_objetivos_anamnese) {
        this.cfg_objetivos_anamnese = cfg_objetivos_anamnese;
    }

    public Boolean getCfg_peso_altura_pa_fc() {
        if (!UteisValidacao.emptyString(cfg_peso_altura_pa_fc)) {
            return cfg_peso_altura_pa_fc.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setCfg_peso_altura_pa_fc(String cfg_peso_altura_pa_fc) {
        this.cfg_peso_altura_pa_fc = cfg_peso_altura_pa_fc;
    }

    public Boolean getCfg_parq() {
        if (!UteisValidacao.emptyString(cfg_parq)) {
            return cfg_parq.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setCfg_parq(String cfg_parq) {
        this.cfg_parq = cfg_parq;
    }

    public Boolean getCfg_dobras_cutaneas() {
        if (!UteisValidacao.emptyString(cfg_dobras_cutaneas)) {
            return cfg_dobras_cutaneas.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setCfg_dobras_cutaneas(String cfg_dobras_cutaneas) {
        this.cfg_dobras_cutaneas = cfg_dobras_cutaneas;
    }

    public Boolean getCfg_perimetria() {
        if (!UteisValidacao.emptyString(cfg_perimetria)) {
            return cfg_perimetria.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setCfg_perimetria(String cfg_perimetria) {
        this.cfg_perimetria = cfg_perimetria;
    }

    public Boolean getCfg_composicao_corporal() {
        if (!UteisValidacao.emptyString(cfg_composicao_corporal)) {
            return cfg_composicao_corporal.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setCfg_composicao_corporal(String cfg_composicao_corporal) {
        this.cfg_composicao_corporal = cfg_composicao_corporal;
    }

    public Boolean getCfg_flexibilidade() {
        if (!UteisValidacao.emptyString(cfg_flexibilidade)) {
            return cfg_flexibilidade.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setCfg_flexibilidade(String cfg_flexibilidade) {
        this.cfg_flexibilidade = cfg_flexibilidade;
    }

    public Boolean getCfg_postura() {
        if (!UteisValidacao.emptyString(cfg_postura)) {
            return cfg_postura.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setCfg_postura(String cfg_postura) {
        this.cfg_postura = cfg_postura;
    }

    public Boolean getCfg_rml() {
        if (!UteisValidacao.emptyString(cfg_rml)) {
            return cfg_rml.trim().equals("true");
        } else {
            return false;
        }
    }

    public boolean getCfg_rml_separado() {
        if (!UteisValidacao.emptyString(cfg_rml_separado)) {
            return cfg_rml_separado.trim().equals("true");
        }
        return false;

    }

    public void setCfg_rml_separado(String cfg_rml_separado) {
        this.cfg_rml_separado = cfg_rml_separado;
    }


    public void setCfg_rml(String cfg_rml) {
        this.cfg_rml = cfg_rml;
    }

    public Boolean getCfg_vo2max() {
        if (!UteisValidacao.emptyString(cfg_vo2max)) {
            return cfg_vo2max.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setCfg_vo2max(String cfg_vo2max) {
        this.cfg_vo2max = cfg_vo2max;
    }

    public Boolean getCfg_recomendacoes() {
        if (!UteisValidacao.emptyString(cfg_recomendacoes)) {
            return cfg_recomendacoes.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setCfg_recomendacoes(String cfg_recomendacoes) {
        this.cfg_recomendacoes = cfg_recomendacoes;
    }

    public Boolean getCfg_ventilometria() {
        if (!UteisValidacao.emptyString(cfg_ventilometria)) {
            return cfg_ventilometria.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setCfg_ventilometria(String cfg_ventilometria) {
        this.cfg_ventilometria = cfg_ventilometria;
    }

    public Boolean getCfg_testes_campo() {
        if (!UteisValidacao.emptyString(cfg_testes_campo)) {
            return cfg_testes_campo.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setCfg_testes_campo(String cfg_testes_campo) {
        this.cfg_testes_campo = cfg_testes_campo;
    }

    public Boolean getCfg_teste_bike() {
        if (!UteisValidacao.emptyString(cfg_teste_bike)) {
            return cfg_teste_bike.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setCfg_teste_bike(String cfg_teste_bike) {
        this.cfg_teste_bike = cfg_teste_bike;
    }

    public Boolean getCfg_somatotipia() {
        if (!UteisValidacao.emptyString(cfg_somatotipia)) {
            return cfg_somatotipia.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setCfg_somatotipia(String cfg_somatotipia) {
        this.cfg_somatotipia = cfg_somatotipia;
    }

    public Boolean getCfg_teste_queens() {
        if (!UteisValidacao.emptyString(cfg_teste_queens)) {
            return cfg_teste_queens.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setCfg_teste_queens(String cfg_teste_queens) {
        this.cfg_teste_queens = cfg_teste_queens;
    }

    public Boolean getCfg_importacao_biosanny() {
        if (!UteisValidacao.emptyString(cfg_importacao_biosanny)) {
            return cfg_importacao_biosanny.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setCfg_importacao_biosanny(String cfg_importacao_biosanny) {
        this.cfg_importacao_biosanny = cfg_importacao_biosanny;
    }

    public Boolean getLancar_agendamento_proxima_avaliacao() {
        if (!UteisValidacao.emptyString(lancar_agendamento_proxima_avaliacao)) {
            return lancar_agendamento_proxima_avaliacao.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setLancar_agendamento_proxima_avaliacao(String lancar_agendamento_proxima_avaliacao) {this.lancar_agendamento_proxima_avaliacao = lancar_agendamento_proxima_avaliacao; }

    public Boolean getLancar_produto_avaliacao() {
        if (!UteisValidacao.emptyString(lancar_produto_avaliacao)) {
            return lancar_produto_avaliacao.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setLancar_produto_avaliacao(String lancar_produto_avaliacao) { this.lancar_produto_avaliacao = lancar_produto_avaliacao; }

    public Boolean getValidar_produto_avaliacao() {
        if (!UteisValidacao.emptyString(validar_produto_avaliacao)) {
            return validar_produto_avaliacao.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setValidar_produto_avaliacao(String validar_produto_avaliacao) { this.validar_produto_avaliacao = validar_produto_avaliacao; }

    public Integer getProduto_avaliacao() {
        try {
            if (!UteisValidacao.emptyString(produto_avaliacao)) {
                return Integer.parseInt(produto_avaliacao);
            } else {
                return 0;
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            return 0;
        }
    }

    public void setProduto_avaliacao(String produto_avaliacao) { this.produto_avaliacao = produto_avaliacao; }

    public Boolean getLancar_produto_avaliacao_data_vencimento() {
        if (!UteisValidacao.emptyString(lancar_produto_avaliacao_data_vencimento)) {
            return lancar_produto_avaliacao_data_vencimento.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setLancar_produto_avaliacao_data_vencimento(String lancar_produto_avaliacao_data_vencimento) { this.lancar_produto_avaliacao_data_vencimento = lancar_produto_avaliacao_data_vencimento; }

    public Boolean getUsar_pressao_sistolica_diastolica() {
        if (!UteisValidacao.emptyString(usar_pressao_sistolica_diastolica)) {
            return usar_pressao_sistolica_diastolica.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setUsar_pressao_sistolica_diastolica(String usar_pressao_sistolica_diastolica) { this.usar_pressao_sistolica_diastolica = usar_pressao_sistolica_diastolica; }

    public Boolean getUsar_ordem_dobras_cutaneas() {
        if (!UteisValidacao.emptyString(usar_ordem_dobras_cutaneas)) {
            return usar_ordem_dobras_cutaneas.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setUsar_ordem_dobras_cutaneas(String usar_ordem_dobras_cutaneas) { this.usar_ordem_dobras_cutaneas = usar_ordem_dobras_cutaneas; }

    public Boolean getObrigar_campos_dobras_bioimpedancia() {
        if (!UteisValidacao.emptyString(obrigar_campos_dobras_bioimpedancia)) {
            return obrigar_campos_dobras_bioimpedancia.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setObrigar_campos_dobras_bioimpedancia(String obrigar_campos_dobras_bioimpedancia) {
        this.obrigar_campos_dobras_bioimpedancia = obrigar_campos_dobras_bioimpedancia;
    }

    public String getProdutos() {
        return produtos;
    }

    public void setProdutos(String produtos) {
        this.produtos = produtos;
    }

    public String getOrdens_dobras() {
        return ordens_dobras;
    }

    public void setOrdens_dobras(String ordens_dobras) {
        this.ordens_dobras = ordens_dobras;
    }

    public String getModelo_evolucao_fisica() {
        return modelo_evolucao_fisica;
    }

    public void setModelo_evolucao_fisica(String modelo_evolucao_fisica) {
        this.modelo_evolucao_fisica = modelo_evolucao_fisica;
    }
    public boolean getCfg_rml_opcoes() {
        if (!UteisValidacao.emptyString(cfg_rml_opcoes)) {
            return cfg_rml_opcoes.trim().equals("true");
        }
        return false;

    }

    public void setCfg_rml_opcoes(String cfg_rml_opcoes) {
        this.cfg_rml_opcoes = cfg_rml_opcoes;
    }

}
