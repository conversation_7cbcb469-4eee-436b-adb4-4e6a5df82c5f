package br.com.pacto.controller.json.eventoUsuario;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.eventoUsuario.EventoUsuario;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.controller.json.aluno.AlunoController;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.eventoUsuario.EventoUsuarioService;
import br.com.pacto.swagger.respostas.eventoUsuario.ExemploRespostaEventoUsuario;
import br.com.pacto.swagger.respostas.eventoUsuario.ExemploRespostaListEventoUsuario;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR> 19/02/2019
 */
@Controller
@RequestMapping("/psec/eventos-usuario")
public class EventoUsuarioController {

    private final EventoUsuarioService eventoUsuarioService;

    @Autowired
    public EventoUsuarioController(EventoUsuarioService eventoUsuarioService) {
        Assert.notNull(eventoUsuarioService, "O serviço de evento usuário não foi injetado corretamente");
        this.eventoUsuarioService = eventoUsuarioService;
    }

    @ApiOperation(
            value = "Listar todos os eventos de usuário",
            notes = "Obtém todos os eventos de usuário cadastrados no sistema. " +
                    "Retorna uma lista completa com todos os eventos personalizados criados pelos usuários.",
            tags = "Evento"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Lista de eventos obtida com sucesso", response = ExemploRespostaListEventoUsuario.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.ALUNOS)
    @RequestMapping(value = "/all", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listaTodosEventosUsuario() {
        try {
            return ResponseEntityFactory.ok(eventoUsuarioService.obterTodosEventoUsuario());
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar todos eventos dos usuários", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Atualizar evento de usuário",
            notes = "Atualiza um evento de usuário específico através do seu código identificador. " +
                    "Recebe os dados do evento no corpo da requisição e retorna o evento atualizado.",
            tags = "Evento"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Evento atualizado com sucesso", response = ExemploRespostaEventoUsuario.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.ALUNOS)
    @RequestMapping(value = "/{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> atualizarEventosUsuario(
            @ApiParam(value = "Código identificador único do evento a ser atualizado", defaultValue = "EVT001", required = true)
            @PathVariable("id") String eventoId,
            @ApiParam(value = "Dados do evento de usuário para atualização", required = true)
            @RequestBody EventoUsuario eventoUsuario) {
        try {
            eventoUsuario.setCodigo(eventoId);
            return ResponseEntityFactory.ok(eventoUsuarioService.atualizarEventoUsuario(eventoUsuario));
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar alterar evento do usuario", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }
}
