/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.acompanhamento;

import br.com.pacto.controller.json.base.SuperJSON;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 *
 * <AUTHOR>
 */
@ApiModel(description = "Dados estatísticos do acompanhamento de treino do aluno")
public class AcompanhamentoDadosJSON extends SuperJSON {

    @ApiModelProperty(value = "Quantidade de dias já executados do programa de treino atual", example = "15")
    private Integer diasProgramaAtual;

    @ApiModelProperty(value = "Quantidade total de dias prevista para o programa de treino atual", example = "30")
    private Integer diasProgramaAtualTotal;

    @ApiModelProperty(value = "Quantidade de dias de presença na semana atual", example = "4")
    private Integer diasPresencaSemana;

    @ApiModelProperty(value = "Expectativa de dias de treino para a semana", example = "5")
    private Integer expectativaSemana;

    @ApiModelProperty(value = "Quantidade de faltas na semana atual", example = "1")
    private Integer faltasNaSemana;

    @ApiModelProperty(value = "Total de dias de treinamento realizados pelo aluno", example = "120")
    private Integer diasDeTreinamento;

    @ApiModelProperty(value = "Quantidade total de programas de treino já utilizados pelo aluno", example = "8")
    private Integer programas;

    @ApiModelProperty(value = "Quantidade total de atividades diferentes já realizadas pelo aluno", example = "45")
    private Integer atividades;

    public Integer getDiasProgramaAtual() {
        return diasProgramaAtual;
    }

    public void setDiasProgramaAtual(Integer diasProgramaAtual) {
        this.diasProgramaAtual = diasProgramaAtual;
    }

    public Integer getDiasPresencaSemana() {
        return diasPresencaSemana;
    }

    public void setDiasPresencaSemana(Integer diasPresencaSemana) {
        this.diasPresencaSemana = diasPresencaSemana;
    }

    public Integer getExpectativaSemana() {
        return expectativaSemana;
    }

    public void setExpectativaSemana(Integer expectativaSemana) {
        this.expectativaSemana = expectativaSemana;
    }

    public Integer getDiasDeTreinamento() {
        return diasDeTreinamento;
    }

    public void setDiasDeTreinamento(Integer diasDeTreinamento) {
        this.diasDeTreinamento = diasDeTreinamento;
    }

    public Integer getProgramas() {
        return programas;
    }

    public void setProgramas(Integer programas) {
        this.programas = programas;
    }

    public Integer getAtividades() {
        return atividades;
    }

    public void setAtividades(Integer atividades) {
        this.atividades = atividades;
    }

    public Integer getDiasProgramaAtualTotal() {
        return diasProgramaAtualTotal;
    }

    public void setDiasProgramaAtualTotal(Integer diasProgramaAtualTotal) {
        this.diasProgramaAtualTotal = diasProgramaAtualTotal;
    }

    public Integer getFaltasNaSemana() {
        return faltasNaSemana;
    }

    public void setFaltasNaSemana(Integer faltasNaSemana) {
        this.faltasNaSemana = faltasNaSemana;
    }
}
