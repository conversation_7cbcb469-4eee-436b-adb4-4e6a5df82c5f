package br.com.pacto.controller.json.agendamento;

import br.com.pacto.controller.json.gestao.BITreinoAgendaDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Contém informações consolidadas da agenda de aulas e treinos.")
public class BiAgendaDTO {

    @ApiModelProperty(
            value = "Data da última atualização da agenda no formato yyyyMMdd.", example = "2025-06-10"
    )
    private String ultimaAtualizacao;
    private BiAulaDTO biAulas;
    private BITreinoAgendaDTO biAgenda;

    public BiAgendaDTO() {

    }
    public BiAgendaDTO(BiAulaDTO biAulas, BITreinoAgendaDTO biAgenda, String ultimaAtualizacao) {
        this.biAulas = biAulas;
        this.ultimaAtualizacao = ultimaAtualizacao;
        this.biAgenda = biAgenda;
    }

    public BiAulaDTO getBiAulas() {
        return biAulas;
    }

    public void setBiAulas(BiAulaDTO biAulas) {
        this.biAulas = biAulas;
    }

    public BITreinoAgendaDTO getBiAgenda() {
        return biAgenda;
    }

    public void setBiAgenda(BITreinoAgendaDTO biAgenda) {
        this.biAgenda = biAgenda;
    }

    public String getUltimaAtualizacao() {
        return ultimaAtualizacao;
    }

    public void setUltimaAtualizacao(String ultimaAtualizacao) {
        this.ultimaAtualizacao = ultimaAtualizacao;
    }
}
