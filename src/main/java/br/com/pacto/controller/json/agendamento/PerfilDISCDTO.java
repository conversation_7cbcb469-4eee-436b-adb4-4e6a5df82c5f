package br.com.pacto.controller.json.agendamento;

import br.com.pacto.enumerador.agenda.StatusAgendamentoEnum;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@ApiModel(description = "Perfil DISC do cliente para análise comportamental")
public class PerfilDISCDTO {

    @ApiModelProperty(value = "Nível de dominância do cliente (0.0 a 100.0)", example = "75.5")
    private Double dominancia;

    @ApiModelProperty(value = "Nível de influência do cliente (0.0 a 100.0)", example = "60.2")
    private Double influencia;

    @ApiModelProperty(value = "Nível de estabilidade do cliente (0.0 a 100.0)", example = "45.8")
    private Double estabilidade;

    @ApiModelProperty(value = "Nível de conformidade do cliente (0.0 a 100.0)", example = "30.1")
    private Double conformidade;

    @ApiModelProperty(value = "Matrícula do cliente", example = "12345")
    private Integer matricula;

    public Double getDominancia() {
        return dominancia;
    }

    public void setDominancia(Double dominancia) {
        this.dominancia = dominancia;
    }

    public Double getInfluencia() {
        return influencia;
    }

    public void setInfluencia(Double influencia) {
        this.influencia = influencia;
    }

    public Double getEstabilidade() {
        return estabilidade;
    }

    public void setEstabilidade(Double estabilidade) {
        this.estabilidade = estabilidade;
    }

    public Double getConformidade() {
        return conformidade;
    }

    public void setConformidade(Double conformidade) {
        this.conformidade = conformidade;
    }

    public Integer getMatricula() {
        return matricula;
    }

    public void setMatricula(Integer matricula) {
        this.matricula = matricula;
    }
}
