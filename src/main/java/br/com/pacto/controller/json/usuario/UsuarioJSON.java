/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.usuario;

import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.base.SuperJSON;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.util.UteisValidacao;
import org.apache.commons.lang3.StringUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
@ApiModel(description = "Dados completos do usuário formatados para aplicativo móvel")
public class UsuarioJSON extends SuperJSON {

    @ApiModelProperty(value = "Código interno do usuário", example = "12345")
    private Integer cod;

    @ApiModelProperty(value = "Código do usuário no sistema", example = "12345")
    private Integer codUsuario;

    @ApiModelProperty(value = "Código do usuário no sistema ZW", example = "67890")
    private Integer codUsuarioZW;

    @ApiModelProperty(value = "Idade do usuário em anos", example = "30")
    private Integer idade;

    @ApiModelProperty(value = "Nome completo do usuário", example = "João Silva Santos")
    private String nome;

    @ApiModelProperty(value = "Nome de usuário para login", example = "<EMAIL>")
    private String username;

    @ApiModelProperty(value = "URL da imagem/foto do usuário", example = "https://cdn.academia.com/fotos/usuario123.jpg")
    private String srcImg;

    @ApiModelProperty(value = "Nível do aluno na academia", example = "Iniciante")
    private String nivel = "";

    @ApiModelProperty(value = "Endereço de e-mail do usuário", example = "<EMAIL>")
    private String email = "";

    @ApiModelProperty(value = "Nome do professor responsável pelo aluno", example = "Carlos Eduardo")
    private String professor = "";

    @ApiModelProperty(value = "Token de autenticação do usuário", example = "abc123def456ghi789")
    private String token;
    @ApiModelProperty(value = "Versão do aplicativo do usuário", example = "15")
    private Integer versao = 0;

    @ApiModelProperty(value = "URL do avatar/logo da empresa", example = "https://cdn.academia.com/logos/empresa123.png")
    private String avatarEmpresa;

    @ApiModelProperty(value = "URL do site da empresa", example = "https://www.academiafitness.com.br")
    private String urlSiteEmpresa = "";

    @ApiModelProperty(value = "URL da imagem de fundo para tela home em resolução 640x551", example = "https://cdn.academia.com/backgrounds/home_640x551.jpg")
    private String urlHomeBackground640x551 = "";

    @ApiModelProperty(value = "URL da imagem de fundo para tela home em resolução 320x276", example = "https://cdn.academia.com/backgrounds/home_320x276.jpg")
    private String urlHomeBackground320x276 = "";

    @ApiModelProperty(value = "Nome da empresa/academia", example = "Academia Fitness Plus")
    private String nomeEmpresa;

    @ApiModelProperty(value = "Código da pessoa no sistema", example = "98765")
    private Integer codigoPessoa;

    @ApiModelProperty(value = "Código do professor no sistema", example = "456")
    private Integer codigoProfessor;

    @ApiModelProperty(value = "Código do colaborador no sistema", example = "789")
    private Integer codigoColaborador;

    @ApiModelProperty(value = "Data de vencimento do plano do aluno", example = "31/12/2024")
    private String vencPlano = "";

    @ApiModelProperty(value = "Código da empresa no sistema", example = "1")
    private Integer codEmpresa;

    @ApiModelProperty(value = "Número da matrícula do aluno", example = "001234")
    private String matricula = "";

    @ApiModelProperty(value = "Código da pessoa cliente no sistema", example = "54321")
    private Integer codigoPessoaCliente;

    @ApiModelProperty(value = "Perfil do usuário no sistema", example = "Aluno")
    private String perfilUsuario = "";
    //DADOS CONTRATO
    @ApiModelProperty(value = "Data de início do plano do aluno", example = "01/01/2024")
    private String inicioPlano = "";

    @ApiModelProperty(value = "Nome do plano contratado pelo aluno", example = "Plano Mensal Completo")
    private String nomePlano = "";

    @ApiModelProperty(value = "Descrição da duração do plano", example = "12 meses")
    private String descricaoDuracao = "";

    @ApiModelProperty(value = "Duração do plano em meses", example = "12")
    private Integer duracaoMeses;

    @ApiModelProperty(value = "Saldo atual de créditos do aluno", example = "15")
    private Integer saldoCreditos;

    //TODO tornar o valor dinamico
    @ApiModelProperty(value = "Total de créditos disponíveis no plano", example = "30")
    private Integer totalCreditos;

    //TODO tornar o valor da label dinamica
    @ApiModelProperty(value = "Rótulo para exibição dos créditos", example = "créditos")
    private String label = "créditos";

    @ApiModelProperty(value = "Código do contrato do aluno", example = "98765")
    private Integer codigoContrato;

    @ApiModelProperty(value = "Código do convidado (se aplicável)", example = "111")
    private Integer codigoConvidado;

    @ApiModelProperty(value = "Código do convite (se aplicável)", example = "222")
    private Integer codigoConvite;

    @ApiModelProperty(value = "Indica se o usuário é um convidado", example = "false")
    private Boolean convidado = false;

    @ApiModelProperty(value = "Modalidades disponíveis para o aluno", example = "Musculação, Funcional, Crossfit")
    private String modalidade;
    @ApiModelProperty(value = "Nomenclatura utilizada para venda de créditos", example = "Créditos de Treino")
    private String nomenclaturaVendaCredito;

    @ApiModelProperty(value = "Situação atual do contrato", example = "ATIVO")
    private String situacaoContratoOperacao;

    @ApiModelProperty(value = "URL de redirecionamento para área administrativa", example = "https://admin.academia.com/redirect?token=abc123")
    private String urlRedirectAdm;

    @ApiModelProperty(value = "URL de redirecionamento para CRM", example = "https://crm.academia.com/redirect?token=abc123")
    private String urlRedirectCRM;

    @ApiModelProperty(value = "URL de redirecionamento para área de treino", example = "https://treino.academia.com/redirect?token=abc123")
    private String urlRedirectTreino;

    @ApiModelProperty(value = "URL de redirecionamento para área financeira", example = "https://financeiro.academia.com/redirect?token=abc123")
    private String urlRedirectFinan;

    @ApiModelProperty(value = "URL de redirecionamento para ranking UCP", example = "https://ranking.academia.com/redirect?token=abc123")
    private String urlRedirectRankingUCP;

    @ApiModelProperty(value = "Lista de objetivos do aluno", example = "[\"Perder peso\", \"Ganhar massa muscular\", \"Melhorar condicionamento\"]")
    private List<String> objetivos;

    @ApiModelProperty(value = "Lista de empresas associadas ao usuário")
    private List<UsuarioEmpresaApp> empresas = new ArrayList<>();

    @ApiModelProperty(value = "Código do cliente no sistema", example = "54321")
    private Integer codigoCliente;

    @ApiModelProperty(value = "Indica se possui contrato de crédito", example = "true")
    private Boolean contratoCredito = false;

    @ApiModelProperty(value = "Indica se utiliza treino independente", example = "false")
    private Boolean treinoIndependente = false;

    @ApiModelProperty(value = "Status geral do usuário", example = "ATIVO")
    private String status;

    @ApiModelProperty(value = "Status específico do aluno", example = "ATIVO")
    private String statusAluno;
    @ApiModelProperty(value = "Número de telefone do usuário", example = "(11) 99999-9999")
    private String telefone;

    @ApiModelProperty(value = "ID do cliente no sistema Spivi", example = "12345")
    private int spiviClientID;

    @ApiModelProperty(value = "Código de acesso do usuário", example = "ABC123")
    private String codigoAcesso;

    @ApiModelProperty(value = "Data de cadastro do usuário em timestamp", example = "1640995200000")
    private Long dataCadastro;

    @ApiModelProperty(value = "Indica se tem acesso à academia", example = "true")
    private Boolean acessoAcademia;

    @ApiModelProperty(value = "Data de validade do CREF (para professores)", example = "31/12/2025")
    private String validadeCREF;

    @ApiModelProperty(value = "Dia específico em timestamp", example = "1640995200000")
    private Long dia;

    @ApiModelProperty(value = "Lista de dependentes do usuário")
    private List<UsuarioDependenteDTO> dependenteDTOS;

    @ApiModelProperty(value = "CPF do usuário", example = "123.456.789-00")
    private String cpf;

    public UsuarioJSON(final Usuario usuario, List<UsuarioEmpresaApp> empresas)  {
        boolean independente = Aplicacao.independente(usuario.getChave());
        this.codUsuario = usuario.getCodigo();
        this.codUsuarioZW = usuario.getUsuarioZW();
        if (!UteisValidacao.emptyString(usuario.getFotoKeyApp())) {
            this.srcImg = usuario.getFotoKeyApp();
        } else {
            this.srcImg = StringUtils.isBlank(usuario.getAvatar()) ? "" : usuario.getAvatar();
        }
        this.username = usuario.getUserName();
        this.token = usuario.getToken();
        this.avatarEmpresa = usuario.getAvatarEmpresaApp();
        this.urlSiteEmpresa = usuario.getUrlSite() == null ? "" : usuario.getUrlSite().toLowerCase();
        this.urlHomeBackground320x276 = usuario.getUrlHomeBackground320x276();
        this.urlHomeBackground640x551 = usuario.getUrlHomeBackground640x551();
        this.nomeEmpresa = usuario.getNomeEmpresa();
        this.codEmpresa = usuario.getEmpresaZW();
        this.status = usuario.getStatus().name();

        if (usuario.getCliente() == null) {
            this.username = usuario.getUserName();
            if (usuario.getProfessor() != null) {
                if ( null != usuario.getProfessor().getEmail() && !usuario.getProfessor().getEmail().isEmpty() && usuario.getProfessor().getEmail() != null){
                    this.email = usuario.getProfessor().getEmail();
                }else{
                    this.email = usuario.getUsuarioEmail() == null || UteisValidacao.emptyNumber(usuario.getUsuarioEmail().getCodigo()) ?  this.username : usuario.getUsuarioEmail().getEmail();
                }
            }
            if (usuario.getProfessor() != null && usuario.getProfessor().getPessoa() != null && !UteisValidacao.emptyList(usuario.getProfessor().getPessoa().getTelefones())){
                this.telefone = usuario.getProfessor().getPessoa().getTelefones().get(0).getTelefone();
            }
            this.avatarEmpresa = usuario.getAvatarEmpresaApp();
            this.urlHomeBackground320x276 = usuario.getUrlHomeBackground320x276();
            this.urlHomeBackground640x551 = usuario.getUrlHomeBackground640x551();
            this.nomeEmpresa = usuario.getNomeEmpresa();
            this.codEmpresa = usuario.getEmpresaZW();
            this.convidado = true;

            this.cod = usuario.getCodigo();
            this.nome = usuario.getProfessor().getNome();
            this.codigoPessoa = usuario.getProfessor().getCodigoPessoa();
            this.codigoProfessor = usuario.getProfessor().getCodigo();
            this.codigoColaborador = usuario.getProfessor().getCodigoColaborador();
            this.perfilUsuario = usuario.getPerfil() == null ? "" : usuario.getPerfil().getNome();
            this.validadeCREF = Uteis.getData(usuario.getProfessor().getValidadeCREF());
            if(!UteisValidacao.emptyList(empresas)) {
                this.empresas.addAll(empresas);
            }
            this.codigoAcesso = usuario.getProfessor().getCodigoAcesso();
            return;
        }
        this.idade = usuario.getCliente().getIdade();
        this.codEmpresa = usuario.getCliente().getEmpresa();
        this.totalCreditos = usuario.getCliente().getTotalCreditoTreino();
        this.cod = usuario.getCliente().getCodigo();
        this.codigoPessoaCliente = usuario.getCliente().getCodigoPessoa();
        this.codigoCliente = usuario.getCliente().getCodigoCliente();
        this.nome = usuario.getCliente().getNome();
        this.nivel = usuario.getCliente().getNivelAluno() != null ? usuario.getCliente().getNivelAluno().getNome() : "N/C";
        this.professor = usuario.getCliente().getProfessorSintetico() == null ? "(Sem professor)" : usuario.getCliente().getProfessorSintetico().getNome();
        this.versao = usuario.getCliente() != null ? usuario.getCliente().getVersao() : 0;
        this.vencPlano = usuario.getCliente().getDataVigenciaAteAjustadaApresentar();
        this.matricula = usuario.getCliente().getMatriculaString();
        this.inicioPlano = independente ? (usuario.getCliente().getDataMatricula()  == null ? "" : Uteis.getData(usuario.getCliente().getDataMatricula())) :
                usuario.getCliente().getDataVigenciaDeApresentar();
        this.saldoCreditos = usuario.getCliente().getSaldoCreditoTreino();
        this.nomePlano = usuario.getCliente().getNomeplano();
        this.duracaoMeses = usuario.getCliente().getDuracaoContratoMeses();

        if(usuario.getCliente().getDescricaoDuracao() != null &&
                usuario.getCliente().getDuracaoContratoMeses() != null){
            this.descricaoDuracao = usuario.getCliente().getDescricaoDuracao() == null
                    ? (usuario.getCliente().getDuracaoContratoMeses() == 1 ? "1 mês"
                    : (usuario.getCliente().getDuracaoContratoMeses() + " meses")) : usuario.getCliente().getDescricaoDuracao();
        }

        this.codigoContrato = usuario.getCliente().getCodigoContrato();
        this.modalidade = usuario.getCliente().getDescricoesModalidades();
        this.situacaoContratoOperacao = usuario.getCliente().getSituacaoContratoOperacao();
        this.objetivos = usuario.getCliente().getObjetivosLista();
        this.codigoAcesso = usuario.getCliente().getCodigoAcesso();
        this.dataCadastro = usuario.getCliente().getDataCadastro() != null
                ? usuario.getCliente().getDataCadastro().getTime(): null;
        this.statusAluno = usuario.getCliente() != null ? usuario.getCliente().getSituacao() : "";
    }
    private String formatUrl(final String urlBase , final Usuario usuario,final String uri) {
        try {
            return  urlBase + Uteis.formatUrlRedirect(usuario.getChave(),"","",usuario.getUsuarioZW(),usuario.getEmpresaZW(),uri, "");
        } catch (Exception ex) {
            Logger.getLogger(UsuarioJSON.class.getName()).log(Level.SEVERE, null, ex);
            return "";
        }
    }
    private String formatUrlUCP(final String urlBase , final Usuario usuario) {
        try {
            return  urlBase + Uteis.formatUrlRedirectUCP(usuario.getChave(),usuario);
        } catch (Exception ex) {
            Logger.getLogger(UsuarioJSON.class.getName()).log(Level.SEVERE, null, ex);
            return "";
        }
    }
    public void montarUrlRedirect(Usuario usuario,String urlBaseTreino){
        this.urlRedirectTreino = formatUrl(urlBaseTreino,usuario,Uteis.URI_REDIRECT_BI_TREINO);
        this.urlRedirectAdm = formatUrl(Aplicacao.getProp(usuario.getChave(), Aplicacao.urlZillyonWeb),usuario,Uteis.URI_REDIRECT_BI_DETALHADO);
        this.urlRedirectFinan = formatUrl(Aplicacao.getProp(usuario.getChave(), Aplicacao.urlZillyonWeb),usuario,Uteis.URI_REDIRECT_FINAN);
        this.urlRedirectCRM = formatUrl(Aplicacao.getProp(usuario.getChave(), Aplicacao.urlZillyonWeb),usuario,Uteis.URI_REDIRECT_CRM_BI);
        this.urlRedirectRankingUCP = formatUrlUCP(Aplicacao.getProp(usuario.getChave(), Aplicacao.myUpUrlBase),usuario);
    }
    public Integer getCod() {
        return cod;
    }

    public void setCod(Integer cod) {
        this.cod = cod;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getSrcImg() {
        return srcImg;
    }

    public void setSrcImg(String srcImg) {
        this.srcImg = srcImg;
    }

    public String getNivel() {
        return nivel;
    }

    public void setNivel(String nivel) {
        this.nivel = nivel;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getProfessor() {
        return professor;
    }

    public void setProfessor(String professor) {
        this.professor = professor;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public Integer getVersao() {
        if (versao == null) {
            versao = 0;
        }
        return versao;
    }

    public void setVersao(Integer versao) {
        this.versao = versao;
    }

    public String getAvatarEmpresa() {
        return avatarEmpresa;
    }

    public void setAvatarEmpresa(String avatarEmpresa) {
        this.avatarEmpresa = avatarEmpresa;
    }

    public String getNomeEmpresa() {
        return nomeEmpresa;
    }

    public void setNomeEmpresa(String nomeEmpresa) {
        this.nomeEmpresa = nomeEmpresa;
    }

    public Integer getCodigoPessoa() {
        return codigoPessoa;
    }

    public void setCodigoPessoa(Integer codigoPessoa) {
        this.codigoPessoa = codigoPessoa;
    }

    public Integer getCodEmpresa() {
        return codEmpresa;
    }

    public void setCodEmpresa(Integer codEmpresa) {
        this.codEmpresa = codEmpresa;
    }

    public String getVencPlano() {
        return vencPlano;
    }

    public void setVencPlano(String vencPlano) {
        this.vencPlano = vencPlano;
    }

    public String getUrlHomeBackground640x551() {
        return urlHomeBackground640x551;
    }

    public void setUrlHomeBackground640x551(String urlHomeBackground640x551) {
        this.urlHomeBackground640x551 = urlHomeBackground640x551;
    }

    public String getUrlHomeBackground320x276() {
        return urlHomeBackground320x276;
    }

    public void setUrlHomeBackground320x276(String urlHomeBackground320x276) {
        this.urlHomeBackground320x276 = urlHomeBackground320x276;
    }

    public Integer getCodigoProfessor() {
        return codigoProfessor;
    }

    public void setCodigoProfessor(Integer codigoProfessor) {
        this.codigoProfessor = codigoProfessor;
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public String getInicioPlano() {
        return inicioPlano;
    }

    public void setInicioPlano(String inicioPlano) {
        this.inicioPlano = inicioPlano;
    }

    public String getNomePlano() {
        return nomePlano;
    }

    public void setNomePlano(String nomePlano) {
        this.nomePlano = nomePlano;
    }

    public Integer getDuracaoMeses() {
        return duracaoMeses;
    }

    public void setDuracaoMeses(Integer duracaoMeses) {
        this.duracaoMeses = duracaoMeses;
    }

    public Integer getSaldoCreditos() {
        return saldoCreditos;
    }

    public void setSaldoCreditos(Integer saldoCreditos) {
        this.saldoCreditos = saldoCreditos;
    }

    public Integer getCodigoContrato() {
        return codigoContrato;
    }

    public void setCodigoContrato(Integer codigoContrato) {
        this.codigoContrato = codigoContrato;
    }

    public String getDescricaoDuracao() {
        return descricaoDuracao;
    }

    public void setDescricaoDuracao(String descricaoDuracao) {
        this.descricaoDuracao = descricaoDuracao;
    }

    public Integer getTotalCreditos() {
        return totalCreditos;
    }

    public void setTotalCreditos(Integer totalCreditos) {
        this.totalCreditos = totalCreditos;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public Boolean getConvidado() {
        return convidado;
    }

    public void setConvidado(Boolean convidado) {
        this.convidado = convidado;
    }

    public Integer getCodigoConvidado() {
        return codigoConvidado;
    }

    public void setCodigoConvidado(Integer codigoConvidado) {
        this.codigoConvidado = codigoConvidado;
    }

    public Integer getCodigoConvite() {
        return codigoConvite;
    }

    public void setCodigoConvite(Integer codigoConvite) {
        this.codigoConvite = codigoConvite;
    }

    public String getModalidade() {
        return modalidade;
    }

    public void setModalidade(String modalidade) {
        this.modalidade = modalidade;
    }

    public String getNomenclaturaVendaCredito() {
        return nomenclaturaVendaCredito;
    }

    public void setNomenclaturaVendaCredito(String nomenclaturaVendaCredito) {
        this.nomenclaturaVendaCredito = nomenclaturaVendaCredito;
    }

    public String getSituacaoContratoOperacao() {
        return situacaoContratoOperacao;
    }

    public void setSituacaoContratoOperacao(String situacaoContratoOperacao) {
        this.situacaoContratoOperacao = situacaoContratoOperacao;
    }

    public List<UsuarioEmpresaApp> getEmpresas() {
        return empresas;
    }

    public void setEmpresas(List<UsuarioEmpresaApp> empresas) {
        this.empresas = empresas;
    }

    public String getUrlRedirectAdm() {
        return urlRedirectAdm;
    }

    public void setUrlRedirectAdm(String urlRedirectAdm) {
        this.urlRedirectAdm = urlRedirectAdm;
    }

    public String getUrlRedirectCRM() {
        return urlRedirectCRM;
    }

    public void setUrlRedirectCRM(String urlRedirectCRM) {
        this.urlRedirectCRM = urlRedirectCRM;
    }

    public String getUrlRedirectTreino() {
        return urlRedirectTreino;
    }

    public void setUrlRedirectTreino(String urlRedirectTreino) {
        this.urlRedirectTreino = urlRedirectTreino;
    }

    public String getUrlRedirectFinan() {
        return urlRedirectFinan;
    }

    public void setUrlRedirectFinan(String urlRedirectFinan) {
        this.urlRedirectFinan = urlRedirectFinan;
    }

    public String getPerfilUsuario() {
        return perfilUsuario;
    }

    public void setPerfilUsuario(String perfilUsuario) {
        this.perfilUsuario = perfilUsuario;
    }

    public String getUrlSiteEmpresa() {
        return urlSiteEmpresa;
    }

    public void setUrlSiteEmpresa(String urlSiteEmpresa) {
        this.urlSiteEmpresa = urlSiteEmpresa;
    }

    public String getUrlRedirectRankingUCP() {
        return urlRedirectRankingUCP;
    }

    public void setUrlRedirectRankingUCP(String urlRedirectRankingUCP) {
        this.urlRedirectRankingUCP = urlRedirectRankingUCP;
    }

    public List<String> getObjetivos() {
        return objetivos;
    }

    public void setObjetivos(List<String> objetivos) {
        this.objetivos = objetivos;
    }

    public Integer getCodigoPessoaCliente() {
        return codigoPessoaCliente;
    }

    public void setCodigoPessoaCliente(Integer codigoPessoaCliente) {
        this.codigoPessoaCliente = codigoPessoaCliente;
    }

    public Integer getCodigoCliente() {
        return codigoCliente;
    }

    public void setCodigoCliente(Integer codigoCliente) {
        this.codigoCliente = codigoCliente;
    }

    public Integer getIdade() {
        return idade;
    }

    public void setIdade(Integer idade) {
        this.idade = idade;
    }

    public Boolean getContratoCredito() {
        if (contratoCredito == null) {
            contratoCredito = false;
        }
        return contratoCredito;
    }

    public void setContratoCredito(Boolean contratoCredito) {
        this.contratoCredito = contratoCredito;
    }

    public Integer getCodUsuario() {
        return codUsuario;
    }

    public void setCodUsuario(Integer codUsuario) {
        this.codUsuario = codUsuario;
    }

    public Boolean getTreinoIndependente() {
        return treinoIndependente;
    }

    public void setTreinoIndependente(Boolean treinoIndependente) {
        this.treinoIndependente = treinoIndependente;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getTelefone() {
        return telefone;
    }

    public void setTelefone(String telefone) {
        this.telefone = telefone;
    }

    public int getSpiviClientID() {
        return spiviClientID;
    }

    public void setSpiviClientID(int spiviClientID) {
        this.spiviClientID = spiviClientID;
    }

    public String getCodigoAcesso() {
        return codigoAcesso;
    }

    public void setCodigoAcesso(String codigoAcesso) {
        this.codigoAcesso = codigoAcesso;
    }

    public Long getDataCadastro() {
        return dataCadastro;
    }

    public void setDataCadastro(Long dataCadastro) {
        this.dataCadastro = dataCadastro;
    }

    public Integer getCodigoColaborador() {
        return codigoColaborador;
    }

    public void setCodigoColaborador(Integer codigoColaborador) {
        this.codigoColaborador = codigoColaborador;
    }

    public Integer getCodUsuarioZW() {
        return codUsuarioZW;
    }

    public void setCodUsuarioZW(Integer codUsuarioZW) {
        this.codUsuarioZW = codUsuarioZW;
    }

    public Boolean getAcessoAcademia() {
        return acessoAcademia;
    }

    public void setAcessoAcademia(Boolean acessoAcademia) {
        this.acessoAcademia = acessoAcademia;
    }

    public String getValidadeCREF() {
        return validadeCREF;
    }

    public void setValidadeCREF(String validadeCREF) {
        this.validadeCREF = validadeCREF;
    }

    public String getStatusAluno() {
        return statusAluno;
    }

    public void setStatusAluno(String statusAluno) {
        this.statusAluno = statusAluno;
    }

    public Long getDia() {
        return dia;
    }

    public void setDia(Long dia) {
        this.dia = dia;
    }

    public List<UsuarioDependenteDTO> getDependenteDTOS() {
        return dependenteDTOS;
    }

    public void setDependenteDTOS(List<UsuarioDependenteDTO> dependenteDTOS) {
        this.dependenteDTOS = dependenteDTOS;
    }

    public String getCpf() {
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }
}
