package br.com.pacto.controller.json.base;


import br.com.pacto.util.UteisValidacao;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


@ApiModel(description = "DTO para configurações gerais do sistema")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ConfiguracoesGeraisDTO {

    @ApiModelProperty(value = "Define se deve validar se a agenda está cheia antes de permitir agendamentos", example = "true")
    private String validar_agenda_aulacheia;

    @ApiModelProperty(value = "Define se deve desativar a tela de treino do aluno", example = "false")
    private String desativar_tela_aluno_treino;

    @ApiModelProperty(value = "Define se deve trocar a nomenclatura 'CrossFit' por outra denominação", example = "false")
    private String troca_nomenclatura_crossfit;

    @ApiModelProperty(value = "Duração em minutos que o aluno pode permanecer na academia", example = "120")
    private String duracao_aluno_na_academia;

    @ApiModelProperty(value = "Define se as configurações de treino devem ser replicadas para toda a rede da empresa", example = "false")
    private String configuracoes_treino_replicar_rede_empresa;

    @ApiModelProperty(value = "Define o tipo de lista rápida de acessos a ser utilizada", example = "1")
    private String escolher_tipo_lista_rapida_acessos;

    @ApiModelProperty(value = "Quantidade de minutos de antecedência necessários para cancelar um agendamento", example = "60")
    private String minutos_cancelar_com_antecedencia;

    public Boolean getValidar_agenda_aulacheia() {
        if (!UteisValidacao.emptyString(validar_agenda_aulacheia)) {
            return validar_agenda_aulacheia.trim().equals("true");
        } else {
            return false;
        }
    }

    public Boolean getDesativar_tela_aluno_treino() {
        if (!UteisValidacao.emptyString(desativar_tela_aluno_treino)) {
            return desativar_tela_aluno_treino.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setDesativar_tela_aluno_treino(String desativar_tela_aluno_treino) {
        this.desativar_tela_aluno_treino = desativar_tela_aluno_treino;
    }

    public void setValidar_agenda_aulacheia(String validar_agenda_aulacheia) {
        this.validar_agenda_aulacheia = validar_agenda_aulacheia;
    }

    public String getDuracao_aluno_na_academia() {return duracao_aluno_na_academia; }

    public void setDuracao_aluno_na_academia(String duracao_aluno_na_academia) { this.duracao_aluno_na_academia = duracao_aluno_na_academia; }

    public Boolean getTroca_nomenclatura_crossfit() {
        if (!UteisValidacao.emptyString(troca_nomenclatura_crossfit)) {
            return troca_nomenclatura_crossfit.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setTroca_nomenclatura_crossfit(String troca_nomenclatura_crossfit) { this.troca_nomenclatura_crossfit = troca_nomenclatura_crossfit; }

    public Boolean getConfiguracoes_treino_replicar_rede_empresa() {
        if (!UteisValidacao.emptyString(configuracoes_treino_replicar_rede_empresa)) {
            return configuracoes_treino_replicar_rede_empresa.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setConfiguracoes_treino_replicar_rede_empresa(String configuracoes_treino_replicar_rede_empresa) {
        this.configuracoes_treino_replicar_rede_empresa = configuracoes_treino_replicar_rede_empresa;
    }

    public Boolean getEscolher_tipo_lista_rapida_acessos() {
        if (!UteisValidacao.emptyString(escolher_tipo_lista_rapida_acessos)) {
            return escolher_tipo_lista_rapida_acessos.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setEscolher_tipo_lista_rapida_acessos(String escolher_tipo_lista_rapida_acessos) {
        this.escolher_tipo_lista_rapida_acessos = escolher_tipo_lista_rapida_acessos;
    }

    public String getMinutos_cancelar_com_antecedencia() {
        return minutos_cancelar_com_antecedencia;
    }

    public void setMinutos_cancelar_com_antecedencia(String minutos_cancelar_com_antecedencia) {
        this.minutos_cancelar_com_antecedencia = minutos_cancelar_com_antecedencia;
    }
}
