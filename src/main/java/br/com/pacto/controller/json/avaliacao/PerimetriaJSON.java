package br.com.pacto.controller.json.avaliacao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Created by alcides on 21/09/2017.
 */
@ApiModel(description = "Medida de perimetria (circunferência) realizada na avaliação física")
public class PerimetriaJSON {

    @ApiModelProperty(value = "Nome da região corporal onde foi medida a circunferência", example = "Braço Direito")
    private String nome;

    @ApiModelProperty(value = "Valor da medida da circunferência em centímetros", example = "32.5")
    private Double valor;

    public PerimetriaJSON(String nome, Double valor) {
        this.nome = nome;
        this.valor = valor;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }
}
