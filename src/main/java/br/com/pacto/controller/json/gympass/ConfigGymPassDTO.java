package br.com.pacto.controller.json.gympass;

import br.com.pacto.bean.gympass.ConfigGymPass;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 23/03/2020
 */
@ApiModel(description = "Dados de configuração do GymPass para uma empresa")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ConfigGymPassDTO {

    @ApiModelProperty(value = "Código único identificador da configuração GymPass", example = "1")
    private Integer codigo;

    @ApiModelProperty(value = "Código da empresa vinculada à configuração", example = "1")
    private Integer empresa;

    @ApiModelProperty(value = "Nome da empresa vinculada à configuração", example = "Academia Fitness Plus")
    private String nome;

    @ApiModelProperty(value = "Código identificador da empresa no sistema GymPass", example = "GYM123456")
    private String codigoGymPass;

    @ApiModelProperty(value = "Indica se a integração com GymPass Booking está ativa", example = "true")
    private boolean usarGymPassBooking;

    @ApiModelProperty(value = "Indica se é permitido realizar WODs (Workout of the Day) via GymPass", example = "true")
    private boolean permitirWod;

    @ApiModelProperty(value = "Limite máximo de acessos por dia para usuários GymPass", example = "3")
    private Integer limiteDeAcessosPorDia;

    @ApiModelProperty(value = "Limite máximo de aulas por dia para usuários GymPass", example = "2")
    private Integer limiteDeAulasPorDia;

    public ConfigGymPassDTO() {
    }

    public ConfigGymPassDTO(ConfigGymPass config) {
        this.codigo = config.getCodigo();
        this.empresa = config.getEmpresa().getCodigo();
        this.nome = config.getEmpresa().getNome();
        this.codigoGymPass = config.getCodigoGymPass();
        this.usarGymPassBooking = config.isUsarGymPassBooking();
        this.limiteDeAcessosPorDia = config.getLimiteDeAcessosPorDia();
        this.limiteDeAulasPorDia = config.getLimiteDeAulasPorDia();
        this.permitirWod = config.getPermitirWod();
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getCodigoGymPass() {
        return codigoGymPass;
    }

    public void setCodigoGymPass(String codigoGymPass) {
        this.codigoGymPass = codigoGymPass;
    }

    public boolean isUsarGymPassBooking() {
        return usarGymPassBooking;
    }

    public void setUsarGymPassBooking(boolean usarGymPassBooking) {
        this.usarGymPassBooking = usarGymPassBooking;
    }

    public Integer getLimiteDeAcessosPorDia() {
        return limiteDeAcessosPorDia;
    }

    public void setLimiteDeAcessosPorDia(Integer limiteDeAcessosPorDia) {
        this.limiteDeAcessosPorDia = limiteDeAcessosPorDia;
    }

    public Integer getLimiteDeAulasPorDia() {
        return limiteDeAulasPorDia;
    }

    public void setLimiteDeAulasPorDia(Integer limiteDeAulasPorDia) {
        this.limiteDeAulasPorDia = limiteDeAulasPorDia;
    }

    public boolean isPermitirWod() {
        return permitirWod;
    }

    public void setPermitirWod(boolean permitirWod) {
        this.permitirWod = permitirWod;
    }
}
