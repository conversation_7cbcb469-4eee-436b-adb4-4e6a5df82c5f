package br.com.pacto.controller.json.agendamento;

import br.com.pacto.bean.Agendamento;
import br.com.pacto.bean.colaborador.ColaboradorSimplesTO;
import br.com.pacto.controller.json.tipoEvento.TipoAgendamentoDTO;
import br.com.pacto.enumerador.agenda.StatusAgendamentoEnum;
import br.com.pacto.objeto.Calendario;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Serviço de agendamento personalizado com informações completas do agendamento")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ServicoAgendamentoPersonalDTO {

    @ApiModelProperty(value = "Código identificador único do agendamento", example = "1001")
    private Integer id;

    @ApiModelProperty(value = "Data do agendamento no formato dd/MM/yyyy", example = "15/01/2024")
    private String dia;

    @ApiModelProperty(value = "Status atual do agendamento. " +
                             "<br/><strong>Valores disponíveis:</strong>" +
                             "<ul>" +
                             "<li>AGUARDANDO_CONFIRMACAO - Aguardando confirmação</li>" +
                             "<li>CONFIRMADO - Confirmado</li>" +
                             "<li>EXECUTADO - Executado</li>" +
                             "<li>CANCELADO - Cancelado</li>" +
                             "<li>FALTOU - Faltou</li>" +
                             "<li>REAGENDADO - Reagendado</li>" +
                             "</ul>",
                             example = "CONFIRMADO")
    private StatusAgendamentoEnum status;

    @ApiModelProperty(value = "Detalhes do tipo de agendamento/serviço")
    private TipoAgendamentoDTO tipoAgendamento;

    @ApiModelProperty(value = "Horário de início do agendamento no formato HH:mm", example = "14:00")
    private String horarioInicial;

    @ApiModelProperty(value = "Horário de término do agendamento no formato HH:mm", example = "15:00")
    private String horarioFinal;

    @ApiModelProperty(value = "Informações do professor responsável pelo atendimento")
    private ColaboradorSimplesTO professor;

    @ApiModelProperty(value = "Informações básicas do aluno agendado")
    private AlunoAgendamentoDTO aluno;

    @ApiModelProperty(value = "Observações adicionais sobre o agendamento",
                      example = "Primeira avaliação física do aluno")
    private String observacao;

    @ApiModelProperty(value = "Código da disponibilidade de horário associada, quando aplicável", example = "501")
    private Integer horarioDisponibilidadeCod;

    public ServicoAgendamentoPersonalDTO(Agendamento agendamento, Boolean treinoIndependente) {
        this.id = agendamento.getCodigo();
        this.dia = Calendario.getData(agendamento.getInicio(), "dd/MM/yyyy");
        this.status = agendamento.getStatus();
        if (agendamento.getTipoEvento() != null) {
            this.tipoAgendamento = new TipoAgendamentoDTO(agendamento.getTipoEvento());
        } else {
            this.tipoAgendamento = new TipoAgendamentoDTO(agendamento.getHorarioDisponibilidade().getDisponibilidade());
            this.tipoAgendamento.setPermitir_app(agendamento.getHorarioDisponibilidade().getPermieAgendarAppTreino());
            this.tipoAgendamento.setSomente_carteira_professor(agendamento.getHorarioDisponibilidade().getApenasAlunosCarteira());
            this.horarioDisponibilidadeCod = agendamento.getHorarioDisponibilidade().getCodigo();
        }
        this.horarioInicial = agendamento.getHoraInicioApresentar();
        this.horarioFinal = agendamento.getHoraFimApresentar();
        this.professor = new ColaboradorSimplesTO(agendamento.getProfessor(), treinoIndependente);
        this.aluno = new AlunoAgendamentoDTO(agendamento.getCliente(), treinoIndependente);
        this.observacao = agendamento.getObservacao();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getDia() {
        return dia;
    }

    public void setDia(String dia) {
        this.dia = dia;
    }

    public StatusAgendamentoEnum getStatus() {
        return status;
    }

    public void setStatus(StatusAgendamentoEnum status) {
        this.status = status;
    }

    public TipoAgendamentoDTO getTipoAgendamento() {
        return tipoAgendamento;
    }

    public void setTipoAgendamento(TipoAgendamentoDTO tipoAgendamento) {
        this.tipoAgendamento = tipoAgendamento;
    }

    public String getHorarioInicial() {
        return horarioInicial;
    }

    public void setHorarioInicial(String horarioInicial) {
        this.horarioInicial = horarioInicial;
    }

    public String getHorarioFinal() {
        return horarioFinal;
    }

    public void setHorarioFinal(String horarioFinal) {
        this.horarioFinal = horarioFinal;
    }

    public ColaboradorSimplesTO getProfessor() {
        return professor;
    }

    public void setProfessor(ColaboradorSimplesTO professor) {
        this.professor = professor;
    }

    public AlunoAgendamentoDTO getAluno() {
        return aluno;
    }

    public void setAluno(AlunoAgendamentoDTO aluno) {
        this.aluno = aluno;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public Integer getHorarioDisponibilidadeCod() {
        return horarioDisponibilidadeCod;
    }

    public void setHorarioDisponibilidadeCod(Integer horarioDisponibilidadeCod) {
        this.horarioDisponibilidadeCod = horarioDisponibilidadeCod;
    }
}
