package br.com.pacto.controller.json.wod;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.bean.wod.FiltroWodJSON;
import br.com.pacto.bean.wod.WodTO;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.wod.WodService;
import br.com.pacto.swagger.respostas.variaveis.ExemploRespostaBoolean;
import br.com.pacto.swagger.respostas.wod.*;
import io.swagger.annotations.*;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by ulisses on 28/08/2018.
 */
@Controller
@RequestMapping("/psec/wods")
public class WodController {

    private final WodService wodService;

    @Autowired
    public WodController(WodService wodService) {
        Assert.notNull(wodService, "O serviço de wod não foi injetado corretamente");
        this.wodService = wodService;
    }

    @ApiOperation(value = "Obter lista de WODs (Workout of the Day) com filtros e paginação",
            tags = "WOD",
            notes = "Permite consultar WODs (Workout of the Day) da empresa com filtros de busca por nome e unidade. " +
                    "Requer permissão para gerenciar WODs e identificação da empresa. Retorna resultados paginados para facilitar a navegação."
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.<br/> Ordena por um atributo contido no objeto da resposta." +
                    "<strong>Ordens disponíveis</strong>" +
                    "<ul>" +
                    "<li>ASC - Ordena pelo atributo escolhido de forma ascendente</li>" +
                    "<li>DESC - Ordena pelo atributo escolhido de forma descendente</li>" +
                    "</ul>" +
                    "Deve ser informado como atributo,ordem", defaultValue = "nome,asc", paramType = "query", dataType = "string"),
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Lista de WODs obtida com sucesso", response = ExemploRespostaListWodResponseTOPaginacao.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.WOD)
    @RequestMapping(value = "", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterListaWods(
            @ApiParam(value = "Código identificador da empresa", required = true, example = "1")
            @RequestHeader(value = "empresaId") Integer empresaId,
            @ApiParam(value = "Filtros de busca, deve ser informado como um JSON.\n\n" +
                    "<strong>É necessário fazer o ENCODE na URL para realizar a requisição com os filtros.</strong>\n\n" +
                    "<strong>Filtros disponíveis:</strong>\n" +
                    "- <strong>quicksearchValue:</strong> Termo de busca para nome do WOD.\n" +
                    "- <strong>unidade:</strong> Filtra por uma ou mais unidades/empresas (Deve ser informado como uma lista de códigos/IDs ex: [1, 2]).",
                    defaultValue = "{\"quicksearchValue\":\"CrossFit\", \"quicksearchFields\":[\"nome\"], \"unidade\":[1]}")
            @RequestParam(value = "filters", required = false) JSONObject filtros,
            @ApiIgnore PaginadorDTO paginadorDTO) throws JSONException {
        try {
            FiltroWodJSON filtroWodJSON = new FiltroWodJSON(filtros);
            boolean restringirEmpresas = false;
            return ResponseEntityFactory.ok(wodService.obterListaWods(filtroWodJSON, paginadorDTO, empresaId, restringirEmpresas), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(WodController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar wods", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Listar todos os WODs (Workout of the Day) de uma data específica",
            tags = "WOD",
            notes = "Permite consultar todos os WODs (Workout of the Day) disponíveis para uma data específica. " +
                    "Requer permissão para gerenciar WODs. A identificação da empresa é opcional para esta consulta.")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Lista de WODs obtida com sucesso", response = ExemploRespostaListWodResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.WOD)
    @RequestMapping(value = "/all", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarTodosOsWods(
            @ApiParam(value = "Código identificador da empresa", example = "1")
            @RequestHeader(value = "empresaId", required = false) Integer empresaId,
            @ApiParam(value = "Data em timestamp para filtrar os WODs", example = "1640995200000")
            @RequestParam(value = "data", required = false) Long data,
            @RequestParam(value = "todasUnidades", required = false, defaultValue = "false") Boolean todasUnidades) {
        try {
            return ResponseEntityFactory.ok(wodService.listarTodosWods(Uteis.dataHoraZeradaUTC(data), empresaId, todasUnidades));
        } catch (ServiceException e) {
            Logger.getLogger(WodController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar wods", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar WOD (Workout of the Day) por ID com informações completas", tags = "WOD",
            notes = "Permite consultar informações detalhadas de um WOD (Workout of the Day) específico através do seu código identificador. " +
                    "Requer permissão para gerenciar WODs.")
    @ApiResponses({
            @ApiResponse(code = 200, message = "WOD encontrado com sucesso", response = ExemploRespostaWodResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.WOD)
    @RequestMapping(value = "/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> buscarWod(HttpServletRequest request,
                                                         @ApiParam(value = "Código identificador do WOD", required = true, example = "1")
                                                         @PathVariable("id") final Integer id) {
        try {
            return ResponseEntityFactory.ok(wodService.buscarWod(id, request));
        } catch (ServiceException e) {
            Logger.getLogger(WodController.class.getName()).log(Level.SEVERE, "Erro ao tentar buscar wod", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Importar WOD (Workout of the Day) do site oficial do CrossFit para uma data específica", tags = "WOD",
            notes = "Permite importar automaticamente o WOD (Workout of the Day) oficial do CrossFit para uma data específica. " +
                    "Requer permissão para gerenciar WODs. Retorna o status da importação realizada."
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "WOD importado com sucesso", response = ExemploRespostaStringImportacaoWod.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.WOD)
    @RequestMapping(value = "/wods-importados-crossfit", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> importarWodCrossfit(
            @ApiParam(value = "Data em timestamp para importar o WOD do CrossFit", required = true, example = "1640995200000")
            @RequestParam("filtroDia") Long filtroDia) {
        try {
            return ResponseEntityFactory.ok(wodService.importarWodCrossfit(Uteis.dataHoraZeradaUTC(filtroDia)));
        } catch (ServiceException e) {
            Logger.getLogger(WodController.class.getName()).log(Level.SEVERE, "Erro ao tentar importar wod crossfit", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Cadastrar novo WOD (Workout of the Day)",
            tags = "WOD",
            notes = "Permite criar um novo WOD (Workout of the Day) personalizado para a empresa. " +
                    "Requer permissão para gerenciar WODs e identificação da empresa. Valida dados obrigatórios e evita duplicações."
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "WOD cadastrado com sucesso", response = ExemploRespostaWodResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.WOD)
    @RequestMapping(value = "", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cadastrarWod(
            @ApiParam(value = "Código identificador da empresa", required = true, example = "1")
            @RequestHeader(value = "empresaId", required = true) Integer empresaId,
            @ApiParam(value = "Dados do WOD a ser cadastrado", required = true)
            @RequestBody WodTO wodTO, HttpServletRequest request
    ) {
        try {
            return ResponseEntityFactory.ok(wodService.cadastrarWod(wodTO, empresaId, request));
        } catch (ServiceException e) {
            Logger.getLogger(WodController.class.getName()).log(Level.SEVERE, "Erro ao tentar cadastrar wod", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ApiOperation(
            value = "Alterar WOD (Workout of the Day) existente",
            tags = "WOD",
            notes = "Permite modificar informações de um WOD (Workout of the Day) já cadastrado. " +
                    "Requer permissão para gerenciar WODs e identificação da empresa. Valida a existência do WOD e dados obrigatórios."
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "WOD alterado com sucesso", response = ExemploRespostaWodResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.WOD)
    @RequestMapping(value = "/{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alterarWod(
            @ApiParam(value = "Código identificador do WOD", required = true, example = "1")
            @PathVariable("id") final Integer id,
            @ApiParam(value = "Código identificador da empresa", required = true, example = "1")
            @RequestHeader(value = "empresaId") Integer empresaId,
            @ApiParam(value = "Dados do WOD a ser alterado", required = true)
            @RequestBody WodTO wodTO,
            HttpServletRequest request) {
        try {
            return ResponseEntityFactory.ok(wodService.alterarWod(wodTO, id, empresaId, request));
        } catch (ServiceException e) {
            Logger.getLogger(WodController.class.getName()).log(Level.SEVERE, "Erro ao tentar alterar wod", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ApiOperation(
            value = "Remover WOD (Workout of the Day)", tags = "WOD",
            notes = "Permite excluir um WOD (Workout of the Day) do sistema. " +
                    "Requer permissão para gerenciar WODs. Valida a existência do WOD antes de realizar a exclusão."
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "WOD removido com sucesso", response = ExemploRespostaBoolean.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.WOD)
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> removerWod(
            @ApiParam(value = "Código identificador do WOD", required = true, example = "1")
            @PathVariable("id") final Integer id) {
        try {
            wodService.removerWod(id);
            return ResponseEntityFactory.ok(true);
        } catch (ServiceException e) {
            Logger.getLogger(WodController.class.getName()).log(Level.SEVERE, "Erro ao tentar alterar wod", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Importar WODs (Workout of the Day) da franqueadora", tags = "WOD",
            notes = "Permite importar WODs disponibilizados pela franqueadora para a empresa. " +
                    "Requer permissão para gerenciar WODs e identificação da empresa. Retorna o status da importação realizada.")
    @ApiResponses({
            @ApiResponse(code = 200, message = "WODs importados da franqueadora com sucesso", response = ExemploRespostaStringImportacaoFranqueadora.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.WOD)
    @RequestMapping(value = "/importar-wods-da-franqueadora", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> importarWodsDaFranqueadora(
            @ApiParam(value = "Código identificador da empresa", required = true, example = "1")
            @RequestHeader(value = "empresaId") Integer empresaId) {
        try {
            return ResponseEntityFactory.ok(wodService.importarWodsDaFranqueadora(empresaId));
        } catch (ServiceException e) {
            Logger.getLogger(WodController.class.getName()).log(Level.SEVERE, "Erro ao tentar importar os wods da franqueadora", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

}
