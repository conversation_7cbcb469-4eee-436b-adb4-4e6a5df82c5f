package br.com.pacto.controller.json.agendamento;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;


@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@ApiModel(description = "Informações da disponibilidade de horários para agendamentos")
public class DisponibilidadeDTO {
    @ApiModelProperty(value = "Data inicial da disponibilidade de horário para aulas (Formato: timestamp)", example = "1749061200")
    private Long dataInicial;
    @ApiModelProperty(value = "Data final da disponibilidade de horário para aulas (Formato: timestamp)", example = "1749067200")
    private Long dataFinal;
    @ApiModelProperty(value = "Data inicial da disponibilidade formatada (Formato: yyyyMMdd)", example = "20250604")
    private String dataInicialFormatada;
    @ApiModelProperty(value = "Data final da disponibilidade formatada (Formato: yyyyMMdd)", example = "20250604")
    private String dataFinalFormatada;
    @ApiModelProperty(value = "Dias da semana para a disponibilidade de horário<br/> <strong>Valores disponíveis</strong>" +
            "<ul>" +
            "<li>0 - DOMINGO</li>" +
            "<li>1 - SEGUNDA</li>" +
            "<li>2 - TERCA</li>" +
            "<li>3 - QUARTA</li>" +
            "<li>4 - QUINTA</li>" +
            "<li>5 - SEXTA</li>" +
            "<li>6 - SABADO</li>" +
            "</ul>", example = "[\"SEGUNDA\", \"TERÇA\"]", allowableValues = "DOMINGO,SEGUNDA,TERCA,QUARTA,QUINTA,SEXTA,SABADO")
    private List<AgendaDiaSemana> diasSemana;
    @ApiModelProperty(value = "Horário inicial da disponibilidade", example = "19:00")
    private String horarioInicial;
    @ApiModelProperty(value = "Horário final da disponibilidade", example = "20:00")
    private String horarioFinal;
    @ApiModelProperty(value = "Lista de códigos dos professores disponíveis para o horário", example = "[1,2,3,4]")
    private List<Integer> professores;
    @ApiModelProperty(value = "Código dos tipos disponíveis para agendamento", example = "[1,2,4]")
    private List<Integer> tipos;

    public String getDataInicialFormatada() {
        return dataInicialFormatada;
    }

    public void setDataInicialFormatada(String dataInicialFormatada) {
        this.dataInicialFormatada = dataInicialFormatada;
    }

    public String getDataFinalFormatada() {
        return dataFinalFormatada;
    }

    public void setDataFinalFormatada(String dataFinalFormatada) {
        this.dataFinalFormatada = dataFinalFormatada;
    }

    public Long getDataInicial() {
        return dataInicial;
    }

    public void setDataInicial(Long dataInicial) {
        this.dataInicial = dataInicial;
    }

    public Long getDataFinal() {
        return dataFinal;
    }

    public void setDataFinal(Long dataFinal) {
        this.dataFinal = dataFinal;
    }

    public List<AgendaDiaSemana> getDiasSemana() {
        return diasSemana;
    }

    public void setDiasSemana(List<AgendaDiaSemana> diasSemana) {
        this.diasSemana = diasSemana;
    }

    public String getHorarioInicial() {
        return horarioInicial;
    }

    public void setHorarioInicial(String horarioInicial) {
        this.horarioInicial = horarioInicial;
    }

    public String getHorarioFinal() {
        return horarioFinal;
    }

    public void setHorarioFinal(String horarioFinal) {
        this.horarioFinal = horarioFinal;
    }

    public List<Integer> getProfessores() {
        return professores;
    }

    public void setProfessores(List<Integer> professores) {
        this.professores = professores;
    }

    public List<Integer> getTipos() {
        return tipos;
    }

    public void setTipos(List<Integer> tipos) {
        this.tipos = tipos;
    }
}
