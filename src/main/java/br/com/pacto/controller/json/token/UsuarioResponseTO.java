package br.com.pacto.controller.json.token;

import br.com.pacto.bean.perfil.Perfil;
import br.com.pacto.bean.programa.ProfessorResponseTO;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.objeto.Uteis;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by <PERSON><PERSON> on 28/09/2018.
 */
@ApiModel(description = "DTO para representar os dados do usuário autenticado")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UsuarioResponseTO {

    @ApiModelProperty(value = "Identificador único do usuário no sistema", example = "1001")
    private Integer id;

    @ApiModelProperty(value = "Nome de usuário para login", example = "joao.silva")
    private String username;

    @ApiModelProperty(value = "Nome completo do usuário", example = "<PERSON>")
    private String nome;

    @ApiModelProperty(value = "URL da imagem de perfil do usuário", example = "https://exemplo.com/fotos/joao.jpg")
    private String imageUri;

    @ApiModelProperty(value = "Lista dos perfis de acesso do usuário", example = "[\"Administrador\"]")
    private List<String> perfis;

    @ApiModelProperty(value = "Dados detalhados do professor/colaborador associado ao usuário")
    private ProfessorResponseTO professorResponse;

    @ApiModelProperty(value = "Identificador do usuário no sistema ZW (integração externa)", example = "5001")
    private Integer usuarioZw;

    public UsuarioResponseTO(Usuario usuario, boolean treinoIndependente) {
        this.id = usuario.getCodigo();
        this.username = usuario.getUserName();
        this.nome = usuario.getProfessor().getNome();
        this.imageUri = usuario.getProfessor().getUriImagem();
        Uteis.logar(null, "Perfil do usuário " + usuario.getPerfil());
        this.perfis = new ArrayList<>();
        perfis.add(usuario.getPerfil().getNome());
        this.professorResponse = new ProfessorResponseTO(usuario,treinoIndependente);
        this.usuarioZw = usuario.getUsuarioZW();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getImageUri() {
        return imageUri;
    }

    public void setImageUri(String imageUri) {
        this.imageUri = imageUri;
    }

    public List<String> getPerfis() {
        return perfis;
    }

    public void setPerfis(List<String> perfis) {
        this.perfis = perfis;
    }

    public ProfessorResponseTO getProfessorResponse() {
        return professorResponse;
    }

    public void setProfessorResponse(ProfessorResponseTO professorResponse) {
        this.professorResponse = professorResponse;
    }

    public Integer getUsuarioZw() {
        return usuarioZw;
    }

    public void setUsuarioZw(Integer usuarioZw) {
        this.usuarioZw = usuarioZw;
    }

}
