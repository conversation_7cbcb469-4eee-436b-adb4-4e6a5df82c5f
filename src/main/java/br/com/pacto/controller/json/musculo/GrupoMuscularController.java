package br.com.pacto.controller.json.musculo;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.musculo.GrupoMuscularTO;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.musculo.GrupoMuscularService;
import br.com.pacto.swagger.respostas.musculo.ExemploRespostaGrupoMuscularResponseTO;
import br.com.pacto.swagger.respostas.musculo.ExemploRespostaListGrupoMuscularResumidoResponseTOPaginacao;
import br.com.pacto.swagger.respostas.musculo.ExemploRespostaListGrupoMuscularSimplesResponseTO;
import br.com.pacto.swagger.respostas.variaveis.ExemploRespostaBoolean;
import io.swagger.annotations.*;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by ulisses on 03/08/2018.
 */
@Controller
@RequestMapping("/psec/grupos-musculares")
public class GrupoMuscularController {

    private final GrupoMuscularService grupoMuscularService;

    @Autowired
    public GrupoMuscularController(GrupoMuscularService grupoMuscularService) {
        Assert.notNull(grupoMuscularService, "O serviço de grupo muscular não foi injetado corretamente");
        this.grupoMuscularService = grupoMuscularService;
    }

    @ApiOperation(value = "Consultar grupos musculares com paginação e filtros",
            notes = "Consulta grupos musculares com suporte a paginação e filtros de busca. " +
                    "Requer permissão de acesso aos grupos musculares. " +
                    "Retorna dados resumidos dos grupos musculares encontrados.",
            tags = "Atividades")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.<br/> Ordena por um atributo contido no objeto da resposta." +
                    "<strong>Ordens disponíveis</strong>" +
                    "<ul>" +
                    "<li>ASC - Ordena pelo atributo escolhido de forma ascendente</li>" +
                    "<li>DESC - Ordena pelo atributo escolhido de forma descendente</li>" +
                    "</ul>" +
                    "Deve ser informado como atributo,ordem", defaultValue = "nome,asc", paramType = "query", dataType = "string"),
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Consulta realizada com sucesso", response = ExemploRespostaListGrupoMuscularResumidoResponseTOPaginacao.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.GRUPOS_MUSCULARES)
    @RequestMapping(value = "", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarGruposMusculares(@ApiParam(value = "Filtros de busca, deve ser informado como um JSON.\n\n" +
                                                                                 "<strong>É necessário fazer o ENCODE na URL para realizar a requisição com os filtros.</strong>\n\n" +
                                                                                 "<strong>Filtros disponíveis:</strong>\n" +
                                                                                 "- <strong>quicksearchValue:</strong> Termo de busca para nome do grupo muscular.\n" +
                                                                                 "- <strong>quicksearchFields:</strong> Define em qual campo o 'quicksearchValue' será aplicado (Deve ser informado como uma lista ex: [\"nome\"]).",
                                                                                 defaultValue = "{\"quicksearchValue\":\"Membros\", \"quicksearchFields\":[\"nome\"]}")
                                                                         @RequestParam(value = "filters", required = false) JSONObject filtros,
                                                                         @ApiIgnore PaginadorDTO paginadorDTO) throws JSONException {
        try {
            FiltroGrupoMuscularJSON filtroGrupoMuscularJSON = new FiltroGrupoMuscularJSON(filtros);
            return ResponseEntityFactory.ok(grupoMuscularService.consultarGruposMusculares(filtroGrupoMuscularJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(GrupoMuscularController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os grupos musculares", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(value = "Consultar todos os grupos musculares",
            notes = "Consulta todos os grupos musculares cadastrados no sistema sem paginação. " +
                    "Requer permissão de acesso aos grupos musculares. " +
                    "Retorna dados simplificados de todos os grupos musculares.",
            tags = "Atividades")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Consulta realizada com sucesso", response = ExemploRespostaListGrupoMuscularSimplesResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.GRUPOS_MUSCULARES)
    @RequestMapping(value = "all", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarTodosGruposMusculares() {
        try {
            return ResponseEntityFactory.ok(grupoMuscularService.consultarTodosGruposMusculares());
        } catch (ServiceException e) {
            Logger.getLogger(GrupoMuscularController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os grupos musculares", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(value = "Consultar grupo muscular por ID",
            notes = "Consulta um grupo muscular específico pelo seu identificador único. " +
                    "Requer permissão de acesso aos grupos musculares. " +
                    "Retorna dados completos do grupo muscular incluindo músculos, atividades e perimetrias associadas.",
            tags = "Atividades")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Grupo muscular encontrado com sucesso", response = ExemploRespostaGrupoMuscularResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.GRUPOS_MUSCULARES)
    @RequestMapping(value = "/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarGrupoMuscular(@ApiParam(value = "Identificador único do grupo muscular", required = true, example = "1")
                                                                      @PathVariable("id") Integer id) {
        try {
            return ResponseEntityFactory.ok(grupoMuscularService.consultarGrupoMuscular(id));
        } catch (ServiceException e) {
            Logger.getLogger(GrupoMuscularController.class.getName()).log(Level.SEVERE, "Erro ao tentar consultar o grupo muscular", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(value = "Incluir novo grupo muscular",
            notes = "Cadastra um novo grupo muscular no sistema. " +
                    "Requer permissão de acesso aos grupos musculares. " +
                    "O nome do grupo muscular deve ser único no sistema. " +
                    "Retorna os dados completos do grupo muscular criado.",
            tags = "Atividades")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Grupo muscular incluído com sucesso", response = ExemploRespostaGrupoMuscularResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.GRUPOS_MUSCULARES)
    @RequestMapping(value = "", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> incluirGrupoMuscular(@ApiParam(value = "Dados do grupo muscular a ser incluído", required = true)
                                                                    @RequestBody GrupoMuscularTO grupoMuscularTO) {
        try {
            return ResponseEntityFactory.ok(grupoMuscularService.inserir(grupoMuscularTO));
        } catch (ServiceException e) {
            Logger.getLogger(GrupoMuscularController.class.getName()).log(Level.SEVERE, "Erro ao tentar incluir Grupo Muscular", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ApiOperation(value = "Alterar grupo muscular existente",
            notes = "Altera os dados de um grupo muscular existente no sistema. " +
                    "Requer permissão de acesso aos grupos musculares. " +
                    "O nome do grupo muscular deve ser único no sistema. " +
                    "Retorna os dados completos do grupo muscular alterado.",
            tags = "Atividades")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Grupo muscular alterado com sucesso", response = ExemploRespostaGrupoMuscularResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.GRUPOS_MUSCULARES)
    @RequestMapping(value = "/{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alterarGrupoMuscular(@ApiParam(value = "Identificador único do grupo muscular a ser alterado", required = true, example = "1")
                                                                    @PathVariable("id") final Integer id,
                                                                    @ApiParam(value = "Dados do grupo muscular a ser alterado", required = true)
                                                                    @RequestBody GrupoMuscularTO grupoMuscularTO) {
        try {
            return ResponseEntityFactory.ok(grupoMuscularService.alterar(id, grupoMuscularTO));

        } catch (ServiceException e) {
            Logger.getLogger(GrupoMuscularController.class.getName()).log(Level.SEVERE, "Erro ao tentar alterar grupo muscular", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }


    @ApiOperation(value = "Excluir grupo muscular",
            notes = "Exclui um grupo muscular do sistema pelo seu identificador único. " +
                    "Requer permissão de acesso aos grupos musculares. " +
                    "A exclusão remove também todas as associações com músculos e atividades. " +
                    "Retorna confirmação da exclusão.",
            tags = "Atividades")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Grupo muscular excluído com sucesso", response = ExemploRespostaBoolean.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.GRUPOS_MUSCULARES)
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> excluirGrupoMuscular(@ApiParam(value = "Identificador único do grupo muscular a ser excluído", required = true, example = "1")
                                                                    @PathVariable("id") final Integer id) {
        try {
            grupoMuscularService.excluir(id);
            return ResponseEntityFactory.ok(true);
        } catch (ServiceException e) {
            Logger.getLogger(GrupoMuscularController.class.getName()).log(Level.SEVERE, "Erro ao tentar excluir grupo muscular", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


}
