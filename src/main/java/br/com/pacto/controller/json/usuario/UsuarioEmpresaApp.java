package br.com.pacto.controller.json.usuario;

import servicos.integracao.adm.client.EmpresaWS;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Dados da empresa associada ao usuário no aplicativo")
public class UsuarioEmpresaApp extends EmpresaWS {

    public UsuarioEmpresaApp(EmpresaWS e) {
        this.alterarDataHoraCheckGestaoPersonal = e.isAlterarDataHoraCheckGestaoPersonal();
        this.bloquearAcessoPersonalSemCredito = e.isBloquearAcessoPersonalSemCredito();
        this.bloqueioTemporario = e.isBloqueioTemporario();
        this.bvObrigatorio = e.isBvObrigatorio();
        this.cep = e.getCEP();
        this.chaveNFSe = e.getChaveNFSe();
        this.cidade = e.getCidade();
        this.cnpj = e.getCnpj();
        this.codigo = e.getCodigo();
        this.codigoColaborador = e.getCodigoColaborador();
        this.codigoFinanceiro = e.getCodigoFinanceiro();
        this.complemento = e.getComplemento();
        this.consumirCreditoPorAlunoVinculado = e.isConsumirCreditoPorAlunoVinculado();
        this.descricaoPerfil = e.getDescricaoPerfil();
        this.diasBloqueioParcelaEmAberto = e.getDiasBloqueioParcelaEmAberto();
        this.duracaoCredito = e.getDuracaoCredito();
        this.email = e.getEmail();
        this.empresaSuspensa = e.isEmpresaSuspensa();
        this.endereco = e.getEndereco();
        this.estado = e.getEstado();
        this.idExterno = e.getIdExterno();
        this.integracaoSpiviHabilitada = e.isIntegracaoSpiviHabilitada();
        this.integracaoSpiviPassword = e.getIntegracaoSpiviPassword();
        this.integracaoSpiviSiteID = e.getIntegracaoSpiviSiteID();
        this.integracaoSpiviSourceName = e.getIntegracaoSpiviSourceName();
        this.latitude = e.getLatitude();
        this.longitude = e.getLongitude();
        this.mostrarFotosAlunosMonitor = e.isMostrarFotosAlunosMonitor();
        this.nome = e.getNome();
        this.numero = e.getNumero();
        this.obrigatorioAssociarAlunoAoCheckIn = e.isObrigatorioAssociarAlunoAoCheckIn();
        this.pais = e.getPais();
        this.setor = e.getSetor();
        this.site = e.getSite();
        this.telefone = e.getTelefone();
        this.tempoCheckOutAutomatica = e.getTempoCheckOutAutomatica();
        this.timeZoneDefault = e.getTimeZoneDefault();
        this.tokenSMS = e.getTokenSMS();
        this.tokenShortcode = e.getTokenShortcode();
        this.totalDiasExtras = e.getTotalDiasExtras();
        this.usarENotas = e.isUsarENotas();
        this.usarFotoPersonal = e.isUsarFotoPersonal();
        this.usarGestaoCreditosPersonal = e.isUsarGestaoCreditosPersonal();
        this.usarNFCe = e.isUsarNFCe();
        this.usarNFSe = e.isUsarNFSe();
    }

    @ApiModelProperty(value = "Código do professor associado à empresa", example = "456")
    private Integer codigoProfessor;

    public Integer getCodigoProfessor() {
        return codigoProfessor;
    }

    public void setCodigoProfessor(Integer codigoProfessor) {
        this.codigoProfessor = codigoProfessor;
    }
}
