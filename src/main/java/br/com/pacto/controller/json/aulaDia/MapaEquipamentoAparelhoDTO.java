package br.com.pacto.controller.json.aulaDia;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Representa o mapeamento de equipamentos e aparelhos em uma aula
 * <AUTHOR> Pacto
 */
@ApiModel(description = "Mapeamento detalhado de equipamentos e aparelhos disponíveis em uma aula")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MapaEquipamentoAparelhoDTO {

    @ApiModelProperty(value = "Posição do equipamento no mapa da academia", example = "A1")
    private String posicaoMapa;

    @ApiModelProperty(value = "Número sequencial da posição do equipamento", example = "1")
    private Integer posicaoNumeroSequencial;

    @ApiModelProperty(value = "Código identificador único do aparelho", example = "101")
    private Integer codigoAparelho;

    @ApiModelProperty(value = "Nome completo do aparelho", example = "Esteira Ergométrica")
    private String nomeAparelho;

    @ApiModelProperty(value = "Sigla do aparelho", example = "EST")
    private String siglaAparelho;

    @ApiModelProperty(value = "Ícone representativo do aparelho", example = "icone_esteira.png")
    private String iconeAparelho;

    @ApiModelProperty(value = "Indica se o equipamento está ocupado", example = "false")
    private Boolean ocupado;

    public MapaEquipamentoAparelhoDTO() { }

    public MapaEquipamentoAparelhoDTO(String posicaoMapa) {
        this.posicaoMapa = posicaoMapa;
    }

    public MapaEquipamentoAparelhoDTO(String posicaoMapa,Integer posicaoNumeroSequencial, Integer codigoAparelho, String nomeAparelho, String siglaAparelho, String iconeAparelho, Boolean ocupado) {
        this.posicaoMapa = posicaoMapa;
        this.posicaoNumeroSequencial = posicaoNumeroSequencial;
        this.codigoAparelho = codigoAparelho;
        this.nomeAparelho = nomeAparelho;
        this.siglaAparelho = siglaAparelho;
        this.iconeAparelho = iconeAparelho;
        this.ocupado = ocupado;
    }

    public String getPosicaoMapa() {
        return posicaoMapa;
    }

    public void setPosicaoMapa(String posicaoMapa) {
        this.posicaoMapa = posicaoMapa;
    }

    public Integer getPosicaoNumeroSequencial() {
        return posicaoNumeroSequencial;
    }

    public void setPosicaoNumeroSequencial(Integer posicaoNumeroSequencial) {
        this.posicaoNumeroSequencial = posicaoNumeroSequencial;
    }

    public Integer getCodigoAparelho() {
        return codigoAparelho;
    }

    public void setCodigoAparelho(Integer codigoAparelho) {
        this.codigoAparelho = codigoAparelho;
    }

    public String getNomeAparelho() {
        return nomeAparelho;
    }

    public void setNomeAparelho(String nomeAparelho) {
        this.nomeAparelho = nomeAparelho;
    }

    public String getSiglaAparelho() {
        return siglaAparelho;
    }

    public void setSiglaAparelho(String siglaAparelho) {
        this.siglaAparelho = siglaAparelho;
    }

    public String getIconeAparelho() {
        return iconeAparelho;
    }

    public void setIconeAparelho(String iconeAparelho) {
        this.iconeAparelho = iconeAparelho;
    }

    public Boolean getOcupado() {
        return ocupado;
    }

    public void setOcupado(Boolean ocupado) {
        this.ocupado = ocupado;
    }

}
