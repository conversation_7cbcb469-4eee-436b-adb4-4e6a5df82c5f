package br.com.pacto.controller.json.agendamento;

import br.com.pacto.enumerador.agenda.StatusAgendamentoEnum;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * created to paulo 31/10/2018
 */
@ApiModel(description = "DTO para criação e alteração de agendamentos personalizados")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AgendamentoPersonalDTO {

    @ApiModelProperty(value = "Data do agendamento no formato dd/MM/yyyy",
                      required = true, example = "15/01/2024")
    private String dia; //No formato dd/MM/yyyy

    @ApiModelProperty(value = "Horário de início do agendamento no formato HH:mm",
                      required = true, example = "14:00")
    private String horarioInicial;

    @ApiModelProperty(value = "Horário de término do agendamento no formato HH:mm",
                      required = true, example = "15:00")
    private String horarioFinal;

    @ApiModelProperty(value = "Código identificador do tipo de agendamento/serviço",
                      required = true, example = "4")
    private Integer tipoAgendamentoId;

    @ApiModelProperty(value = "Código identificador do professor responsável pelo atendimento",
                      required = true, example = "25")
    private Integer professorId;

    @ApiModelProperty(value = "Número da matrícula do aluno que será atendido",
                      required = true, example = "12345")
    private Integer matricula;

    @ApiModelProperty(value = "Status atual do agendamento. " +
                             "<br/><strong>Valores disponíveis:</strong>" +
                             "<ul>" +
                             "<li>AGUARDANDO_CONFIRMACAO - Aguardando confirmação</li>" +
                             "<li>CONFIRMADO - Confirmado</li>" +
                             "<li>EXECUTADO - Executado</li>" +
                             "<li>CANCELADO - Cancelado</li>" +
                             "<li>FALTOU - Faltou</li>" +
                             "<li>REAGENDADO - Reagendado</li>" +
                             "</ul>",
                      example = "AGUARDANDO_CONFIRMACAO")
    private StatusAgendamentoEnum status;

    @ApiModelProperty(value = "Observações adicionais sobre o agendamento",
                      example = "Primeira avaliação física do aluno")
    private String observacao;

    @ApiModelProperty(value = "Código identificador da empresa",
                      required = true, example = "1")
    private Integer empresa;

    @ApiModelProperty(value = "Indica se é um tipo de evento (true) ou disponibilidade (false)",
                      example = "true")
    private Boolean tipoEvento;

    @ApiModelProperty(value = "Código da disponibilidade de horário, quando aplicável",
                      example = "501")
    private Integer horarioDisponibilidadeCod;

    public String getDia() {
        return dia;
    }

    public void setDia(String dia) {
        this.dia = dia;
    }

    public String getHorarioInicial() {
        return horarioInicial;
    }

    public void setHorarioInicial(String horarioInicial) {
        this.horarioInicial = horarioInicial;
    }

    public String getHorarioFinal() {
        return horarioFinal;
    }

    public void setHorarioFinal(String horarioFinal) {
        this.horarioFinal = horarioFinal;
    }

    public Integer getTipoAgendamentoId() {
        return tipoAgendamentoId;
    }

    public void setTipoAgendamentoId(Integer tipoAgendamentoId) {
        this.tipoAgendamentoId = tipoAgendamentoId;
    }

    public Integer getProfessorId() {
        return professorId;
    }

    public void setProfessorId(Integer professorId) {
        this.professorId = professorId;
    }

    public Integer getMatricula() {
        return matricula;
    }

    public void setMatricula(Integer matricula) {
        this.matricula = matricula;
    }

    public StatusAgendamentoEnum getStatus() {
        return status;
    }

    public void setStatus(StatusAgendamentoEnum status) {
        this.status = status;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public Boolean getTipoEvento() {
        return tipoEvento;
    }

    public void setTipoEvento(Boolean tipoEvento) {
        this.tipoEvento = tipoEvento;
    }

    public Integer getHorarioDisponibilidadeCod() {
        return horarioDisponibilidadeCod;
    }

    public void setHorarioDisponibilidadeCod(Integer horarioDisponibilidadeCod) {
        this.horarioDisponibilidadeCod = horarioDisponibilidadeCod;
    }
}
