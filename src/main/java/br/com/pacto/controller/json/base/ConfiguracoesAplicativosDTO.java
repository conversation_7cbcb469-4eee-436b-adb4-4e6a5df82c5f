package br.com.pacto.controller.json.base;

import br.com.pacto.util.UteisValidacao;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "DTO para configurações de aplicativos móveis")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ConfiguracoesAplicativosDTO {

    @ApiModelProperty(value = "Define se o aplicativo móvel deve sempre atualizar a carga da ficha de treino", example = "true")
    private String mobile_sempre_atualizar_carga_ficha;

    @ApiModelProperty(value = "Define se permite reagendamento de aulas pelo próprio aluno", example = "true")
    private String permitir_reagendamento_por_aluno;

    @ApiModelProperty(value = "Define se o módulo de aulas está habilitado no aplicativo", example = "true")
    private String modulo_aulas;

    @ApiModelProperty(value = "Define se a aba de turmas está habilitada no módulo de aulas", example = "true")
    private String modulo_aulas_aba_turmas;

    @ApiModelProperty(value = "Define se a aba de saldo está habilitada no módulo de aulas", example = "true")
    private String modulo_aulas_aba_saldo;

    @ApiModelProperty(value = "Define se o módulo de treinar está habilitado no aplicativo", example = "true")
    private String modulo_treinar;

    @ApiModelProperty(value = "Define se o aluno pode marcar sua própria aula pelo aplicativo", example = "true")
    private String aluno_marcar_propria_aula;

    @ApiModelProperty(value = "Define se o módulo CrossFit está habilitado no aplicativo", example = "false")
    private String habilitar_crossfit;

    @ApiModelProperty(value = "Define se permite visualizar WODs de todas as empresas no aplicativo", example = "false")
    private String habilitar_ver_wod_todas_empresas_app;

    @ApiModelProperty(value = "Define se não deve exibir o número de vezes dos exercícios no aplicativo", example = "false")
    private String nao_exibir_numero_de_vezes_no_app;

    @ApiModelProperty(value = "Define se proíbe marcar aula quando há parcela vencida", example = "false")
    private String proibir_marcar_aula_parcela_vencida;

    @ApiModelProperty(value = "Define se proíbe buscar programa de treino quando há parcela vencida", example = "false")
    private String proibir_buscar_programa_parcela_vencida;

    @ApiModelProperty(value = "Nome do aplicativo para envio de e-mails", example = "Academia Pacto App")
    private String nome_aplicativo_para_envio_email;

    @ApiModelProperty(value = "Link do aplicativo para incluir nos e-mails", example = "https://play.google.com/store/apps/details?id=com.academia.app")
    private String link_app_email;

    @ApiModelProperty(value = "Número de dias para mostrar informações no totem", example = "7")
    private String dias_mostrar_totem;

    @ApiModelProperty(value = "Define se a fila de espera está habilitada", example = "true")
    private String habilitar_fila_espera;

    public Boolean getModulo_aulas() {
        if (!UteisValidacao.emptyString(modulo_aulas)) {
            return modulo_aulas.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setModulo_aulas(String modulo_aulas) {
        this.modulo_aulas = modulo_aulas;
    }


    public Boolean getModulo_aulas_aba_turmas() {
        if (!UteisValidacao.emptyString(modulo_aulas_aba_turmas)) {
            return modulo_aulas_aba_turmas.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setModulo_aulas_aba_turmas(String modulo_aulas_aba_turmas) {
        this.modulo_aulas_aba_turmas = modulo_aulas_aba_turmas;
    }

    public Boolean getModulo_aulas_aba_saldo() {
        if (!UteisValidacao.emptyString(modulo_aulas_aba_saldo)) {
            return modulo_aulas_aba_saldo.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setModulo_aulas_aba_saldo(String modulo_aulas_aba_saldo) {
        this.modulo_aulas_aba_saldo = modulo_aulas_aba_saldo;
    }

    public Boolean getPermitir_reagendamento_por_aluno() {
        if (!UteisValidacao.emptyString(permitir_reagendamento_por_aluno)) {
            return permitir_reagendamento_por_aluno.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setPermitir_reagendamento_por_aluno(String permitir_reagendamento_por_aluno) {
        this.permitir_reagendamento_por_aluno = permitir_reagendamento_por_aluno;
    }

    public Boolean getProibir_marcar_aula_parcela_vencida() {
        if (!UteisValidacao.emptyString(proibir_marcar_aula_parcela_vencida)) {
            return proibir_marcar_aula_parcela_vencida.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setProibir_marcar_aula_parcela_vencida(String proibir_marcar_aula_parcela_vencida) {
        this.proibir_marcar_aula_parcela_vencida = proibir_marcar_aula_parcela_vencida;
    }

    public Boolean getProibir_buscar_programa_parcela_vencida() {
        if (!UteisValidacao.emptyString(proibir_buscar_programa_parcela_vencida)) {
            return proibir_buscar_programa_parcela_vencida.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setProibir_buscar_programa_parcela_vencida(String proibir_buscar_programa_parcela_vencida) {
        this.proibir_buscar_programa_parcela_vencida = proibir_buscar_programa_parcela_vencida;
    }

    public String getNome_aplicativo_para_envio_email() {
        return nome_aplicativo_para_envio_email;
    }

    public void setNome_aplicativo_para_envio_email(String nome_aplicativo_para_envio_email) {
        this.nome_aplicativo_para_envio_email = nome_aplicativo_para_envio_email;
    }

    public Boolean getAluno_marcar_propria_aula() {
        if (!UteisValidacao.emptyString(aluno_marcar_propria_aula)) {
            return aluno_marcar_propria_aula.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setAluno_marcar_propria_aula(String aluno_marcar_propria_aula) {
        this.aluno_marcar_propria_aula = aluno_marcar_propria_aula;
    }

    public String getLink_app_email() {
        return link_app_email;
    }

    public void setLink_app_email(String link_app_email) {
        this.link_app_email = link_app_email;
    }

    public Boolean getMobile_sempre_atualizar_carga_ficha() {
        if (!UteisValidacao.emptyString(mobile_sempre_atualizar_carga_ficha)) {
            return mobile_sempre_atualizar_carga_ficha.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setMobile_sempre_atualizar_carga_ficha(String mobile_sempre_atualizar_carga_ficha) {
        this.mobile_sempre_atualizar_carga_ficha = mobile_sempre_atualizar_carga_ficha;
    }

    public Boolean getModulo_treinar() {
        if (!UteisValidacao.emptyString(modulo_treinar)) {
            return modulo_treinar.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setModulo_treinar(String modulo_treinar) {
        this.modulo_treinar = modulo_treinar;
    }

    public Boolean getHabilitar_crossfit() {
        if (!UteisValidacao.emptyString(habilitar_crossfit)) {
            return habilitar_crossfit.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setHabilitar_crossfit(String habilitar_crossfit) {
        this.habilitar_crossfit = habilitar_crossfit;
    }

    public Boolean getNao_exibir_numero_de_vezes_no_app() {
        if (!UteisValidacao.emptyString(nao_exibir_numero_de_vezes_no_app)) {
            return nao_exibir_numero_de_vezes_no_app.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setNao_exibir_numero_de_vezes_no_app(String nao_exibir_numero_de_vezes_no_app) {
        this.nao_exibir_numero_de_vezes_no_app = nao_exibir_numero_de_vezes_no_app;
    }

    public String getDias_mostrar_totem() {
        return dias_mostrar_totem;
    }

    public void setDias_mostrar_totem(String dias_mostrar_totem) {
        this.dias_mostrar_totem = dias_mostrar_totem;
    }


    public Boolean getHabilitar_fila_espera() {
        if (!UteisValidacao.emptyString(habilitar_fila_espera)) {
            return habilitar_fila_espera.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setHabilitar_fila_espera(String habilitar_fila_espera) {
        this.habilitar_fila_espera = habilitar_fila_espera;
    }

    public Boolean getHabilitar_ver_wod_todas_empresas_app() {
        if (!UteisValidacao.emptyString(habilitar_ver_wod_todas_empresas_app)) {
            return habilitar_ver_wod_todas_empresas_app.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setHabilitar_ver_wod_todas_empresas_app(String habilitar_ver_wod_todas_empresas_app) {
        this.habilitar_ver_wod_todas_empresas_app = habilitar_ver_wod_todas_empresas_app;
    }
}
