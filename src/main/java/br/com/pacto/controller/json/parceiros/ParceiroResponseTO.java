package br.com.pacto.controller.json.parceiros;

import br.com.pacto.bean.parceiro.Parceiro;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Created by <PERSON><PERSON> on 26/09/2018.
 */

@ApiModel(description = "Dados de resposta do parceiro")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ParceiroResponseTO {

    @ApiModelProperty(value = "Identificador único do parceiro", example = "1")
    private Integer id;

    @ApiModelProperty(value = "Nome do parceiro", example = "Academia Fitness Plus")
    private String nome;

    @ApiModelProperty(value = "URI da imagem do parceiro armazenada no sistema", example = "https://storage.example.com/parceiros/academia-fitness-plus.png")
    private String uri;

    @ApiModelProperty(value = "Situação do parceiro no sistema (ativo/inativo)", example = "true")
    private Boolean situacao;

    public ParceiroResponseTO(Parceiro parceiro) {
        this.id = parceiro.getCodigo();
        this.uri = parceiro.getUrlImagem();
        this.situacao = parceiro.getSituacao();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getUri() {
        return uri;
    }

    public void setUri(String uri) {
        this.uri = uri;
    }

    public Boolean getSituacao() {
        return situacao;
    }

    public void setSituacao(Boolean situacao) {
        this.situacao = situacao;
    }
}
