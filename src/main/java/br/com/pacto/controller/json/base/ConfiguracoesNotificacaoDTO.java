package br.com.pacto.controller.json.base;


import br.com.pacto.util.UteisValidacao;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "DTO para configurações de notificações do sistema")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ConfiguracoesNotificacaoDTO {
    @ApiModelProperty(value = "Define se deve notificar quando o aluno iniciou o treino", example = "true")
    private String iniciou_treino;

    @ApiModelProperty(value = "Define se deve notificar quando o aluno aumentou a carga", example = "true")
    private String aumentou_carga;

    @ApiModelProperty(value = "Define se deve notificar quando o aluno diminuiu a carga", example = "false")
    private String diminuiu_carga;

    @ApiModelProperty(value = "Define se deve notificar quando o aluno chegou na academia", example = "true")
    private String aluno_chegou;

    @ApiModelProperty(value = "Define se deve notificar sobre eventos da agenda", example = "true")
    private String agenda;

    @ApiModelProperty(value = "Define se deve notificar quando o agendamento foi confirmado", example = "true")
    private String agendamento_confirmado;

    @ApiModelProperty(value = "Define se deve notificar para solicitar renovação", example = "true")
    private String solicitar_renovacao;

    @ApiModelProperty(value = "Define se deve lembrar o aluno sobre compromissos", example = "true")
    private String lembrar_aluno_compromisso;

    @ApiModelProperty(value = "Define se deve notificar quando o agendamento foi alterado", example = "true")
    private String agendamento_alterado;

    @ApiModelProperty(value = "Define se deve notificar quando o aluno está em risco", example = "true")
    private String aluno_em_risco;

    @ApiModelProperty(value = "Define se deve notificar quando o aluno fez um agendamento", example = "true")
    private String aluno_agendou;

    @ApiModelProperty(value = "Número de dias para notificar sobre treino vencido", example = "7")
    private String numero_dias_notificar_treino_vencido;

    @ApiModelProperty(value = "Define se deve enviar notificações via SMS", example = "false")
    private String sms_notificacao;

    public Boolean getIniciou_treino() {
        if (!UteisValidacao.emptyString(iniciou_treino)) {
            return iniciou_treino.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setIniciou_treino(String iniciou_treino) {
        this.iniciou_treino = iniciou_treino;
    }

    public Boolean getAumentou_carga() {
        if (!UteisValidacao.emptyString(aumentou_carga)) {
            return aumentou_carga.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setAumentou_carga(String aumentou_carga) {
        this.aumentou_carga = aumentou_carga;
    }

    public Boolean getDiminuiu_carga() {
        if (!UteisValidacao.emptyString(diminuiu_carga)) {
            return diminuiu_carga.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setDiminuiu_carga(String diminuiu_carga) {
        this.diminuiu_carga = diminuiu_carga;
    }

    public Boolean getAluno_chegou() {
        if (!UteisValidacao.emptyString(aluno_chegou)) {
            return aluno_chegou.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setAluno_chegou(String aluno_chegou) {
        this.aluno_chegou = aluno_chegou;
    }

    public Boolean getAgenda() {
        if (!UteisValidacao.emptyString(agenda)) {
            return agenda.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setAgenda(String agenda) {
        this.agenda = agenda;
    }

    public Boolean getAgendamento_confirmado() {
        if (!UteisValidacao.emptyString(agendamento_confirmado)) {
            return agendamento_confirmado.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setAgendamento_confirmado(String agendamento_confirmado) {
        this.agendamento_confirmado = agendamento_confirmado;
    }

    public Boolean getSolicitar_renovacao() {
        if (!UteisValidacao.emptyString(solicitar_renovacao)) {
            return solicitar_renovacao.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setSolicitar_renovacao(String solicitar_renovacao) {
        this.solicitar_renovacao = solicitar_renovacao;
    }

    public Boolean getLembrar_aluno_compromisso() {
        if (!UteisValidacao.emptyString(lembrar_aluno_compromisso)) {
            return lembrar_aluno_compromisso.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setLembrar_aluno_compromisso(String lembrar_aluno_compromisso) {
        this.lembrar_aluno_compromisso = lembrar_aluno_compromisso;
    }

    public Boolean getAgendamento_alterado() {
        if (!UteisValidacao.emptyString(agendamento_alterado)) {
            return agendamento_alterado.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setAgendamento_alterado(String agendamento_alterado) {
        this.agendamento_alterado = agendamento_alterado;
    }

    public Boolean getAluno_em_risco() {
        if (!UteisValidacao.emptyString(aluno_em_risco)) {
            return aluno_em_risco.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setAluno_em_risco(String aluno_em_risco) {
        this.aluno_em_risco = aluno_em_risco;
    }

    public Boolean getAluno_agendou() {
        if (!UteisValidacao.emptyString(aluno_agendou)) {
            return aluno_agendou.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setAluno_agendou(String aluno_agendou) {
        this.aluno_agendou = aluno_agendou;
    }

    public String getNumero_dias_notificar_treino_vencido() {
        return numero_dias_notificar_treino_vencido;
    }

    public void setNumero_dias_notificar_treino_vencido(String numero_dias_notificar_treino_vencido) {
        this.numero_dias_notificar_treino_vencido = numero_dias_notificar_treino_vencido;
    }

    public Boolean getSms_notificacao() {
        if (!UteisValidacao.emptyString(sms_notificacao)) {
            return sms_notificacao.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setSms_notificacao(String sms_notificacao) {
        this.sms_notificacao = sms_notificacao;
    }
}
