package br.com.pacto.controller.json.avaliacao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Dados de confirmação do agendamento de avaliação física")
public class ConfirmacaoAgendamentoJSON {

    @ApiModelProperty(value = "Código da venda gerada para o agendamento da avaliação física", example = "12345")
    private String venda;

    @ApiModelProperty(value = "Nome do professor/avaliador físico responsável pela avaliação", example = "<PERSON>")
    private String professor;

    @ApiModelProperty(value = "Data e hora do lançamento do agendamento no sistema", example = "24/07/2025 14:30")
    private String lancamento;

    @ApiModelProperty(value = "Data e hora agendada para a realização da avaliação física", example = "25/07/2025 09:00")
    private String diaHora;

    public String getVenda() {
        return venda;
    }

    public void setVenda(String venda) {
        this.venda = venda;
    }

    public String getProfessor() {
        return professor;
    }

    public void setProfessor(String professor) {
        this.professor = professor;
    }

    public String getLancamento() {
        return lancamento;
    }

    public void setLancamento(String lancamento) {
        this.lancamento = lancamento;
    }

    public String getDiaHora() {
        return diaHora;
    }

    public void setDiaHora(String diaHora) {
        this.diaHora = diaHora;
    }
}
