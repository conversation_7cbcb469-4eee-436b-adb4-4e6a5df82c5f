/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.programa.read;

import br.com.pacto.controller.json.ficha.read.FichaVersaoJSON;
import br.com.pacto.controller.json.base.SuperJSON;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
@ApiModel(description = "Informações de versão do programa de treino")
public class ProgramaVersaoJSON extends SuperJSON {

    @ApiModelProperty(value = "Código único identificador do programa de treino", example = "1001")
    private Integer codPrograma;

    @ApiModelProperty(value = "Número da versão atual do programa", example = "2")
    private Integer versao;

    @ApiModelProperty(value = "Lista de fichas do programa com suas respectivas versões e última execução")
    private List<FichaVersaoJSON> fichas = new ArrayList<FichaVersaoJSON>();

    public ProgramaVersaoJSON() {
    }

    public ProgramaVersaoJSON(Integer codPrograma, Integer versao) {
        this.codPrograma = codPrograma;
        this.versao = versao;
    }

    public Integer getCodPrograma() {
        return codPrograma;
    }

    public void setCodPrograma(Integer codPrograma) {
        this.codPrograma = codPrograma;
    }

    public Integer getVersao() {
        return versao;
    }

    public void setVersao(Integer versao) {
        this.versao = versao;
    }

    public List<FichaVersaoJSON> getFichas() {
        return fichas;
    }

    public void setFichas(List<FichaVersaoJSON> fichas) {
        this.fichas = fichas;
    }
}
