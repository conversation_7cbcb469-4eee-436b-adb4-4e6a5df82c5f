package br.com.pacto.controller.json.professor;

import br.com.pacto.bean.gestao.AgrupadorIndicadores;
import br.com.pacto.bean.gestao.Indicador;
import br.com.pacto.bean.gestao.IndicadorEnum;
import br.com.pacto.bean.professor.ProfessorSintetico;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * Created paulo 05/11/2018
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Resposta contendo indicadores de um professor")
public class ProfessorIndicadorResponseDTO {

    @ApiModelProperty(value = "Dad<PERSON> básicos do professor")
    private ProfessorSimplesResponseDTO professor;

    @ApiModelProperty(value = "Número de alunos com treino", example = "25")
    private Number comTreino;

    @ApiModelProperty(value = "Número de alunos sem treino", example = "5")
    private Number semTreino;

    @ApiModelProperty(value = "Número de treinos vencidos", example = "3")
    private Number vencidos;

    @ApiModelProperty(value = "Número de treinos próximos ao vencimento", example = "8")
    private Number proxVencimento;

    @ApiModelProperty(value = "Nota média das avaliações", example = "4.5")
    private Number avaliacao;

    @ApiModelProperty(value = "Número de avaliações com 2 estrelas", example = "2")
    private Number estrelas2;

    //############### Indicadores de atividades ######################
    @ApiModelProperty(value = "Número de treinos novos criados", example = "12")
    private Number novos;

    @ApiModelProperty(value = "Número de treinos renovados", example = "8")
    private Number renovados;

    @ApiModelProperty(value = "Número de treinos revisados", example = "15")
    private Number revisados;

    @ApiModelProperty(value = "Número de acompanhamentos realizados", example = "20")
    private Number acompanhamentos;

    @ApiModelProperty(value = "Número de treinos que precisam ser revisados", example = "5")
    private Number revisar;

    @ApiModelProperty(value = "Número de atividades de acompanhamento", example = "18")
    private Number atividadesAcompanhamentos;

    @ApiModelProperty(value = "Data do último acompanhamento realizado", example = "2025-06-20")
    private Date dataAcompanhamento;

    //############### Indicadores da agenda ######################
    @ApiModelProperty(value = "Número de agendamentos realizados", example = "45")
    private Number agendados;

    @ApiModelProperty(value = "Número de agendamentos executados", example = "38")
    private Number executados;

    @ApiModelProperty(value = "Número de agendamentos cancelados", example = "4")
    private Number cancelados;

    @ApiModelProperty(value = "Número de faltas registradas", example = "3")
    private Number faltas;

    @ApiModelProperty(value = "Nome do professor", example = "João Silva")
    private String nome;
    private Number tempoMedioPermanenciaTreino;

    public ProfessorIndicadorResponseDTO() {
    }

    public ProfessorIndicadorResponseDTO(AgrupadorIndicadores agrupador, ProfessorSintetico professorSintetico){
        this.professor = new ProfessorSimplesResponseDTO(professorSintetico);
        this.nome = professorSintetico.getNomeAbreviado();
        for (Indicador indicador : agrupador.getIndicadores()) {
            if (indicador != null) {
                if (indicador.getTipo() == IndicadorEnum.N_PROF_CARTEIRA) {
                    this.comTreino = indicador.getTotal();
                } else if (getComTreino() == null) {
                    this.comTreino = 0;
                }
                if (indicador.getTipo() == IndicadorEnum.N_PROF_CARTEIRA_SEM_TREINO) {
                    this.semTreino = indicador.getTotal();
                } else if (getSemTreino() == null) {
                    this.semTreino = 0;
                }
                if (indicador.getTipo() == IndicadorEnum.N_PROF_CARTEIRA_VENCIDOS) {
                    this.vencidos = indicador.getTotal();
                } else if (getVencidos() == null) {
                    this.vencidos = 0;
                }
                if (indicador.getTipo() == IndicadorEnum.N_PROF_CARTEIRA_PROX_VENCIMENTO) {
                    this.proxVencimento = indicador.getTotal();
                } else if (getProxVencimento() == null) {
                    this.proxVencimento = 0;
                }
                if (indicador.getTipo() == IndicadorEnum.N_PROF_CARTEIRA_MEDIA_AVALIACAO) {
                    this.avaliacao = indicador.getTotal();
                } else if (getAvaliacao() == null) {
                    this.avaliacao = 0;
                }
                if (indicador.getTipo() == IndicadorEnum.N_PROF_CARTEIRA_2_ESTRELAS) {
                    this.estrelas2 = indicador.getTotal();
                } else if (getEstrelas2() == null) {
                    this.estrelas2 = 0;
                }
                //##################### Indicadores de atividade ##########################

                if(indicador.getTipo() == IndicadorEnum.N_PROF_TREINO_NOVO) {
                    this.novos = indicador.getTotal();
                } else if (getNovos() == null) {
                    this.novos = 0;
                }
                if (indicador.getTipo() == IndicadorEnum.N_PROF_TREINO_RENOVADO) {
                    this.renovados = indicador.getTotal();
                } else if (getRenovados() == null) {
                    this.renovados = 0;
                }
                if (indicador.getTipo() == IndicadorEnum.N_PROF_TREINO_REVISADO) {
                    this.revisados = indicador.getTotal();
                } else if (getRevisados() == null) {
                    this.revisados = 0;
                }
                if (indicador.getTipo() == IndicadorEnum.N_PROF_TREINO_ACOMPANHADO) {
                    this.acompanhamentos = indicador.getTotal();
                    if (this.dataAcompanhamento == null) {
                        this.dataAcompanhamento = indicador.getDataInicio();
                    }
                } else if (getAcompanhamentos() == null) {
                    this.acompanhamentos = 0;
                }
                if (indicador.getTipo() == IndicadorEnum.N_PROF_TREINO_REVISAR) {
                    this.revisar = indicador.getTotal();
                } else if (getRevisados() == null) {
                    this.revisar = 0;
                }
                if (indicador.getTipo() == IndicadorEnum.N_ATIVIDADE_PROF_TREINO_ACOMPANHADO) {
                    this.atividadesAcompanhamentos = indicador.getTotal();
                } else if (getAtividadesAcompanhamentos() == null) {
                    this.atividadesAcompanhamentos = 0;
                }
                //##################### Indicadores da agenda ##########################
                if (indicador.getTipo() == IndicadorEnum.N_AGENDADOS) {
                    this.agendados = indicador.getTotal();
                } else if (getAgendados() == null) {
                    this.agendados = 0;
                }
                if (indicador.getTipo() == IndicadorEnum.N_AGEND_EXECUTADOS) {
                    this.executados = indicador.getTotal();
                } else if (getExecutados() == null) {
                    this.executados = 0;
                }
                if (indicador.getTipo() == IndicadorEnum.N_AGEND_CANCELADOS) {
                    this.cancelados = indicador.getTotal();
                } else if (getCancelados() == null) {
                    this.cancelados = 0;
                }
                if (indicador.getTipo() == IndicadorEnum.N_AGEND_FALTOU) {
                    this.faltas = indicador.getTotal();
                } else if (getFaltas() == null) {
                    this.faltas = 0;
                }
                if (indicador.getTipo() == IndicadorEnum.N_TEMPO_MEDIO_PERMANENCIA_TREINO) {
                    this.tempoMedioPermanenciaTreino = indicador.getTotal();
                } else if (getTempoMedioPermanenciaTreino() == null) {
                    this.tempoMedioPermanenciaTreino = 0;
                }
            }
        }
    }

    public ProfessorSimplesResponseDTO getProfessor() {
        return professor;
    }

    public void setProfessor(ProfessorSimplesResponseDTO professor) {
        this.professor = professor;
    }

    public Number getComTreino() {
        return comTreino;
    }

    public void setComTreino(Number comTreino) {
        this.comTreino = comTreino;
    }

    public Number getSemTreino() {
        return semTreino;
    }

    public void setSemTreino(Number semTreino) {
        this.semTreino = semTreino;
    }

    public Number getVencidos() {
        return vencidos;
    }

    public void setVencidos(Number vencidos) {
        this.vencidos = vencidos;
    }

    public Number getProxVencimento() {
        return proxVencimento;
    }

    public void setProxVencimento(Number proxVencimento) {
        this.proxVencimento = proxVencimento;
    }

    public Number getAvaliacao() {
        return avaliacao;
    }

    public void setAvaliacao(Number avaliacao) {
        this.avaliacao = avaliacao;
    }

    public Number getEstrelas2() {
        return estrelas2;
    }

    public void setEstrelas2(Number estrelas2) {
        this.estrelas2 = estrelas2;
    }

    public Number getNovos() {
        return novos;
    }

    public void setNovos(Number novos) {
        this.novos = novos;
    }

    public Number getRenovados() {
        return renovados;
    }

    public void setRenovados(Number renovados) {
        this.renovados = renovados;
    }

    public Number getRevisados() {
        return revisados;
    }

    public void setRevisados(Number revisados) {
        this.revisados = revisados;
    }

    public Number getAcompanhamentos() {
        return acompanhamentos;
    }

    public void setAcompanhamentos(Number acompanhamentos) {
        this.acompanhamentos = acompanhamentos;
    }

    public Number getRevisar() {
        return revisar;
    }

    public void setRevisar(Number revisar) {
        this.revisar = revisar;
    }

    public Number getAgendados() {return agendados;}

    public void setAgendados(Number agendados) {this.agendados = agendados;}

    public Number getExecutados() {return executados;}

    public void setExecutados(Number executados) {this.executados = executados;}

    public Number getCancelados() {return cancelados;}

    public void setCancelados(Number cancelados) {this.cancelados = cancelados;}

    public Number getFaltas() {return faltas;}

    public void setFaltas(Number faltas) {this.faltas = faltas;}

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Number getAtividadesAcompanhamentos() {
        return atividadesAcompanhamentos;
    }

    public void setAtividadesAcompanhamentos(Number atividadesAcompanhamentos) {
        this.atividadesAcompanhamentos = atividadesAcompanhamentos;
    }
    public Date getDataAcompanhamento() {
        return dataAcompanhamento;
    }

    public void setDataAcompanhamento(Date dataAcompanhamento) {
        this.dataAcompanhamento = dataAcompanhamento;
    }

    public Number getTempoMedioPermanenciaTreino() {
        return tempoMedioPermanenciaTreino;
    }

    public void setTempoMedioPermanenciaTreino(Number tempoMedioPermanenciaTreino) {
        this.tempoMedioPermanenciaTreino = tempoMedioPermanenciaTreino;
    }

}
