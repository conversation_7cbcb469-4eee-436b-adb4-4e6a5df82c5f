    package br.com.pacto.controller.json.notificacao;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.notificacao.NotificacaoService;
import br.com.pacto.swagger.respostas.ExemploRespostaVazia;
import br.com.pacto.swagger.respostas.notificacao.ExemploRespostaListNotificacaoDTOPaginacao;
import br.com.pacto.swagger.respostas.notificacao.ExemploRespostaQuantidadeNaoLidas;
import springfox.documentation.annotations.ApiIgnore;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Novo controlador de notificações.
 * TODO: este controlador deve ser substituído para utilizar as features do Spring 5 quando fizermos a migração
 *
 * <AUTHOR> Karlus
 * @since 12/07/2018
 */
@Controller
@RequestMapping("/psec/notifications")
public class NotificacaoController {

    private final NotificacaoService service;

    /**
     * @param service       {@link NotificacaoService}
     */
    @Autowired
    public NotificacaoController(NotificacaoService service) {
        Assert.notNull(service, "O serviço de notificações não foi injetado corretamente");

        this.service = service;
    }

    /**
     * Recupera as notificações de um usuário.
     *
     * @return As notificações deste usuário em um JSON
     */
    @ApiOperation(
            value = "Consultar notificações do usuário",
            notes = "Recupera as notificações do usuário atual com suporte a paginação. " +
                    "Retorna uma lista paginada das notificações ordenadas por data de registro, " +
                    "incluindo informações sobre tipo, gravidade, status de visualização e dados adicionais.",
            tags = "Notificações"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.<br/> Ordena por um atributo contido no objeto da resposta." +
                    "<strong>Ordens disponíveis</strong>" +
                    "<ul>" +
                    "<li>ASC - Ordena pelo atributo escolhido de forma ascendente</li>" +
                    "<li>DESC - Ordena pelo atributo escolhido de forma descendente</li>" +
                    "</ul>" +
                    "Deve ser informado como atributo,ordem.<br/><br/>" +
                    "<strong>Campos disponíveis para ordenação:</strong><br/>" +
                    "- <strong>dataRegistro:</strong> Data de registro da notificação<br/>" +
                    "- <strong>type:</strong> Tipo da notificação<br/>" +
                    "- <strong>seen:</strong> Status de visualização da notificação",
                    defaultValue = "dataRegistro,desc", paramType = "query", dataType = "string"),
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Notificações consultadas com sucesso)", response = ExemploRespostaListNotificacaoDTOPaginacao.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.NOTIFICACOES)
    @RequestMapping(method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> getNotificacoes(@ApiIgnore PaginadorDTO paginadorDTO) {
        try {
            return ResponseEntityFactory.ok(service.getNotificacoes(paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(NotificacaoController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar as notificações", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    /**
     * Marca uma notificação como LIDA
     *
     * @param id ID da notificação que será marcada como lida
     * @return Um objeto de resposta informando se tudo ocorreu bem na marcação
     */
    @ApiOperation(
            value = "Marcar notificação como visualizada",
            notes = "Marca uma notificação específica como lida/visualizada no sistema. " +
                    "Após a marcação, a notificação não aparecerá mais na contagem de notificações não lidas " +
                    "e será identificada como visualizada nas consultas futuras.",
            tags = "Notificações"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Notificação marcada como visualizada com sucesso)", response = ExemploRespostaVazia.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.NOTIFICACOES)
    @RequestMapping(value = "{id}/markAsSeen", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> marcarComoVisualizada(
            @ApiParam(value = "ID único da notificação que será marcada como visualizada", defaultValue = "123", required = true)
            @PathVariable("id") final Integer id) {
        try {
            service.marcarComoLida(id);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(NotificacaoController.class.getName()).log(Level.SEVERE, "Erro ao tentar marcar a notificação como lida", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    /**
     * Procura a quantidade de notificações não lidas pelo usuário.
     *
     * @return Um objeto de resposta contendo a quantidade de notificações não lidas.
     */
    @ApiOperation(
            value = "Consultar quantidade de notificações não lidas",
            notes = "Calcula e retorna a quantidade total de notificações não lidas para o usuário atual. " +
                    "Útil para exibir badges ou indicadores visuais de notificações pendentes na interface do usuário.",
            tags = "Notificações"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Quantidade de notificações não lidas consultada com sucesso)", response = ExemploRespostaQuantidadeNaoLidas.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.NOTIFICACOES)
    @RequestMapping(value = "unseenCount", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> getQuantidadeNaoLidas() {
        try {
            return ResponseEntityFactory.ok(service.getQuantidadeNaoLidas());
        } catch (ServiceException e) {
            Logger.getLogger(NotificacaoController.class.getName()).log(Level.SEVERE, "Erro ao calcular a quantidade de notificações não lidas", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

}
