package br.com.pacto.controller.json.disponibilidade;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

@ApiModel(description = "Informação da validação de disponibilidade")
public class ItemValidacaoDisponibilidadeDTO implements Serializable {

    @ApiModelProperty(value = "Código identificador do item de validação da disponibilidade", example = "123")
    private Integer codigo;
    @ApiModelProperty(value = "Código do plano para a validação", example = "4")
    private Integer plano;
    @ApiModelProperty(value = "Código do produto para a validação", example = "2")
    private Integer produto;
    @ApiModelProperty(value = "Descrição do item de validação", example = "Validação de horários")
    private String descricao;
    @ApiModelProperty(value = "Valor final da validação", example = "10")
    private Double valorFinal;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getPlano() {
        return plano;
    }

    public void setPlano(Integer plano) {
        this.plano = plano;
    }

    public Integer getProduto() {
        return produto;
    }

    public void setProduto(Integer produto) {
        this.produto = produto;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Double getValorFinal() {
        return valorFinal;
    }

    public void setValorFinal(Double valorFinal) {
        this.valorFinal = valorFinal;
    }
}
