package br.com.pacto.controller.json.locacao;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.locacao.AlunoLocacaoHorarioPlayCheckinCheckoutDTO;
import br.com.pacto.bean.locacao.AlunoLocacaoHorarioPlayDTO;
import br.com.pacto.bean.locacao.LocacaoPlayCanceladaFinalizadaDTO;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.controller.json.SuperController;
import br.com.pacto.controller.json.aulaDia.AulasController;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.locacao.AlunoLocacaoHorarioPlayService;
import br.com.pacto.service.intf.locacao.LocacaoHorarioService;
import br.com.pacto.service.intf.locacao.LocacaoPlayCanceladaFinalizadaService;
import br.com.pacto.service.intf.locacao.LocacaoService;
import br.com.pacto.swagger.respostas.ExemploRespostaVazia;
import br.com.pacto.swagger.respostas.locacao.*;
import br.com.pacto.util.UteisValidacao;
import io.swagger.annotations.*;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.text.ParseException;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

@Controller
@RequestMapping("/psec/locacoes")
public class LocacaoController extends SuperController {

    private final LocacaoService locacaoService;
    @Autowired
    private SessaoService sessaoService;
    @Autowired
    private LocacaoHorarioService locacaoHorarioService;
    @Autowired
    private AlunoLocacaoHorarioPlayService alunoLocacaoHorarioPlayService;
    @Autowired
    private LocacaoPlayCanceladaFinalizadaService locacaoPlayCanceladaFinalizadaService;

    @Autowired
    public LocacaoController(LocacaoService locacaoService) {
        Assert.notNull(locacaoService, "O serviço de locação não foi injetado corretamente");
        this.locacaoService = locacaoService;
    }

    @ApiOperation(
            value = "Consultar locações",
            notes = "Consulta locações com suporte a filtros e paginação. Permite buscar por nome, descrição e aplicar filtros avançados de disponibilidade.",
            tags = "Locação"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.<br/> Ordena por um atributo contido no objeto da resposta." +
                    "<strong>Ordens disponíveis</strong>" +
                    "<ul>" +
                    "<li>ASC - Ordena pelo atributo escolhido de forma ascendente</li>" +
                    "<li>DESC - Ordena pelo atributo escolhido de forma descendente</li>" +
                    "</ul>" +
                    "Deve ser informado como atributo,ordem", defaultValue = "nome,asc", paramType = "query", dataType = "string"),
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaListLocacaoTOPaginacao.class)
    })
    @ResponseBody
    @RequestMapping(value = "", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultar(
            @ApiParam(value = "Filtros de busca, deve ser informado como um JSON.\n\n" +
                    "<strong>É necessário fazer o ENCODE na URL para realizar a requisição com os filtros.</strong>\n\n" +
                    "<strong>Filtros disponíveis:</strong>\n" +
                    "- <strong>quicksearchValue:</strong> Termo de busca para nome ou descrição da locação.\n" +
                    "- <strong>filtrosNew:</strong> Filtros avançados de disponibilidade (objeto FiltrosNewDTO).",
                    defaultValue = "{\"quicksearchValue\":\"Quadra\", \"filtrosNew\":{\"tipoHorarioPreDefinidoFc\":true}}")
            @RequestParam(value = "filters", required = false) JSONObject filters,
            @ApiIgnore PaginadorDTO paginadorDTO,
            @ApiParam(value = "ID da empresa", defaultValue = "1", required = true)
            @RequestHeader(value = "empresaId") Integer empresaId) throws JSONException {
        try {
            FiltrosLocacaoJSON filtros = new FiltrosLocacaoJSON(filters);
            return ResponseEntityFactory.ok(locacaoService.consultar(filtros, empresaId, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(LocacaoController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar locações", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar locação por código",
            notes = "Consulta uma locação específica através do seu código identificador único.",
            tags = "Locação"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaLocacaoTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "/{codigo}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultar(
            @ApiParam(value = "Código único da locação", defaultValue = "1", required = true)
            @PathVariable Integer codigo) throws JSONException {
        try {
            return ResponseEntityFactory.ok(locacaoService.consultarPorCodigo(codigo));
        } catch (ServiceException e) {
            Logger.getLogger(LocacaoController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar locações", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Cadastrar locação",
            notes = "Cadastra uma nova locação no sistema com todas as suas configurações, horários e validações.",
            tags = "Locação"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaLocacaoTO.class)
    })
    @ResponseBody
    @RequestMapping(method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cadastrar(
            @ApiParam(value = "ID da empresa", defaultValue = "1", required = true)
            @RequestHeader(value = "empresaId", required = true) Integer empresaId,
            @ApiParam(value = "Dados da locação a ser cadastrada", required = true)
            @RequestBody LocacaoTO locacaoTO) {
        try {
            return ResponseEntityFactory.ok(locacaoService.cadastrar(locacaoTO, empresaId));
        } catch (ServiceException e) {
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, "Erro ao tentar cadastrar a aula", e);
            if (e.getCause() == null || e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroConhecido(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ApiOperation(
            value = "Adicionar aluno ao horário play",
            notes = "Adiciona um aluno a um horário específico de locação do tipo play, permitindo que o aluno utilize o espaço no horário determinado.",
            tags = "Locação"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaAdicionarAlunoHorarioPlay.class)
    })
    @ResponseBody
    @RequestMapping(value = "/addAlunoHorarioPlay", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cadastrar(
            @ApiParam(value = "Dados do aluno para adicionar ao horário play", required = true)
            @RequestBody AlunoLocacaoHorarioPlayDTO alunoLocacaoHorarioPlayDTO) {
        try {
            return ResponseEntityFactory.ok(alunoLocacaoHorarioPlayService.addAlunoHorarioPlay(alunoLocacaoHorarioPlayDTO, sessaoService.getUsuarioAtual().getChave()));
        } catch (ServiceException e) {
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, "Erro ao tentar cadastrar a aula", e);
            if (e.getCause() == null || e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroConhecido(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ApiOperation(
            value = "Cancelar locação horário play",
            notes = "Cancela uma locação do tipo play em um horário específico, registrando a justificativa do cancelamento.",
            tags = "Locação"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaCancelarLocacaoHorarioPlay.class)
    })
    @ResponseBody
    @RequestMapping(value = "/cancelaLocacaoHorarioPlay", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cancelaLocacaoHorarioPlay(
            @ApiParam(value = "Dados para cancelamento da locação play", required = true)
            @RequestBody LocacaoPlayCanceladaFinalizadaDTO locacaoPlayCanceladaFinalizadaDTO) {
        try {
            return ResponseEntityFactory.ok(locacaoPlayCanceladaFinalizadaService.cancelaLocacaoHorarioPlay(locacaoPlayCanceladaFinalizadaDTO));
        } catch (ServiceException e) {
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, "Erro ao tentar cadastrar a aula", e);
            if (e.getCause() == null || e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroConhecido(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ApiOperation(
            value = "Registrar check-in/check-out do aluno",
            notes = "Registra o check-in ou check-out de um aluno em uma locação do tipo play, controlando a entrada e saída do espaço.",
            tags = "Locação"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaCheckinCheckoutPlay.class)
    })
    @ResponseBody
    @RequestMapping(value = "/addAlunoHorarioPlayCheckinCheckout", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> addAlunoHorarioPlayCheckinCheckout(
            @ApiParam(value = "Dados do check-in/check-out do aluno", required = true)
            @RequestBody AlunoLocacaoHorarioPlayCheckinCheckoutDTO dto) {
        try {
            return ResponseEntityFactory.ok(alunoLocacaoHorarioPlayService.addAlunoHorarioPlayCheckinCheckout(dto, sessaoService.getUsuarioAtual().getChave()));
        } catch (ServiceException e) {
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, "Erro ao tentar cadastrar a aula", e);
            if (e.getCause() == null || e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroConhecido(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ApiOperation(
            value = "Salvar ou atualizar horários da locação",
            notes = "Salva ou atualiza os horários de funcionamento de uma locação específica, definindo dias da semana, horários de início e fim.",
            tags = "Locação"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaListLocacaoHorarioTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "/{codigoLocacao}/locacao-horarios", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> saveOrUpdateLocacaoHorarios(
            @ApiParam(value = "Código da locação", defaultValue = "1", required = true)
            @PathVariable(value = "codigoLocacao") Integer codigoLocacao,
            @ApiParam(value = "Lista de horários da locação", required = true)
            @RequestBody List<LocacaoHorarioTO> horarios) {
        try {
            return ResponseEntityFactory.ok(locacaoService.saveOrUpdateLocacaoHorarios(codigoLocacao, horarios));
        } catch (ServiceException e) {
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, "Erro ao tentar cadastrar a aula", e);
            if (e.getCause() == null || e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroConhecido(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ApiOperation(
            value = "Consultar horário específico da locação",
            notes = "Consulta um horário específico de locação através do código, ambiente e dia, retornando detalhes do horário configurado.",
            tags = "Locação"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaLocacaoHorarioTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "/locacao-horario/{codigo}/{ambiente}/{dia}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> locacaoHorario(
            @ApiParam(value = "Código do horário da locação", defaultValue = "1", required = true)
            @PathVariable Integer codigo,
            @ApiParam(value = "Código do ambiente", defaultValue = "1", required = true)
            @PathVariable Integer ambiente,
            @ApiParam(value = "Dia no formato string", defaultValue = "2024-01-15", required = true)
            @PathVariable String dia) throws JSONException {
        try {
            return ResponseEntityFactory.ok(locacaoHorarioService.findById(codigo, dia, ambiente));
        } catch (ServiceException e) {
            Logger.getLogger(LocacaoController.class.getName()).log(Level.SEVERE, "Erro ao tentar excluir locação horário", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @ApiOperation(
            value = "Excluir aluno do horário play",
            notes = "Remove um aluno de um horário específico de locação do tipo play, liberando a vaga para outros usuários.",
            tags = "Locação"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaRemoverAlunoHorarioPlay.class)
    })
    @ResponseBody
    @RequestMapping(value = "/aluno-locacao-horario-play/{codigo}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> deleteAlunoLocacaoHorarioPlay(
            @ApiParam(value = "Código do aluno no horário play", defaultValue = "1", required = true)
            @PathVariable Integer codigo) throws JSONException {
        try {
            return ResponseEntityFactory.ok(alunoLocacaoHorarioPlayService.deleteAlunoLocacaoHorarioPlay(codigo, sessaoService.getUsuarioAtual().getChave()));
        } catch (ServiceException e) {
            Logger.getLogger(LocacaoController.class.getName()).log(Level.SEVERE, "Erro ao tentar excluir locação horário", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Excluir horário da locação",
            notes = "Exclui um horário específico de uma locação, removendo permanentemente o horário de funcionamento configurado.",
            tags = "Locação"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaVazia.class)
    })
    @ResponseBody
    @RequestMapping(value = "/locacao-horario/{codigo}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> deletarLocacaoHorario(
            @ApiParam(value = "Código do horário da locação", defaultValue = "1", required = true)
            @PathVariable Integer codigo) throws JSONException {
        try {
            locacaoService.deletarLocacaoHorario(codigo);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(LocacaoController.class.getName()).log(Level.SEVERE, "Erro ao tentar excluir locação horário", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Agendar locação",
            notes = "Agenda uma locação para um dia específico, vinculando cliente, ambiente, horários e serviços adicionais.",
            tags = "Locação"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaAgendarLocacao.class)
    })
    @ResponseBody
    @RequestMapping(value = "/agendar/{dia}", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> agendar(
            @ApiParam(value = "ID da empresa", defaultValue = "1", required = true)
            @RequestHeader(value = "empresaId") Integer empresaId,
            @ApiParam(value = "Dia do agendamento no formato yyyyMMdd", defaultValue = "20240115", required = true)
            @PathVariable String dia,
            @ApiParam(value = "Dados do agendamento da locação", required = true)
            @RequestBody AgendamentoLocacaoDTO agendamentoLocacaoDTO) {
        try {
            return ResponseEntityFactory.ok(locacaoService.agendar(
                    Calendario.getDate("yyyyMMdd", dia),
                    agendamentoLocacaoDTO, empresaId, sessaoService.getUsuarioAtual().getChave(),
                    sessaoService.getUsuarioAtual().getId()));
        } catch (ParseException e) {
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, "Erro ao tentar agendar a locação", e);
            return ResponseEntityFactory.erroInterno("error_date", e.getMessage());
        } catch (ServiceException e) {
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, "Erro ao tentar agendar a locação", e);
            if (e.getCause() == null || e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroConhecido(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ApiOperation(
            value = "Alterar locação",
            notes = "Altera os dados de uma locação existente, incluindo configurações, horários e validações.",
            tags = "Locação"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaLocacaoTO.class)
    })
    @ResponseBody
    @RequestMapping(method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alterar(
            @ApiParam(value = "ID da empresa", defaultValue = "1", required = true)
            @RequestHeader(value = "empresaId", required = true) Integer empresaId,
            @ApiParam(value = "Dados da locação a ser alterada", required = true)
            @RequestBody LocacaoTO locacaoTO) {
        try {
            return ResponseEntityFactory.ok(locacaoService.alterar(locacaoTO, empresaId));
        } catch (ServiceException e) {
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, "Erro ao tentar cadastrar a aula", e);
            if (e.getCause() == null || e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroConhecido(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ApiOperation(
            value = "Excluir locação",
            notes = "Exclui uma locação do sistema através do seu código identificador único.",
            tags = "Locação"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaVazia.class)
    })
    @ResponseBody
    @RequestMapping(value = "/{codigo}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> deletar(
            @ApiParam(value = "Código único da locação", defaultValue = "1", required = true)
            @PathVariable Integer codigo) throws JSONException {
        try {
            locacaoService.deletar(codigo);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(LocacaoController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar locações", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @ApiOperation(
            value = "Consultar disponibilidades de locação",
            notes = "Consulta as disponibilidades de locação para um dia e horário específicos, retornando os espaços disponíveis para agendamento.",
            tags = "Locação"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.<br/> Ordena por um atributo contido no objeto da resposta." +
                    "<strong>Ordens disponíveis</strong>" +
                    "<ul>" +
                    "<li>ASC - Ordena pelo atributo escolhido de forma ascendente</li>" +
                    "<li>DESC - Ordena pelo atributo escolhido de forma descendente</li>" +
                    "</ul>" +
                    "Deve ser informado como atributo,ordem", defaultValue = "nome,asc", paramType = "query", dataType = "string"),
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaListDisponibilidadeDTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/disponibilidades/{dia}/{horaSelecionada}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> aulaDetalhada(
            @ApiParam(value = "ID da empresa", defaultValue = "1", required = true)
            @RequestHeader("empresaId") Integer empresaId,
            @ApiParam(value = "Dia em timestamp (milissegundos)", defaultValue = "1705276800000", required = true)
            @PathVariable("dia") String dia,
            @ApiParam(value = "Horário selecionado", defaultValue = "08:00", required = true)
            @PathVariable("horaSelecionada") String horaSelecionada,
            @ApiIgnore PaginadorDTO paginadorDTO) {
        try {
            return ResponseEntityFactory.ok(locacaoService.disponibilidades(empresaId, sessaoService.getUsuarioAtual().getChave(),
                    new Date(Long.parseLong(dia)), horaSelecionada, -1, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Configurar agendamento de locação",
            notes = "Obtém a configuração de agendamento para um horário específico ou edita um agendamento existente.",
            tags = "Locação"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaConfigAgendamentoLocacaoDTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/config/agendamento/{idHorario}/{agendamento}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> aulaDetalhada(
            @ApiParam(value = "ID do horário da locação", defaultValue = "1", required = true)
            @PathVariable("idHorario") Integer idHorario,
            @ApiParam(value = "Código do agendamento (0 para novo agendamento)", defaultValue = "0", required = true)
            @PathVariable("agendamento") Integer agendamento,
            @ApiParam(value = "Data do agendamento", defaultValue = "2024-01-15")
            @RequestParam(value = "data", required = false) String data) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            if (UteisValidacao.emptyNumber(agendamento)) {
                return ResponseEntityFactory.ok(locacaoService.configAgendamento(idHorario, data, ctx));
            }
            return ResponseEntityFactory.ok(locacaoService.editAgendamento(agendamento, data, ctx));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Obter tipos de horário de locação",
            notes = "Retorna todos os tipos de horário disponíveis para configuração de locações (Livre, Play, Pré-definido).",
            tags = "Locação"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaListTipoHorarioLocacaoDTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "/tiposHorarios", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterLocacaoTipoHorario() throws JSONException {
        List<TipoHorarioLocacaoDTO> tiposHorarios = new ArrayList<>();
        for (TipoHorarioLocacaoEnum thl : TipoHorarioLocacaoEnum.values()) {
            tiposHorarios.add(new TipoHorarioLocacaoDTO(thl.getCodigo(), thl.getDescricao()));
        }
        return ResponseEntityFactory.ok(tiposHorarios);
    }

    @ApiOperation(
            value = "Obter detalhes do agendamento de locação",
            notes = "Consulta os detalhes completos de um agendamento de locação específico através do seu código.",
            tags = "Locação"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaConfigAgendamentoLocacaoDTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/detalhes-agendamento/{codigoAgendamentoLocacao}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> detalhesAgendamentoLocacao(
            @ApiParam(value = "Código do agendamento de locação", defaultValue = "1", required = true)
            @PathVariable("codigoAgendamentoLocacao") Integer codigoAgendamentoLocacao,
            @ApiParam(value = "Data do agendamento", defaultValue = "2024-01-15")
            @RequestParam(value = "data", required = false) String data) {
        try {
            return ResponseEntityFactory.ok(locacaoService.editAgendamento(codigoAgendamentoLocacao, data, sessaoService.getUsuarioAtual().getChave()));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Cancelar agendamento de locação",
            notes = "Cancela um agendamento de locação específico, registrando a justificativa do cancelamento.",
            tags = "Locação"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaConfigAgendamentoLocacaoDTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/cancelar-agendamento-locacao/{codigoAgendamentoLocacao}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cancelarAgendamentoLocacao(
            @ApiParam(value = "Código do agendamento de locação", defaultValue = "1", required = true)
            @PathVariable("codigoAgendamentoLocacao") Integer codigoAgendamentoLocacao,
            @ApiParam(value = "Dados do cancelamento contendo a justificativa",
                    defaultValue = "{\"justificativa\":\"Cliente solicitou cancelamento\"}", required = true)
            @RequestBody Map<String, String> requestBody) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            Integer codigoUsuarioZW = sessaoService.getUsuarioAtual().getId();
            return ResponseEntityFactory.ok(locacaoService.cancelarAgendamentoLocacao(ctx, codigoUsuarioZW, false, codigoAgendamentoLocacao, requestBody.get("justificativa")));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Finalizar agendamento de locação",
            notes = "Finaliza um agendamento de locação, confirmando a utilização do espaço e processando os valores.",
            tags = "Locação"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaFinalizarAgendamentoLocacao.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/finalizar-agendamento-locacao/{codigoAgendamentoLocacao}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> finalizarAgendamentoLocacao(
            @ApiParam(value = "Código do agendamento de locação", defaultValue = "1", required = true)
            @PathVariable("codigoAgendamentoLocacao") Integer codigoAgendamentoLocacao,
            @ApiParam(value = "Data da finalização no formato dd/MM/yyyy", defaultValue = "15/01/2024", required = true)
            @RequestParam("data") String data,
            @ApiParam(value = "ID da empresa", defaultValue = "1", required = true)
            @RequestHeader(value = "empresaId") Integer empresaId,
            @ApiParam(value = "Dados do agendamento para finalização", required = true)
            @RequestBody AgendamentoLocacaoDTO agendamentoLocacaoDTO) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            Integer codigoUsuarioZW = sessaoService.getUsuarioAtual().getId();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
            return ResponseEntityFactory.ok(
                    locacaoService.finalizarAgendamentoLocacao(ctx,
                            codigoUsuarioZW,
                            codigoAgendamentoLocacao,
                            agendamentoLocacaoDTO, Date.from(LocalDate.parse(data, formatter).atStartOfDay(ZoneId.systemDefault()).toInstant()), empresaId)
            );
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Validar data para reagendamento",
            notes = "Valida se uma nova data é válida para reagendamento de uma locação específica, verificando disponibilidade e regras de negócio.",
            tags = "Locação"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaListLocacaoHorarioTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "/validar-reagendamento/{codigoAgendamentoLocacao}/{novaData}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> validarDataReagendamento(
            @ApiParam(value = "Código do agendamento de locação", defaultValue = "1", required = true)
            @PathVariable("codigoAgendamentoLocacao") Integer codigoAgendamentoLocacao,
            @ApiParam(value = "Nova data em timestamp (milissegundos)", defaultValue = "1705276800000", required = true)
            @PathVariable("novaData") String novaData) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            Date date;
            try {
                date = new Date(Long.parseLong(novaData));
            } catch (Exception e) {
                throw new ServiceException("A data fornecida não está no formato válido.");
            }

            return ResponseEntityFactory.ok(locacaoService.validarDataReagendamento(ctx, codigoAgendamentoLocacao, date));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Reagendar locação",
            notes = "Reagenda uma locação para uma nova data, mantendo as configurações originais do agendamento.",
            tags = "Locação"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaVazia.class)
    })
    @ResponseBody
    @RequestMapping(value = "/reagendar/{codigoAgendamentoLocacao}/{dia}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> reagendar(
            @ApiParam(value = "Código do agendamento de locação", defaultValue = "1", required = true)
            @PathVariable("codigoAgendamentoLocacao") Integer codigoAgendamentoLocacao,
            @ApiParam(value = "Nova data em timestamp (milissegundos)", defaultValue = "1705276800000", required = true)
            @PathVariable("dia") String dia,
            @ApiParam(value = "Dados do agendamento para reagendamento", required = true)
            @RequestBody AgendamentoLocacaoDTO agendamentoLocacaoDTO) {
        try {
            Date date;
            try {
                date = new Date(Long.parseLong(dia));
            } catch (Exception e) {
                throw new ServiceException("A data fornecida não está no formato válido.");
            }
            locacaoService.reagendar(codigoAgendamentoLocacao, agendamentoLocacaoDTO, date, sessaoService.getUsuarioAtual().getChave());
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, "Erro ao tentar reagendar a locação", e);
            if (e.getCause() == null || e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroConhecido(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ApiOperation(
            value = "Sincronizar venda avulsa com agendamento",
            notes = "Sincroniza uma venda avulsa com um agendamento de locação, vinculando a transação financeira ao uso do espaço.",
            tags = "Locação"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaVazia.class)
    })
    @ResponseBody
    @RequestMapping(value = "/sincronizar-venda-avulsa/{agendamentoLocacaoCodigo}/{vendaAvulsaCodigo}", method = RequestMethod.PATCH, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> sincronizarVendaAvulsa(
            @ApiParam(value = "Código do agendamento de locação", defaultValue = "1", required = true)
            @PathVariable("agendamentoLocacaoCodigo") Integer agendamentoLocacaoCodigo,
            @ApiParam(value = "Código da venda avulsa", defaultValue = "1", required = true)
            @PathVariable("vendaAvulsaCodigo") Integer vendaAvulsaCodigo) {
        try {
            locacaoService.sincronizarVendaAvulsa(agendamentoLocacaoCodigo, vendaAvulsaCodigo);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, "Erro ao tentar sincronizar a venda avulsa da locação", e);
            if (e.getCause() == null || e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroConhecido(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

}
