package br.com.pacto.controller.json.token;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.empresa.EmpresaService;
import br.com.pacto.service.intf.usuario.FotoService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.swagger.respostas.token.ExemploRespostaStringEmpresas;
import br.com.pacto.swagger.respostas.token.ExemploRespostaStringTokenCriptografado;
import br.com.pacto.swagger.respostas.token.ExemploRespostaTokenResponseTO;
import br.com.pacto.util.UtilContext;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import servicos.integracao.adm.AdmWSConsumer;
import servicos.integracao.adm.client.EmpresaWS;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by Joao Moita on 28/09/2018.
 */
@Controller
@RequestMapping("/psec/validateToken")
public class TokenController {

    @Autowired
    private SessaoService sessaoService;
    @Autowired
    private UsuarioService usuarioService;
    @Autowired
    private EmpresaService empresaService;
    @Autowired
    private FotoService fotoService;

    @ApiOperation(value = "Validar token de autenticação",
                  notes = "Valida o token de autenticação do usuário e retorna informações completas incluindo dados pessoais, empresas vinculadas e permissões de acesso. " +
                          "Permite especificar uma empresa específica para definir o contexto da sessão.",
                  tags = "Autenticação")
    @ApiResponses({
        @ApiResponse(code = 200, message = "Token validado com sucesso", response = ExemploRespostaTokenResponseTO.class)
    })
    @ResponseBody
    @RequestMapping(method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> validateToken(HttpServletRequest request,
            @ApiParam(value = "Código da empresa para definir contexto da sessão", defaultValue = "1")
            @RequestParam(value = "empresaId", required = false) Integer empresaId) {
        try {
            TokenResponseTO tokenResponseTO = obterUsuario(request, empresaId);
            return ResponseEntityFactory.ok(tokenResponseTO);
        } catch (ServiceException e) {
            Logger.getLogger(TokenController.class.getName()).log(Level.SEVERE, "Erro ao tentar validar Token", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(value = "Validar token com criptografia",
                  notes = "Valida o token de autenticação e retorna as informações do usuário em formato criptografado para maior segurança. " +
                          "Os dados são os mesmos do endpoint de validação padrão, porém encriptados usando algoritmo interno.",
                  tags = "Autenticação")
    @ApiResponses({
        @ApiResponse(code = 200, message = "Token validado e dados criptografados retornados", response = ExemploRespostaStringTokenCriptografado.class)
    })
    @ResponseBody
    @RequestMapping(value = "/sec", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> validateTokenSec(HttpServletRequest request,
            @ApiParam(value = "Código da empresa para definir contexto da sessão", defaultValue = "1")
            @RequestParam(value = "empresaId", required = false) Integer empresaId) {
        try {
            TokenResponseTO tokenResponseTO = obterUsuario(request, empresaId);
            String json = new JSONObject(tokenResponseTO).toString();
            return ResponseEntityFactory.ok(Uteis.encriptarZWUI(json));
        } catch (ServiceException e) {
            Logger.getLogger(TokenController.class.getName()).log(Level.SEVERE, "Erro ao tentar validar Token", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            Logger.getLogger(TokenController.class.getName()).log(Level.SEVERE, "Erro ao tentar validar Token", e);
            return ResponseEntityFactory.erroInterno("erro-validate-token", e.getMessage());
        }
    }

    private TokenResponseTO obterUsuario(HttpServletRequest request, Integer empresaId) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        if (empresaId != null) {
            sessaoService.getUsuarioAtual().setEmpresaAtual(empresaId);
        }
        Integer usuarioID = sessaoService.getUsuarioAtual().getId();
        Usuario usuario = usuarioService.obterPorId(ctx, usuarioID);
        Uteis.logar(null, "Usuário logado: " + usuario);
        Boolean treinoIndependente = SuperControle.independente(ctx);

        List<Empresa> empresas = new ArrayList<>();
        if (treinoIndependente) {
            empresas = empresaService.obterTodos(ctx);
        } else {
            List<EmpresaWS> empresasZW = UtilContext.getBean(AdmWSConsumer.class).obterEmpresasZW(ctx, usuario.getUsuarioZW());

            for (EmpresaWS empresaZW : empresasZW) {
                empresas.add(Empresa.copyAttributes(new Empresa(), empresaZW));
            }
        }

        if (usuario != null && usuario.getProfessor() != null) {
            usuario.getProfessor().setUriImagem(fotoService.defineURLFotoPessoa(request, usuario.getProfessor().getPessoa().getFotoKey(), usuario.getProfessor().getCodigoPessoa(), false, ctx, !SuperControle.independente(ctx)));
        }

        return new TokenResponseTO(usuario, empresas, treinoIndependente);
    }

    @ApiOperation(value = "Obter códigos das empresas vinculadas",
                  notes = "Retorna uma string contendo os códigos de todas as empresas vinculadas ao usuário autenticado, separados por vírgula. " +
                          "Útil para identificar quais unidades o usuário tem acesso.",
                  tags = "Autenticação")
    @ApiResponses({
        @ApiResponse(code = 200, message = "Códigos das empresas obtidos com sucesso", response = ExemploRespostaStringEmpresas.class)
    })
    @ResponseBody
    @RequestMapping(value = "/empresas", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> empresas(
            @ApiParam(value = "Código da empresa para definir contexto da sessão", defaultValue = "1")
            @RequestHeader(value = "empresaId", required = false) Integer empresaId) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            Integer usuarioID = sessaoService.getUsuarioAtual().getId();

            if (empresaId != null) {
                sessaoService.getUsuarioAtual().setEmpresaAtual(empresaId);
            }
            Usuario usuario = usuarioService.obterPorId(ctx, usuarioID);


            List<EmpresaWS> empresas = UtilContext.getBean(AdmWSConsumer.class).obterEmpresasZW(ctx, usuario.getUsuarioZW());
            String empresascod = "";
            for (EmpresaWS empresa : empresas) {
                empresascod += "," + empresa.getCodigo();
            }
            return ResponseEntityFactory.ok(empresascod.replaceFirst(",", ""));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
}
