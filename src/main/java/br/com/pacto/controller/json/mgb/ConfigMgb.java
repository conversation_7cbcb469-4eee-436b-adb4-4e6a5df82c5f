package br.com.pacto.controller.json.mgb;

import br.com.pacto.controller.json.base.SuperJSON;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "DTO para configurações de integração MGB")
public class ConfigMgb extends SuperJSON {

    @ApiModelProperty(value = "Código da empresa (referência ao codZW)", example = "789")
    private Integer empresa; // referencia ao codzw da empresa

    @ApiModelProperty(value = "Token de integração MGB", example = "mgb_token_xyz789")
    private String token;

    @ApiModelProperty(value = "Nome da empresa", example = "Academia Strong Fitness")
    private String nome;

    public ConfigMgb() {
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

}
