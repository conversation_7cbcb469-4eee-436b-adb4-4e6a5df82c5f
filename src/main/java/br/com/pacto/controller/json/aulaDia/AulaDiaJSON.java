/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.aulaDia;

import br.com.pacto.bean.aula.AulaAluno;
import br.com.pacto.bean.aula.AulaDia;
import br.com.pacto.controller.json.base.SuperJSON;
import br.com.pacto.controller.json.turma.TurmaVideoDTO;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.bean.AgendaTotalTO;
import br.com.pacto.util.json.AgendaTotalJSON;
import br.com.pacto.util.AgendadoJSON;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import java.util.List;

/**
 * Representa informações detalhadas de uma aula em um dia específico
 * <AUTHOR>
 */
@ApiModel(description = "Informações detalhadas de uma aula em um dia específico")
public class AulaDiaJSON extends SuperJSON {

    @ApiModelProperty(value = "Código identificador único da aula", example = "1001")
    private Integer codigo;

    @ApiModelProperty(value = "Nome da empresa/academia", example = "Academia Fitness Pro")
    private String nomeEmpresa;

    @ApiModelProperty(value = "Chave da logo da empresa", example = "logo_academia_123")
    private String logoEmpresa;

    @ApiModelProperty(value = "Horário de início da aula no formato HH:mm", example = "08:00")
    private String inicio;

    @ApiModelProperty(value = "Horário de término da aula no formato HH:mm", example = "09:00")
    private String fim;

    @ApiModelProperty(value = "Nome/título da aula", example = "Musculação Iniciantes")
    private String nome;

    @ApiModelProperty(value = "Nível de dificuldade da aula", example = "Iniciante")
    private String nivel;

    @ApiModelProperty(value = "Nome da modalidade da aula", example = "Musculação")
    private String modalidade;

    @ApiModelProperty(value = "Nome do professor responsável pela aula", example = "Carlos Silva")
    private String professor;

    @ApiModelProperty(value = "Nome do ambiente/sala onde ocorre a aula", example = "Sala de Musculação 1")
    private String ambiente;

    @ApiModelProperty(value = "Pontos de bonificação concedidos pela participação na aula", example = "10")
    private Integer pontos;

    @ApiModelProperty(value = "Capacidade máxima de alunos na aula", example = "20")
    private Integer capacidade;

    @ApiModelProperty(value = "Número de vagas ainda disponíveis na aula", example = "5")
    private Integer vagasRestantes;

    @ApiModelProperty(value = "Número atual de alunos matriculados na aula", example = "15")
    private Integer ocupacao;

    @ApiModelProperty(value = "Indica se ainda restam vagas disponíveis na aula", example = "true")
    private boolean restamVagas;

    @ApiModelProperty(value = "URL do ícone da modalidade", example = "https://academia.com/icones/musculacao.png")
    private String urlIcone;

    @ApiModelProperty(value = "Data da aula no formato dd/MM/yyyy", example = "20/01/2024")
    private String dia;

    @ApiModelProperty(value = "Dia da semana da aula", example = "Segunda-feira")
    private String diaSemana;
    @ApiModelProperty(value = "Indica se o aluno já marcou 'Eu Quero' para esta aula", example = "false")
    private boolean jaMarcouEuQuero = false;

    @ApiModelProperty(value = "Indica se o aluno está matriculado nesta aula", example = "true")
    private boolean alunoEstaNaAula = false;

    @ApiModelProperty(value = "Indica se é uma aula coletiva", example = "true")
    private boolean coletiva = false;

    @ApiModelProperty(value = "Data da aula como objeto Date")
    private Date diaDate;

    @ApiModelProperty(value = "Data de lançamento da aula no sistema")
    private Date datalancamento;

    @ApiModelProperty(value = "Código da cor associada à modalidade", example = "#FF5722")
    private String corModalidade;

    @ApiModelProperty(value = "Chave da foto do professor", example = "foto_professor_123")
    private String fotoProfessor;

    @ApiModelProperty(value = "Chave da foto da modalidade", example = "foto_modalidade_456")
    private String fotoModalidade;

    @ApiModelProperty(value = "Indica se há integração com Spivi", example = "false")
    private Boolean integracaoSpivi;

    @ApiModelProperty(value = "Código da aula de origem quando se trata de reposição", example = "999")
    private Integer codigoAulaOrigemReposicao;

    @ApiModelProperty(value = "Período de vigência da aula", example = "Janeiro/2024")
    private String vigencia;

    @ApiModelProperty(value = "URL do vídeo no YouTube relacionado à aula", example = "https://youtube.com/watch?v=abc123")
    private String urlVideoYoutube;

    @ApiModelProperty(value = "URL da imagem da aula", example = "https://academia.com/imagens/aula_123.jpg")
    private String imageUrl;

    @ApiModelProperty(value = "Lista de vídeos relacionados à turma")
    private List<TurmaVideoDTO> linkVideos;

    @ApiModelProperty(value = "Tipo de reserva de equipamento", example = "NUMERADO")
    private String tipoReservaEquipamento;

    @ApiModelProperty(value = "Equipamentos já ocupados", example = "1,3,5,7")
    private String equipamentosOcupados;

    @ApiModelProperty(value = "Mapa de equipamentos disponíveis", example = "Esteira:5,Bike:3,Elíptico:2")
    private String mapaEquipamentos;

    @ApiModelProperty(value = "Lista detalhada do mapa de equipamentos e aparelhos")
    private List<MapaEquipamentoAparelhoDTO> listaMapaEquipamentoAparelho;

    @ApiModelProperty(value = "Indica se deve apresentar equipamentos por numeração", example = "false")
    private Boolean apresentarEquipamentoPorNumeracao = false;

    public AulaDiaJSON(AulaDia aulaDia, List<AulaAluno> alunos){
        dia = Uteis.getData(aulaDia.getInicio());
        codigo = aulaDia.getCodigo();
        inicio = Uteis.getDataAplicandoFormatacao(aulaDia.getInicio(), "HH:mm");
        fim = Uteis.getDataAplicandoFormatacao(aulaDia.getFim(), "HH:mm");
        modalidade = aulaDia.getAula().getNome();
        professor = aulaDia.getProfessor().getNome();
        ambiente = aulaDia.getAula().getAmbiente().getNome();
        pontos = aulaDia.getAula().getPontosBonus();
        ocupacao = alunos == null ? 0 : alunos.size();
        capacidade = aulaDia.getAula().getCapacidade();
        vagasRestantes = capacidade - ocupacao; 
        restamVagas = ocupacao < capacidade;

        try {
            urlIcone = aulaDia.getAula().getModalidade().getAnimacao().getUrl();
        } catch (Exception e) {
            urlIcone = "";
        }
    }

    public AulaDiaJSON(AgendadoJSON json){
        inicio = json.getInicio();
        fim = json.getFim();
    }
    
    public AulaDiaJSON(AgendaTotalJSON json) throws Exception{
        nome = json.getTitulo();
        try {
            diaDate = Uteis.getDate(json.getInicio(), "dd/MM/yyyy HH:mm");
            dia = Uteis.getData(diaDate);
            inicio = Uteis.getDataAplicandoFormatacao(Uteis.getDate(json.getInicio(), "dd/MM/yyyy HH:mm"), "HH:mm");
            fim = Uteis.getDataAplicandoFormatacao(Uteis.getDate(json.getFim(), "dd/MM/yyyy HH:mm"), "HH:mm");
        } catch (Exception e) {
            inicio = json.getInicio();
            fim = json.getFim();
        }
        codigo = Integer.valueOf(json.getId());
        nomeEmpresa = json.getNomeEmpresa();
        logoEmpresa = json.getLogoEmpresa();
        datalancamento = json.getDatalancamento();
        modalidade = json.getTipo();
        professor = json.getResponsavel();
        ambiente = json.getLocal();
        diaSemana = json.getDiaSemana();
        capacidade = json.getNrVagas();
        coletiva = json.getAulaCheia();
        ocupacao = json.getNrVagasPreenchidas() == null ? 0 : json.getNrVagasPreenchidas();
        restamVagas = ocupacao < capacidade;
        vagasRestantes = capacidade - ocupacao; 
        pontos = json.getPontosBonus();
        jaMarcouEuQuero = json.isJaMarcouEuQuero();
        corModalidade = json.getCor();
        fotoProfessor = json.getFotoProfessor();
        fotoModalidade = json.getFotoModalidade();
        nivel = json.getNivel() == null ? "" : json.getNivel();
        codigoAulaOrigemReposicao = json.getCodigoAulaOrigemReposicao();
        vigencia = Uteis.getData(json.getFimVigencia(), "dd/MM/yyyy");
        alunoEstaNaAula = json.isAlunoEstaNaAula();
        urlVideoYoutube = json.getUrlVideoYoutube();
        imageUrl = json.getImageUrl();
        linkVideos = json.getLinkVideos();
        tipoReservaEquipamento = json.getTipoReservaEquipamento();
        mapaEquipamentos = json.getMapaEquipamentos();
    }
    
    public AulaDiaJSON(AgendaTotalTO to) throws Exception{
        nome = to.getTitulo();
        try {
            dia = Uteis.getData(Uteis.getDate(to.getDia(), "dd/MM/yyyy"));
            inicio = Uteis.getDataAplicandoFormatacao(Uteis.getDate(to.getInicio(), "dd/MM/yyyy HH:mm"), "HH:mm");
            fim = Uteis.getDataAplicandoFormatacao(Uteis.getDate(to.getFim(), "dd/MM/yyyy HH:mm"), "HH:mm");
        } catch (Exception e) {
            inicio = to.getInicio();
            fim = to.getFim();
        }
        diaDate = Uteis.getDate(dia + " "+inicio, "dd/MM/yyyy HH:mm");
        codigo = Integer.valueOf(to.getId());
        modalidade = to.getTipo();
        professor = to.getResponsavel();
        ambiente = to.getLocal();
        diaSemana = to.getDiaSemana();
        capacidade = to.getNrVagas();
        ocupacao = to.getNrVagasPreenchidas() == null ? 0 : to.getNrVagasPreenchidas();
        restamVagas = ocupacao < capacidade;
        vagasRestantes = capacidade - ocupacao;
        jaMarcouEuQuero = to.getJaMarcouEuQuero();
        fotoProfessor = to.getFotoProfessor();
        fotoModalidade = to.getFotoModalidade();
        nivel = to.getNivel() == null ? "" : to.getNivel();

        vigencia = Uteis.getData(to.getVigencia(), "dd/MM/yyyy");
        integracaoSpivi = to.isIntegracaoSpivi();
        alunoEstaNaAula = to.isAlunoEstaNaAula();
        linkVideos = to.getLinkVideos();
    }

    public String getUrlIcone() {
        return urlIcone;
    }

    public void setUrlIcone(String urlIcone) {
        this.urlIcone = urlIcone;
    }
    
    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNomeEmpresa() {
        return nomeEmpresa;
    }

    public void setNomeEmpresa(String nomeEmpresa) {
        this.nomeEmpresa = nomeEmpresa;
    }

    public String getLogoEmpresa() { return logoEmpresa; }

    public void setLogoEmpresa(String logoEmpresa) { this.logoEmpresa = logoEmpresa; }

    public String getInicio() {
        return inicio;
    }

    public void setInicio(String inicio) {
        this.inicio = inicio;
    }

    public String getFim() {
        return fim;
    }

    public void setFim(String fim) {
        this.fim = fim;
    }

    public String getModalidade() {
        return modalidade;
    }

    public void setModalidade(String modalidade) {
        this.modalidade = modalidade;
    }

    public String getProfessor() {
        return professor;
    }

    public void setProfessor(String professor) {
        this.professor = professor;
    }

    public String getAmbiente() {
        return ambiente;
    }

    public void setAmbiente(String ambiente) {
        this.ambiente = ambiente;
    }

    public Integer getPontos() {
        return pontos;
    }

    public void setPontos(Integer pontos) {
        this.pontos = pontos;
    }

    public Integer getCapacidade() {
        return capacidade;
    }

    public void setCapacidade(Integer capacidade) {
        this.capacidade = capacidade;
    }

    public Integer getOcupacao() {
        return ocupacao;
    }

    public void setOcupacao(Integer ocupacao) {
        this.ocupacao = ocupacao;
    }

    public boolean isRestamVagas() {
        return restamVagas;
    }

    public void setRestamVagas(boolean restamVagas) {
        this.restamVagas = restamVagas;
    }

    public String getDia() {
        return dia;
    }

    public void setDia(String dia) {
        this.dia = dia;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getDiaSemana() {
        return diaSemana;
    }

    public void setDiaSemana(String diaSemana) {
        this.diaSemana = diaSemana;
    }

    public boolean isJaMarcouEuQuero() {
        return jaMarcouEuQuero;
    }

    public void setJaMarcouEuQuero(boolean jaMarcouEuQuero) {
        this.jaMarcouEuQuero = jaMarcouEuQuero;
    }

    public Date getDiaDate() {
        return diaDate;
    }

    public void setDiaDate(Date diaDate) {
        this.diaDate = diaDate;
    }

    public Date getDatalancamento() { return datalancamento; }

    public void setDatalancamento(Date datalancamento) { this.datalancamento = datalancamento; }

    public Integer getVagasRestantes() {
        return vagasRestantes;
    }

    public void setVagasRestantes(Integer vagasRestantes) {
        this.vagasRestantes = vagasRestantes;
    }

    public boolean isAlunoEstaNaAula() {
        return alunoEstaNaAula;
    }

    public void setAlunoEstaNaAula(boolean alunoEstaNaAula) {
        this.alunoEstaNaAula = alunoEstaNaAula;
    }

    public String getCodDia(){
        try {
            return UteisValidacao.emptyString(getDia()) ? "" : (codigo + "_"+getDia());
        }catch (Exception e){
            return "";
        }
    }

    public String getNivel() {
        return nivel;
    }

    public void setNivel(String nivel) {
        this.nivel = nivel;
    }

    public String getCorModalidade() {
        return corModalidade;
    }

    public void setCorModalidade(String corModalidade) {
        this.corModalidade = corModalidade;
    }

    public String getFotoProfessor() {
        return fotoProfessor;
    }

    public void setFotoProfessor(String fotoProfessor) {
        this.fotoProfessor = fotoProfessor;
    }

    public String getFotoModalidade() {
        return fotoModalidade;
    }

    public void setFotoModalidade(String fotoModalidade) {
        this.fotoModalidade = fotoModalidade;
    }

    public boolean isColetiva() {
        return coletiva;
    }

    public void setColetiva(boolean coletiva) {
        this.coletiva = coletiva;
    }

    public Integer getCodigoAulaOrigemReposicao() {
        return codigoAulaOrigemReposicao;
    }

    public void setCodigoAulaOrigemReposicao(Integer codigoAulaOrigemReposicao) {
        this.codigoAulaOrigemReposicao = codigoAulaOrigemReposicao;
    }

    public String getVigencia() {
        return vigencia;
    }

    public void setVigencia(String vigencia) {
        this.vigencia = vigencia;
    }

    public Boolean getIntegracaoSpivi() {
        return integracaoSpivi;
    }

    public void setIntegracaoSpivi(Boolean integracaoSpivi) {
        this.integracaoSpivi = integracaoSpivi;
    }

    public String getUrlVideoYoutube() {
        return urlVideoYoutube;
    }

    public void setUrlVideoYoutube(String urlVideoYoutube) {
        this.urlVideoYoutube = urlVideoYoutube;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public List<TurmaVideoDTO> getLinkVideos() {
        return linkVideos;
    }

    public void setLinkVideos(List<TurmaVideoDTO> linkVideos) {
        this.linkVideos = linkVideos;
    }

    public String getTipoReservaEquipamento() {
        return tipoReservaEquipamento;
    }

    public void setTipoReservaEquipamento(String tipoReservaEquipamento) {
        this.tipoReservaEquipamento = tipoReservaEquipamento;
    }

    public String getEquipamentosOcupados() {
        return equipamentosOcupados;
    }

    public void setEquipamentosOcupados(String equipamentosOcupados) {
        this.equipamentosOcupados = equipamentosOcupados;
    }

    public String getMapaEquipamentos() {
        return mapaEquipamentos;
    }

    public void setMapaEquipamentos(String mapaEquipamentos) {
        this.mapaEquipamentos = mapaEquipamentos;
    }

    public List<MapaEquipamentoAparelhoDTO> getListaMapaEquipamentoAparelho() {
        return listaMapaEquipamentoAparelho;
    }

    public void setListaMapaEquipamentoAparelho(List<MapaEquipamentoAparelhoDTO> listaMapaEquipamentoAparelho) {
        this.listaMapaEquipamentoAparelho = listaMapaEquipamentoAparelho;
    }

    public Boolean getApresentarEquipamentoPorNumeracao() {
        if (apresentarEquipamentoPorNumeracao == null) {
            return false;
        }
        return apresentarEquipamentoPorNumeracao;
    }

    public void setApresentarEquipamentoPorNumeracao(Boolean apresentarEquipamentoPorNumeracao) {
        this.apresentarEquipamentoPorNumeracao = apresentarEquipamentoPorNumeracao;
    }

}
