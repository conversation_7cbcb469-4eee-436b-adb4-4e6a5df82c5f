package br.com.pacto.controller.json.parceiros;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Created by <PERSON><PERSON> on 26/09/2018.
 */

@ApiModel(description = "Dados do parceiro para criação ou atualização")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ParceiroTO {

    @ApiModelProperty(value = "Identificador único do parceiro. Deve ser nulo para criação ou preenchido para atualização", example = "1")
    private Integer id;

    @ApiModelProperty(value = "Nome do parceiro", example = "Academia Fitness Plus", required = true)
    private String nome;

    @ApiModelProperty(value = "Dados da imagem do parceiro codificados em Base64", example = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==")
    private String dadosImagem;

    @ApiModelProperty(value = "Tipo/extensão da imagem do parceiro", example = "png")
    private String tipoImagem;

    @ApiModelProperty(value = "Situação do parceiro no sistema (ativo/inativo)", example = "true", required = true)
    private Boolean situacao;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getDadosImagem() {
        return dadosImagem;
    }

    public void setDadosImagem(String dadosImagem) {
        this.dadosImagem = dadosImagem;
    }

    public Boolean getSituacao() {
        return situacao;
    }

    public void setSituacao(Boolean situacao) {
        this.situacao = situacao;
    }

    public String getTipoImagem() {
        return tipoImagem;
    }

    public void setTipoImagem(String tipoImagem) {
        this.tipoImagem = tipoImagem;
    }
}
