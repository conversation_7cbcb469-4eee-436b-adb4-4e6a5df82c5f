/*
 * To change this template, choose <PERSON><PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.agendamento;

import br.com.pacto.bean.Agendamento;
import br.com.pacto.bean.configuracoes.ConfiguracaoSistema;
import br.com.pacto.bean.configuracoes.ConfiguracoesEnum;
import br.com.pacto.bean.notificacao.TipoNotificacaoEnum;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.enumerador.agenda.StatusAgendamentoEnum;
import br.com.pacto.enumerador.agenda.TipoDuracaoEvento;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.agenda.AgendaService;
import br.com.pacto.service.intf.agenda.AgendamentoService;
import br.com.pacto.service.intf.cliente.ClienteSinteticoService;
import br.com.pacto.service.intf.configuracoes.ConfiguracaoSistemaService;
import br.com.pacto.service.intf.memcached.CachedManagerInterfaceFacade;
import br.com.pacto.service.intf.notificacao.NotificacaoService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.swagger.respostas.agendamento.*;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.impl.UtilReflection;
import io.swagger.annotations.*;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import servicos.integracao.zw.IntegracaoTurmasWSConsumer;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/agendamento")
public class AgendamentoJSONControle extends SuperControle {

    @Autowired
    private AgendamentoService service;
    @Autowired
    private UsuarioService usuarioService;
    @Autowired
    private NotificacaoService notificacaoService;
    @Autowired
    private ClienteSinteticoService cs;
    @Autowired
    private ConfiguracaoSistemaService css;
    @Autowired
    private AgendaService agendaService;

    @ApiOperation(value = "Buscar agendamentos do aluno",
                  notes = "Retorna os agendamentos abertos e concluídos do aluno no período de um mês anterior e posterior à data atual. " +
                          "Permite busca por nome de usuário ou matrícula. " +
                          "Agendamentos são separados em duas listas: abertos (aguardando confirmação, confirmados ou reagendados) e concluídos (demais status).",
                  tags = "Agendamento")
    @ApiResponses({
        @ApiResponse(code = 200, message = "Agendamentos encontrados com sucesso", response = ExemploRespostaAgendamentosTodos.class)
    })
    @RequestMapping(value = "{ctx}/todos", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap todos(@PathVariable @ApiParam(value = "Contexto da empresa", required = true, defaultValue = "academia") String ctx,
            @RequestParam @ApiParam(value = "Nome de usuário do aluno", required = true, defaultValue = "joao.silva") String username,
            @RequestParam(required = false) @ApiParam(value = "Matrícula do aluno (opcional, se não informado será usado o username)", defaultValue = "12345") String matricula
                   ) {
        ModelMap mm = new ModelMap();
        try {
            //super.init(ctx, token);
            Usuario usuario;
            if(UteisValidacao.emptyString(matricula)){
                usuario = usuarioService.obterPorAtributo(ctx, "username", username);
            } else {
                usuario = usuarioService.consultarPorMatricula(ctx, Integer.valueOf(matricula));
            }
            if (usuario != null) {
                List<AgendamentoJSON> jSONAbertos = new ArrayList<AgendamentoJSON>();
                List<AgendamentoJSON> jSONConcluidos = new ArrayList<AgendamentoJSON>();
                List<Agendamento> lista = service.consultarPorData(ctx,
                        Calendario.anterior(Calendar.MONTH, Calendario.hoje()),
                        Calendario.proximo(Calendar.MONTH, Calendario.hoje()), null, null, null, "0,1,2",
                        usuario.getCliente().getCodigo(), false, false, null, null, null, null);
                for (Agendamento agendamento : lista) {
                    String nomeAgendamento = agendamento.getTipoEvento() != null ? agendamento.getTipoEvento().getNome() : agendamento.getHorarioDisponibilidade().getDisponibilidade().getNome();
                    if(!UteisValidacao.emptyString(agendamento.getNomeProfessor())) {
                        nomeAgendamento += " com " + agendamento.getNomeProfessor();
                    }
                    AgendamentoJSON json = new AgendamentoJSON(agendamento.getCodigo(),
                            Calendario.getData(agendamento.getInicio(), Calendario.MASC_DATAHORA),
                            Calendario.getHora(agendamento.getInicio(), Calendario.MASC_HORA),
                            Calendario.getHora(agendamento.getFim(), Calendario.MASC_HORA),
                            nomeAgendamento,
                            agendamento.getStatus().getDescricao(), agendamento.getNomeProfessor(),
                            agendamento.getStatus().getCor(), agendamento.getInicio().getTime());
                    if (agendamento.getStatus() == StatusAgendamentoEnum.AGUARDANDO_CONFIRMACAO
                            || agendamento.getStatus() == StatusAgendamentoEnum.CONFIRMADO
                            || agendamento.getStatus() == StatusAgendamentoEnum.REAGENDADO) {
                        jSONAbertos.add(json);
                    } else {
                        jSONConcluidos.add(json);
                    }
                }
                if (!jSONAbertos.isEmpty() || !jSONConcluidos.isEmpty()) {
                    mm.addAttribute("agendamentosAbertos", jSONAbertos);
                    mm.addAttribute("agendamentosConcluidos", jSONConcluidos);
                } else {
                    throw new ServiceException(getViewUtils().getMensagem("mobile.nenhumagendamento"));
                }
            } else {
                throw new ServiceException(getViewUtils().getMensagem("mobile.usuarioinvalido"));
            }
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(AgendamentoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(value = "Buscar histórico de agendamentos do cliente",
                  notes = "Retorna o histórico completo de agendamentos do cliente com paginação. " +
                          "Busca agendamentos desde 1900 até um ano no futuro, ordenados por data de início decrescente. " +
                          "Retorna apenas agendamentos em status aberto (aguardando confirmação, confirmados ou reagendados).",
                  tags = "Agendamento")
    @ApiResponses({
        @ApiResponse(code = 200, message = "Histórico de agendamentos encontrado com sucesso", response = ExemploRespostaHistoricoAgendamentos.class)
    })
    @RequestMapping(value = "{ctx}/historico", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap historico(@PathVariable @ApiParam(value = "Contexto da empresa", required = true, defaultValue = "academia") String ctx,
            @RequestParam @ApiParam(value = "Código do cliente", required = true, defaultValue = "123") final Integer codigoCliente,
            @RequestParam @ApiParam(value = "Quantidade máxima de resultados por página", required = true, defaultValue = "10") final Integer maxResults,
            @RequestParam @ApiParam(value = "Índice da página (para paginação)", required = true, defaultValue = "0") final Integer index) {
        ModelMap mm = new ModelMap();
        try {
            //super.init(ctx, token);
            if (codigoCliente != null && codigoCliente > 0) {
                List<AgendamentoJSON> arrJson = new ArrayList<AgendamentoJSON>();
                List<Agendamento> lista = service.consultarPorData(ctx,
                        Calendario.getDate(Calendario.MASC_DATA, "01/01/1900"),
                        Calendario.proximo(Calendar.YEAR, Calendario.hoje()), null, null, null, "0,1,2",
                        codigoCliente, false, false,  "obj.inicio DESC", index, maxResults, null);
                for (Agendamento agendamento : lista) {
                    AgendamentoJSON json = new AgendamentoJSON(agendamento.getCodigo(),
                            Calendario.getData(agendamento.getInicio(), Calendario.MASC_DATAHORA),
                            Calendario.getHora(agendamento.getInicio(), Calendario.MASC_HORA),
                            Calendario.getHora(agendamento.getFim(), Calendario.MASC_HORA),
                            agendamento.getTipoEvento().getNome(),
                            agendamento.getStatus().getDescricao(), agendamento.getNomeProfessor(),
                            agendamento.getStatus().getCor(), agendamento.getInicio().getTime());
                    if (agendamento.getStatus() == StatusAgendamentoEnum.AGUARDANDO_CONFIRMACAO
                            || agendamento.getStatus() == StatusAgendamentoEnum.CONFIRMADO
                            || agendamento.getStatus() == StatusAgendamentoEnum.REAGENDADO) {
                        arrJson.add(json);
                    }
                }
                mm.addAttribute(RETURN, arrJson);
            } else {
                throw new ServiceException(getViewUtils().getMensagem("mobile.usuarioinvalido"));
            }
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(AgendamentoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(value = "Buscar horários disponíveis para reagendamento (30 dias)",
                  notes = "Retorna os horários disponíveis para reagendamento de um agendamento específico nos próximos 30 dias a partir da data inicial informada. " +
                          "Para cada dia que possui horários disponíveis, retorna a data e a lista de horários. " +
                          "Permite busca por nome de usuário ou matrícula.",
                  tags = "Agendamento")
    @ApiResponses({
        @ApiResponse(code = 200, message = "Horários disponíveis encontrados com sucesso", response = ExemploRespostaAgendadosAppAluno.class)
    })
    @RequestMapping(value = "{ctx}/agendadosAppAluno", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap agendadosAppAluno(@PathVariable @ApiParam(value = "Contexto da empresa", required = true, defaultValue = "academia") String ctx,
                               @RequestParam @ApiParam(value = "Nome de usuário do aluno", required = true, defaultValue = "joao.silva") final String username,
                               @RequestParam @ApiParam(value = "ID do agendamento que será reagendado", required = true, defaultValue = "456") final String idAgendamento,
                               @RequestParam @ApiParam(value = "Data inicial para busca (formato dd/MM/yyyy)", required = true, defaultValue = "15/12/2024") final String diaInicial,
                               @RequestParam(required = false) @ApiParam(value = "Matrícula do aluno (opcional, se não informado será usado o username)", defaultValue = "12345") final String matricula
                               ) {
        ModelMap mm = new ModelMap();
        try {
            Date dataInicial = Calendario.getDate(Calendario.MASC_DATA, diaInicial);
            List<Date> periodo = Uteis.getDiasEntreDatas(dataInicial, Uteis.somarDias(dataInicial, 30));

            JSONArray array = new JSONArray();
            for (Date dia : periodo) {
                String diaApresentar = Uteis.getDataAplicandoFormatacao(dia, Calendario.MASC_DATA);

                ModelMap retorno = agendados(ctx, username, idAgendamento, diaApresentar, matricula);
                JSONObject horarioJson = new JSONObject(retorno);
                JSONObject diaJson = new JSONObject();

                List horarios = Uteis.toList(horarioJson.getJSONArray("horarios"));
                diaJson.put("dia", diaApresentar);
                diaJson.put("horarios", horarios);
                if (horarios.size() > 0) {
                    array.put(diaJson);
                }
            }

            return mm.addAttribute(RETURN, Uteis.toList(array));
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(AgendamentoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(value = "Buscar horários disponíveis para reagendamento em um dia específico",
                  notes = "Retorna os horários disponíveis para reagendamento de uma atividade específica em um dia determinado. " +
                          "Verifica a disponibilidade do professor e os horários livres, considerando a duração da atividade e eventos concomitantes. " +
                          "Permite busca por nome de usuário ou matrícula.",
                  tags = "Agendamento")
    @ApiResponses({
        @ApiResponse(code = 200, message = "Horários disponíveis encontrados com sucesso", response = ExemploRespostaHorariosDisponiveis.class)
    })
    @RequestMapping(value = "{ctx}/agendados", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap agendados(@PathVariable @ApiParam(value = "Contexto da empresa", required = true, defaultValue = "academia") String ctx,
                       @RequestParam @ApiParam(value = "Nome de usuário do aluno", required = true, defaultValue = "joao.silva") final String username,
                       @RequestParam @ApiParam(value = "ID da atividade/agendamento que será reagendado", required = true, defaultValue = "456") final String idAtividade,
                       @RequestParam @ApiParam(value = "Data para busca de horários (formato dd/MM/yyyy)", required = true, defaultValue = "15/12/2024") final String dia,
                       @RequestParam(required = false) @ApiParam(value = "Matrícula do aluno (opcional, se não informado será usado o username)", defaultValue = "12345") final String matricula
    ) {
        ModelMap mm = new ModelMap();
        try {
            //super.init(ctx, token);
            Usuario usuario;
            if(UteisValidacao.emptyString(matricula)){
                usuario = usuarioService.obterPorAtributo(ctx, "username", username);
            } else {
                usuario = usuarioService.consultarPorMatricula(ctx, Integer.valueOf(matricula));
            }
            if (usuario != null) {
                Agendamento agendamentoAnterior = service.obterPorId(ctx, Integer.valueOf(idAtividade));
                if (agendamentoAnterior != null) {
                    List<Agendamento> disponibilidades = service.disponibilidadeProfessor(ctx,
                            agendamentoAnterior.getProfessor().getCodigo(), Calendario.getDate(Calendario.MASC_DATA, dia));
                    if (disponibilidades != null) {
                        Integer duracao = (agendamentoAnterior.getTipoEvento() == null
                                || agendamentoAnterior.getTipoEvento().getDuracao() == null
                                || agendamentoAnterior.getTipoEvento().getDuracao().equals(TipoDuracaoEvento.DURACAO_LIVRE)
                                || agendamentoAnterior.getTipoEvento().getDuracaoMinutosMin() == 0)
                                ? 60 : agendamentoAnterior.getTipoEvento().getDuracaoMinutosMin();
                        List<String> horarios = new ArrayList<String>();
                        for (Agendamento disp : disponibilidades) {
                            if (agendamentoAnterior.getTipoEvento().equals(disp.getTipoEvento())) {
                                Date inicio = disp.getInicio();
                                Date fim = Uteis.somarCampoData(inicio, Calendar.MINUTE, duracao);
                                HORAS:
                                do {
                                    Agendamento agendamento = agendamentoAnterior.getClone(inicio, fim, true);
                                    List<Agendamento> concomitantes = service.verificarEventosConcomitantes(ctx, agendamento.getInicio(),
                                            agendamento.getFim(), agendamento.getProfessor().getCodigo(), agendamento.getCodigo());

                                    if (!concomitantes.isEmpty()) {
                                        for (Agendamento conc : concomitantes) {
                                            if (conc.getInicio().getTime() == agendamento.getFim().getTime()) {
                                                agendamento.setFim(Uteis.somarCampoData(agendamento.getFim(), Calendar.MINUTE, -1));
                                            } else if (conc.getFim().getTime() == agendamento.getInicio().getTime()) {
                                                agendamento.setInicio(Uteis.somarCampoData(agendamento.getInicio(), Calendar.MINUTE, 1));
                                            } else {
                                                inicio = conc.getFim();
                                                fim = Uteis.somarCampoData(inicio, Calendar.MINUTE, duracao);
                                                continue HORAS;
                                            }
                                        }
                                    }
                                    horarios.add(String.format("%s - %s", new Object[]{
                                        Calendario.getData(agendamento.getInicio(), Calendario.MASC_HORA),
                                        Calendario.getData(agendamento.getFim(), Calendario.MASC_HORA)
                                    }));
                                    inicio = fim;
                                    fim = Uteis.somarCampoData(inicio, Calendar.MINUTE, duracao);
                                } while (fim.before(disp.getFim()) || fim.equals(disp.getFim()));
                            }
                        }
                        mm.addAttribute("horarios", horarios);
                    } else {
                        throw new ServiceException(getViewUtils().getMensagem("validacao.agenda.disponibilidade"));
                    }
                }
            } else {
                throw new ServiceException(getViewUtils().getMensagem("mobile.usuarioinvalido"));
            }
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(AgendamentoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(value = "Reagendar um agendamento",
                  notes = "Realiza o reagendamento de uma atividade para uma nova data e horário. " +
                          "Valida se o aluno não possui outros compromissos no horário escolhido e envia notificações. " +
                          "Grava log da alteração e atualiza o status do agendamento. " +
                          "Permite busca por nome de usuário ou matrícula.",
                  tags = "Agendamento")
    @ApiResponses({
        @ApiResponse(code = 200, message = "Reagendamento realizado com sucesso", response = ExemploRespostaReagendamento.class)
    })
    @RequestMapping(value = "{ctx}/submit", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap submit(@PathVariable @ApiParam(value = "Contexto da empresa", required = true, defaultValue = "academia") String ctx,
            @RequestParam @ApiParam(value = "Nome de usuário do aluno", required = true, defaultValue = "joao.silva") final String username,
            @RequestParam @ApiParam(value = "ID da atividade/agendamento que será reagendado", required = true, defaultValue = "456") final String idAtividade,
            @RequestParam @ApiParam(value = "Nova data para o agendamento (formato dd/MM/yyyy)", required = true, defaultValue = "15/12/2024") final String dia,
            @RequestParam @ApiParam(value = "Novo horário para o agendamento (formato HH:mm - HH:mm)", required = true, defaultValue = "14:00 - 15:00") final String horario,
            @RequestParam(required = false) @ApiParam(value = "Matrícula do aluno (opcional, se não informado será usado o username)", defaultValue = "12345") final Integer matricula) {
        ModelMap mm = new ModelMap();
        try {
            Usuario usuario;
            if(matricula == null){
                usuario = usuarioService.obterPorAtributo(ctx, "username", username);
            }else{
                usuario = usuarioService.consultarPorMatricula(ctx, matricula);
            }

            if (usuario != null) {
                Agendamento agendamentoAnterior = service.obterPorId(ctx, Integer.valueOf(idAtividade));
                Agendamento antes = UtilReflection.copy(agendamentoAnterior);
                if (agendamentoAnterior != null) {
                    notificacaoService.notificarSolicitacaoReagendamento(ctx, agendamentoAnterior, dia, horario,
                            TipoNotificacaoEnum.ALUNO_AGENDOU);
                } else {
                    throw new ServiceException(getViewUtils().getMensagem("mobile.nenhumagendamento"));
                }
                Date diaDate = Calendario.getDate(Calendario.MASC_DATA, dia);
                String[] horarios = horario.split("\\-");

                String horaInicio = horarios[0].trim() + ":00";
                String horaFim =horarios[1].trim() + ":00";
                Date inicio = Calendario.getDataComHora(diaDate, horarios[0].trim() + ":00");
                Date fim =Calendario.getDataComHora(diaDate, horarios[1].trim() + ":00");

                ConfiguracaoSistema cfTipo = css.consultarPorTipo(ctx, ConfiguracoesEnum.VALIDAR_AGENDA_AULACHEIA);
                if (cfTipo.getValorAsBoolean()) {
                    IntegracaoTurmasWSConsumer integracaoWS = UtilContext.getBean(IntegracaoTurmasWSConsumer.class);
                    final String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg);
                    Calendar dataInicio = Calendar.getInstance();
                    dataInicio.setTime(inicio);
                    String dados = integracaoWS.consultarAulaPeloClienteHorario(ctx, agendamentoAnterior.getCliente().getMatriculaString(), Calendario.getDataComHoraZerada(inicio), horaInicio, horaFim);
                    if (!dados.contains("ERRO")) {
                        JSONArray jsonArray = new JSONArray(dados);
                        if (jsonArray.length() > 0) {
                            StringBuilder retorno = new StringBuilder("");
                            for (int i = 0; i < jsonArray.length(); i++) {
                                JSONObject jObj = jsonArray.optJSONObject(i);
                                if (retorno.toString().isEmpty()) {
                                    retorno.append(jObj.getString("identificadorturma"));
                                }else{
                                    retorno.append(",").append(jObj.getString("identificadorturma"));
                                }
                            }
                            throw new Exception("Você já possui compromissos nesse horário: "+retorno);
                        }
                    }else{
                        throw new Exception(dados);
                    }
                }

                agendamentoAnterior.setHoraInicio(horaInicio);
                agendamentoAnterior.setHoraFim(horaFim);
                agendamentoAnterior.setInicio(inicio);
                agendamentoAnterior.setFim(fim);

                Agendamento depois = service.alterarTodasEmpresas(ctx, agendamentoAnterior, usuario, diaDate);
                service.gravarLogAlteracao(ctx, usuario == null ? "-" : usuario.getUserName(), antes, depois);

                mm.addAttribute(STATUS, getViewUtils().getMensagem("mobile.reagendamentoRealizado"));
            } else {
                throw new ServiceException(getViewUtils().getMensagem("mobile.usuarioinvalido"));
            }
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, getViewUtils().getMensagem(ex.getMessage()));
            Logger.getLogger(AgendamentoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(value = "Confirmar um agendamento",
                  notes = "Confirma um agendamento que estava aguardando confirmação, alterando seu status para confirmado. " +
                          "Envia notificações sobre a confirmação do agendamento. " +
                          "Permite busca por nome de usuário ou matrícula.",
                  tags = "Agendamento")
    @ApiResponses({
        @ApiResponse(code = 200, message = "Agendamento confirmado com sucesso", response = ExemploRespostaConfirmacao.class)
    })
    @RequestMapping(value = "{ctx}/confirma", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap confirmar(@PathVariable @ApiParam(value = "Contexto da empresa", required = true, defaultValue = "academia") String ctx,
            @RequestParam @ApiParam(value = "Nome de usuário do aluno", required = true, defaultValue = "joao.silva") final String username,
            @RequestParam @ApiParam(value = "ID do agendamento que será confirmado", required = true, defaultValue = "456") final String idAgendamento,
            @RequestParam(required = false) @ApiParam(value = "Matrícula do aluno (opcional, se não informado será usado o username)", defaultValue = "12345") final Integer matricula) {
        ModelMap mm = new ModelMap();
        try {
            //super.init(ctx, token);
            Usuario usuario;
            if(matricula == null){
                usuario = usuarioService.obterPorAtributo(ctx, "username", username);
            }else{
                usuario = usuarioService.consultarPorMatricula(ctx, matricula);
            }
            if (usuario != null) {
                Agendamento agendamento = service.obterPorId(ctx, Integer.valueOf(idAgendamento));
                if (agendamento != null
                        && agendamento.getStatus() == StatusAgendamentoEnum.AGUARDANDO_CONFIRMACAO) {
                    service.alterarStatus(ctx, agendamento, StatusAgendamentoEnum.CONFIRMADO, "");
                    notificacaoService.notificarStatusAgendamento(ctx, agendamento,
                            TipoNotificacaoEnum.AGENDAMENTO_CONFIRMADO,
                            "agendamento.confirmado");
                } else {
                    throw new ServiceException(getViewUtils().getMensagem("mobile.nenhumagendamento"));
                }
                mm.addAttribute(STATUS, getViewUtils().getMensagem("mobile.agendamentoConfirmado"));
            } else {
                throw new ServiceException(getViewUtils().getMensagem("mobile.usuarioinvalido"));
            }
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(AgendamentoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(value = "Cancelar um agendamento",
                  notes = "Cancela um agendamento que estava aguardando confirmação ou confirmado, alterando seu status para cancelado. " +
                          "Grava log do cancelamento e envia notificações sobre o cancelamento. " +
                          "Permite busca por nome de usuário ou matrícula.",
                  tags = "Agendamento")
    @ApiResponses({
        @ApiResponse(code = 200, message = "Agendamento cancelado com sucesso", response = ExemploRespostaCancelamento.class)
    })
    @RequestMapping(value = "{ctx}/cancelar", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap cancelar(@PathVariable @ApiParam(value = "Contexto da empresa", required = true, defaultValue = "academia") String ctx,
            @RequestParam @ApiParam(value = "Nome de usuário do aluno", required = true, defaultValue = "joao.silva") final String username,
            @RequestParam @ApiParam(value = "ID do agendamento que será cancelado", required = true, defaultValue = "456") final String idAgendamento,
            @RequestParam(required = false) @ApiParam(value = "Matrícula do aluno (opcional, se não informado será usado o username)", defaultValue = "12345") final Integer matricula) {
        ModelMap mm = new ModelMap();
        try {
            //super.init(ctx, token);
            super.prepare(ctx);
            Usuario usuario;
            if(matricula == null){
                usuario = usuarioService.obterPorAtributo(ctx, "username", username);
            }else{
                usuario = usuarioService.consultarPorMatricula(ctx, matricula);
            }
            if (usuario != null) {
                Agendamento agendamento = service.obterPorId(ctx, Integer.valueOf(idAgendamento));
                Agendamento antes = UtilReflection.copy(agendamento);
                if (agendamento != null
                        && (agendamento.getStatus() == StatusAgendamentoEnum.AGUARDANDO_CONFIRMACAO
                        || agendamento.getStatus() == StatusAgendamentoEnum.CONFIRMADO)) {
                    Agendamento depois = service.alterarStatus(ctx, agendamento, StatusAgendamentoEnum.CANCELADO, "Aplicativo");
                    service.gravarLogCancelamento(ctx,
                            usuario.getCliente() == null ? usuario.getUserName() :
                                    (usuario.getCliente().getNome() + " (aluno)"), antes, depois);
                    notificacaoService.notificarStatusAgendamento(ctx, agendamento, TipoNotificacaoEnum.AGENDAMENTO_CANCELADO,
                            "agendamento.cancelado");
                } else {
                    throw new ServiceException(getViewUtils().getMensagem("mobile.nenhumagendamento"));
                }

                mm.addAttribute(STATUS, getViewUtils().getMensagem("mobile.agendamentoCancelado"));
            } else {
                throw new ServiceException(getViewUtils().getMensagem("mobile.usuarioinvalido"));
            }
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(AgendamentoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(value = "Incluir equipamento no horário do aluno",
                  notes = "Inclui um equipamento específico no horário de aula do aluno. " +
                          "Valida se o equipamento está disponível no horário e se o aluno pode utilizá-lo. " +
                          "Não disponível para clientes de Treino Independente.",
                  tags = "Agendamento")
    @ApiResponses({
        @ApiResponse(code = 200, message = "Equipamento incluído com sucesso", response = ExemploRespostaIncluirEquipamento.class)
    })
    @RequestMapping(value = "{ctx}/equipamento/{horario-turma-id}/{dia}", method = RequestMethod.POST)
    public @ResponseBody ModelMap incluirHorarioEquipamentoAluno(@PathVariable @ApiParam(value = "Contexto da empresa", required = true, defaultValue = "academia") String ctx,
                                                                 @RequestHeader("empresaId") @ApiParam(value = "ID da empresa", required = true, defaultValue = "1") Integer empresaId,
                                                                 @PathVariable("horario-turma-id") @ApiParam(value = "ID do horário da turma/aula", required = true, defaultValue = "123") Integer aulaHorarioId,
                                                                 @PathVariable("dia") @ApiParam(value = "Data da aula (formato dd/MM/yyyy)", required = true, defaultValue = "15/12/2024") String dia,
                                                                 @RequestParam("equipamento") @ApiParam(value = "Nome ou código do equipamento", required = true, defaultValue = "Esteira 01") String equipamento,
                                                                 @RequestParam("usuarioId") @ApiParam(value = "ID do usuário/aluno", required = true, defaultValue = "456") Integer usuarioId,
                                                                 HttpServletRequest request) {
        ModelMap mm = new ModelMap();
        try {
            if(SuperControle.independente(ctx)) {
                throw new ServiceException("Operação não permitida para clientes de Treino Independente");
            }
            if (!UteisValidacao.emptyString(equipamento) && equipamento.startsWith(";")) {
                equipamento = equipamento.split(";")[1];
            }
            Usuario u = usuarioService.obterPorId(ctx, usuarioId, true);
            agendaService.validarHorarioEquipamento(ctx, empresaId, aulaHorarioId, equipamento, u, dia);
            agendaService.incluirHorarioEquipamentoAluno(ctx, empresaId, aulaHorarioId, u.getCliente().getCodigoCliente(), equipamento, usuarioId, dia, true);
            mm.addAttribute(STATUS, "Equipamento incluído com sucesso");
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(AgendamentoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(value = "Remover equipamento do horário do aluno",
                  notes = "Remove um equipamento específico do horário de aula do aluno. " +
                          "Remove a reserva do equipamento se ela existir para o horário e aluno especificados. " +
                          "Não disponível para clientes de Treino Independente.",
                  tags = "Agendamento")
    @ApiResponses({
        @ApiResponse(code = 200, message = "Equipamento removido com sucesso", response = ExemploRespostaRemoverEquipamento.class)
    })
    @RequestMapping(value = "{ctx}/equipamento/{horario-turma-id}/{dia}", method = RequestMethod.DELETE)
    public @ResponseBody ModelMap deletarHorarioEquipamentoAluno(@PathVariable @ApiParam(value = "Contexto da empresa", required = true, defaultValue = "academia") String ctx,
                                                                 @RequestHeader("empresaId") @ApiParam(value = "ID da empresa", required = true, defaultValue = "1") Integer empresaId,
                                                                 @PathVariable("horario-turma-id") @ApiParam(value = "ID do horário da turma/aula", required = true, defaultValue = "123") Integer aulaHorarioId,
                                                                 @PathVariable("dia") @ApiParam(value = "Data da aula (formato yyyyMMdd)", required = true, defaultValue = "20241215") String dia,
                                                                 @RequestParam("usuarioId") @ApiParam(value = "ID do usuário/aluno", required = true, defaultValue = "456") Integer usuarioId,
                                                                 @RequestParam("equipamento") @ApiParam(value = "Nome ou código do equipamento", required = true, defaultValue = "Esteira 01") String equipamento,
                                                                 HttpServletRequest request) {
        ModelMap mm = new ModelMap();
        try {
            if(SuperControle.independente(ctx)) {
                throw new ServiceException("Operação não permitida para clientes de Treino Independente");
            }
            Usuario u = usuarioService.obterPorId(ctx, usuarioId, true);
            agendaService.deletarHorarioEquipamentoAlunoSeExistir(ctx, empresaId, aulaHorarioId, u.getCliente().getCodigoCliente(), Calendario.getDate("yyyyMMdd", dia), equipamento);
            mm.addAttribute(STATUS, "Equipamento removido com sucesso");
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(AgendamentoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(value = "Buscar agendamentos de avaliação física",
                  notes = "Retorna os agendamentos de avaliação física para um dia específico. " +
                          "Permite filtrar por agendamentos confirmados e aplicar regra de uma vez por ano. " +
                          "Quando 'umaVezAno' e 'confirmado' são verdadeiros, aplica filtro especial para avaliações anuais.",
                  tags = "Agendamento")
    @ApiResponses({
        @ApiResponse(code = 200, message = "Agendamentos de avaliação encontrados com sucesso", response = ExemploRespostaAgendamentoAvaliacao.class)
    })
    @RequestMapping(value = "{ctx}/agendamentoAvaliacao/{dia}/{confirmado}/{umaVezAno}", method = RequestMethod.POST)
    public @ResponseBody ModelMap buscarAgendamentoAvaliacao(@PathVariable @ApiParam(value = "Contexto da empresa", required = true, defaultValue = "academia") String ctx,
                                                                 @PathVariable("dia") @ApiParam(value = "Data para busca (formato dd/MM/yyyy)", required = true, defaultValue = "15/12/2024") String dia,
                                                                 @PathVariable("confirmado") @ApiParam(value = "Filtrar apenas agendamentos confirmados", required = true, defaultValue = "true") Boolean confirmado,
                                                                 @PathVariable("umaVezAno") @ApiParam(value = "Aplicar regra de uma avaliação por ano", required = true, defaultValue = "false") Boolean umaVezAno,
                                                                 @RequestParam("empresaZw") @ApiParam(value = "ID da empresa ZW", required = true, defaultValue = "1") Integer empresaZw,
                                                                 HttpServletRequest request) {
        ModelMap mm = new ModelMap();
        try {
            JSONArray retorno;
            if (umaVezAno && confirmado) {
                retorno = agendaService.obterAgendamentoAvaliacaoDiaConfirmadoUmaVezAno(ctx, dia, empresaZw);
            } else {
                retorno = agendaService.obterAgendamentoAvaliacaoDia(ctx, dia, confirmado, empresaZw);
            }
            mm.addAttribute(STATUS_SUCESSO, retorno.toString());
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(AgendamentoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }
}
