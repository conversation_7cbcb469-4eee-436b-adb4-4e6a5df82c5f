package br.com.pacto.controller.json.musculo;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.musculo.MusculoTO;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.controller.json.nivel.FiltroNivelJSON;
import br.com.pacto.controller.json.nivel.NivelController;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.musculo.MusculoService;
import br.com.pacto.swagger.respostas.musculo.ExemploRespostaListMusculoResponseTO;
import br.com.pacto.swagger.respostas.musculo.ExemploRespostaListMusculoResponseTOPaginacao;
import br.com.pacto.swagger.respostas.musculo.ExemploRespostaMusculoResponseTO;
import br.com.pacto.swagger.respostas.variaveis.ExemploRespostaBoolean;
import io.swagger.annotations.*;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by ulisses on 24/08/2018.
 */
@Controller
@RequestMapping("/psec/musculos")
public class MusculoController {

    private final MusculoService musculoService;

    @Autowired
    public MusculoController(MusculoService musculoService) {
        Assert.notNull(musculoService, "O serviço de Musculo não foi injetado corretamente");
        this.musculoService = musculoService;
    }

    @ApiOperation(
            value = "Consultar todos os músculos",
            notes = "Consulta todos os músculos cadastrados no sistema. " +
                    "Retorna uma lista completa com todos os músculos disponíveis, incluindo seus grupos musculares e atividades associadas.",
            tags = "Programa de Treino"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaListMusculoResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.MUSCULOS)
    @RequestMapping(value = "/all", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarTodos() {
        try {
            return ResponseEntityFactory.ok(musculoService.consultarTodos());
        } catch (ServiceException e) {
            Logger.getLogger(MusculoController.class.getName()).log(Level.SEVERE, "Erro ao consultar todos os músculos", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar músculos com filtros e paginação",
            notes = "Consulta músculos aplicando filtros de busca e paginação. " +
                    "Permite buscar músculos por nome e retorna os resultados paginados com informações dos grupos musculares e atividades associadas.",
            tags = "Programa de Treino"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.<br/> Ordena por um atributo contido no objeto da resposta." +
                    "<strong>Ordens disponíveis</strong>" +
                    "<ul>" +
                    "<li>ASC - Ordena pelo atributo escolhido de forma ascendente</li>" +
                    "<li>DESC - Ordena pelo atributo escolhido de forma descendente</li>" +
                    "</ul>" +
                    "Deve ser informado como atributo,ordem", defaultValue = "nome,asc", paramType = "query", dataType = "string"),
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaListMusculoResponseTOPaginacao.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.MUSCULOS)
    @RequestMapping(value = "", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarMusculos(
            @ApiParam(value = "Filtros de busca, deve ser informado como um JSON.\n\n" +
                    "<strong>É necessário fazer o ENCODE na URL para realizar a requisição com os filtros.</strong>\n\n" +
                    "<strong>Filtros disponíveis:</strong>\n" +
                    "- <strong>quicksearchValue:</strong> Termo de busca para nome do músculo.",
                    defaultValue = "{\"quicksearchValue\":\"Bíceps\"}")
            @RequestParam(value = "filters", required = false) JSONObject filtros,
            @ApiIgnore PaginadorDTO paginadorDTO) throws JSONException {
        try {
            FiltroNivelJSON filtroNivelJSON = new FiltroNivelJSON(filtros);
            return ResponseEntityFactory.ok(musculoService.consultarMusculos(filtroNivelJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(NivelController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os níveis", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar músculo por ID",
            notes = "Consulta um músculo específico através do seu identificador único. " +
                    "Retorna os dados completos do músculo incluindo grupos musculares e atividades associadas.",
            tags = "Programa de Treino"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaMusculoResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.MUSCULOS)
    @RequestMapping(value = "/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarMusculo(
            @ApiParam(value = "ID único do músculo a ser consultado", defaultValue = "1", required = true)
            @PathVariable("id") Integer id) {
        try {
            return ResponseEntityFactory.ok(musculoService.consultarMusculo(id));
        } catch (ServiceException e) {
            Logger.getLogger(MusculoController.class.getName()).log(Level.SEVERE, "Erro ao tentar consultar o músculo", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Incluir novo músculo",
            notes = "Cadastra um novo músculo no sistema. " +
                    "Permite associar o músculo a grupos musculares e atividades específicas através dos IDs fornecidos.",
            tags = "Programa de Treino"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaMusculoResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.MUSCULOS)
    @RequestMapping(value = "", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> incluirMusculo(
            @ApiParam(value = "Dados do músculo a ser cadastrado", required = true)
            @RequestBody MusculoTO musculoTO) {
        try {
            return ResponseEntityFactory.ok(musculoService.inserir(musculoTO));
        } catch (ServiceException e) {
            Logger.getLogger(MusculoController.class.getName()).log(Level.SEVERE, "Erro ao tentar incluir músculo", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ApiOperation(
            value = "Alterar músculo existente",
            notes = "Altera os dados de um músculo existente no sistema. " +
                    "Permite modificar o nome do músculo e suas associações com grupos musculares e atividades.",
            tags = "Programa de Treino"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaMusculoResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.MUSCULOS)
    @RequestMapping(value = "/{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alterarMusculo(
            @ApiParam(value = "ID único do músculo a ser alterado", defaultValue = "1", required = true)
            @PathVariable("id") final Integer id,
            @ApiParam(value = "Dados atualizados do músculo", required = true)
            @RequestBody MusculoTO musculoTO) {
        try {
            return ResponseEntityFactory.ok(musculoService.alterar(id, musculoTO));

        } catch (ServiceException e) {
            Logger.getLogger(MusculoController.class.getName()).log(Level.SEVERE, "Erro ao tentar alterar músculo", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ApiOperation(
            value = "Excluir músculo",
            notes = "Remove um músculo do sistema através do seu identificador único. " +
                    "A exclusão é permanente e remove todas as associações do músculo com grupos musculares e atividades.",
            tags = "Programa de Treino"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaBoolean.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.MUSCULOS)
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> excluirMusculo(
            @ApiParam(value = "ID único do músculo a ser excluído", defaultValue = "1", required = true)
            @PathVariable("id") final Integer id) {
        try {
            musculoService.excluir(id);
            return ResponseEntityFactory.ok(true);
        } catch (ServiceException e) {
            Logger.getLogger(MusculoController.class.getName()).log(Level.SEVERE, "Erro ao tentar excluir músculo", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

}
