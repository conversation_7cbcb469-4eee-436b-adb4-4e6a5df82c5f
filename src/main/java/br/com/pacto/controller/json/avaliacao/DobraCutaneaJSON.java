package br.com.pacto.controller.json.avaliacao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Created by alcides on 21/09/2017.
 */
@ApiModel(description = "Medida de dobra cutânea realizada na avaliação física")
public class DobraCutaneaJSON {

    @ApiModelProperty(value = "Nome da dobra cutânea medida", example = "Tricipital")
    private String nome;

    @ApiModelProperty(value = "Valor da medida da dobra cutânea em milímetros", example = "12.5")
    private Double valor;

    public DobraCutaneaJSON(String nome, Double valor) {
        this.nome = nome;
        this.valor = valor;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }
}
