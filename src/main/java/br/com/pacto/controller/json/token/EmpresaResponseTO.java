package br.com.pacto.controller.json.token;

import br.com.pacto.bean.empresa.Empresa;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Created by <PERSON><PERSON> on 28/09/2018.
 */
@ApiModel(description = "DTO para representar os dados de uma empresa/unidade vinculada ao usuário")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EmpresaResponseTO {

    @ApiModelProperty(value = "Identificador único da empresa/unidade", example = "123")
    private Integer id;

    @ApiModelProperty(value = "Nome da empresa/unidade", example = "Academia Fitness Center - Unidade Centro")
    private String nome;

    public EmpresaResponseTO(Empresa empresa, Boolean treinoIndependente) {
        this.id = treinoIndependente ? empresa.getCodigo() : empresa.getCodZW();
        this.nome = empresa.getNome();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }
}
