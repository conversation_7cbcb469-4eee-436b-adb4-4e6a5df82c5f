package br.com.pacto.controller.json.avaliacao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Created by alcides on 21/09/2017.
 */
@ApiModel(description = "Foto da avaliação física para acompanhamento visual da evolução")
public class FotoAvaliacaoJSON {

    @ApiModelProperty(value = "URL da foto da avaliação física", example = "https://fotos.academia.com/avaliacoes/foto123.jpg")
    private String url;

    @ApiModelProperty(value = "Descrição da foto (posição, ângulo, etc.)", example = "Foto frontal - antes do treino")
    private String descricao;

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}
