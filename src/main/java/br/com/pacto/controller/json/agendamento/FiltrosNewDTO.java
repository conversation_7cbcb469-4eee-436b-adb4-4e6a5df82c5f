package br.com.pacto.controller.json.agendamento;

import br.com.pacto.controller.json.base.SuperJSON;
import br.com.pacto.controller.json.disponibilidade.ItemValidacaoDisponibilidadeDTO;
import br.com.pacto.controller.json.tipoEvento.TipoAgendamentoDTO;
import br.com.pacto.controller.json.tipoEvento.VigenciaDTO;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;
import java.util.Map;

@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Filtros avançados para busca de disponibilidades")
public class FiltrosNewDTO extends SuperJSON {

    @ApiModelProperty(value = "Lista de vigências para filtrar as disponibilidades")
    private List<VigenciaDTO> vigencia;

    @ApiModelProperty(value = "Lista de tipos de agendamento/comportamento para filtrar as disponibilidades")
    private List<TipoAgendamentoDTO> comportamentoFc;

    @ApiModelProperty(value = "Mapa com informações do tipo de validação (id e nome)", example = "{\"id\": 1, \"nome\": \"Plano\"}")
    private Map<Object, Object> tipoValidacaoFc;

    @ApiModelProperty(value = "Lista de itens de validação para filtrar as disponibilidades")
    private List<ItemValidacaoDisponibilidadeDTO> itemValidacaoFc;

    @ApiModelProperty(value = "Filtro para tipo de horário pré-definido", example = "true")
    private Boolean tipoHorarioPreDefinidoFc;

    @ApiModelProperty(value = "Filtro para tipo de horário livre", example = "false")
    private Boolean tipoHorarioLivreFc;

    @ApiModelProperty(value = "Filtro para tipo de horário com intervalo de tempo", example = "false")
    private Boolean tipoHorarioIntervaloTempoFc;

    @ApiModelProperty(value = "Filtro para tipo de horário play", example = "true")
    private Boolean tipoHorarioPlayFc;

    @ApiModelProperty(value = "Filtro para disponibilidades às segundas-feiras", example = "true")
    private Boolean segFc;

    @ApiModelProperty(value = "Filtro para disponibilidades às terças-feiras", example = "false")
    private Boolean terFc;

    @ApiModelProperty(value = "Filtro para disponibilidades às quartas-feiras", example = "true")
    private Boolean quaFc;

    @ApiModelProperty(value = "Filtro para disponibilidades às quintas-feiras", example = "false")
    private Boolean quiFc;

    @ApiModelProperty(value = "Filtro para disponibilidades às sextas-feiras", example = "true")
    private Boolean sexFc;

    @ApiModelProperty(value = "Filtro para disponibilidades aos sábados", example = "false")
    private Boolean sabFc;

    @ApiModelProperty(value = "Filtro para disponibilidades aos domingos", example = "false")
    private Boolean domFc;

    public List<TipoAgendamentoDTO> getComportamentoFc() {
        return comportamentoFc;
    }

    public void setComportamentoFc(List<TipoAgendamentoDTO> comportamentoFc) {
        this.comportamentoFc = comportamentoFc;
    }

    public Map<Object, Object> getTipoValidacaoFc() {
        return tipoValidacaoFc;
    }

    public void setTipoValidacaoFc(Map<Object, Object> tipoValidacaoFc) {
        this.tipoValidacaoFc = tipoValidacaoFc;
    }

    public List<ItemValidacaoDisponibilidadeDTO> getItemValidacaoFc() {
        return itemValidacaoFc;
    }

    public void setItemValidacaoFc(List<ItemValidacaoDisponibilidadeDTO> itemValidacaoFc) {
        this.itemValidacaoFc = itemValidacaoFc;
    }

    public Boolean getTipoHorarioPreDefinidoFc() {
        return tipoHorarioPreDefinidoFc;
    }

    public void setTipoHorarioPreDefinidoFc(Boolean tipoHorarioPreDefinidoFc) {
        this.tipoHorarioPreDefinidoFc = tipoHorarioPreDefinidoFc;
    }

    public Boolean getTipoHorarioLivreFc() {
        return tipoHorarioLivreFc;
    }

    public void setTipoHorarioLivreFc(Boolean tipoHorarioLivreFc) {
        this.tipoHorarioLivreFc = tipoHorarioLivreFc;
    }

    public Boolean getTipoHorarioIntervaloTempoFc() {
        return tipoHorarioIntervaloTempoFc;
    }

    public void setTipoHorarioIntervaloTempoFc(Boolean tipoHorarioIntervaloTempoFc) {
        this.tipoHorarioIntervaloTempoFc = tipoHorarioIntervaloTempoFc;
    }

    public Boolean getSegFc() {
        return segFc;
    }

    public void setSegFc(Boolean segFc) {
        this.segFc = segFc;
    }

    public Boolean getTerFc() {
        return terFc;
    }

    public void setTerFc(Boolean terFc) {
        this.terFc = terFc;
    }

    public Boolean getQuaFc() {
        return quaFc;
    }

    public void setQuaFc(Boolean quaFc) {
        this.quaFc = quaFc;
    }

    public Boolean getQuiFc() {
        return quiFc;
    }

    public void setQuiFc(Boolean quiFc) {
        this.quiFc = quiFc;
    }

    public Boolean getSexFc() {
        return sexFc;
    }

    public void setSexFc(Boolean sexFc) {
        this.sexFc = sexFc;
    }

    public Boolean getSabFc() {
        return sabFc;
    }

    public void setSabFc(Boolean sabFc) {
        this.sabFc = sabFc;
    }

    public Boolean getDomFc() {
        return domFc;
    }

    public void setDomFc(Boolean domFc) {
        this.domFc = domFc;
    }

    public List<VigenciaDTO> getVigencia() {
        return vigencia;
    }

    public void setVigencia(List<VigenciaDTO> vigencia) {
        this.vigencia = vigencia;
    }

    public Boolean getTipoHorarioPlayFc() {
        return tipoHorarioPlayFc;
    }

    public void setTipoHorarioPlayFc(Boolean tipoHorarioPlayFc) {
        this.tipoHorarioPlayFc = tipoHorarioPlayFc;
    }
}
