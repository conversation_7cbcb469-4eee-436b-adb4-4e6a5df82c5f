package br.com.pacto.controller.json.avaliacao;

import br.com.pacto.bean.anamnese.RespostaCliente;
import br.com.pacto.objeto.Uteis;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Resposta do aluno para uma pergunta do questionário PAR-Q (Physical Activity Readiness Questionnaire)")
public class RespostaClienteJSON {

    @ApiModelProperty(value = "Código identificador único da resposta", example = "1001")
    private Integer codigo;

    @ApiModelProperty(value = "Resposta fornecida pelo aluno para a pergunta do questionário PAR-Q", example = "Sim")
    private String resposta;

    @ApiModelProperty(value = "Observações adicionais sobre a resposta do aluno", example = "Aluno pratica corrida 3 vezes por semana")
    private String obs;

    @ApiModelProperty(value = "URL da assinatura digital do aluno no questionário PAR-Q", example = "https://storage.exemplo.com/assinaturas/aluno_1001_parq.png")
    private String urlAssinatura;

    @ApiModelProperty(value = "Data e hora em que a resposta foi registrada", example = "15/03/2024 14:30:25")
    private String dataResposta;

    @ApiModelProperty(value = "Ordem da pergunta no questionário PAR-Q", example = "1")
    private Integer ordem;

    public RespostaClienteJSON() {
    }

    public RespostaClienteJSON(RespostaCliente rc) {
        this.codigo = rc.getCodigo();
        this.resposta = rc.getResposta();
        this.obs = rc.getObs();
        if(rc.getRespostaClienteParQ() != null) {
            this.dataResposta = Uteis.getDataAplicandoFormatacao(rc.getRespostaClienteParQ().getDataResposta(), "dd/MM/yyyy HH:mm:ss");
            this.urlAssinatura = Uteis.valorVazioString(rc.getRespostaClienteParQ().getUrlAssinatura()) ?
                    "" : Uteis.getPaintFotoDaNuvem(rc.getRespostaClienteParQ().getUrlAssinatura());
        }
        if(rc.getPerguntaAnamnese() != null && rc.getPerguntaAnamnese().getPergunta() != null && rc.getPerguntaAnamnese().getPergunta().getOrdem() != null) {
            this.ordem = rc.getPerguntaAnamnese().getPergunta().getOrdem();
        }
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getResposta() {
        return resposta == null ? "" : this.resposta;
    }

    public void setResposta(String resposta) {
        this.resposta = resposta;
    }

    public String getObs() {
        return obs;
    }

    public void setObs(String obs) {
        this.obs = obs;
    }

    public String getUrlAssinatura() {
        return urlAssinatura;
    }

    public void setUrlAssinatura(String urlAssinatura) {
        this.urlAssinatura = urlAssinatura;
    }

    public String getDataResposta() {
        return dataResposta;
    }

    public void setDataResposta(String dataResposta) {
        this.dataResposta = dataResposta;
    }

    public Integer getOrdem() {
        return ordem;
    }

    public void setOrdem(Integer ordem) {
        this.ordem = ordem;
    }
}
