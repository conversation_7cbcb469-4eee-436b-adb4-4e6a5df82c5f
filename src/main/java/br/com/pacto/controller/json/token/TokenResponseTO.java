package br.com.pacto.controller.json.token;

import br.com.pacto.bean.colaborador.PerfilUsuarioResponseTO;
import br.com.pacto.bean.colaborador.PerfilUsuarioTO;
import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.colaborador.PerfilUsuarioDTO;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by <PERSON><PERSON> on 28/09/2018.
 */
@ApiModel(description = "DTO para representar a resposta completa de validação de token")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TokenResponseTO {

    @ApiModelProperty(value = "Dados completos do usuário autenticado incluindo informações pessoais e profissionais")
    private UsuarioResponseTO user;

    @ApiModelProperty(value = "Lista das empresas/unidades vinculadas ao usuário")
    private List<EmpresaResponseTO> unidadesEmpresa;

    @ApiModelProperty(value = "Perfil de acesso do usuário contendo permissões e funcionalidades disponíveis")
    private PerfilUsuarioDTO perfilUsuario;


    public TokenResponseTO(Usuario user) {

        this.user = new UsuarioResponseTO(user,true);
    }
    public TokenResponseTO(Usuario user, List<Empresa> empresas, Boolean treinoIndependente) {
        this.user = new UsuarioResponseTO(user,treinoIndependente);

        List<EmpresaResponseTO> empresaResponseTOList = new ArrayList<>();
        for (Empresa empresa : empresas){
            empresaResponseTOList.add(new EmpresaResponseTO(empresa, treinoIndependente));
        }
        unidadesEmpresa = empresaResponseTOList;

        this.perfilUsuario = new PerfilUsuarioDTO(user.getPerfil());
    }

    public UsuarioResponseTO getUser() {
        return user;
    }

    public void setUser(UsuarioResponseTO user) {
        this.user = user;
    }

    public List<EmpresaResponseTO> getUnidadesEmpresa() {return unidadesEmpresa;}

    public void setUnidadesEmpresa(List<EmpresaResponseTO> unidadesEmpresa) {this.unidadesEmpresa = unidadesEmpresa;}

    public PerfilUsuarioDTO getPerfilUsuario() {
        return perfilUsuario;
    }

    public void setPerfilUsuario(PerfilUsuarioDTO perfilUsuario) {
        this.perfilUsuario = perfilUsuario;
    }
}
