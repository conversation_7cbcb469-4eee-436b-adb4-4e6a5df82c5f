package br.com.pacto.controller.json.benchmark;

import br.com.pacto.enumerador.crossfit.TipoWodEnum;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Created by j<PERSON><PERSON> moita on 27/09/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Dados do benchmark para operações de cadastro e alteração")
public class BenchmarkTO {

    @ApiModelProperty(value = "Nome do benchmark", example = "Fran", required = true)
    private String nome;

    @ApiModelProperty(value = "ID do tipo de benchmark", example = "1", required = true)
    private Integer tipoBenchmarkId;

    @ApiModelProperty(value = "Tipo de exercício do benchmark. Valores possíveis: 'FOR_TIME', 'FOR_REPS', 'FOR_WEIGHT', 'AMRAP'", example = "FOR_TIME", required = true)
    private TipoWodEnum tipoExercicio;

    @ApiModelProperty(value = "Descrição detalhada dos exercícios do benchmark", example = "21-15-9 repetições de:\\n- Thrusters (43kg)\\n- Pull-ups")
    private String exercicios;

    @ApiModelProperty(value = "Observações adicionais sobre o benchmark", example = "Benchmark clássico do CrossFit, criado em homenagem a Fran Berkowitz")
    private String observacao;

    @ApiModelProperty(value = "Dados da imagem em formato Base64", example = "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ...")
    private String imagemData;

    @ApiModelProperty(value = "URI do vídeo demonstrativo", example = "https://www.youtube.com/watch?v=abc123")
    private String videoUri;

    @ApiModelProperty(value = "Extensão do arquivo de imagem", example = "jpg")
    private String extensaoImagem;

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getTipoBenchmarkId() {
        return tipoBenchmarkId;
    }

    public void setTipoBenchmarkId(Integer tipoBenchmarkId) {
        this.tipoBenchmarkId = tipoBenchmarkId;
    }

    public TipoWodEnum getTipoExercicio() {
        return tipoExercicio;
    }

    public void setTipoExercicio(TipoWodEnum tipoExercicio) {
        this.tipoExercicio = tipoExercicio;
    }

    public String getExercicios() {
        return exercicios;
    }

    public void setExercicios(String exercicios) {
        this.exercicios = exercicios;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public String getVideoUri() {
        return videoUri;
    }

    public void setVideoUri(String videoUri) {
        this.videoUri = videoUri;
    }

    public String getImagemData() {
        return imagemData;
    }

    public void setImagemData(String imagemData) {
        this.imagemData = imagemData;
    }

    public String getExtensaoImagem() {
        return extensaoImagem;
    }

    public void setExtensaoImagem(String extensaoImagem) {
        this.extensaoImagem = extensaoImagem;
    }
}
