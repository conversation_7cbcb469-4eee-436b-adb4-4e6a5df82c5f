/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.atividade.read;

import br.com.pacto.controller.json.base.SuperJSON;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 *
 * <AUTHOR>
 */
@ApiModel(description = "Dados de uma série de exercício para alteração")
public class SerieJSON extends SuperJSON {

    @ApiModelProperty(value = "Primeiro valor auxiliar da série", example = "12")
    private String valor1;

    @ApiModelProperty(value = "Repetições complementares da série em formato texto", example = "12x")
    private String repeticaoComp;

    @ApiModelProperty(value = "Segundo valor auxiliar da série", example = "15")
    private String valor2;

    @ApiModelProperty(value = "Terceiro valor auxiliar da série", example = "20")
    private String valor3;

    @ApiModelProperty(value = "Carga complementar da série em formato texto", example = "80kg")
    private String cargaComp;

    @ApiModelProperty(value = "Tempo de descanso entre séries", example = "60s")
    private String descanso;

    @ApiModelProperty(value = "Código identificador único da série", example = "1523")
    private Integer codSerie;

    @ApiModelProperty(value = "Observações complementares sobre a execução da série", example = "Manter postura ereta durante todo o movimento")
    private String complemento;

    @ApiModelProperty(value = "Cadência de execução do exercício", example = "2-1-2-1")
    private String cadencia;

    @ApiModelProperty(value = "Ordem de execução da série dentro da atividade", example = "1")
    private Integer ordem;

    @ApiModelProperty(value = "Número de repetições da série", example = "12")
    private Integer repeticao = 0;

    @ApiModelProperty(value = "Carga utilizada na série em gramas", example = "80000.0")
    private Double carga = 0.0;//gramas

    @ApiModelProperty(value = "Duração do exercício em minutos", example = "30")
    private Integer duracao = 0;//minutos

    @ApiModelProperty(value = "Distância percorrida em metros", example = "5000")
    private Integer distancia = 0;//metros

    @ApiModelProperty(value = "Velocidade de execução em km/h", example = "12.5")
    private Double velocidade = 0.0;//km/h

    @ApiModelProperty(value = "Código da atividade relacionada à série", example = "45")
    private Integer codAtividade;

    @ApiModelProperty(value = "Tipo da atividade (1=Musculação, 2=Aeróbico, 3=Funcional)", example = "1")
    private Integer tipoAtividade;

    @ApiModelProperty(value = "Carga registrada pelo aplicativo móvel", example = "80kg")
    private String cargaApp;

    @ApiModelProperty(value = "Repetições registradas pelo aplicativo móvel", example = "12")
    private String repeticaoApp;


    public String getValor1() {
        return valor1;
    }

    public void setValor1(String valor1) {
        this.valor1 = valor1;
    }

    public String getValor2() {
        return valor2;
    }

    public void setValor2(String valor2) {
        this.valor2 = valor2;
    }

    public String getDescanso() {
        return descanso;
    }

    public void setDescanso(String descanso) {
        this.descanso = descanso;
    }

    public Integer getCodSerie() {
        return codSerie;
    }

    public void setCodSerie(Integer codSerie) {
        this.codSerie = codSerie;
    }

    public String getComplemento() {
        return complemento;
    }

    public void setComplemento(String complemento) {
        this.complemento = complemento;
    }

    public String getRepeticaoComp() {
        return repeticaoComp;
    }

    public void setRepeticaoComp(String repeticaoComp1) {
        this.repeticaoComp = repeticaoComp1;
    }

    public String getCargaComp() {
        return cargaComp;
    }

    public void setCargaComp(String cargaComp2) {
        this.cargaComp = cargaComp2;
    }

    public String getCadencia() {
        return cadencia;
    }

    public void setCadencia(String cadencia) {
        this.cadencia = cadencia;
    }

    public Integer getOrdem() {
        return ordem;
    }

    public void setOrdem(Integer ordem) {
        this.ordem = ordem;
    }

    public String getValor3() {
        return valor3;
    }

    public void setValor3(String valor3) {
        this.valor3 = valor3;
    }

    public Integer getRepeticao() {
        return repeticao;
    }

    public void setRepeticao(Integer repeticao) {
        this.repeticao = repeticao;
    }

    public Double getCarga() {
        return carga;
    }

    public void setCarga(Double carga) {
        this.carga = carga;
    }

    public Integer getDuracao() {
        return duracao;
    }

    public void setDuracao(Integer duracao) {
        this.duracao = duracao;
    }

    public Integer getDistancia() {
        return distancia;
    }

    public void setDistancia(Integer distancia) {
        this.distancia = distancia;
    }

    public Double getVelocidade() {
        return velocidade;
    }

    public void setVelocidade(Double velocidade) {
        this.velocidade = velocidade;
    }

    public Integer getCodAtividade() {
        if (codAtividade == null) {
            codAtividade = 0;
        }
        return codAtividade;
    }

    public void setCodAtividade(Integer codAtividade) {
        this.codAtividade = codAtividade;
    }

    public Integer getTipoAtividade() {
        if (tipoAtividade == null) {
            tipoAtividade = 0;
        }
        return tipoAtividade;
    }

    public void setTipoAtividade(Integer tipoAtividade) {
        this.tipoAtividade = tipoAtividade;
    }

    public String getCargaApp() {
        return cargaApp;
    }

    public void setCargaApp(String cargaApp) {
        this.cargaApp = cargaApp;
    }

    public String getRepeticaoApp() {
        return repeticaoApp;
    }

    public void setRepeticaoApp(String repeticaoApp) {
        this.repeticaoApp = repeticaoApp;
    }
}
