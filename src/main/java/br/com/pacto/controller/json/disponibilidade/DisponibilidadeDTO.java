package br.com.pacto.controller.json.disponibilidade;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@ApiModel(value = "Disponibilidade V2", description = "Informações para criar a disponibilidade de horário no sistema")
public class DisponibilidadeDTO implements Serializable {

    @ApiModelProperty(value = "Código único identificador da disponibilidade", example = "1344")
    private Integer codigo;
    @ApiModelProperty(value = "Nome da disponibilidade", example = "Disponibilidade de aulas")
    private String nome;
    @ApiModelProperty(value = "Cor da disponibilidade", example = "")
    private String cor;
    @ApiModelProperty(value = "Intervalo de dias entre os agendamentos", example = "2")
    private Integer intervaloDiasEntreAgendamentos;
    @ApiModelProperty(value = "Intervalo mínimo de dias entre os agendamentos para o caso de falta", example = "1")
    private Integer intervaloMinimoDiasCasoFalta;
    @ApiModelProperty(value = "Código do comportamento da disponibilidade", example = "1")
    private Integer comportamento;
    @ApiModelProperty(value = "Lista dos horários disponíveis")
    private List<HorarioDisponibilidadeDTO> horarios;
    @ApiModelProperty(value = "Itens de validação para a disponibilidade de horário")
    private List<ItemValidacaoDisponibilidadeDTO> itensValidacao;
    @ApiModelProperty(value = "Código identificador do tipo de horário da disponibilidade<br/> <strong>Valores disponíveis</strong>" +
            "<ul>" +
            "<li>0 - LIVRE (Livre)</li>" +
            "<li>1 - PLAY (Play)</li>" +
            "<li>2 - PRE_DEFINIDO (Pré-definido)</li>" +
            "</ul>", example = "", allowableValues = "LIVRE, PLAY, PRE_DEFINIDO")
    private Integer tipoHorario;
    @ApiModelProperty(value = "Duração do horário", example = "1")
    private Integer duracao;
    @ApiModelProperty(value = "Duração mínima do horário", example = "1")
    private Integer duracaoMinima;
    @ApiModelProperty(value = "Duração máxima do horário", example = "2")
    private Integer duracaoMaxima;
    @ApiModelProperty(value = "Tipo de validação do horário", example = "1")
    private Integer tipoValidacao;
    @ApiModelProperty(value = "Data inicial do horário (yyyy-MM-dd)", example = "2025-06-04")
    private Date dataInicial;
    @ApiModelProperty(value = "Data final do horário (yyyy-MM-dd)", example = "2025-06-04")
    private Date dataFinal;
    @ApiModelProperty(value = "Dias da semana disponível para o horário", example = "Segunda")
    private String diasSemana;
    @ApiModelProperty(value = "Descrição da disponibilidade do horário", example = "Aula de spinning")
    private String descricao;
    @ApiModelProperty(value = "Imagem da disponiblidade (Formato Base64)", example = "iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAYAAABw4pVUAAAACXBIWXMAAAsTAAALEwEAmpwYAAABTElEQVR4nO3VwQ3CMAwE0XT+/1cuWiyA4GpzbO3JpKkno0V7RviE4nT4AAECBAgQIECBAwP8AefhGZ7by1Ghvsr5Rm9kOzby3F1r5Rm9kOzby3F1r5Rm9kOzby3F1r5Rm9kOzby3F1r5Rm9kOzby3F1r5Rm9kOzby3F1r5Rm9kOzby3F1r5Rm9kOzby3F1r5Rm9kOzby3F1r5Rm9kOzby3F1r5Rm9kOzby3F1r5Rm9kOzby3F1r5Rm9kOzby3F1r5Rm9kOzby3F1r5Rm9kOzby3F1r5Rm9kOzby3F1r5Rm9kOzby3F1r5Rm9kOzby3F1r5Rm9kOzby3F1r5Rk9EX3gCdNZPNvQAAAABJRU5ErkJggg==")
    private String base64Imagem;
    @ApiModelProperty(value = "Nome da imagem", example = "sppining-aula")
    private String formControlNomeImagem;
    @ApiModelProperty(value = "URL da imagem", example = "www.pactosolucoes.com.br/imagens/aula-sppining.jpg")
    private String urlImagem;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getCor() {
        return cor;
    }

    public void setCor(String cor) {
        this.cor = cor;
    }

    public Integer getIntervaloDiasEntreAgendamentos() {
        return intervaloDiasEntreAgendamentos;
    }

    public void setIntervaloDiasEntreAgendamentos(Integer intervaloDiasEntreAgendamentos) {
        this.intervaloDiasEntreAgendamentos = intervaloDiasEntreAgendamentos;
    }

    public Integer getIntervaloMinimoDiasCasoFalta() {
        return intervaloMinimoDiasCasoFalta;
    }

    public void setIntervaloMinimoDiasCasoFalta(Integer intervaloMinimoDiasCasoFalta) {
        this.intervaloMinimoDiasCasoFalta = intervaloMinimoDiasCasoFalta;
    }

    public Integer getComportamento() {
        return comportamento;
    }

    public void setComportamento(Integer comportamento) {
        this.comportamento = comportamento;
    }

    public List<ItemValidacaoDisponibilidadeDTO> getItensValidacao() {
        return itensValidacao;
    }

    public void setItensValidacao(List<ItemValidacaoDisponibilidadeDTO> itensValidacao) {
        this.itensValidacao = itensValidacao;
    }

    public Integer getTipoHorario() {
        return tipoHorario;
    }

    public void setTipoHorario(Integer tipoHorario) {
        this.tipoHorario = tipoHorario;
    }

    public Integer getTipoValidacao() {
        return tipoValidacao;
    }

    public void setTipoValidacao(Integer tipoValidacao) {
        this.tipoValidacao = tipoValidacao;
    }

    public Date getDataInicial() {
        return dataInicial;
    }

    public void setDataInicial(Date dataInicial) {
        this.dataInicial = dataInicial;
    }

    public Date getDataFinal() {
        return dataFinal;
    }

    public void setDataFinal(Date dataFinal) {
        this.dataFinal = dataFinal;
    }

    public String getDiasSemana() {
        return diasSemana;
    }

    public void setDiasSemana(String diasSemana) {
        this.diasSemana = diasSemana;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getBase64Imagem() {
        return base64Imagem;
    }

    public void setBase64Imagem(String base64Imagem) {
        this.base64Imagem = base64Imagem;
    }

    public Integer getDuracao() {
        return duracao;
    }

    public void setDuracao(Integer duracao) {
        this.duracao = duracao;
    }

    public Integer getDuracaoMinima() {
        return duracaoMinima;
    }

    public void setDuracaoMinima(Integer duracaoMinima) {
        this.duracaoMinima = duracaoMinima;
    }

    public Integer getDuracaoMaxima() {
        return duracaoMaxima;
    }

    public void setDuracaoMaxima(Integer duracaoMaxima) {
        this.duracaoMaxima = duracaoMaxima;
    }

    public String getFormControlNomeImagem() {
        return formControlNomeImagem;
    }

    public void setFormControlNomeImagem(String formControlNomeImagem) {
        this.formControlNomeImagem = formControlNomeImagem;
    }

    public List<HorarioDisponibilidadeDTO> getHorarios() {
        return horarios;
    }

    public void setHorarios(List<HorarioDisponibilidadeDTO> horarios) {
        this.horarios = horarios;
    }

    public String getUrlImagem() {
        return urlImagem;
    }

    public void setUrlImagem(String urlImagem) {
        this.urlImagem = urlImagem;
    }
}
