package br.com.pacto.controller.json.turma;

import br.com.pacto.bean.atividade.AtividadeVideo;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Objects;

/**
 * Created by ulisses on 23/08/2018.
 */
@ApiModel(description = "Informações de vídeo vinculado a uma turma")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TurmaVideoDTO {

    @ApiModelProperty(value = "Código identificador único do vídeo", example = "1")
    private Integer id;

    @ApiModelProperty(value = "Código da turma à qual o vídeo está vinculado", example = "123")
    private Integer turma_codigo;

    @ApiModelProperty(value = "Link/URL do vídeo da aula", example = "https://www.youtube.com/watch?v=abc123")
    private String linkVideo;

    @ApiModelProperty(value = "Indica se o vídeo é específico para o professor", example = "false")
    private Boolean professor = false;

    public TurmaVideoDTO(){
    }
    public TurmaVideoDTO(TurmaVideoDTO aa) {
        this.id = aa.getId();
        this.turma_codigo = aa.getTurma_codigo();
        this.linkVideo = aa.getLinkVideo();
        this.professor = aa.getProfessor();
    }

    public Integer getId() {return id;}

    public void setId(Integer id) {this.id = id;}

    public String getLinkVideo() {
        return linkVideo;
    }

    public void setLinkVideo(String linkVideo) {
        this.linkVideo = linkVideo;
    }

    public Boolean getProfessor() {
        return professor;
    }

    public void setProfessor(Boolean professor) {
        this.professor = professor;
    }

    public Integer getTurma_codigo() {
        return turma_codigo;
    }

    public void setTurma_codigo(Integer turma_codigo) {
        this.turma_codigo = turma_codigo;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        TurmaVideoDTO that = (TurmaVideoDTO) o;
        return Objects.equals(id, that.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }
}
