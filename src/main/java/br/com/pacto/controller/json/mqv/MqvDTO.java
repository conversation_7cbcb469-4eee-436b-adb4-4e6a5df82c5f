package br.com.pacto.controller.json.mqv;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "DTO para configurações de integração MQV")
public class MqvDTO {

    @ApiModelProperty(value = "Código da empresa no sistema Treino", example = "123")
    private Integer empresa; //codigo empresa do treino

    @ApiModelProperty(value = "Nome da empresa", example = "Academia Fitness Plus")
    private String nome;

    @ApiModelProperty(value = "Token de integração MQV", example = "mqv_token_abc123")
    private String token;

    public MqvDTO() {}

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }
}
