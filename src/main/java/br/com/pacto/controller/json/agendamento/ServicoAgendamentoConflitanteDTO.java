package br.com.pacto.controller.json.agendamento;

import br.com.pacto.bean.Agendamento;
import br.com.pacto.bean.colaborador.ColaboradorSimplesTO;
import br.com.pacto.controller.json.tipoEvento.TipoAgendamentoDTO;
import br.com.pacto.objeto.Calendario;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Created by paulo 03/08/2019
 */

@ApiModel(description = "Informações dos agendamentos conflitantes com um horário")
public class ServicoAgendamentoConflitanteDTO {

    @ApiModelProperty(value = "Código único identificador do agendamento conflitante", example = "65")
    private Integer id;
    @ApiModelProperty(value = "Dia do agendamento conflitante (Formato yyyyMMdd)", example = "20250605")
    private String dia;
    @ApiModelProperty(value = "Professor responsável pelo horário")
    private ColaboradorSimplesTO professor;
    @ApiModelProperty(value = "Tipo do agendamento")
    private TipoAgendamentoDTO tipoAgendamento;
    @ApiModelProperty(value = "Horário inicial do agendamento conflitante", example = "19:00")
    private String horarioInicial;
    @ApiModelProperty(value = "Horário final do agendamento conflitante", example = "20:00")
    private String horarioFinal;

    public ServicoAgendamentoConflitanteDTO(Agendamento agendamento, Boolean treinoIndependente) {
        this.id = agendamento.getCodigo();
        this.dia = Calendario.getData(agendamento.getInicio(), "yyyyMMdd");
        this.professor = new ColaboradorSimplesTO(agendamento.getProfessor(), treinoIndependente);
        this.tipoAgendamento = new TipoAgendamentoDTO(agendamento.getTipoEvento());
        this.horarioInicial = agendamento.getHoraInicioApresentar();
        this.horarioFinal = agendamento.getHoraFimApresentar();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getDia() {
        return dia;
    }

    public void setDia(String dia) {
        this.dia = dia;
    }

    public ColaboradorSimplesTO getProfessor() {
        return professor;
    }

    public void setProfessor(ColaboradorSimplesTO professor) {
        this.professor = professor;
    }

    public TipoAgendamentoDTO getTipoAgendamento() {
        return tipoAgendamento;
    }

    public void setTipoAgendamento(TipoAgendamentoDTO tipoAgendamento) {
        this.tipoAgendamento = tipoAgendamento;
    }

    public String getHorarioInicial() {
        return horarioInicial;
    }

    public void setHorarioInicial(String horarioInicial) {
        this.horarioInicial = horarioInicial;
    }

    public String getHorarioFinal() {
        return horarioFinal;
    }

    public void setHorarioFinal(String horarioFinal) {
        this.horarioFinal = horarioFinal;
    }
}
