package br.com.pacto.controller.json.atividade;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.atividade.AtividadeTO;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.atividade.AtividadeService;
import br.com.pacto.swagger.respostas.ExemploRespostaVazia;
import br.com.pacto.swagger.respostas.atividade.ExemploRespostaAtividadeCompletaResponseTO;
import br.com.pacto.swagger.respostas.atividade.ExemploRespostaDesativarAtividadesIA;
import br.com.pacto.swagger.respostas.atividade.ExemploRespostaListAtividadeCompletaResponseTOPaginacao;
import br.com.pacto.swagger.respostas.atividade.ExemploRespostaListAtividadeSimplesResponseTOPaginacao;
import br.com.pacto.swagger.respostas.atividade.empresa.ExemploRespostaListAtividadeEmpresaResponseTO;
import br.com.pacto.swagger.respostas.variaveis.ExemploRespostaBoolean;
import io.swagger.annotations.*;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import br.com.pacto.service.impl.atividade.RelatorioExclusaoAtividadeIADTO;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;


/**
 * Created by ulisses on 21/08/2018.
 */
@Controller
@RequestMapping("/psec/atividades")
public class AtividadeController {

    private final AtividadeService atividadeService;

    @Autowired
    public AtividadeController(AtividadeService atividadeService) {
        Assert.notNull(atividadeService, "O serviço de atividade não foi injetado corretamente");
        this.atividadeService = atividadeService;
    }

    @ApiOperation(
            value = "Consultar atividades físicas",
            notes = "Consulta as informações das atividades físicas cadastradas no sistema. As informações incluem: Grupos musculares que serão exercitados, aparelhos que devem ser utilizados, entre outras informações pertinentes a atividade física.",
            tags = "Atividades"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "tipo", value = "Tipo da atividade física que será buscada.<br/><strong>Valores disponíveis</strong>" +
                    "<ul>" +
                    "<li>0 - NEUROMUSCULAR (Neuromuscular)</li>" +
                    "<li>1 - CARDIOVASCULAR (Cardiovascular)</li>" +
                    "</ul>", defaultValue = "NEUROMUSCULAR", allowableValues = "NEUROMUSCULAR,CARDIOVASCULAR", paramType = "query"),
            @ApiImplicitParam(name = "crossfit", value = "Indica se deve buscar apenas por atividades do crossfit", defaultValue = "false", paramType = "query", dataType = "boolean"),
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.<br/> Ordena por um atributo contido no objeto da resposta." +
                    "<strong>Ordens disponíveis</strong>" +
                    "<ul>" +
                    "<li>ASC - Ordena pelo atributo escolhido de forma ascendente</li>" +
                    "<li>DESC - Ordena pelo atributo escolhido de forma descendente</li>" +
                    "</ul>" +
                    "Deve ser informado como atributo,ordem", defaultValue = "nome,asc", paramType = "query", dataType = "string"),
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaListAtividadeCompletaResponseTOPaginacao.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.ATIVIDADES)
    @RequestMapping(value = "", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarAtividades(
            @ApiParam(value = "Filtros de busca, deve ser informado como um JSON." +
                    "<br/><strong>É necessário fazer o ENCODE na URL para realizar a requisição com os filtros.</strong></br>" +
                    "<br/><strong>Filtros disponíveis:</strong>" +
                    "<ul>" +
                    "<li><strong>quicksearchValue:</strong> Filtra pelo nome da atividade</li>" +
                    "<li><strong>situacaoAtividade:</strong> Filtra pela situação da atividade (ex: [\"true\"] para ativos)</li>" +
                    "<li><strong>tiposEnuns:</strong> Filtra pelos tipos da atividade." +
                    "<strong>Valores disponíveis</strong>" +
                    "<ul>" +
                    "<li>0 - NEUROMUSCULAR (Neuromuscular)</li>" +
                    "<li>1 - CARDIOVASCULAR (Cardiovascular)</li>" +
                    "</ul>" +
                    "</li>" +
                    "<li><strong>atividades:</strong> Filtra por tipo de atividade, podendo ser [\"IA\"], [\"CONVENCIONAL\"] ou ambos</li>" +
                    "<li><strong>empresas:</strong> Filtra pelos códigos das empresas associadas à atividade (ex: [1, 2, 3])</li>" +
                    "<li><strong>aparelhos:</strong> Filtra pelas atividades associadas a determinados aparelhos (ex: [10, 20])</li>" +
                    "</ul>",
                    defaultValue = "{\"quicksearchValue\":\"costas\",\"situacaoAtividade\":[\"true\"],\"tiposEnuns\":[\"NEUROMUSCULAR\"],\"atividades\":[\"IA\"],\"empresas\":[1],\"aparelhos\":[10]}")
            @RequestParam(value = "filters", required = false) JSONObject filtros,
            @ApiParam(value = "Código único identificador da empresa em que a atividade está vinculada", defaultValue = "1")
            @RequestHeader(value = "empresaId", required = true) Integer empresaId,
            @ApiIgnore String tipo,
            @ApiIgnore PaginadorDTO paginadorDTO,
            @ApiIgnore boolean crossfit) throws JSONException {
        try {
            FiltroAtividadeJSON filtroAtividadeJSON = new FiltroAtividadeJSON(filtros);
            return ResponseEntityFactory.ok(atividadeService.listarAtividades(filtroAtividadeJSON, tipo, paginadorDTO, crossfit, empresaId), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(AtividadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar as atividades", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar atividades físicas geradas pela inteligência artificial",
            notes = "Consulta as informações das atividades físicas que foram geradas pela inteligência artificial. As informações incluem: Grupos musculares que serão exercitados, aparelhos que devem ser utilizados, entre outras informações pertinentes a atividade física.",
            tags = "Atividades"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "tipo", value = "Tipo da atividade física que será buscada.<br/><strong>Valores disponíveis</strong>" +
                    "<ul>" +
                    "<li>0 - NEUROMUSCULAR (Neuromuscular)</li>" +
                    "<li>1 - CARDIOVASCULAR (Cardiovascular)</li>" +
                    "</ul>", defaultValue = "NEUROMUSCULAR", allowableValues = "NEUROMUSCULAR,CARDIOVASCULAR", paramType = "query"),
            @ApiImplicitParam(name = "crossfit", value = "Indica se deve buscar apenas por atividades do crossfit", defaultValue = "false", paramType = "query", dataType = "boolean"),
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.<br/> Ordena por um atributo contido no objeto da resposta." +
                    "<strong>Ordens disponíveis</strong>" +
                    "<ul>" +
                    "<li>ASC - Ordena pelo atributo escolhido de forma ascendente</li>" +
                    "<li>DESC - Ordena pelo atributo escolhido de forma descendente</li>" +
                    "</ul>" +
                    "Deve ser informado como atributo,ordem", defaultValue = "nome,asc", paramType = "query", dataType = "string"),
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaListAtividadeCompletaResponseTOPaginacao.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.ATIVIDADES)
    @RequestMapping(value = "/ia", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarAtividadesIA(
            @ApiParam(value = "Filtros de busca, deve ser informado como um JSON." +
                    "<br/><strong>É necessário fazer o ENCODE na URL para realizar a requisição com os filtros.</strong></br>" +
                    "<br/><strong>Filtros disponíveis:</strong>" +
                    "<ul>" +
                    "<li><strong>quicksearchValue:</strong> Filtra pelo nome da atividade</li>" +
                    "<li><strong>situacaoAtividade:</strong> Filtra pela situação da atividade (ex: [\"true\"] para ativos)</li>" +
                    "<li><strong>tiposEnuns:</strong> Filtra pelos tipos da atividade." +
                    "<strong>Valores disponíveis</strong>" +
                    "<ul>" +
                    "<li>0 - NEUROMUSCULAR (Neuromuscular)</li>" +
                    "<li>1 - CARDIOVASCULAR (Cardiovascular)</li>" +
                    "</ul>" +
                    "</li>" +
                    "<li><strong>atividades:</strong> Filtra por tipo de atividade, podendo ser [\"IA\"], [\"CONVENCIONAL\"] ou ambos</li>" +
                    "<li><strong>empresas:</strong> Filtra pelos códigos das empresas associadas à atividade (ex: [1, 2, 3])</li>" +
                    "<li><strong>aparelhos:</strong> Filtra pelas atividades associadas a determinados aparelhos (ex: [10, 20])</li>" +
                    "</ul>",
                    defaultValue = "{\"quicksearchValue\":\"costas\",\"situacaoAtividade\":[\"true\"],\"tiposEnuns\":[\"NEUROMUSCULAR\"],\"atividades\":[\"IA\"],\"empresas\":[1],\"aparelhos\":[10]}")
            @RequestParam(value = "filters", required = false) JSONObject filtros,
            @ApiParam(value = "Código único identificador da empresa em que a atividade está vinculada", defaultValue = "1")
            @RequestHeader(value = "empresaId", required = true) Integer empresaId,
            @ApiIgnore String tipo, @ApiIgnore PaginadorDTO paginadorDTO, @ApiIgnore boolean crossfit) throws JSONException {
        try {
            FiltroAtividadeJSON filtroAtividadeJSON = new FiltroAtividadeJSON(filtros);
            filtroAtividadeJSON.getTreinoia().add("todas");
            return ResponseEntityFactory.ok(atividadeService.listarAtividades(filtroAtividadeJSON, tipo, paginadorDTO, crossfit, empresaId), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(AtividadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar as atividades", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @ApiOperation(
            value = "Consultar pescrição de atividades físicas.",
            notes = "Consulta as informações das pescrições de atividades físicas. As informações incluem: Grupos musculares que serão exercitados, aparelhos que devem ser utilizados, entre outras informações pertinentes a atividade física.",
            tags = "Atividades"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "tipo", value = "Tipo da atividade física que será buscada.<br/><strong>Valores disponíveis</strong>" +
                    "<ul>" +
                    "<li>0 - NEUROMUSCULAR (Neuromuscular)</li>" +
                    "<li>1 - CARDIOVASCULAR (Cardiovascular)</li>" +
                    "</ul>", defaultValue = "NEUROMUSCULAR", allowableValues = "NEUROMUSCULAR,CARDIOVASCULAR", paramType = "query"),
            @ApiImplicitParam(name = "crossfit", value = "Indica se deve buscar apenas por atividades do crossfit", defaultValue = "false", paramType = "query", dataType = "boolean"),
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.<br/> Ordena por um atributo contido no objeto da resposta." +
                    "<strong>Ordens disponíveis</strong>" +
                    "<ul>" +
                    "<li>ASC - Ordena pelo atributo escolhido de forma ascendente</li>" +
                    "<li>DESC - Ordena pelo atributo escolhido de forma descendente</li>" +
                    "</ul>" +
                    "Deve ser informado como atributo,ordem", defaultValue = "nome,asc", paramType = "query", dataType = "string"),
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaListAtividadeCompletaResponseTOPaginacao.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.ATIVIDADES)
    @RequestMapping(value = "/prescricao", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarAtividadesPrescricao(
            @ApiParam(value = "Filtros de busca, deve ser informado como um JSON." +
                    "<br/><strong>É necessário fazer o ENCODE na URL para realizar a requisição com os filtros.</strong></br>" +
                    "<br/><strong>Filtros disponíveis:</strong>" +
                    "<ul>" +
                    "<li><strong>quicksearchValue:</strong> Filtra pelo nome da atividade</li>" +
                    "<li><strong>situacaoAtividade:</strong> Filtra pela situação da atividade (ex: [\"true\"] para ativos)</li>" +
                    "<li><strong>tiposEnuns:</strong> Filtra pelos tipos da atividade." +
                    "<strong>Valores disponíveis</strong>" +
                    "<ul>" +
                    "<li>0 - NEUROMUSCULAR (Neuromuscular)</li>" +
                    "<li>1 - CARDIOVASCULAR (Cardiovascular)</li>" +
                    "</ul>" +
                    "</li>" +
                    "<li><strong>atividades:</strong> Filtra por tipo de atividade, podendo ser [\"IA\"], [\"CONVENCIONAL\"] ou ambos</li>" +
                    "<li><strong>empresas:</strong> Filtra pelos códigos das empresas associadas à atividade (ex: [1, 2, 3])</li>" +
                    "<li><strong>aparelhos:</strong> Filtra pelas atividades associadas a determinados aparelhos (ex: [10, 20])</li>" +
                    "</ul>",
                    defaultValue = "{\"quicksearchValue\":\"costas\",\"situacaoAtividade\":[\"true\"],\"tiposEnuns\":[\"NEUROMUSCULAR\"],\"atividades\":[\"IA\"],\"empresas\":[1],\"aparelhos\":[10]}")
            @RequestParam(value = "filters", required = false) JSONObject filtros,
            @ApiParam(value = "Código único identificador da empresa em que a atividade está vinculada", defaultValue = "1")
            @RequestHeader(value = "empresaId", required = true) Integer empresaId,
            @ApiIgnore String tipo, @ApiIgnore PaginadorDTO paginadorDTO, @ApiIgnore boolean crossfit) throws JSONException {
        try {
            FiltroAtividadeJSON filtroAtividadeJSON = new FiltroAtividadeJSON(filtros);
            return ResponseEntityFactory.ok(atividadeService.listarAtividades(filtroAtividadeJSON, tipo, paginadorDTO, crossfit, empresaId), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(AtividadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar as atividades", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @ApiOperation(
            value = "Consultar atividades físicas para montagem de treino através de uma ficha de atividades.",
            notes = "Consulta as atividades físicas para montagem de um treino através de um ficha de atividade.",
            tags = "Atividades"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "tipo", value = "Tipo da atividade física que será buscada.<br/><strong>Valores disponíveis</strong>" +
                    "<ul>" +
                    "<li>0 - NEUROMUSCULAR (Neuromuscular)</li>" +
                    "<li>1 - CARDIOVASCULAR (Cardiovascular)</li>" +
                    "</ul>", defaultValue = "NEUROMUSCULAR", allowableValues = "NEUROMUSCULAR,CARDIOVASCULAR", paramType = "query"),
            @ApiImplicitParam(name = "crossfit", value = "Indica se deve buscar apenas por atividades do crossfit", defaultValue = "false", paramType = "query", dataType = "boolean"),
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.<br/> Ordena por um atributo contido no objeto da resposta." +
                    "<strong>Ordens disponíveis</strong>" +
                    "<ul>" +
                    "<li>ASC - Ordena pelo atributo escolhido de forma ascendente</li>" +
                    "<li>DESC - Ordena pelo atributo escolhido de forma descendente</li>" +
                    "</ul>" +
                    "Deve ser informado como atributo,ordem", defaultValue = "nome,asc", paramType = "query", dataType = "string"),
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaListAtividadeSimplesResponseTOPaginacao.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.ATIVIDADES)
    @RequestMapping(value = "/montarTreino/{ficha}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarAtividadesMontarTreino(
            @ApiParam(
                    value = "Filtros de busca, deve ser informado como um JSON codificado na URL." +
                            "<br/><strong>É necessário fazer o ENCODE na URL para realizar a requisição com os filtros.</strong></br>" +
                            "<br/><strong>Filtros disponíveis:</strong>" +
                            "<ul>" +
                            "<li><strong>quicksearchValue:</strong> Filtra pelo nome da atividade</li>" +
                            "<li><strong>grupoMuscularesIds:</strong> Lista de IDs dos grupos musculares</li>" +
                            "<li><strong>aparelhos:</strong> Lista de IDs dos aparelhos</li>" +
                            "<li><strong>categorias:</strong> Lista de IDs das categorias de atividade</li>" +
                            "<li><strong>niveis:</strong> Lista de IDs dos níveis da atividade</li>" +
                            "<li><strong>empresas:</strong> Lista de IDs das empresas</li>" +
                            "<li><strong>avoid:</strong> Lista de IDs de atividades que devem ser ignoradas</li>" +
                            "</ul>",
                    defaultValue = "{\"quicksearchValue\":\"Supino\", \"grupoMuscularesIds\":[1,2], \"aparelhos\":[3], \"categorias\":[4], \"niveis\":[5], \"empresas\":[1], \"avoid\":[10,11]}"
            )
            @RequestParam(value = "filters", required = false) JSONObject filtros,
            @ApiParam(value = "Código identificador da ficha de atividades que será usada para consulta", defaultValue = "1", required = true)
            @PathVariable Integer ficha,
            @ApiParam(value = "Código único identificador da empresa em que a atividade está vinculada", defaultValue = "1")
            @RequestHeader(value = "empresaId", required = true) Integer empresaId,
            @ApiIgnore String tipo, @ApiIgnore PaginadorDTO paginadorDTO, @ApiIgnore boolean crossfit) throws JSONException {
        try {
            ficha = ficha != -1 ? ficha : null;
            FiltroAtividadeJSON filtroAtividadeJSON = new FiltroAtividadeJSON(filtros.getJSONObject("filters"));
            paginadorDTO.setSize(0l);
            return ResponseEntityFactory.ok(atividadeService.listarAtividadesMontagemTreino(ficha, filtroAtividadeJSON, false, tipo, paginadorDTO, crossfit, empresaId), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(AtividadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar as atividades", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar atividades físicas para montagem de treino.",
            notes = "Consulta as atividades físicas para montagem de um treino sem precisar de uma ficha de atividades.",
            tags = "Atividades"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "tipo", value = "Tipo da atividade física que será buscada.<br/><strong>Valores disponíveis</strong>" +
                    "<ul>" +
                    "<li>0 - NEUROMUSCULAR (Neuromuscular)</li>" +
                    "<li>1 - CARDIOVASCULAR (Cardiovascular)</li>" +
                    "</ul>", defaultValue = "NEUROMUSCULAR", allowableValues = "NEUROMUSCULAR,CARDIOVASCULAR", paramType = "query"),
            @ApiImplicitParam(name = "crossfit", value = "Indica se deve buscar apenas por atividades do crossfit", defaultValue = "false", paramType = "query", dataType = "boolean"),
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.<br/> Ordena por um atributo contido no objeto da resposta." +
                    "<strong>Ordens disponíveis</strong>" +
                    "<ul>" +
                    "<li>ASC - Ordena pelo atributo escolhido de forma ascendente</li>" +
                    "<li>DESC - Ordena pelo atributo escolhido de forma descendente</li>" +
                    "</ul>" +
                    "Deve ser informado como atributo,ordem", defaultValue = "nome,asc", paramType = "query", dataType = "string"),
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaListAtividadeSimplesResponseTOPaginacao.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.ATIVIDADES)
    @RequestMapping(value = "/montarTreino", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarAtividadesMontarTreinoSemFicha(
            @ApiParam(
                    value = "Filtros de busca, deve ser informado como um JSON codificado na URL." +
                            "<br/><strong>É necessário fazer o ENCODE na URL para realizar a requisição com os filtros.</strong></br>" +
                            "<br/><strong>Filtros disponíveis:</strong>" +
                            "<ul>" +
                            "<li><strong>quicksearchValue:</strong> Filtra pelo nome da atividade</li>" +
                            "<li><strong>grupoMuscularesIds:</strong> Lista de IDs dos grupos musculares</li>" +
                            "<li><strong>aparelhos:</strong> Lista de IDs dos aparelhos</li>" +
                            "<li><strong>categorias:</strong> Lista de IDs das categorias de atividade</li>" +
                            "<li><strong>niveis:</strong> Lista de IDs dos níveis da atividade</li>" +
                            "<li><strong>empresas:</strong> Lista de IDs das empresas</li>" +
                            "<li><strong>avoid:</strong> Lista de IDs de atividades que devem ser ignoradas</li>" +
                            "</ul>",
                    defaultValue = "{\"quicksearchValue\":\"Supino\", \"grupoMuscularesIds\":[1,2], \"aparelhos\":[3], \"categorias\":[4], \"niveis\":[5], \"empresas\":[1], \"avoid\":[10,11]}")
            @RequestParam(value = "filters", required = false) JSONObject filtros,
            @ApiParam(value = "Código único identificador da empresa em que a atividade está vinculada", defaultValue = "1")
            @RequestHeader(value = "empresaId", required = true) Integer empresaId,
            @ApiIgnore String tipo, @ApiIgnore PaginadorDTO paginadorDTO, @ApiIgnore boolean crossfit) throws JSONException {
        try {
            FiltroAtividadeJSON filtroAtividadeJSON = new FiltroAtividadeJSON(filtros);
            paginadorDTO.setSize(100l);
            return ResponseEntityFactory.ok(atividadeService.listarAtividadesMontagemTreino(null, filtroAtividadeJSON, true, tipo, paginadorDTO, crossfit, empresaId), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(AtividadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar as atividades", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar atividade físicas.",
            notes = "Consulta as informações de uma atividade física.",
            tags = "Atividades"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaAtividadeCompletaResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.ATIVIDADES)
    @RequestMapping(value = "/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> buscarAtividade(
            @ApiParam(value = "Código único identificador da atividade que será consultada", defaultValue = "1", required = true)
            @PathVariable("id") final Integer id) {
        try {
            return ResponseEntityFactory.ok(atividadeService.buscarAtividade(id));
        } catch (ServiceException e) {
            Logger.getLogger(AtividadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar buscar a atividade", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Replicar atividade física de uma matriz para uma empresa filial",
            notes = "Replica uma atividade física de uma empresa matriz para uma empresa filial.",
            tags = "Atividades"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaAtividadeCompletaResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.ATIVIDADES)
    @RequestMapping(value = "/replicar/{ctxMatriz}/{codigoEmpresaZWMatriz}/{ctxFilial}/{codigoEmpresaZWFilial}/{status}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> replicarAtividade(
            @ApiParam(value = "CTX da empresa matriz", defaultValue = "ctxPactoGO", required = true)
            @PathVariable("ctxMatriz") String ctxMatriz,
            @ApiParam(value = "Código da empresa matriz no sistema ZW", defaultValue = "1", required = true)
            @PathVariable("codigoEmpresaZWMatriz") final Integer codigoEmpresaZWMatriz,
            @ApiParam(value = "CTX da empresa filial", defaultValue = "ctxPactoSP", required = true)
            @PathVariable("ctxFilial") String ctxFilial,
            @ApiParam(value = "Código da empresa filial no sistema ZW", defaultValue = "2", required = true)
            @PathVariable("codigoEmpresaZWFilial") final Integer codigoEmpresaZWFilial,
            @ApiParam(value = "Indica se a atividade deve ficar como ativa na filial quando for duplicada", required = true, defaultValue = "true")
            @PathVariable("status") Boolean status) {
        try {
            return ResponseEntityFactory.ok(atividadeService.replicarAtividade(ctxMatriz, codigoEmpresaZWMatriz, ctxFilial, codigoEmpresaZWFilial, status));
        } catch (ServiceException e) {
            Logger.getLogger(AtividadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar replicar as atividades", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar empresas que estão disponíveis para o cadastro de atividades",
            notes = "Consulta as informações das empresas disponíveis para o cadastro de atividades.",
            tags = "Atividades"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaListAtividadeEmpresaResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.ATIVIDADES)
    @RequestMapping(value = "/config-empresa", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> configEmpresa() {
        try {
            return ResponseEntityFactory.ok(atividadeService.configsEmpresa());
        } catch (ServiceException e) {
            Logger.getLogger(AtividadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar buscar a atividade", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar todas as atividades físicas",
            notes = "Consulta as informações de todas as atividades físicas cadastradas no sistema. As informações incluem: Grupos musculares que serão exercitados, aparelhos que devem ser utilizados, entre outras informações pertinentes a atividade física.",
            tags = "Atividades"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "crossfit", value = "Indica se deve buscar apenas por atividades do crossfit", defaultValue = "false", paramType = "query", dataType = "boolean"),
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaListAtividadeCompletaResponseTOPaginacao.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.ATIVIDADES)
    @RequestMapping(value = "/all", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarTodasAtividades(
            @ApiParam(value = "Filtros de busca, deve ser informado como um JSON." +
                    "<br/><strong>É necessário fazer o ENCODE na URL para realizar a requisição com os filtros.</strong></br>" +
                    "<br/><strong>Filtros disponíveis:</strong>" +
                    "<ul>" +
                    "<li><strong>crossfit:</strong>Filtra se a atividade é de crossfit (Deve ser informado como um booleano: true/false)</li>" +
                    "<li><strong>quicksearchValue:</strong>Filtra pelo nome da atividade</li>" +
                    "<li><strong>quicksearchFields:</strong>Define em qual campo o 'quicksearchValue' será aplicado (Para filtrar pelo nome, deve ser informado como uma lista ex: [\"nome\"])</li>" +
                    "<li><strong>index:</strong>Define o índice inicial para a paginação dos resultados</li>" +
                    "<li><strong>maxResults:</strong>Define a quantidade máxima de resultados por página</li>" +
                    "</ul>", defaultValue = "{\"crossfit\":true, \"quicksearchValue\":\"WOD\", \"quicksearchFields\":[\"nome\"], \"index\":0, \"maxResults\":10}")
            @RequestParam(value = "filters", required = false) JSONObject filtros, @ApiIgnore boolean crossfit) throws JSONException {
        try {
            FiltroAtividadeCrossfitJSON filtroAtividadeCrossfitJSON = new FiltroAtividadeCrossfitJSON(filtros);
            /**
             * temporario até o harlei arrumar o front
             */
            filtroAtividadeCrossfitJSON.setCrossfit(crossfit);
            return ResponseEntityFactory.ok(atividadeService.listarTodasAtividades(filtroAtividadeCrossfitJSON));
        } catch (ServiceException e) {
            Logger.getLogger(AtividadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar todas as atividades", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Cadastrar atividade física",
            notes = "Cadastra uma nova atividade física no sistema. As informações incluem: Grupos musculares que serão exercitados, aparelhos que devem ser utilizados, entre outras informações pertinentes a atividade física.",
            tags = "Atividades"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaAtividadeCompletaResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.ATIVIDADES)
    @RequestMapping(value = "", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cadastrarAtividade(
            @ApiParam(value = "Informações da atividade física.")
            @RequestBody AtividadeTO atividadeTO) {
        try {
            return ResponseEntityFactory.ok(atividadeService.cadastrarAtividade(atividadeTO));
        } catch (ServiceException e) {
            Logger.getLogger(AtividadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar cadastrar atividade", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Atualizar atividade física",
            notes = "Atualiza as informações de uma atividade física. As informações incluem: Grupos musculares que serão exercitados, aparelhos que devem ser utilizados, entre outras informações pertinentes a atividade física.",
            tags = "Atividades"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaAtividadeCompletaResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.ATIVIDADES)
    @RequestMapping(value = "/{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> atualizarAtividade(@ApiParam(value = "Código da atividade física que será atualizada", defaultValue = "1", required = true)
                                                                  @PathVariable("id") final Integer id,
                                                                  @ApiParam(value = "Informações para atualização da atividade física. Não é necessário informar o código.")
                                                                  @RequestBody AtividadeTO atividadeTO) {
        try {
            atividadeTO.setId(id);
            return ResponseEntityFactory.ok(atividadeService.atualizarAtividade(atividadeTO));
        } catch (ServiceException e) {
            Logger.getLogger(AtividadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar atualizar atividade", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Marcar as atividades para serem usadas numa pescrição de treino",
            notes = "Marca as atividades para serem usadas numa pescrição de treino.",
            tags = "Atividades"
    )
    @ApiResponses(
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida). Não retorna corpo de resposta.", response = ExemploRespostaVazia.class)
    )
    @ResponseBody
    @Permissao(recursos = RecursoEnum.ATIVIDADES)
    @RequestMapping(value = "/atualizar-prescricao-ia/{marcar}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> atualizarAtividade(@ApiParam(value = "Indica se deve ser usado para marcar a atividade", allowableValues = "true,false", required = true, defaultValue = "true")
                                                                  @PathVariable("marcar") final String marcar) {
        try {
            atividadeService.toggleAtividadesIA(marcar.equals("t"));
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(AtividadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar atualizar atividade", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Atualizar situação de uma atividade",
            notes = "Atualiza a situação de uma atividade. Se a atividade estiver como ativa (true), coloca ela como inativa (false).",
            tags = "Atividades"
    )
    @ApiResponses(
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida). Não retorna corpo de resposta.", response = ExemploRespostaVazia.class)
    )
    @ResponseBody
    @Permissao(recursos = RecursoEnum.ATIVIDADES)
    @RequestMapping(value = "situacao/{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> atualizarStituacaoAtividade(@ApiParam(value = "Código da atividade física que será atualizada", defaultValue = "1", required = true)
                                                                           @PathVariable("id") final Integer id,
                                                                           @ApiParam(value = "Informações para atualização da atividade física.")
                                                                           @RequestBody AtividadeTO atividadeTO) {
        try {
            atividadeTO.setId(id);
            atividadeService.atualizarSituacaoAtividade(atividadeTO, id);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(AtividadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar atualizar atividade", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Excluir uma atividade física",
            notes = "Deleta as informações da atividade física informada através do id.",
            tags = "Atividades"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaBoolean.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.ATIVIDADES)
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> removerAtividade(@ApiParam(value = "Código identificador da atividade que será excluída", defaultValue = "1", required = true)
                                                                @PathVariable("id") final Integer id) {
        try {
            atividadeService.removerAtividade(id);
            return ResponseEntityFactory.ok(true);
        } catch (ServiceException e) {
            Logger.getLogger(AtividadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar excluir atividade", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Reduzir tamanho em megabytes de um gif de uma atividade física",
            notes = "Reduz o tamanho em megabytes de um gif de uma atividade física.",
            tags = "Atividades"
    )
    @ResponseBody
    @Permissao(recursos = RecursoEnum.ATIVIDADES)
    @RequestMapping(value = "/reduzir-tamanho-gif", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> reduzirTamanhoGif(@ApiParam(value = "Tamanho máximo em megabytes do gif", defaultValue = "1024")
                                                                 @RequestParam(value = "tamanhoMaximoMB", required = true) double tamanhoMaximoMB,
                                                                 @ApiParam(value = "Indica que deve fazer apenas a validação do tamanho do GIF sem reduzir ele", defaultValue = "false")
                                                                 @RequestParam(value = "somenteValidacao", required = true) boolean somenteValidacao,
                                                                 @ApiParam(value = "Código da atividade que terá o tamanho do GIF reduzido", defaultValue = "1")
                                                                 @RequestParam(value = "idAtividade", required = false) Integer idAtividade) {
        try {
            return ResponseEntityFactory.ok(atividadeService.processoReduzirTamanhoGif(tamanhoMaximoMB, somenteValidacao, idAtividade));
        } catch (ServiceException e) {
            Logger.getLogger(AtividadeController.class.getName()).log(Level.SEVERE, "Erro ao reduzir gif", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.ATIVIDADES)
    @RequestMapping(value = "/excluir-fotokey-aws-csv", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> excluirFotokeyAwsCsv(@RequestBody Map<String, String> requestBody) {
        try {
            /*
             * Para este fluxo deve-se obrigatoriamente o arquivo ser do tipo .csv e possuir somente a coluna contendo o valor de fotokey
             */
            String csvBase64Data = requestBody.get("fileBase64");
            return ResponseEntityFactory.ok(atividadeService.excluirFotokeyAwsCsv(csvBase64Data));
        } catch (ServiceException e) {
            Logger.getLogger(AtividadeController.class.getName()).log(Level.SEVERE, "Erro ao remover fotokey da aws s3", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.ATIVIDADES)
    @RequestMapping(value = "/replicar/imagens/{ctxMatriz}/{codigoEmpresaZWMatriz}/{ctxFilial}/{codigoEmpresaZWFilial}/{substituirImagens}",
            method = RequestMethod.GET,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> replicarImagensAtividades(
            @PathVariable("ctxMatriz") String ctxMatriz,
            @PathVariable("codigoEmpresaZWMatriz") final Integer codigoEmpresaZWMatriz,
            @PathVariable("ctxFilial") String ctxFilial,
            @PathVariable("codigoEmpresaZWFilial") final Integer codigoEmpresaZWFilial,
            @PathVariable("substituirImagens") Boolean substituirImagens) {
        try {
            return ResponseEntityFactory.ok(
                    atividadeService.replicarImagensAtividades(ctxMatriz, codigoEmpresaZWMatriz, ctxFilial, codigoEmpresaZWFilial, substituirImagens)
            );
        } catch (ServiceException e) {
            Logger.getLogger(AtividadeController.class.getName()).log(Level.SEVERE, "Erro ao replicar as imagens das atividades", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Desativar atividades geradas por Inteligência Artificial",
            notes = "Desativa as atividades que foram geradas pela inteligência artificial do Treino por IA.",
            tags = "Atividades"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaDesativarAtividadesIA.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.ATIVIDADES)
    @RequestMapping(
            value = "/desativar-atividades-idia",
            method = RequestMethod.PUT,
            produces = MediaType.APPLICATION_JSON_VALUE
    )
    public ResponseEntity<EnvelopeRespostaDTO> desativarAtividadesComIdia() {
        try {
            return ResponseEntityFactory.ok(atividadeService.desativarAtividadesComIdiaPreenchido());
        } catch (ServiceException e) {
            Logger.getLogger(AtividadeController.class.getName())
                    .log(Level.SEVERE, "Erro ao desativar atividades com idia e idia2 preenchidos", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.ATIVIDADES)
    @RequestMapping(value = "/remover-geradas-ia", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> removerAtividadesGeradasPorIA() {
        try {
            RelatorioExclusaoAtividadeIADTO relatorio = atividadeService.removerAtividadesGeradasPorIA(false);
            return ResponseEntityFactory.ok(relatorio);
        } catch (ServiceException e) {
            Logger.getLogger(AtividadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar remover atividades geradas por IA", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.ATIVIDADES)
    @RequestMapping(value = "/remover-geradas-ia/{forcarExclusaoTotal}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> removerAtividadesGeradasPorIAComModo(
            @PathVariable("forcarExclusaoTotal") Boolean forcarExclusaoTotal) {
        try {
            RelatorioExclusaoAtividadeIADTO relatorio = atividadeService.removerAtividadesGeradasPorIA(forcarExclusaoTotal);
            return ResponseEntityFactory.ok(relatorio);
        } catch (ServiceException e) {
            Logger.getLogger(AtividadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar remover atividades geradas por IA", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.ATIVIDADES)
    @RequestMapping(value = "/replicar-substituindo/{ctxOrigem}/{codigoEmpresaZWOrigem}/{ctxDestino}/{codigoEmpresaZWDestino}", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> replicarAtividadesSubstituindo(@PathVariable("ctxOrigem") String ctxOrigem,
                                                                              @PathVariable("codigoEmpresaZWOrigem") Integer codigoEmpresaZWOrigem,
                                                                              @PathVariable("ctxDestino") String ctxDestino,
                                                                              @PathVariable("codigoEmpresaZWDestino") Integer codigoEmpresaZWDestino) {
        try {
            return ResponseEntityFactory.ok(atividadeService.replicarAtividadesSubstituindo(ctxOrigem, codigoEmpresaZWOrigem, ctxDestino, codigoEmpresaZWDestino));
        } catch (ServiceException e) {
            Logger.getLogger(AtividadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar replicar atividades substituindo as existentes", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
}
