package br.com.pacto.controller.json.professor;

import br.com.pacto.bean.bi.IndicadorDashboardEnum;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Created by paulo 28/11/2018
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Configuração de ranking de professores")
public class ConfiguracaoRankingDTO {

    @ApiModelProperty(value = "Identificador único da configuração", example = "1")
    private Integer id;

    @ApiModelProperty(value = "Indicador do dashboard utilizado na configuração")
    private IndicadorDashboardEnum indicador;

    @ApiModelProperty(value = "Indica se a operação é positiva (true) ou negativa (false)", example = "true")
    private Boolean operacao = true;

    @ApiModelProperty(value = "Indica se a configuração está ativa", example = "false")
    private Boolean ativa = false;

    @ApiModelProperty(value = "Peso atribuído ao indicador no cálculo do ranking", example = "1.5")
    private Double peso;

    @ApiModelProperty(value = "Pontuação calculada para o indicador", example = "85.5")
    private String pontuacao;

    public ConfiguracaoRankingDTO() {
    }

    public ConfiguracaoRankingDTO(IndicadorDashboardEnum indicador) {
        this.indicador = indicador;
        this.operacao = true;
        this.ativa = false;
        this.peso = 0.0;
        this.pontuacao = "0";
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public IndicadorDashboardEnum getIndicador() {
        return indicador;
    }

    public void setIndicador(IndicadorDashboardEnum indicador) {
        this.indicador = indicador;
    }

    public Boolean getOperacao() {
        return operacao;
    }

    public void setOperacao(Boolean operacao) {
        this.operacao = operacao;
    }

    public Double getPeso() {
        return peso;
    }

    public void setPeso(Double peso) {
        this.peso = peso;
    }

    public Boolean getAtiva() {
        return ativa;
    }

    public void setAtiva(Boolean ativa) {
        this.ativa = ativa;
    }

    public String getPontuacao() {
        return pontuacao;
    }

    public void setPontuacao(String pontuacao) {
        this.pontuacao = pontuacao;
    }

    public String getIndicadorSort(){
        return indicador == null ? "" : indicador.getLabelSort();
    }
}
