package br.com.pacto.controller.json.colaborador;

import br.com.pacto.bean.usuario.Usuario;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * paulo 19/10/2018
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "DTO de resposta simplificado para listagem de usuários colaboradores")
public class UsuarioResponseTO {

    @ApiModelProperty(value = "Identificador único do usuário", example = "1")
    private Integer id;

    @ApiModelProperty(value = "Nome completo do usuário colaborador", example = "João Silva")
    private String nome;

    @ApiModelProperty(value = "Nome de usuário para acesso ao aplicativo", example = "joao.silva")
    private String appUserName;

    public UsuarioResponseTO(Usuario usuario) {
        this.id          = usuario.getCodigo();
        this.nome        = usuario.getProfessor().getPessoa().getNome();
        this.appUserName = usuario.getUserName();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getAppUserName() {
        return appUserName;
    }

    public void setAppUserName(String appUserName) {
        this.appUserName = appUserName;
    }
}
