package br.com.pacto.controller.json.professor;

import br.com.pacto.bean.professor.ProfessorSintetico;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Resposta contendo informações sobre professores e alunos com aviso médico")
public class ProfessoresAlunosAvisoMedicoResponseDTO {

    @ApiModelProperty(value = "Dados básicos do professor")
    private ProfessorSimplesResponseDTO professor;

    @ApiModelProperty(value = "Nome do professor ou aluno", example = "Maria Santos")
    private String nome;

    @ApiModelProperty(value = "Quantidade de alunos com mensagem de aviso médico", example = "3")
    private Number qtdAlunosMsg;

    @ApiModelProperty(value = "Número da matrícula do aluno", example = "123456")
    private String matricula;

    @ApiModelProperty(value = "Situação atual do aluno", example = "Ativo")
    private String situacao;

    @ApiModelProperty(value = "Data de término do contrato", example = "2025-12-31")
    private String terminoContrato;

    @ApiModelProperty(value = "Data de término do programa vigente", example = "2025-10-15")
    private String terminoProgramaVigente;

    @ApiModelProperty(value = "Descrição do aviso médico", example = "Restrição para exercícios de impacto")
    private String avisoMedico;

    public ProfessoresAlunosAvisoMedicoResponseDTO() {
    }

    public ProfessoresAlunosAvisoMedicoResponseDTO(ProfessorSintetico professorSintetico){
        this.professor = new ProfessorSimplesResponseDTO(professorSintetico);
        this.nome = professorSintetico.getNomeAbreviado();
    }

    public ProfessorSimplesResponseDTO getProfessor() {
        return professor;
    }

    public void setProfessor(ProfessorSimplesResponseDTO professor) {
        this.professor = professor;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Number getQtdAlunosMsg() {
        return qtdAlunosMsg;
    }

    public void setQtdAlunosMsg(Number qtdAlunosMsg) {
        this.qtdAlunosMsg = qtdAlunosMsg;
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public String getTerminoContrato() {
        return terminoContrato;
    }

    public void setTerminoContrato(String terminoContrato) {
        this.terminoContrato = terminoContrato;
    }

    public String getTerminoProgramaVigente() {
        return terminoProgramaVigente;
    }

    public void setTerminoProgramaVigente(String terminoProgramaVigente) {
        this.terminoProgramaVigente = terminoProgramaVigente;
    }

    public String getAvisoMedico() {
        return avisoMedico;
    }

    public void setAvisoMedico(String avisoMedico) {
        this.avisoMedico = avisoMedico;
    }
}
