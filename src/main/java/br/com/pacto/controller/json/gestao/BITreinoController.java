package br.com.pacto.controller.json.gestao;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.bi.DashboardBI;
import br.com.pacto.bean.bi.IndicadorDashboardEnum;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.professor.Professor<PERSON>int<PERSON>;
import br.com.pacto.controller.json.SuperController;
import br.com.pacto.controller.json.exportar.ExportarPDF;
import br.com.pacto.controller.json.exportar.ExportarXLS;
import br.com.pacto.enumerador.agenda.StatusAgendamentoEnum;
import br.com.pacto.enumerador.agenda.TipoAgendamentoEnum;
import br.com.pacto.enumerador.cliente.TiposAcompanhamentoEnum;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.cliente.ClienteSinteticoService;
import br.com.pacto.service.intf.gestao.BITreinoService;
import br.com.pacto.service.intf.gestao.DashboardBIService;
import br.com.pacto.service.intf.professor.ProfessorSinteticoService;
import br.com.pacto.swagger.respostas.ExemploRespostaVazia;
import br.com.pacto.swagger.respostas.treino.*;
import br.com.pacto.swagger.respostas.variaveis.ExemploRespostaBoolean;
import io.swagger.annotations.*;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Array;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR> Ramos
 * @since 29/11/2018
 */

@Api(tags = "BI Treino", description = "Endpoints para Business Intelligence relacionados a treinos, incluindo dashboards, indicadores, relatórios e análises de dados de treinamento")
@Controller
@RequestMapping("/psec/treino-bi")
public class BITreinoController extends SuperController {

    private final BITreinoService biService;
    @Autowired
    private SessaoService sessaoService;
    @Autowired
    private ClienteSinteticoService clienteSinteticoService;
    @Autowired
    private ProfessorSinteticoService professorSinteticoService;
    @Autowired
    private DashboardBIService dashboardBIService;


    @Autowired
    public BITreinoController(BITreinoService bi) {
        Assert.notNull(bi, "O serviço O serviço do BI do treino não foi injetado corretamente");
        this.biService = bi;
    }


    @ApiOperation(value = "Obter dados de Business Intelligence de treino", notes = "Retorna dados consolidados de BI relacionados a treinos para um professor específico")
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "Dados de BI obtidos com sucesso", response = ExemploRespostaBITreinoResponseDTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "/dados", params = {"idProfessor"}, method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterListaWods(
            @ApiParam(value = "ID do professor para filtrar os dados de BI", required = true, defaultValue = "1")
            @RequestParam("idProfessor") Integer idProfessor,
            @ApiParam(value = "ID da empresa", required = true, defaultValue = "1")
            @RequestHeader("empresaId") Integer empresaId) {
        try {
            return ResponseEntityFactory.ok(biService.gerarBI(idProfessor, empresaId));
        } catch (ServiceException e) {
            Logger.getLogger(BITreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter BI", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(value = "Atualizar indicadores de Business Intelligence", notes = "Atualiza e retorna o dashboard de BI com os indicadores mais recentes para um professor específico")
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "Indicadores atualizados com sucesso", response = ExemploRespostaDashboardBI.class),
            @ApiResponse(code = 500, message = "Erro interno do servidor")
    })
    @ResponseBody
    @RequestMapping(value = "/atualizar", params = {"idProfessor"}, method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> atualizarIndicadores(
            @ApiParam(value = "ID do professor para atualizar os indicadores", required = true, defaultValue = "1")
            @RequestParam("idProfessor") Integer idProfessor,
            @ApiParam(value = "ID da empresa", required = true, defaultValue = "1")
            @RequestHeader("empresaId") Integer empresaId,
            @ApiParam(value = "Código da pessoa para validação", required = true, defaultValue = "1")
            @RequestParam("codigoPessoa") Integer codigoPessoa) {
        try {
            if (idProfessor == (-1)) {
                idProfessor = 0;
            } else {
                idProfessor = verificarIdProfessor(idProfessor, empresaId, codigoPessoa);
            }
            DashboardBI dash = biService.atualizarDash(idProfessor, empresaId);
            return ResponseEntityFactory.ok(dash);
        } catch (ServiceException e) {
            Logger.getLogger(BITreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter BI", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(value = "Obter dashboard de Business Intelligence", notes = "Retorna o dashboard completo de BI com todos os indicadores para um professor específico")
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "Dashboard obtido com sucesso", response = ExemploRespostaDashboardBI.class),
            @ApiResponse(code = 500, message = "Erro interno do servidor")
    })
    @ResponseBody
    @RequestMapping(value = "/dash", params = {"idProfessor"}, method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterDashboard(
            @ApiParam(value = "ID do professor para obter o dashboard", required = true, defaultValue = "1")
            @RequestParam("idProfessor") Integer idProfessor,
            @ApiParam(value = "ID da empresa", required = true, defaultValue = "1")
            @RequestHeader("empresaId") Integer empresaId,
            @ApiParam(value = "ID da pessoa para validação", required = true, defaultValue = "1")
            @RequestParam("idPessoa") Integer idPessoa) {
        try {
            idProfessor = verificarIdProfessor(idProfessor, empresaId, idPessoa);
            if (idProfessor == (-1)) {
                idProfessor = 0;
            }
            return ResponseEntityFactory.ok(biService.obterDash(idProfessor, empresaId));
        } catch (ServiceException e) {
            Logger.getLogger(BITreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter BI", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(value = "Obter dados de carteira de Business Intelligence", notes = "Retorna informações de BI relacionadas à carteira de alunos de um professor")
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "Dados de carteira obtidos com sucesso", response = ExemploRespostaBITreinoCarteiraDTO.class),
            @ApiResponse(code = 500, message = "Erro interno do servidor")
    })
    @ResponseBody
    @RequestMapping(value = "/carteira", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> carteira(
            @ApiParam(value = "ID do professor (opcional)", required = false, defaultValue = "1")
            @RequestParam(value = "professorId", required = false) Integer idProfessor,
            @ApiParam(value = "ID da empresa", required = true, defaultValue = "1")
            @RequestHeader("empresaId") Integer empresaId) {
        try {
            idProfessor = verificarIdProfessor(idProfessor, empresaId, null);
            if (idProfessor == (-1)) {
                idProfessor = 0;
            }
            return ResponseEntityFactory.ok(biService.biCarteira(idProfessor, empresaId));
        } catch (ServiceException e) {
            Logger.getLogger(BITreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter BI", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(value = "Processar dados de Business Intelligence", notes = "Executa o processamento dos dados de BI para um professor específico")
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "Dados processados com sucesso", response = br.com.pacto.swagger.respostas.ExemploRespostaVazia.class)
    })
    @ResponseBody
    @RequestMapping(value = "/processar-dados", params = {"professorId"},
            method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> procesardados(
            @ApiParam(value = "ID do professor para processar os dados", required = true, defaultValue = "1")
            @RequestParam("professorId") Integer idProfessor,
            @ApiParam(value = "ID da empresa", required = true, defaultValue = "1")
            @RequestHeader("empresaId") Integer empresaId) {
        try {
            idProfessor = verificarIdProfessor(idProfessor, empresaId, null);
            if (idProfessor == (-1)) {
                idProfessor = 0;
            }
            biService.processar(idProfessor, empresaId);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(BITreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter BI", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(value = "Obter dados de treinamento de Business Intelligence", notes = "Retorna informações de BI relacionadas aos treinamentos de um professor")
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "Dados de treinamento obtidos com sucesso", response = ExemploRespostaBITreinoTreinamentoDTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "/treinamento",
            method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> treinamento(
            @ApiParam(value = "ID do professor (opcional)", required = false, defaultValue = "1")
            @RequestParam(value = "professorId", required = false) Integer idProfessor,
            @ApiParam(value = "ID da empresa", required = true, defaultValue = "1")
            @RequestHeader("empresaId") Integer empresaId) {
        try {
            idProfessor = verificarIdProfessor(idProfessor, empresaId, null);
            if (idProfessor == (-1)) {
                idProfessor = 0;
            }
            return ResponseEntityFactory.ok(biService.treinamento(idProfessor, empresaId));
        } catch (ServiceException e) {
            Logger.getLogger(BITreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter BI", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(value = "Obter dados de agenda de Business Intelligence", notes = "Retorna informações de BI relacionadas à agenda e agendamentos de um professor")
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "Dados de agenda obtidos com sucesso", response = ExemploRespostaBITreinoAgendaDTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "/agenda", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> agenda(
            @ApiParam(value = "ID do professor (opcional)", required = false, defaultValue = "1")
            @RequestParam(value = "professorId", required = false) Integer idProfessor,
            @ApiParam(value = "ID da empresa", required = true, defaultValue = "1")
            @RequestHeader("empresaId") Integer empresaId) {
        try {
            idProfessor = verificarIdProfessor(idProfessor, empresaId, null);
            if (idProfessor == (-1)) {
                idProfessor = 0;
            }
            BITreinoAgendaDTO biTreinoAgendaDTO = biService.agenda(idProfessor, empresaId);
            return ResponseEntityFactory.ok(biTreinoAgendaDTO);
        } catch (ServiceException e) {
            Logger.getLogger(BITreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter BI", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(value = "Listar alunos com execução de treino nos últimos dias", notes = "Retorna lista paginada de alunos que executaram treinos nos últimos dias com base nos filtros especificados")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.<br/> Ordena por um atributo contido no objeto da resposta." +
                    "<strong>Ordens disponíveis</strong>" +
                    "<ul>" +
                    "<li>ASC - Ordena pelo atributo escolhido de forma ascendente</li>" +
                    "<li>DESC - Ordena pelo atributo escolhido de forma descendente</li>" +
                    "</ul>" +
                    "Deve ser informado como atributo,ordem", defaultValue = "nome,asc", paramType = "query", dataType = "string"),
    })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "Lista de alunos obtida com sucesso", response = ExemploRespostaListAlunoSimplesDTOPaginacao.class)
    })
    @ResponseBody
    @RequestMapping(value = "/lista-alunos-execucao-treino-ultimos-dias/{empresaId}/{dia}/{periodo}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listAlunosExecucaoTreinoUltimosDias(
            @ApiParam(value = "ID da empresa", required = true, defaultValue = "1")
            @PathVariable("empresaId") Integer empresaId,
            @ApiParam(value = "Dia específico para filtrar execuções", required = true, defaultValue = "1")
            @PathVariable("dia") Integer dia,
            @ApiParam(value = "Período do dia para filtrar execuções (manhã, tarde, noite)", required = true, defaultValue = "manha")
            @PathVariable("periodo") String periodo,
            @ApiParam(value = "Filtros de busca, deve ser informado como um JSON.\n\n" +
                    "<strong>É necessário fazer o ENCODE na URL para realizar a requisição com os filtros.</strong>\n\n" +
                    "<strong>Filtros disponíveis:</strong>\n" +
                    "- <strong>quicksearchValue:</strong> Termo de busca para nome do aluno.",
                    defaultValue = "{\"quicksearchValue\":\"Silva\"}")
            @RequestParam(value = "filters", required = false) JSONObject filter,
            @ApiIgnore PaginadorDTO paginadorDTO) {
        try {
            return ResponseEntityFactory.ok(biService.listAlunosExecucaoTreinoUltimosDias(0, empresaId, dia, periodo, filter, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(BITreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter BI", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    // ---------------- métodos de listagem de alunos dos BIs

    private ResponseEntity<EnvelopeRespostaDTO> listaGenerica(Integer idProfessor, Integer empresaId, IndicadorDashboardEnum indicador, PaginadorDTO paginadorDTO, String filter) {
        try {
            if (paginadorDTO.getSort() != null) {
                return ResponseEntityFactory.ok(biService.listaBI(idProfessor, empresaId, indicador, paginadorDTO, filter), paginadorDTO);
            } else if (paginadorDTO.getSize() == null) {
                return ResponseEntityFactory.ok(biService.listaBI(idProfessor, empresaId, indicador, null, filter), paginadorDTO);
            } else {
                return ResponseEntityFactory.ok(biService.listaBI(idProfessor, empresaId, indicador, paginadorDTO, filter), paginadorDTO);
            }
        } catch (ServiceException e) {
            Logger.getLogger(BITreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter lista BI " + indicador, e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    private ResponseEntity<EnvelopeRespostaDTO> listaGenericaRequest(Integer idProfessor, Integer empresaId, IndicadorDashboardEnum indicador, PaginadorDTO paginadorDTO, String filter, HttpServletRequest request) {
        try {
            return ResponseEntityFactory.ok(biService.listaBIRequest(idProfessor, empresaId, indicador, paginadorDTO, filter, request), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(BITreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter lista BI " + indicador, e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    private ResponseEntity<EnvelopeRespostaDTO> listaDisponibilidades(Integer idProfessor, Integer empresaId, StatusAgendamentoEnum statusAg, TipoAgendamentoEnum tipoAg, String filter) {
        try {
            IndicadorDashboardEnum indicadorSelecionado = statusAg == null ? IndicadorDashboardEnum.DISPONIBILIDADES : statusAg.getIndicador();
            return ResponseEntityFactory.ok(biService.listaDisponibilidades(empresaId, idProfessor, indicadorSelecionado, statusAg, tipoAg, filter));
        } catch (ServiceException e) {
            Logger.getLogger(BITreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter BI", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(value = "Listar alunos ativos com treino", notes = "Retorna lista paginada de alunos ativos que possuem treino ativo")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.<br/> Ordena por um atributo contido no objeto da resposta." +
                    "<strong>Ordens disponíveis</strong>" +
                    "<ul>" +
                    "<li>ASC - Ordena pelo atributo escolhido de forma ascendente</li>" +
                    "<li>DESC - Ordena pelo atributo escolhido de forma descendente</li>" +
                    "</ul>" +
                    "Deve ser informado como atributo,ordem", defaultValue = "nome,asc", paramType = "query", dataType = "string"),
    })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "Lista de alunos ativos com treino obtida com sucesso", response = ExemploRespostaListMapStringObjectPaginacao.class)
    })
    @ResponseBody
    @RequestMapping(value = "/alunos-ativos-treino/{codigoPessoa}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alunosativostreino(
            @ApiParam(value = "ID do professor (opcional)", required = false, defaultValue = "1")
            @RequestParam(value = "professorId", required = false) Integer idProfessor,
            @ApiParam(value = "Filtros de busca em formato JSON", required = false, defaultValue = "{\"inicio\":1640995200000,\"fim\":1672531199000}")
            @RequestParam(value = "filters", required = false) String filter,
            @ApiParam(value = "ID da empresa", required = true, defaultValue = "1")
            @RequestHeader("empresaId") Integer empresaId,
            @ApiParam(value = "Código da pessoa", required = true, defaultValue = "1")
            @PathVariable Integer codigoPessoa,
            @ApiIgnore PaginadorDTO paginadorDTO) {
        Integer[] filtro = verificarFiltroProfessorDash(filter);
        idProfessor = filtro[0] != null ? filtro[0] : 0;
        codigoPessoa = filtro[1] == 0 ? codigoPessoa : filtro[1];
        if (idProfessor != 0) {
            idProfessor = verificarIdProfessor(idProfessor, empresaId, codigoPessoa);
        }
        return listaGenerica(idProfessor, empresaId, IndicadorDashboardEnum.ATIVOS_COM_TREINO, paginadorDTO, filter);
    }

    @ApiOperation(value = "Exportar lista de alunos ativos", notes = "Gera arquivo de exportação (XLS ou PDF) com lista de alunos ativos")
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "URL do arquivo gerado com sucesso", response = ExemploRespostaUrlExportacao.class)
    })
    @ResponseBody
    @RequestMapping(value = "/alunos-ativos/exportar", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alunosAtivosTreinoExportar(
            @ApiParam(value = "URL base para treino", required = false, defaultValue = "http://localhost:8080")
            @RequestParam(value = "urlTreino", required = false) String urlTreino,
            @ApiParam(value = "Filtros em formato JSON", required = false, defaultValue = "{\"professorId\":1}")
            @RequestParam(value = "filtros", required = false) String filter,
            @ApiParam(value = "Formato do arquivo de exportação", required = false, defaultValue = "xls")
            @RequestParam(value = "format", required = false) String format,
            @ApiParam(value = "Campo para ordenação", required = false, defaultValue = "nome")
            @RequestParam(value = "sortField", required = false) String sortField,
            @ApiParam(value = "Direção da ordenação", required = false, defaultValue = "asc")
            @RequestParam(value = "sortDirection", required = false) String sortDirection,
            @ApiParam(value = "ID da empresa", required = true, defaultValue = "1")
            @RequestHeader("empresaId") Integer empresaId,
            @ApiIgnore PaginadorDTO paginadorDTO,
            HttpServletRequest request) {
        try {
            paginadorDTO.setSort(sortField + "," + sortDirection);
            paginadorDTO.setPage((long) 0);
            Integer idProfessor = Integer.parseInt(new JSONObject(filter).get("professorId").toString());
            idProfessor = verificarFiltroProfessor(idProfessor, filter);
            List<LinkedHashMap<String, Object>> lista = biService.listaBILinkedMap(idProfessor, empresaId, IndicadorDashboardEnum.ATIVOS, paginadorDTO, filter);
            String url = "";
            url = processarUrl(request, url, format, urlTreino, lista, "Alunos Ativos");
            return ResponseEntityFactory.ok(url);
        } catch (Exception e) {
            Logger.getLogger(BITreinoController.class.getName()).log(Level.SEVERE, "Erro ao gerar o arquivo", e);
            return ResponseEntityFactory.erroInterno("erro ao gerar o arquivo", e.getMessage());
        }
    }

    @ApiOperation(value = "Exportar lista total de alunos", notes = "Gera arquivo de exportação (XLS ou PDF) com lista total de alunos")
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "URL do arquivo gerado com sucesso", response = ExemploRespostaUrlExportacao.class)
    })
    @ResponseBody
    @RequestMapping(value = "/alunos-total/exportar", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alunosTotalTreinoExportar(
            @ApiParam(value = "URL base para treino", required = false, defaultValue = "http://localhost:8080")
            @RequestParam(value = "urlTreino", required = false) String urlTreino,
            @ApiParam(value = "Filtros em formato JSON", required = false, defaultValue = "{\"professorId\":1}")
            @RequestParam(value = "filtros", required = false) String filter,
            @ApiParam(value = "Formato do arquivo de exportação", required = false, defaultValue = "xls")
            @RequestParam(value = "format", required = false) String format,
            @ApiParam(value = "Campo para ordenação", required = false, defaultValue = "nome")
            @RequestParam(value = "sortField", required = false) String sortField,
            @ApiParam(value = "Direção da ordenação", required = false, defaultValue = "asc")
            @RequestParam(value = "sortDirection", required = false) String sortDirection,
            @ApiParam(value = "ID da empresa", required = true, defaultValue = "1")
            @RequestHeader("empresaId") Integer empresaId,
            @ApiIgnore PaginadorDTO paginadorDTO,
            HttpServletRequest request) {
        try {
            paginadorDTO.setSort(sortField + "," + sortDirection);
            paginadorDTO.setPage((long) 0);
            Integer idProfessor = Integer.parseInt(new JSONObject(filter).get("professorId").toString());
            idProfessor = verificarFiltroProfessor(idProfessor, filter);
            List<LinkedHashMap<String, Object>> lista = biService.listaBILinkedMap(idProfessor, empresaId, IndicadorDashboardEnum.TOTAL_ALUNOS, paginadorDTO, filter);
            String url = "";
            url = processarUrl(request, url, format, urlTreino, lista, "Total Alunos");
            return ResponseEntityFactory.ok(url);
        } catch (Exception e) {
            Logger.getLogger(BITreinoController.class.getName()).log(Level.SEVERE, "Erro ao gerar o arquivo", e);
            return ResponseEntityFactory.erroInterno("erro ao gerar o arquivo", e.getMessage());
        }
    }

    @ApiOperation(value = "Exportar lista de alunos inativos", notes = "Gera arquivo de exportação (XLS ou PDF) com lista de alunos inativos")
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "URL do arquivo gerado com sucesso", response = ExemploRespostaUrlExportacao.class)
    })
    @ResponseBody
    @RequestMapping(value = "/alunos-inativos/exportar", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alunosInativosTreinoExportar(
            @ApiParam(value = "URL base para treino", required = false, defaultValue = "http://localhost:8080")
            @RequestParam(value = "urlTreino", required = false) String urlTreino,
            @ApiParam(value = "Filtros em formato JSON", required = false, defaultValue = "{\"professorId\":1}")
            @RequestParam(value = "filtros", required = false) String filter,
            @ApiParam(value = "Formato do arquivo de exportação", required = false, defaultValue = "xls")
            @RequestParam(value = "format", required = false) String format,
            @ApiParam(value = "Campo para ordenação", required = false, defaultValue = "nome")
            @RequestParam(value = "sortField", required = false) String sortField,
            @ApiParam(value = "Direção da ordenação", required = false, defaultValue = "asc")
            @RequestParam(value = "sortDirection", required = false) String sortDirection,
            @ApiParam(value = "ID da empresa", required = true, defaultValue = "1")
            @RequestHeader("empresaId") Integer empresaId,
            @ApiIgnore PaginadorDTO paginadorDTO,
            HttpServletRequest request) {
        try {
            paginadorDTO.setSort(sortField + "," + sortDirection);
            paginadorDTO.setPage((long) 0);
            Integer idProfessor = Integer.parseInt(new JSONObject(filter).get("professorId").toString());
            idProfessor = verificarFiltroProfessor(idProfessor, filter);
            List<LinkedHashMap<String, Object>> lista = biService.listaBILinkedMap(idProfessor, empresaId, IndicadorDashboardEnum.INATIVOS, paginadorDTO, filter);
            String url = "";
            url = processarUrl(request, url, format, urlTreino, lista, "Alunos Inativos");
            return ResponseEntityFactory.ok(url);
        } catch (Exception e) {
            Logger.getLogger(BITreinoController.class.getName()).log(Level.SEVERE, "Erro ao gerar o arquivo", e);
            return ResponseEntityFactory.erroInterno("erro ao gerar o arquivo", e.getMessage());
        }
    }

    @ApiOperation(value = "Exportar lista de alunos visitantes", notes = "Gera arquivo de exportação (XLS ou PDF) com lista de alunos visitantes")
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "URL do arquivo gerado com sucesso", response = ExemploRespostaUrlExportacao.class)
    })
    @ResponseBody
    @RequestMapping(value = "/alunos-visitantes/exportar", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alunosVisitantesTreinoExportar(
            @ApiParam(value = "URL base para treino", required = false, defaultValue = "http://localhost:8080")
            @RequestParam(value = "urlTreino", required = false) String urlTreino,
            @ApiParam(value = "Filtros em formato JSON", required = false, defaultValue = "{\"professorId\":1}")
            @RequestParam(value = "filtros", required = false) String filter,
            @ApiParam(value = "Formato do arquivo de exportação", required = false, defaultValue = "xls")
            @RequestParam(value = "format", required = false) String format,
            @ApiParam(value = "Campo para ordenação", required = false, defaultValue = "nome")
            @RequestParam(value = "sortField", required = false) String sortField,
            @ApiParam(value = "Direção da ordenação", required = false, defaultValue = "asc")
            @RequestParam(value = "sortDirection", required = false) String sortDirection,
            @ApiParam(value = "ID da empresa", required = true, defaultValue = "1")
            @RequestHeader("empresaId") Integer empresaId,
            @ApiIgnore PaginadorDTO paginadorDTO,
            HttpServletRequest request) {
        try {
            paginadorDTO.setSort(sortField + "," + sortDirection);
            paginadorDTO.setPage((long) 0);
            Integer idProfessor = Integer.parseInt(new JSONObject(filter).get("professorId").toString());
            idProfessor = verificarFiltroProfessor(idProfessor, filter);
            List<LinkedHashMap<String, Object>> lista = biService.listaBILinkedMap(idProfessor, empresaId, IndicadorDashboardEnum.VISITANTES, paginadorDTO, filter);
            String url = "";
            url = processarUrl(request, url, format, urlTreino, lista, "Alunos Visitantes");
            return ResponseEntityFactory.ok(url);
        } catch (Exception e) {
            Logger.getLogger(BITreinoController.class.getName()).log(Level.SEVERE, "Erro ao gerar o arquivo", e);
            return ResponseEntityFactory.erroInterno("erro ao gerar o arquivo", e.getMessage());
        }
    }

    @ApiOperation(value = "Exportar lista de alunos com treino vencido", notes = "Gera arquivo de exportação (XLS ou PDF) com lista de alunos que possuem treino vencido")
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "URL do arquivo gerado com sucesso", response = ExemploRespostaUrlExportacao.class)
    })
    @ResponseBody
    @RequestMapping(value = "/alunos-treino-vencido/exportar", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alunosTreinoVencidosExportar(
            @ApiParam(value = "URL base para treino", required = false, defaultValue = "http://localhost:8080")
            @RequestParam(value = "urlTreino", required = false) String urlTreino,
            @ApiParam(value = "Filtros em formato JSON", required = false, defaultValue = "{\"professorId\":1}")
            @RequestParam(value = "filtros", required = false) String filter,
            @ApiParam(value = "Formato do arquivo de exportação", required = false, defaultValue = "xls")
            @RequestParam(value = "format", required = false) String format,
            @ApiParam(value = "Campo para ordenação", required = false, defaultValue = "nome")
            @RequestParam(value = "sortField", required = false) String sortField,
            @ApiParam(value = "Direção da ordenação", required = false, defaultValue = "asc")
            @RequestParam(value = "sortDirection", required = false) String sortDirection,
            @ApiParam(value = "ID da empresa", required = true, defaultValue = "1")
            @RequestHeader("empresaId") Integer empresaId,
            @ApiIgnore PaginadorDTO paginadorDTO,
            HttpServletRequest request) {
        try {
            paginadorDTO.setSort(sortField + "," + sortDirection);
            paginadorDTO.setPage((long) 0);
            Integer idProfessor = Integer.parseInt(new JSONObject(filter).get("professorId").toString());
            idProfessor = verificarFiltroProfessor(idProfessor, filter);
            List<LinkedHashMap<String, Object>> lista = biService.listaBILinkedMap(idProfessor, empresaId, IndicadorDashboardEnum.VENCIDOS, paginadorDTO, filter);
            String url = "";
            url = processarUrl(request, url, format, urlTreino, lista, "Alunos com treino vencido");
            return ResponseEntityFactory.ok(url);
        } catch (Exception e) {
            Logger.getLogger(BITreinoController.class.getName()).log(Level.SEVERE, "Erro ao gerar o arquivo", e);
            return ResponseEntityFactory.erroInterno("erro ao gerar o arquivo", e.getMessage());
        }
    }

    @ApiOperation(value = "Exportar lista de alunos ativos sem treino", notes = "Gera arquivo de exportação (XLS ou PDF) com lista de alunos ativos que não possuem treino")
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "URL do arquivo gerado com sucesso", response = ExemploRespostaUrlExportacao.class)
    })
    @ResponseBody
    @RequestMapping(value = "/alunos-ativo-sem-treino/exportar", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alunosAtivosSemTreinoExportar(
            @ApiParam(value = "URL base para treino", required = false, defaultValue = "http://localhost:8080")
            @RequestParam(value = "urlTreino", required = false) String urlTreino,
            @ApiParam(value = "Filtros em formato JSON", required = false, defaultValue = "{\"professorId\":1}")
            @RequestParam(value = "filtros", required = false) String filter,
            @ApiParam(value = "Formato do arquivo de exportação", required = false, defaultValue = "xls")
            @RequestParam(value = "format", required = false) String format,
            @ApiParam(value = "Campo para ordenação", required = false, defaultValue = "nome")
            @RequestParam(value = "sortField", required = false) String sortField,
            @ApiParam(value = "Direção da ordenação", required = false, defaultValue = "asc")
            @RequestParam(value = "sortDirection", required = false) String sortDirection,
            @ApiParam(value = "ID da empresa", required = true, defaultValue = "1")
            @RequestHeader("empresaId") Integer empresaId,
            @ApiIgnore PaginadorDTO paginadorDTO,
            HttpServletRequest request) {
        try {
            paginadorDTO.setSort(sortField + "," + sortDirection);
            paginadorDTO.setPage((long) 0);
            Integer idProfessor = Integer.parseInt(new JSONObject(filter).get("professorId").toString());
            idProfessor = verificarFiltroProfessor(idProfessor, filter);
            List<LinkedHashMap<String, Object>> lista = biService.listaBILinkedMap(idProfessor, empresaId, IndicadorDashboardEnum.ATIVOS_SEM_TREINO, paginadorDTO, filter);
            String url = "";
            url = processarUrl(request, url, format, urlTreino, lista, "Alunos ativos sem treino");
            return ResponseEntityFactory.ok(url);
        } catch (Exception e) {
            Logger.getLogger(BITreinoController.class.getName()).log(Level.SEVERE, "Erro ao gerar o arquivo", e);
            return ResponseEntityFactory.erroInterno("erro ao gerar o arquivo", e.getMessage());
        }
    }

    @ApiOperation(value = "Exportar lista de alunos com treino a renovar", notes = "Gera arquivo de exportação (XLS ou PDF) com lista de alunos que precisam renovar o treino")
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "URL do arquivo gerado com sucesso", response = ExemploRespostaUrlExportacao.class)
    })
    @ResponseBody
    @RequestMapping(value = "/alunos-treino-a-renovar/exportar", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alunosTreinoRenovarExportar(
            @ApiParam(value = "URL base para treino", required = false, defaultValue = "http://localhost:8080")
            @RequestParam(value = "urlTreino", required = false) String urlTreino,
            @ApiParam(value = "Filtros em formato JSON", required = false, defaultValue = "{\"professorId\":1}")
            @RequestParam(value = "filtros", required = false) String filter,
            @ApiParam(value = "Formato do arquivo de exportação", required = false, defaultValue = "xls")
            @RequestParam(value = "format", required = false) String format,
            @ApiParam(value = "Campo para ordenação", required = false, defaultValue = "nome")
            @RequestParam(value = "sortField", required = false) String sortField,
            @ApiParam(value = "Direção da ordenação", required = false, defaultValue = "asc")
            @RequestParam(value = "sortDirection", required = false) String sortDirection,
            @ApiParam(value = "ID da empresa", required = true, defaultValue = "1")
            @RequestHeader("empresaId") Integer empresaId,
            @ApiIgnore PaginadorDTO paginadorDTO,
            HttpServletRequest request) {
        try {
            paginadorDTO.setSort(sortField + "," + sortDirection);
            paginadorDTO.setPage((long) 0);
            Integer idProfessor = Integer.parseInt(new JSONObject(filter).get("professorId").toString());
            idProfessor = verificarFiltroProfessor(idProfessor, filter);
            List<LinkedHashMap<String, Object>> lista = biService.listaBILinkedMap(idProfessor, empresaId, IndicadorDashboardEnum.TREINOS_A_VENCER, paginadorDTO, filter);
            String url = "";
            url = processarUrl(request, url, format, urlTreino, lista, "Alunos a renovar treino");
            return ResponseEntityFactory.ok(url);
        } catch (Exception e) {
            Logger.getLogger(BITreinoController.class.getName()).log(Level.SEVERE, "Erro ao gerar o arquivo", e);
            return ResponseEntityFactory.erroInterno("erro ao gerar o arquivo", e.getMessage());
        }
    }

    @ApiOperation(value = "Exportar lista de alunos com treino em dia", notes = "Gera arquivo de exportação (XLS ou PDF) com lista de alunos que possuem treino em dia")
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "URL do arquivo gerado com sucesso", response = ExemploRespostaUrlExportacao.class)
    })
    @ResponseBody
    @RequestMapping(value = "/alunos-treino-em-dia/exportar", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alunosTreinoEmDiaExportar(
            @ApiParam(value = "URL base para treino", required = false, defaultValue = "http://localhost:8080")
            @RequestParam(value = "urlTreino", required = false) String urlTreino,
            @ApiParam(value = "Filtros em formato JSON", required = false, defaultValue = "{\"professorId\":1}")
            @RequestParam(value = "filtros", required = false) String filter,
            @ApiParam(value = "Formato do arquivo de exportação", required = false, defaultValue = "xls")
            @RequestParam(value = "format", required = false) String format,
            @ApiParam(value = "Campo para ordenação", required = false, defaultValue = "nome")
            @RequestParam(value = "sortField", required = false) String sortField,
            @ApiParam(value = "Direção da ordenação", required = false, defaultValue = "asc")
            @RequestParam(value = "sortDirection", required = false) String sortDirection,
            @ApiParam(value = "ID da empresa", required = true, defaultValue = "1")
            @RequestHeader("empresaId") Integer empresaId,
            @ApiIgnore PaginadorDTO paginadorDTO,
            HttpServletRequest request) {
        try {
            paginadorDTO.setSort(sortField + "," + sortDirection);
            paginadorDTO.setPage((long) 0);
            Integer idProfessor = Integer.parseInt(new JSONObject(filter).get("professorId").toString());
            idProfessor = verificarFiltroProfessor(idProfessor, filter);
            List<LinkedHashMap<String, Object>> lista = biService.listaBILinkedMap(idProfessor, empresaId, IndicadorDashboardEnum.EM_DIA, paginadorDTO, filter);
            String url = "";
            url = processarUrl(request, url, format, urlTreino, lista, "Alunos com treino em dia");
            return ResponseEntityFactory.ok(url);
        } catch (Exception e) {
            Logger.getLogger(BITreinoController.class.getName()).log(Level.SEVERE, "Erro ao gerar o arquivo", e);
            return ResponseEntityFactory.erroInterno("erro ao gerar o arquivo", e.getMessage());
        }
    }

    @ApiOperation(value = "Exportar lista de alunos ativos com treino", notes = "Gera arquivo de exportação (XLS ou PDF) com lista de alunos ativos que possuem treino")
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "URL do arquivo gerado com sucesso", response = ExemploRespostaUrlExportacao.class)
    })
    @ResponseBody
    @RequestMapping(value = "/alunos-ativos-treino/exportar", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alunosTreinoAtivosExportar(
            @ApiParam(value = "URL base para treino", required = false, defaultValue = "http://localhost:8080")
            @RequestParam(value = "urlTreino", required = false) String urlTreino,
            @ApiParam(value = "Filtros em formato JSON", required = false, defaultValue = "{\"professorId\":1}")
            @RequestParam(value = "filtros", required = false) String filter,
            @ApiParam(value = "Formato do arquivo de exportação", required = false, defaultValue = "xls")
            @RequestParam(value = "format", required = false) String format,
            @ApiParam(value = "Campo para ordenação", required = false, defaultValue = "nome")
            @RequestParam(value = "sortField", required = false) String sortField,
            @ApiParam(value = "Direção da ordenação", required = false, defaultValue = "asc")
            @RequestParam(value = "sortDirection", required = false) String sortDirection,
            @ApiParam(value = "ID da empresa", required = true, defaultValue = "1")
            @RequestHeader("empresaId") Integer empresaId,
            @ApiIgnore PaginadorDTO paginadorDTO,
            HttpServletRequest request) {
        try {
            paginadorDTO.setSort(sortField + "," + sortDirection);
            paginadorDTO.setPage((long) 0);
            Integer idProfessor = Integer.parseInt(new JSONObject(filter).get("professorId").toString());
            idProfessor = verificarFiltroProfessor(idProfessor, filter);
            List<LinkedHashMap<String, Object>> lista = biService.listaBILinkedMap(idProfessor, empresaId, IndicadorDashboardEnum.ATIVOS_COM_TREINO, paginadorDTO, filter);
            String url = "";
            url = processarUrl(request, url, format, urlTreino, lista, "Alunos ativos com treino");
            return ResponseEntityFactory.ok(url);
        } catch (Exception e) {
            Logger.getLogger(BITreinoController.class.getName()).log(Level.SEVERE, "Erro ao gerar o arquivo", e);
            return ResponseEntityFactory.erroInterno("erro ao gerar o arquivo", e.getMessage());
        }
    }

    //    /treino-bi/alunos-vencer-30-dias/exportar
    @ResponseBody
    @RequestMapping(value = "/alunos-vencer-30-dias/exportar", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alunosVencer30DiasExportar(@RequestParam(value = "urlTreino", required = false) String urlTreino,
                                                                          @RequestParam(value = "filtros", required = false) String filter,
                                                                          @RequestParam(value = "format", required = false) String format,
                                                                          @RequestParam(value = "sortField", required = false) String sortField,
                                                                          @RequestParam(value = "sortDirection", required = false) String sortDirection,
                                                                          @RequestHeader("empresaId") Integer empresaId, PaginadorDTO paginadorDTO,
                                                                          HttpServletRequest request) {
        try {
            paginadorDTO.setSort(sortField + "," + sortDirection);
            paginadorDTO.setPage((long) 0);
            Integer idProfessor = Integer.parseInt(new JSONObject(filter).get("professorId").toString());
            idProfessor = verificarFiltroProfessor(idProfessor, filter);
            List<LinkedHashMap<String, Object>> lista = biService.listaBILinkedMap(idProfessor, empresaId, IndicadorDashboardEnum.ALUNOS_A_VENCER, paginadorDTO, filter);
            String url = "";
            url = processarUrl(request, url, format, urlTreino, lista, "Alunos com contrato a vencer em 30 dias");
            return ResponseEntityFactory.ok(url);
        } catch (Exception e) {
            Logger.getLogger(BITreinoController.class.getName()).log(Level.SEVERE, "Erro ao gerar o arquivo", e);
            return ResponseEntityFactory.erroInterno("erro ao gerar o arquivo", e.getMessage());
        }
    }

    //    /treino-bi/alunos-renovaram/exportar
    @ResponseBody
    @RequestMapping(value = "/alunos-renovaram/exportar", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alunosRenovaramExportar(@RequestParam(value = "urlTreino", required = false) String urlTreino,
                                                                       @RequestParam(value = "filtros", required = false) String filter,
                                                                       @RequestParam(value = "format", required = false) String format,
                                                                       @RequestParam(value = "sortField", required = false) String sortField,
                                                                       @RequestParam(value = "sortDirection", required = false) String sortDirection,
                                                                       @RequestHeader("empresaId") Integer empresaId, PaginadorDTO paginadorDTO,
                                                                       HttpServletRequest request) {
        try {
            paginadorDTO.setSort(sortField + "," + sortDirection);
            paginadorDTO.setPage((long) 0);
            Integer idProfessor = Integer.parseInt(new JSONObject(filter).get("professorId").toString());
            idProfessor = verificarFiltroProfessor(idProfessor, filter);
            List<LinkedHashMap<String, Object>> lista = biService.listaBILinkedMap(idProfessor, empresaId, IndicadorDashboardEnum.RENOVARAM, paginadorDTO, filter);
            String url = "";
            url = processarUrl(request, url, format, urlTreino, lista, "Alunos que renovaram");
            return ResponseEntityFactory.ok(url);
        } catch (Exception e) {
            Logger.getLogger(BITreinoController.class.getName()).log(Level.SEVERE, "Erro ao gerar o arquivo", e);
            return ResponseEntityFactory.erroInterno("erro ao gerar o arquivo", e.getMessage());
        }
    }

    @ApiOperation(value = "Exportar lista de agendamentos", notes = "Gera arquivo de exportação (XLS ou PDF) com lista de agendamentos")
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "URL do arquivo gerado com sucesso", response = ExemploRespostaUrlExportacao.class),
            @ApiResponse(code = 500, message = "Erro interno do servidor")
    })
    @ResponseBody
    @RequestMapping(value = "/agendamentos/exportar", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> agendamentosExportar(
            @ApiParam(value = "URL base para treino", required = false, defaultValue = "http://localhost:8080")
            @RequestParam(value = "urlTreino", required = false) String urlTreino,
            @ApiParam(value = "Filtros em formato JSON", required = false, defaultValue = "{\"professorId\":1}")
            @RequestParam(value = "filtros", required = false) String filter,
            @ApiParam(value = "Formato do arquivo de exportação", required = false, defaultValue = "xls")
            @RequestParam(value = "format", required = false) String format,
            @ApiParam(value = "Campo para ordenação", required = false, defaultValue = "nome")
            @RequestParam(value = "sortField", required = false) String sortField,
            @ApiParam(value = "Direção da ordenação", required = false, defaultValue = "asc")
            @RequestParam(value = "sortDirection", required = false) String sortDirection,
            @ApiParam(value = "ID da empresa", required = true, defaultValue = "1")
            @RequestHeader("empresaId") Integer empresaId,
            @ApiIgnore PaginadorDTO paginadorDTO,
            HttpServletRequest request) {
        try {
            paginadorDTO.setSort(sortField + "," + sortDirection);
            paginadorDTO.setPage((long) 0);
            Integer idProfessor = Integer.parseInt(new JSONObject(filter).get("professorId").toString());
            idProfessor = verificarFiltroProfessor(idProfessor, filter);
            List<LinkedHashMap<String, Object>> lista = biService.listaBILinkedMap(idProfessor, empresaId, IndicadorDashboardEnum.AGENDAMENTOS, paginadorDTO, filter);
            String url = "";
            url = processarUrl(request, url, format, urlTreino, lista, "Agendamentos");
            return ResponseEntityFactory.ok(url);
        } catch (Exception e) {
            Logger.getLogger(BITreinoController.class.getName()).log(Level.SEVERE, "Erro ao gerar o arquivo", e);
            return ResponseEntityFactory.erroInterno("erro ao gerar o arquivo", e.getMessage());
        }
    }

    //    /treino-bi/agendamento-executaram/exportar
    @ResponseBody
    @RequestMapping(value = "/agendamento-executaram/exportar", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> agendamentosExecutaramExportar(@RequestParam(value = "urlTreino", required = false) String urlTreino,
                                                                              @RequestParam(value = "filtros", required = false) String filter,
                                                                              @RequestParam(value = "format", required = false) String format,
                                                                              @RequestParam(value = "sortField", required = false) String sortField,
                                                                              @RequestParam(value = "sortDirection", required = false) String sortDirection,
                                                                              @RequestHeader("empresaId") Integer empresaId, PaginadorDTO paginadorDTO,
                                                                              HttpServletRequest request) {
        try {
            paginadorDTO.setSort(sortField + "," + sortDirection);
            paginadorDTO.setPage((long) 0);
            Integer idProfessor = Integer.parseInt(new JSONObject(filter).get("professorId").toString());
            idProfessor = verificarFiltroProfessor(idProfessor, filter);
            List<LinkedHashMap<String, Object>> lista = biService.listaBILinkedMap(idProfessor, empresaId, IndicadorDashboardEnum.COMPARECERAM, paginadorDTO, filter);
            String url = "";
            url = processarUrl(request, url, format, urlTreino, lista, "Agendamentos executados");
            return ResponseEntityFactory.ok(url);
        } catch (Exception e) {
            Logger.getLogger(BITreinoController.class.getName()).log(Level.SEVERE, "Erro ao gerar o arquivo", e);
            return ResponseEntityFactory.erroInterno("erro ao gerar o arquivo", e.getMessage());
        }
    }

    //    /treino-bi/agendamento-faltaram/exportar

    @ResponseBody
    @RequestMapping(value = "/agendamento-faltaram/exportar", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> agendamentosFaltaramExportar(@RequestParam(value = "urlTreino", required = false) String urlTreino,
                                                                            @RequestParam(value = "filtros", required = false) String filter,
                                                                            @RequestParam(value = "format", required = false) String format,
                                                                            @RequestParam(value = "sortField", required = false) String sortField,
                                                                            @RequestParam(value = "sortDirection", required = false) String sortDirection,
                                                                            @RequestHeader("empresaId") Integer empresaId, PaginadorDTO paginadorDTO,
                                                                            HttpServletRequest request) {
        try {
            paginadorDTO.setSort(sortField + "," + sortDirection);
            paginadorDTO.setPage((long) 0);
            Integer idProfessor = Integer.parseInt(new JSONObject(filter).get("professorId").toString());
            idProfessor = verificarFiltroProfessor(idProfessor, filter);
            List<LinkedHashMap<String, Object>> lista = biService.listaBILinkedMap(idProfessor, empresaId, IndicadorDashboardEnum.FALTARAM, paginadorDTO, filter);
            String url = "";
            url = processarUrl(request, url, format, urlTreino, lista, "Agendamentos com falta");
            return ResponseEntityFactory.ok(url);
        } catch (Exception e) {
            Logger.getLogger(BITreinoController.class.getName()).log(Level.SEVERE, "Erro ao gerar o arquivo", e);
            return ResponseEntityFactory.erroInterno("erro ao gerar o arquivo", e.getMessage());
        }
    }

    //    /treino-bi/agendamento-cancelaram/exportar

    @ResponseBody
    @RequestMapping(value = "/agendamento-cancelaram/exportar", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> agendamentosCancelaramExportar(@RequestParam(value = "urlTreino", required = false) String urlTreino,
                                                                              @RequestParam(value = "filtros", required = false) String filter,
                                                                              @RequestParam(value = "format", required = false) String format,
                                                                              @RequestParam(value = "sortField", required = false) String sortField,
                                                                              @RequestParam(value = "sortDirection", required = false) String sortDirection,
                                                                              @RequestHeader("empresaId") Integer empresaId, PaginadorDTO paginadorDTO,
                                                                              HttpServletRequest request) {
        try {
            paginadorDTO.setSort(sortField + "," + sortDirection);
            paginadorDTO.setPage((long) 0);
            Integer idProfessor = Integer.parseInt(new JSONObject(filter).get("professorId").toString());
            idProfessor = verificarFiltroProfessor(idProfessor, filter);
            List<LinkedHashMap<String, Object>> lista = biService.listaBILinkedMap(idProfessor, empresaId, IndicadorDashboardEnum.CANCELARAM, paginadorDTO, filter);
            String url = "";
            url = processarUrl(request, url, format, urlTreino, lista, "Agendamentos cancelados");
            return ResponseEntityFactory.ok(url);
        } catch (Exception e) {
            Logger.getLogger(BITreinoController.class.getName()).log(Level.SEVERE, "Erro ao gerar o arquivo", e);
            return ResponseEntityFactory.erroInterno("erro ao gerar o arquivo", e.getMessage());
        }
    }

    //    /treino-bi/disponibilidades/professores/exportar
    @ResponseBody
    @RequestMapping(value = "/disponibilidades/professores/exportar", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> disponibilidadeProfessoresExportar(@RequestParam(value = "urlTreino", required = false) String urlTreino,
                                                                                  @RequestParam(value = "filtros", required = false) String filter,
                                                                                  @RequestParam(value = "format", required = false) String format,
                                                                                  @RequestParam(value = "sortField", required = false) String sortField,
                                                                                  @RequestParam(value = "sortDirection", required = false) String sortDirection,
                                                                                  @RequestHeader("empresaId") Integer empresaId, PaginadorDTO paginadorDTO,
                                                                                  HttpServletRequest request) {
        try {
            paginadorDTO.setSort(sortField + "," + sortDirection);
            paginadorDTO.setPage((long) 0);
            Integer idProfessor = Integer.parseInt(new JSONObject(filter).get("professorId").toString());
            idProfessor = verificarFiltroProfessor(idProfessor, filter);
            List<LinkedHashMap<String, Object>> lista = biService.listaBILinkedMap(idProfessor, empresaId, IndicadorDashboardEnum.PROFESSORES, paginadorDTO, filter);
            String url = "";
            url = processarUrl(request, url, format, urlTreino, lista, "Professores");
            return ResponseEntityFactory.ok(url);
        } catch (Exception e) {
            Logger.getLogger(BITreinoController.class.getName()).log(Level.SEVERE, "Erro ao gerar o arquivo", e);
            return ResponseEntityFactory.erroInterno("erro ao gerar o arquivo", e.getMessage());
        }
    }

    //    /treino-bi/percentual-ocupacao/exportar
    @ResponseBody
    @RequestMapping(value = "/percentual-ocupacao/exportar", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> percOcupacaoExportar(@RequestParam(value = "urlTreino", required = false) String urlTreino,
                                                                    @RequestParam(value = "filtros", required = false) String filter,
                                                                    @RequestParam(value = "format", required = false) String format,
                                                                    @RequestParam(value = "sortField", required = false) String sortField,
                                                                    @RequestParam(value = "sortDirection", required = false) String sortDirection,
                                                                    @RequestHeader("empresaId") Integer empresaId, PaginadorDTO paginadorDTO,
                                                                    HttpServletRequest request) {
        try {
            paginadorDTO.setSort(sortField + "," + sortDirection);
            paginadorDTO.setPage((long) 0);
            Integer idProfessor = Integer.parseInt(new JSONObject(filter).get("professorId").toString());
            idProfessor = verificarFiltroProfessor(idProfessor, filter);
            List<LinkedHashMap<String, Object>> lista = biService.listaBILinkedMap(idProfessor, empresaId, IndicadorDashboardEnum.OCUPACAO, paginadorDTO, filter);
            String url = "";
            url = processarUrl(request, url, format, urlTreino, lista, "Ocupação");
            return ResponseEntityFactory.ok(url);
        } catch (Exception e) {
            Logger.getLogger(BITreinoController.class.getName()).log(Level.SEVERE, "Erro ao gerar o arquivo", e);
            return ResponseEntityFactory.erroInterno("erro ao gerar o arquivo", e.getMessage());
        }
    }

    //    /treino-bi/disponibilidades/treinos-novos/exportar
    @ResponseBody
    @RequestMapping(value = "/disponibilidades/treinos-novos/exportar", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> disponibilidadeNovosTreinosExportar(@RequestParam(value = "urlTreino", required = false) String urlTreino,
                                                                                   @RequestParam(value = "filtros", required = false) String filter,
                                                                                   @RequestParam(value = "format", required = false) String format,
                                                                                   @RequestParam(value = "sortField", required = false) String sortField,
                                                                                   @RequestParam(value = "sortDirection", required = false) String sortDirection,
                                                                                   @RequestHeader("empresaId") Integer empresaId, PaginadorDTO paginadorDTO,
                                                                                   HttpServletRequest request) {
        try {
            paginadorDTO.setSort(sortField + "," + sortDirection);
            paginadorDTO.setPage((long) 0);
            Integer idProfessor = Integer.parseInt(new JSONObject(filter).get("professorId").toString());
            idProfessor = verificarFiltroProfessor(idProfessor, filter);
            List<LinkedHashMap<String, Object>> lista = biService.listaBILinkedMap(idProfessor, empresaId, IndicadorDashboardEnum.NOVOS_TREINOS, paginadorDTO, filter);
            String url = "";
            url = processarUrl(request, url, format, urlTreino, lista, "Treinos novos");
            return ResponseEntityFactory.ok(url);
        } catch (Exception e) {
            Logger.getLogger(BITreinoController.class.getName()).log(Level.SEVERE, "Erro ao gerar o arquivo", e);
            return ResponseEntityFactory.erroInterno("erro ao gerar o arquivo", e.getMessage());
        }
    }

    //    /treino-bi/treinos-renovados/exportar
    @ResponseBody
    @RequestMapping(value = "/treinos-renovados/exportar", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> treinosRenovadosExportar(@RequestParam(value = "urlTreino", required = false) String urlTreino,
                                                                        @RequestParam(value = "filtros", required = false) String filter,
                                                                        @RequestParam(value = "format", required = false) String format,
                                                                        @RequestParam(value = "sortField", required = false) String sortField,
                                                                        @RequestParam(value = "sortDirection", required = false) String sortDirection,
                                                                        @RequestHeader("empresaId") Integer empresaId, PaginadorDTO paginadorDTO,
                                                                        HttpServletRequest request) {
        try {
            paginadorDTO.setSort(sortField + "," + sortDirection);
            paginadorDTO.setPage((long) 0);
            Integer idProfessor = Integer.parseInt(new JSONObject(filter).get("professorId").toString());
            idProfessor = verificarFiltroProfessor(idProfessor, filter);
            List<LinkedHashMap<String, Object>> lista = biService.listaBILinkedMap(idProfessor, empresaId, IndicadorDashboardEnum.TREINOS_RENOVADOS, paginadorDTO, filter);
            String url = "";
            url = processarUrl(request, url, format, urlTreino, lista, "Treinos renovados");
            return ResponseEntityFactory.ok(url);
        } catch (Exception e) {
            Logger.getLogger(BITreinoController.class.getName()).log(Level.SEVERE, "Erro ao gerar o arquivo", e);
            return ResponseEntityFactory.erroInterno("erro ao gerar o arquivo", e.getMessage());
        }
    }

    //    /treino-bi/avaliacoes-fisicas/exportar
    @ResponseBody
    @RequestMapping(value = "/avaliacoes-fisicas/exportar", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> avaliacoesFisicasExportar(@RequestParam(value = "urlTreino", required = false) String urlTreino,
                                                                         @RequestParam(value = "filtros", required = false) String filter,
                                                                         @RequestParam(value = "format", required = false) String format,
                                                                         @RequestParam(value = "sortField", required = false) String sortField,
                                                                         @RequestParam(value = "sortDirection", required = false) String sortDirection,
                                                                         @RequestHeader("empresaId") Integer empresaId, PaginadorDTO paginadorDTO,
                                                                         HttpServletRequest request) {
        try {
            paginadorDTO.setSort(sortField + "," + sortDirection);
            paginadorDTO.setPage((long) 0);
            Integer idProfessor = Integer.parseInt(new JSONObject(filter).get("professorId").toString());
            idProfessor = verificarFiltroProfessor(idProfessor, filter);
            List<LinkedHashMap<String, Object>> lista = biService.listaBILinkedMap(idProfessor, empresaId, IndicadorDashboardEnum.AVALIACOES_FISICAS, paginadorDTO, filter);
            String url = "";
            url = processarUrl(request, url, format, urlTreino, lista, "Avaliações fisicas");
            return ResponseEntityFactory.ok(url);
        } catch (Exception e) {
            Logger.getLogger(BITreinoController.class.getName()).log(Level.SEVERE, "Erro ao gerar o arquivo", e);
            return ResponseEntityFactory.erroInterno("erro ao gerar o arquivo", e.getMessage());
        }
    }

    //    /treino-bi/avaliacoes-fisicas-nao-realizado/exportar
    @ResponseBody
    @RequestMapping(value = "/avaliacoes-fisicas-nao-realizado/exportar", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> avaliFisicasNaoRealizadoExportar(@RequestParam(value = "urlTreino", required = false) String urlTreino,
                                                                                @RequestParam(value = "filtros", required = false) String filter,
                                                                                @RequestParam(value = "format", required = false) String format,
                                                                                @RequestParam(value = "sortField", required = false) String sortField,
                                                                                @RequestParam(value = "sortDirection", required = false) String sortDirection,
                                                                                @RequestHeader("empresaId") Integer empresaId, PaginadorDTO paginadorDTO,
                                                                                HttpServletRequest request) {
        try {
            paginadorDTO.setSort(sortField + "," + sortDirection);
            paginadorDTO.setPage((long) 0);
            Integer idProfessor = Integer.parseInt(new JSONObject(filter).get("professorId").toString());
            idProfessor = verificarFiltroProfessor(idProfessor, filter);
            List<LinkedHashMap<String, Object>> lista = biService.listaBILinkedMap(idProfessor, empresaId, IndicadorDashboardEnum.SEM_AVALIACAO, paginadorDTO, filter);
            String url = "";
            url = processarUrl(request, url, format, urlTreino, lista, "Não realizadas");
            return ResponseEntityFactory.ok(url);
        } catch (Exception e) {
            Logger.getLogger(BITreinoController.class.getName()).log(Level.SEVERE, "Erro ao gerar o arquivo", e);
            return ResponseEntityFactory.erroInterno("erro ao gerar o arquivo", e.getMessage());
        }
    }

    //    /treino-bi/avaliacoes-fisicas-realizado/exportar
    @ResponseBody
    @RequestMapping(value = "/avaliacoes-fisicas-realizado/exportar", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> avaFisicasRealizadoExportar(@RequestParam(value = "urlTreino", required = false) String urlTreino,
                                                                           @RequestParam(value = "filtros", required = false) String filter,
                                                                           @RequestParam(value = "format", required = false) String format,
                                                                           @RequestParam(value = "sortField", required = false) String sortField,
                                                                           @RequestParam(value = "sortDirection", required = false) String sortDirection,
                                                                           @RequestHeader("empresaId") Integer empresaId, PaginadorDTO paginadorDTO,
                                                                           HttpServletRequest request) {
        try {
            paginadorDTO.setSort(sortField + "," + sortDirection);
            paginadorDTO.setPage((long) 0);
            Integer idProfessor = Integer.parseInt(new JSONObject(filter).get("professorId").toString());
            idProfessor = verificarFiltroProfessor(idProfessor, filter);
            List<LinkedHashMap<String, Object>> lista = biService.listaBILinkedMap(idProfessor, empresaId, IndicadorDashboardEnum.COM_AVALIACAO_FISICA, paginadorDTO, filter);
            String url = "";
            url = processarUrl(request, url, format, urlTreino, lista, "Realizadas");
            return ResponseEntityFactory.ok(url);
        } catch (Exception e) {
            Logger.getLogger(BITreinoController.class.getName()).log(Level.SEVERE, "Erro ao gerar o arquivo", e);
            return ResponseEntityFactory.erroInterno("erro ao gerar o arquivo", e.getMessage());
        }
    }

    @ApiOperation(value = "Listar alunos com treino em dia", notes = "Retorna lista paginada de alunos que possuem treino em dia")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.<br/> Ordena por um atributo contido no objeto da resposta." +
                    "<strong>Ordens disponíveis</strong>" +
                    "<ul>" +
                    "<li>ASC - Ordena pelo atributo escolhido de forma ascendente</li>" +
                    "<li>DESC - Ordena pelo atributo escolhido de forma descendente</li>" +
                    "</ul>" +
                    "Deve ser informado como atributo,ordem", defaultValue = "nome,asc", paramType = "query", dataType = "string"),
    })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "Lista de alunos com treino em dia obtida com sucesso", response = ExemploRespostaListMapStringObjectPaginacao.class)
    })
    @ResponseBody
    @RequestMapping(value = "/alunos-treino-em-dia/{codigoPessoa}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alunostreinoemdia(
            @ApiParam(value = "ID do professor (opcional)", required = false, defaultValue = "1")
            @RequestParam(value = "professorId", required = false) Integer idProfessor,
            @ApiParam(value = "Filtros de busca em formato JSON", required = false, defaultValue = "{\"inicio\":1640995200000,\"fim\":1672531199000}")
            @RequestParam(value = "filters", required = false) String filter,
            @ApiParam(value = "ID da empresa", required = true, defaultValue = "1")
            @RequestHeader("empresaId") Integer empresaId,
            @ApiParam(value = "Código da pessoa", required = true, defaultValue = "1")
            @PathVariable Integer codigoPessoa,
            @ApiIgnore PaginadorDTO paginadorDTO) {
        Integer[] filtro = verificarFiltroProfessorDash(filter);
        idProfessor = filtro[0] != null ? filtro[0] : 0;
        codigoPessoa = filtro[1] == 0 ? codigoPessoa : filtro[1];
        if (idProfessor != 0) {
            idProfessor = verificarIdProfessor(idProfessor, empresaId, codigoPessoa);
        }
        return listaGenerica(idProfessor, empresaId, IndicadorDashboardEnum.EM_DIA, paginadorDTO, filter);
    }

    @ApiOperation(value = "Listar alunos com treino vencido", notes = "Retorna lista paginada de alunos que possuem treino vencido")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.<br/> Ordena por um atributo contido no objeto da resposta." +
                    "<strong>Ordens disponíveis</strong>" +
                    "<ul>" +
                    "<li>ASC - Ordena pelo atributo escolhido de forma ascendente</li>" +
                    "<li>DESC - Ordena pelo atributo escolhido de forma descendente</li>" +
                    "</ul>" +
                    "Deve ser informado como atributo,ordem", defaultValue = "nome,asc", paramType = "query", dataType = "string"),
    })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "Lista de alunos com treino vencido obtida com sucesso", response = ExemploRespostaListMapStringObjectPaginacao.class)
    })
    @ResponseBody
    @RequestMapping(value = "/alunos-treino-vencido/{codigoPessoa}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alunostreinovencido(
            @ApiParam(value = "ID do professor (opcional)", required = false, defaultValue = "1")
            @RequestParam(value = "professorId", required = false) Integer idProfessor,
            @ApiParam(value = "Filtros de busca em formato JSON", required = false, defaultValue = "{\"inicio\":1640995200000,\"fim\":1672531199000}")
            @RequestParam(value = "filters", required = false) String filter,
            @ApiParam(value = "ID da empresa", required = true, defaultValue = "1")
            @RequestHeader("empresaId") Integer empresaId,
            @ApiParam(value = "Código da pessoa", required = true, defaultValue = "1")
            @PathVariable Integer codigoPessoa,
            @ApiIgnore PaginadorDTO paginadorDTO) {
        Integer[] filtro = verificarFiltroProfessorDash(filter);
        idProfessor = filtro[0] != null ? filtro[0] : 0;
        codigoPessoa = filtro[1] == 0 ? codigoPessoa : filtro[1];
        if (idProfessor != 0) {
            idProfessor = verificarIdProfessor(idProfessor, empresaId, codigoPessoa);
        }
        return listaGenerica(idProfessor, empresaId, IndicadorDashboardEnum.VENCIDOS, paginadorDTO, filter);
    }

    @ApiOperation(value = "Listar alunos com treino a renovar", notes = "Retorna lista paginada de alunos que precisam renovar o treino")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.<br/> Ordena por um atributo contido no objeto da resposta." +
                    "<strong>Ordens disponíveis</strong>" +
                    "<ul>" +
                    "<li>ASC - Ordena pelo atributo escolhido de forma ascendente</li>" +
                    "<li>DESC - Ordena pelo atributo escolhido de forma descendente</li>" +
                    "</ul>" +
                    "Deve ser informado como atributo,ordem", defaultValue = "nome,asc", paramType = "query", dataType = "string"),
    })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "Lista de alunos com treino a renovar obtida com sucesso", response = ExemploRespostaListMapStringObjectPaginacao.class)
    })
    @ResponseBody
    @RequestMapping(value = "/alunos-treino-a-renovar/{codigoPessoa}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alunostreinoarenovar(
            @ApiParam(value = "ID do professor (opcional)", required = false, defaultValue = "1")
            @RequestParam(value = "professorId", required = false) Integer idProfessor,
            @ApiParam(value = "Filtros de busca em formato JSON", required = false, defaultValue = "{\"inicio\":1640995200000,\"fim\":1672531199000}")
            @RequestParam(value = "filters", required = false) String filter,
            @ApiParam(value = "ID da empresa", required = true, defaultValue = "1")
            @RequestHeader("empresaId") Integer empresaId,
            @ApiParam(value = "Código da pessoa", required = true, defaultValue = "1")
            @PathVariable Integer codigoPessoa,
            @ApiIgnore PaginadorDTO paginadorDTO) {
        Integer[] filtro = verificarFiltroProfessorDash(filter);
        idProfessor = filtro[0] != null ? filtro[0] : 0;
        codigoPessoa = filtro[1] == 0 ? codigoPessoa : filtro[1];
        if (idProfessor != 0) {
            idProfessor = verificarIdProfessor(idProfessor, empresaId, codigoPessoa);
        }
        return listaGenerica(idProfessor, empresaId, IndicadorDashboardEnum.TREINOS_A_VENCER, paginadorDTO, filter);
    }

    @ApiOperation(value = "Listar alunos com treino a renovar em 30 dias", notes = "Retorna lista paginada de alunos que precisam renovar o treino nos próximos 30 dias")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.<br/> Ordena por um atributo contido no objeto da resposta." +
                    "<strong>Ordens disponíveis</strong>" +
                    "<ul>" +
                    "<li>ASC - Ordena pelo atributo escolhido de forma ascendente</li>" +
                    "<li>DESC - Ordena pelo atributo escolhido de forma descendente</li>" +
                    "</ul>" +
                    "Deve ser informado como atributo,ordem", defaultValue = "nome,asc", paramType = "query", dataType = "string"),
    })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "Lista de alunos com treino a renovar em 30 dias obtida com sucesso", response = ExemploRespostaListMapStringObjectPaginacao.class)
    })
    @ResponseBody
    @RequestMapping(value = "/alunos-treino-a-renovar-30-dias", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alunostreinoarenovar30dias(
            @ApiParam(value = "ID do professor (opcional)", required = false, defaultValue = "1")
            @RequestParam(value = "professorId", required = false) Integer idProfessor,
            @ApiParam(value = "Filtros de busca em formato JSON", required = false, defaultValue = "{\"inicio\":1640995200000,\"fim\":1672531199000}")
            @RequestParam(value = "filters", required = false) String filter,
            @ApiParam(value = "ID da empresa", required = true, defaultValue = "1")
            @RequestHeader("empresaId") Integer empresaId,
            @ApiIgnore PaginadorDTO paginadorDTO) {
        idProfessor = verificarFiltroProfessor(idProfessor, filter);
        return listaGenerica(idProfessor, empresaId, IndicadorDashboardEnum.TREINOS_A_VENCER, paginadorDTO, filter);
    }

    @ApiOperation(value = "Listar alunos ativos sem treino", notes = "Retorna lista paginada de alunos ativos que não possuem treino ativo")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.<br/> Ordena por um atributo contido no objeto da resposta." +
                    "<strong>Ordens disponíveis</strong>" +
                    "<ul>" +
                    "<li>ASC - Ordena pelo atributo escolhido de forma ascendente</li>" +
                    "<li>DESC - Ordena pelo atributo escolhido de forma descendente</li>" +
                    "</ul>" +
                    "Deve ser informado como atributo,ordem", defaultValue = "nome,asc", paramType = "query", dataType = "string"),
    })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "Lista de alunos ativos sem treino obtida com sucesso", response = ExemploRespostaListMapStringObjectPaginacao.class)
    })
    @ResponseBody
    @RequestMapping(value = "/alunos-ativo-sem-treino/{codigoPessoa}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alunosativosemtreino(
            @ApiParam(value = "ID do professor (opcional)", required = false, defaultValue = "1")
            @RequestParam(value = "professorId", required = false) Integer idProfessor,
            @ApiParam(value = "Filtros de busca em formato JSON", required = false, defaultValue = "{\"inicio\":1640995200000,\"fim\":1672531199000}")
            @RequestParam(value = "filters", required = false) String filter,
            @ApiParam(value = "ID da empresa", required = true, defaultValue = "1")
            @RequestHeader("empresaId") Integer empresaId,
            @ApiParam(value = "Código da pessoa", required = true, defaultValue = "1")
            @PathVariable Integer codigoPessoa,
            @ApiIgnore PaginadorDTO paginadorDTO) {
        Integer[] filtro = verificarFiltroProfessorDash(filter);
        idProfessor = filtro[0] != null ? filtro[0] : 0;
        codigoPessoa = filtro[1] == 0 ? codigoPessoa : filtro[1];
        if (idProfessor != 0) {
            idProfessor = verificarIdProfessor(idProfessor, empresaId, codigoPessoa);
        }
        return listaGenerica(idProfessor, empresaId, IndicadorDashboardEnum.ATIVOS_SEM_TREINO, paginadorDTO, filter);
    }

    @ApiOperation(value = "Obter dados de acessos de alunos", notes = "Retorna informações sobre acessos de alunos em um dia específico e tipo de acesso")
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "Dados de acessos obtidos com sucesso", response = ExemploRespostaListMapStringObjectPaginacao.class)
    })
    @ResponseBody
    @RequestMapping(value = "/alunos-acessos",
            method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alunosacessos(
            @ApiParam(value = "ID do professor (opcional)", required = false, defaultValue = "1")
            @RequestParam(value = "professorId", required = false) Integer idProfessor,
            @ApiParam(value = "Filtros de busca em formato JSON", required = false, defaultValue = "{\"professorId\":1}")
            @RequestParam(value = "filters", required = false) String filter,
            @ApiParam(value = "Dia em timestamp para filtrar acessos", required = true, defaultValue = "1640995200000")
            @RequestParam("dia") Long dia,
            @ApiParam(value = "Tipo de acesso (EXECUCOES_TREINO, SMARTPHONE, ACESSO)", required = true, defaultValue = "EXECUCOES_TREINO")
            @RequestParam("tipo") String tipo,
            @ApiParam(value = "ID da empresa", required = true, defaultValue = "1")
            @RequestHeader("empresaId") Integer empresaId) {
        try {
            idProfessor = verificarFiltroProfessor(idProfessor, filter);
            return ResponseEntityFactory.ok(biService.listaAcessos(idProfessor, empresaId, dia, tipo));
        } catch (ServiceException e) {
            Logger.getLogger(BITreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter BI", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(value = "Listar agendamentos", notes = "Retorna lista paginada de agendamentos")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.<br/> Ordena por um atributo contido no objeto da resposta." +
                    "<strong>Ordens disponíveis</strong>" +
                    "<ul>" +
                    "<li>ASC - Ordena pelo atributo escolhido de forma ascendente</li>" +
                    "<li>DESC - Ordena pelo atributo escolhido de forma descendente</li>" +
                    "</ul>" +
                    "Deve ser informado como atributo,ordem", defaultValue = "nome,asc", paramType = "query", dataType = "string"),
    })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "Lista de agendamentos obtida com sucesso", response = ExemploRespostaListMapStringObjectPaginacao.class)
    })
    @ResponseBody
    @RequestMapping(value = "/agendamentos", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> agendamentos(
            @ApiParam(value = "ID do professor (opcional)", required = false, defaultValue = "1")
            @RequestParam(value = "professorId", required = false) Integer idProfessor,
            @ApiParam(value = "Filtros de busca em formato JSON", required = false, defaultValue = "{\"professorId\":1}")
            @RequestParam(value = "filters", required = false) String filter,
            @ApiParam(value = "ID da empresa", required = true, defaultValue = "1")
            @RequestHeader("empresaId") Integer empresaId,
            @ApiIgnore PaginadorDTO paginadorDTO) {
        idProfessor = verificarFiltroProfessor(idProfessor, filter);
        return listaGenerica(idProfessor, empresaId, IndicadorDashboardEnum.AGENDAMENTOS, paginadorDTO, filter);
    }

    @ApiOperation(value = "Listar agendamentos executados", notes = "Retorna lista paginada de agendamentos que foram executados (alunos compareceram)")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.<br/> Ordena por um atributo contido no objeto da resposta." +
                    "<strong>Ordens disponíveis</strong>" +
                    "<ul>" +
                    "<li>ASC - Ordena pelo atributo escolhido de forma ascendente</li>" +
                    "<li>DESC - Ordena pelo atributo escolhido de forma descendente</li>" +
                    "</ul>" +
                    "Deve ser informado como atributo,ordem", defaultValue = "nome,asc", paramType = "query", dataType = "string"),
    })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "Lista de agendamentos executados obtida com sucesso", response = ExemploRespostaListMapStringObjectPaginacao.class)
    })
    @ResponseBody
    @RequestMapping(value = "/agendamento-executaram", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> agendamentoexecutaram(
            @ApiParam(value = "ID do professor (opcional)", required = false, defaultValue = "1")
            @RequestParam(value = "professorId", required = false) Integer idProfessor,
            @ApiParam(value = "Filtros de busca em formato JSON", required = false, defaultValue = "{\"professorId\":1}")
            @RequestParam(value = "filters", required = false) String filter,
            @ApiParam(value = "ID da empresa", required = true, defaultValue = "1")
            @RequestHeader("empresaId") Integer empresaId,
            @ApiIgnore PaginadorDTO paginadorDTO) {
        idProfessor = verificarFiltroProfessor(idProfessor, filter);
        return listaGenerica(idProfessor, empresaId, IndicadorDashboardEnum.COMPARECERAM, paginadorDTO, filter);
    }

    @ApiOperation(value = "Listar agendamentos com faltas", notes = "Retorna lista paginada de agendamentos onde os alunos faltaram")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.<br/> Ordena por um atributo contido no objeto da resposta." +
                    "<strong>Ordens disponíveis</strong>" +
                    "<ul>" +
                    "<li>ASC - Ordena pelo atributo escolhido de forma ascendente</li>" +
                    "<li>DESC - Ordena pelo atributo escolhido de forma descendente</li>" +
                    "</ul>" +
                    "Deve ser informado como atributo,ordem", defaultValue = "nome,asc", paramType = "query", dataType = "string"),
    })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "Lista de agendamentos com faltas obtida com sucesso", response = ExemploRespostaListMapStringObjectPaginacao.class)
    })
    @ResponseBody
    @RequestMapping(value = "/agendamento-faltaram", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> agendamentofaltaram(
            @ApiParam(value = "ID do professor (opcional)", required = false, defaultValue = "1")
            @RequestParam(value = "professorId", required = false) Integer idProfessor,
            @ApiParam(value = "Filtros de busca em formato JSON", required = false, defaultValue = "{\"professorId\":1}")
            @RequestParam(value = "filters", required = false) String filter,
            @ApiParam(value = "ID da empresa", required = true, defaultValue = "1")
            @RequestHeader("empresaId") Integer empresaId,
            @ApiIgnore PaginadorDTO paginadorDTO) {
        idProfessor = verificarFiltroProfessor(idProfessor, filter);
        return listaGenerica(idProfessor, empresaId, IndicadorDashboardEnum.FALTARAM, paginadorDTO, filter);
    }

    /// treino-bi/agendamento-cancelaram
    @ResponseBody
    @RequestMapping(value = "/agendamento-cancelaram", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> agendamentocancelaram(@RequestParam(value = "professorId", required = false) Integer idProfessor,
                                                                     @RequestParam(value = "filters", required = false) String filter,
                                                                     @RequestHeader("empresaId") Integer empresaId, PaginadorDTO paginadorDTO) {
        idProfessor = verificarFiltroProfessor(idProfessor, filter);
        return listaGenerica(idProfessor, empresaId, IndicadorDashboardEnum.CANCELARAM, paginadorDTO, filter);
    }

    /// treino-bi/disponibilidades/aguardando-confirmacao
    @ResponseBody
    @RequestMapping(value = "/disponibilidades/aguardando-confirmacao", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> aguardandoconfirmacao(@RequestParam(value = "professorId", required = false) Integer idProfessor, @RequestHeader("empresaId") Integer empresaId,
                                                                     @RequestParam(value = "filters", required = false) String filter,
                                                                     @RequestParam("tipo") String tipo) {
        idProfessor = verificarFiltroProfessor(idProfessor, filter);
        return listaDisponibilidades(idProfessor, empresaId, StatusAgendamentoEnum.AGUARDANDO_CONFIRMACAO, TipoAgendamentoEnum.valueOf(tipo), filter);
    }

    /// treino-bi/disponibilidades/confirmaram
    @ResponseBody
    @RequestMapping(value = "/disponibilidades/confirmaram", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> confirmaram(@RequestParam(value = "professorId", required = false) Integer idProfessor, @RequestHeader("empresaId") Integer empresaId,
                                                           @RequestParam(value = "filters", required = false) String filter,
                                                           @RequestParam("tipo") String tipo) {
        idProfessor = verificarFiltroProfessor(idProfessor, filter);
        return listaDisponibilidades(idProfessor, empresaId, StatusAgendamentoEnum.CONFIRMADO, TipoAgendamentoEnum.valueOf(tipo), filter);
    }

    /// treino-bi/disponibilidades/executaram
    @ResponseBody
    @RequestMapping(value = "/disponibilidades/executaram", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> executaram(@RequestParam(value = "professorId", required = false) Integer idProfessor, @RequestHeader("empresaId") Integer empresaId,
                                                          @RequestParam(value = "filters", required = false) String filter,
                                                          @RequestParam("tipo") String tipo) {
        idProfessor = verificarFiltroProfessor(idProfessor, filter);
        return listaDisponibilidades(idProfessor, empresaId, StatusAgendamentoEnum.EXECUTADO, TipoAgendamentoEnum.valueOf(tipo), filter);
    }

    /// treino-bi/disponibilidades/faltaram
    @ResponseBody
    @RequestMapping(value = "/disponibilidades/faltaram", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> faltaram(@RequestParam(value = "professorId", required = false) Integer idProfessor, @RequestHeader("empresaId") Integer empresaId,
                                                        @RequestParam(value = "filters", required = false) String filter,
                                                        @RequestParam("tipo") String tipo) {
        idProfessor = verificarFiltroProfessor(idProfessor, filter);
        return listaDisponibilidades(idProfessor, empresaId, StatusAgendamentoEnum.FALTOU, TipoAgendamentoEnum.valueOf(tipo), filter);
    }

    /// treino-bi/disponibilidades/professores
    @ResponseBody
    @RequestMapping(value = "/disponibilidades/professores/{codigoPessoa}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> professores(@RequestParam(value = "professorId", required = false) Integer idProfessor,
                                                           @RequestParam(value = "filters", required = false) String filter,
                                                           @RequestHeader("empresaId") Integer empresaId,
                                                           @PathVariable Integer codigoPessoa, PaginadorDTO paginadorDTO) {
        Integer[] filtro = verificarFiltroProfessorDash(filter);
        idProfessor = filtro[0] != null ? filtro[0] : 0;
        codigoPessoa = filtro[1] == 0 ? codigoPessoa : filtro[1];
        if (idProfessor != 0) {
            idProfessor = verificarIdProfessor(idProfessor, empresaId, codigoPessoa);
        }
        return listaGenerica(idProfessor, empresaId, IndicadorDashboardEnum.PROFESSORES, paginadorDTO, filter);
    }

    /// treino-bi/disponibilidades/horas-executadas
    @ResponseBody
    @RequestMapping(value = "/disponibilidades/horas-executadas", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> horasexecutadas(@RequestParam(value = "professorId", required = false) Integer idProfessor,
                                                               @RequestParam(value = "filters", required = false) String filter,
                                                               @RequestHeader("empresaId") Integer empresaId, PaginadorDTO paginadorDTO) {
        idProfessor = verificarFiltroProfessor(idProfessor, filter);
        return listaGenerica(idProfessor, empresaId, IndicadorDashboardEnum.HRS_ATENDIMENTO, paginadorDTO, filter);
    }

    @ResponseBody
    @RequestMapping(value = "/disponibilidades/horas-disponibilidade", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> horasdisponibilidade(@RequestParam(value = "professorId", required = false) Integer idProfessor,
                                                                    @RequestParam(value = "filters", required = false) String filter,
                                                                    @RequestHeader("empresaId") Integer empresaId, PaginadorDTO paginadorDTO) {
        idProfessor = verificarFiltroProfessor(idProfessor, filter);
        return listaGenerica(idProfessor, empresaId, IndicadorDashboardEnum.HRS_DISPONIBILIDADE, paginadorDTO, filter);
    }

    /// treino-bi/treinos-novos
    @ResponseBody
    @RequestMapping(value = "/disponibilidades/treinos-novos/{codigoPessoa}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> treinosnovos(@RequestParam(value = "professorId", required = false) Integer idProfessor,
                                                            @RequestParam(value = "filters", required = false) String filter,
                                                            @RequestHeader("empresaId") Integer empresaId,
                                                            @PathVariable Integer codigoPessoa, PaginadorDTO paginadorDTO) {
        Integer[] filtro = verificarFiltroProfessorDash(filter);
        idProfessor = filtro[0] != null ? filtro[0] : 0;
        codigoPessoa = filtro[1] == 0 ? codigoPessoa : filtro[1];
        if (idProfessor != 0) {
            idProfessor = verificarIdProfessor(idProfessor, empresaId, codigoPessoa);
        }
        return listaGenerica(idProfessor, empresaId, IndicadorDashboardEnum.NOVOS_TREINOS, paginadorDTO, filter);
    }

    /// treino-bi/percentual-ocupacao
    @ResponseBody
    @RequestMapping(value = "/percentual-ocupacao/{codigoPessoa}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> percentualocupacao(@RequestParam(value = "professorId", required = false) Integer idProfessor,
                                                                  @RequestParam(value = "filters", required = false) String filter,
                                                                  @RequestHeader("empresaId") Integer empresaId,
                                                                  @PathVariable Integer codigoPessoa, PaginadorDTO paginadorDTO) {
        Integer[] filtro = verificarFiltroProfessorDash(filter);
        idProfessor = filtro[0] != null ? filtro[0] : 0;
        codigoPessoa = filtro[1] == 0 ? codigoPessoa : filtro[1];
        if (idProfessor != 0) {
            idProfessor = verificarIdProfessor(idProfessor, empresaId, codigoPessoa);
        }
        return listaGenerica(idProfessor, empresaId, IndicadorDashboardEnum.OCUPACAO, paginadorDTO, filter);
    }

    /// treino-bi/treinos-renovados
    @ResponseBody
    @RequestMapping(value = "/treinos-renovados/{codigoPessoa}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> treinosrenovados(@RequestParam(value = "professorId", required = false) Integer idProfessor,
                                                                @RequestParam(value = "filters", required = false) String filter,
                                                                @RequestHeader("empresaId") Integer empresaId,
                                                                @PathVariable Integer codigoPessoa, PaginadorDTO paginadorDTO) {
        Integer[] filtro = verificarFiltroProfessorDash(filter);
        idProfessor = filtro[0] != null ? filtro[0] : 0;
        codigoPessoa = filtro[1] == 0 ? codigoPessoa : filtro[1];
        if (idProfessor != 0) {
            idProfessor = verificarIdProfessor(idProfessor, empresaId, codigoPessoa);
        }
        return listaGenerica(idProfessor, empresaId, IndicadorDashboardEnum.TREINOS_RENOVADOS, paginadorDTO, filter);
    }

    /// treino-bi/avaliacoes-fisicas
    @ResponseBody
    @RequestMapping(value = "/avaliacoes-fisicas/{codigoPessoa}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> avaliacoesfisicas(@RequestParam(value = "professorId", required = false) Integer idProfessor,
                                                                 @RequestParam(value = "filters", required = false) String filter,
                                                                 @RequestHeader("empresaId") Integer empresaId,
                                                                 @PathVariable Integer codigoPessoa, PaginadorDTO paginadorDTO) {
        Integer[] filtro = verificarFiltroProfessorDash(filter);
        idProfessor = filtro[0] != null ? filtro[0] : 0;
        codigoPessoa = filtro[1] == 0 ? codigoPessoa : filtro[1];
        if (idProfessor != 0) {
            idProfessor = verificarIdProfessor(idProfessor, empresaId, codigoPessoa);
        }
        return listaGenerica(idProfessor, empresaId, IndicadorDashboardEnum.AVALIACOES_FISICAS, paginadorDTO, filter);
    }

    /// treino-bi/avaliacoes-fisicas-nao-realizado
    @ResponseBody
    @RequestMapping(value = "/avaliacoes-fisicas-nao-realizado/{codigoPessoa}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> avaliacoesFisicasSemAgendamento(@RequestParam(value = "professorId", required = false) Integer idProfessor,
                                                                               @RequestParam(value = "filters", required = false) String filter,
                                                                               @RequestHeader("empresaId") Integer empresaId,
                                                                               @PathVariable Integer codigoPessoa, PaginadorDTO paginadorDTO) {
        Integer[] filtro = verificarFiltroProfessorDash(filter);
        idProfessor = filtro[0] != null ? filtro[0] : 0;
        codigoPessoa = filtro[1] == 0 ? codigoPessoa : filtro[1];
        if (idProfessor != 0) {
            idProfessor = verificarIdProfessor(idProfessor, empresaId, codigoPessoa);
        }
        return listaGenerica(idProfessor, empresaId, IndicadorDashboardEnum.SEM_AVALIACAO, paginadorDTO, filter);
    }

    /// treino-bi/avaliacoes-fisicas-realizado
    @ResponseBody
    @RequestMapping(value = "/avaliacoes-fisicas-realizado/{codigoPessoa}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> avaliacoesFisicasComAgendamento(@RequestParam(value = "professorId", required = false) Integer idProfessor,
                                                                               @RequestParam(value = "filters", required = false) String filter,
                                                                               @RequestHeader("empresaId") Integer empresaId,
                                                                               @PathVariable Integer codigoPessoa, PaginadorDTO paginadorDTO) {
        Integer[] filtro = verificarFiltroProfessorDash(filter);
        idProfessor = filtro[0] != null ? filtro[0] : 0;
        codigoPessoa = filtro[1] == 0 ? codigoPessoa : filtro[1];
        if (idProfessor != 0) {
            idProfessor = verificarIdProfessor(idProfessor, empresaId, codigoPessoa);
        }
        return listaGenerica(idProfessor, empresaId, IndicadorDashboardEnum.COM_AVALIACAO_FISICA, paginadorDTO, filter);
    }

    @ApiOperation(value = "Listar total de alunos", notes = "Retorna lista paginada com o total de alunos")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.<br/> Ordena por um atributo contido no objeto da resposta." +
                    "<strong>Ordens disponíveis</strong>" +
                    "<ul>" +
                    "<li>ASC - Ordena pelo atributo escolhido de forma ascendente</li>" +
                    "<li>DESC - Ordena pelo atributo escolhido de forma descendente</li>" +
                    "</ul>" +
                    "Deve ser informado como atributo,ordem", defaultValue = "nome,asc", paramType = "query", dataType = "string"),
    })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "Lista total de alunos obtida com sucesso", response = ExemploRespostaListMapStringObjectPaginacao.class)
    })
    @ResponseBody
    @RequestMapping(value = "/alunos-total/{codigoPessoa}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alunostotal(
            @ApiParam(value = "ID do professor (opcional)", required = false, defaultValue = "1")
            @RequestParam(value = "professorId", required = false) Integer idProfessor,
            @ApiParam(value = "Filtros de busca em formato JSON", required = false, defaultValue = "{\"professorId\":1}")
            @RequestParam(value = "filters", required = false) String filter,
            @ApiParam(value = "ID da empresa", required = true, defaultValue = "1")
            @RequestHeader("empresaId") Integer empresaId,
            @ApiParam(value = "Código da pessoa", required = true, defaultValue = "1")
            @PathVariable Integer codigoPessoa,
            @ApiIgnore PaginadorDTO paginadorDTO) {
        Integer[] filtro = verificarFiltroProfessorDash(filter);
        idProfessor = filtro[0] != null ? filtro[0] : 0;
        codigoPessoa = filtro[1] == 0 ? codigoPessoa : filtro[1];
        if (idProfessor != 0) {
            idProfessor = verificarIdProfessor(idProfessor, empresaId, codigoPessoa);
        }
        return listaGenerica(idProfessor, empresaId, IndicadorDashboardEnum.TOTAL_ALUNOS, paginadorDTO, filter);
    }

    @ApiOperation(value = "Listar alunos ativos", notes = "Retorna lista paginada de alunos com situação ativa")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.<br/> Ordena por um atributo contido no objeto da resposta." +
                    "<strong>Ordens disponíveis</strong>" +
                    "<ul>" +
                    "<li>ASC - Ordena pelo atributo escolhido de forma ascendente</li>" +
                    "<li>DESC - Ordena pelo atributo escolhido de forma descendente</li>" +
                    "</ul>" +
                    "Deve ser informado como atributo,ordem", defaultValue = "nome,asc", paramType = "query", dataType = "string"),
    })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "Lista de alunos ativos obtida com sucesso", response = ExemploRespostaListMapStringObjectPaginacao.class)
    })
    @ResponseBody
    @RequestMapping(value = "/alunos-ativos/{codigoPessoa}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alunosativos(
            @ApiParam(value = "ID do professor (opcional)", required = false, defaultValue = "1")
            @RequestParam(value = "professorId", required = false) Integer idProfessor,
            @ApiParam(value = "Filtros de busca em formato JSON", required = false, defaultValue = "{\"professorId\":1}")
            @RequestParam(value = "filters", required = false) String filter,
            @ApiParam(value = "ID da empresa", required = true, defaultValue = "1")
            @RequestHeader("empresaId") Integer empresaId,
            @ApiParam(value = "Código da pessoa", required = true, defaultValue = "1")
            @PathVariable Integer codigoPessoa,
            @ApiIgnore PaginadorDTO paginadorDTO) {
        Integer[] filtro = verificarFiltroProfessorDash(filter);
        idProfessor = filtro[0] != null ? filtro[0] : 0;
        codigoPessoa = filtro[1] == 0 ? codigoPessoa : filtro[1];
        if (idProfessor != 0) {
            idProfessor = verificarIdProfessor(idProfessor, empresaId, codigoPessoa);
        }
        return listaGenerica(idProfessor, empresaId, IndicadorDashboardEnum.ATIVOS, paginadorDTO, filter);
    }

    @ApiOperation(value = "Listar alunos inativos", notes = "Retorna lista paginada de alunos com situação inativa")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.<br/> Ordena por um atributo contido no objeto da resposta." +
                    "<strong>Ordens disponíveis</strong>" +
                    "<ul>" +
                    "<li>ASC - Ordena pelo atributo escolhido de forma ascendente</li>" +
                    "<li>DESC - Ordena pelo atributo escolhido de forma descendente</li>" +
                    "</ul>" +
                    "Deve ser informado como atributo,ordem", defaultValue = "nome,asc", paramType = "query", dataType = "string"),
    })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "Lista de alunos inativos obtida com sucesso", response = ExemploRespostaListMapStringObjectPaginacao.class)
    })
    @ResponseBody
    @RequestMapping(value = "/alunos-inativos/{codigoPessoa}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alunosinativos(
            @ApiParam(value = "ID do professor (opcional)", required = false, defaultValue = "1")
            @RequestParam(value = "professorId", required = false) Integer idProfessor,
            @ApiParam(value = "Filtros de busca em formato JSON", required = false, defaultValue = "{\"professorId\":1}")
            @RequestParam(value = "filters", required = false) String filter,
            @ApiParam(value = "ID da empresa", required = true, defaultValue = "1")
            @RequestHeader("empresaId") Integer empresaId,
            @ApiParam(value = "Código da pessoa", required = true, defaultValue = "1")
            @PathVariable Integer codigoPessoa,
            @ApiIgnore PaginadorDTO paginadorDTO) {
        Integer[] filtro = verificarFiltroProfessorDash(filter);
        idProfessor = filtro[0] != null ? filtro[0] : 0;
        codigoPessoa = filtro[1] == 0 ? codigoPessoa : filtro[1];
        if (idProfessor != 0) {
            idProfessor = verificarIdProfessor(idProfessor, empresaId, codigoPessoa);
        }
        return listaGenerica(idProfessor, empresaId, IndicadorDashboardEnum.INATIVOS, paginadorDTO, filter);
    }

    @ApiOperation(value = "Listar alunos sem acompanhamento", notes = "Retorna lista paginada de alunos que não possuem acompanhamento ativo")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.<br/> Ordena por um atributo contido no objeto da resposta." +
                    "<strong>Ordens disponíveis</strong>" +
                    "<ul>" +
                    "<li>ASC - Ordena pelo atributo escolhido de forma ascendente</li>" +
                    "<li>DESC - Ordena pelo atributo escolhido de forma descendente</li>" +
                    "</ul>" +
                    "Deve ser informado como atributo,ordem", defaultValue = "nome,asc", paramType = "query", dataType = "string"),
    })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "Lista de alunos sem acompanhamento obtida com sucesso", response = ExemploRespostaListMapStringObjectPaginacao.class)
    })
    @ResponseBody
    @RequestMapping(value = "/alunos-sem-acompanhamento/{codigoPessoa}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alunosSemAcompanhamento(
            @ApiParam(value = "ID do professor (opcional)", required = false, defaultValue = "1")
            @RequestParam(value = "professorId", required = false) Integer idProfessor,
            @ApiParam(value = "Filtros de busca em formato JSON", required = false, defaultValue = "{\"professorId\":1}")
            @RequestParam(value = "filters", required = false) String filter,
            @ApiParam(value = "ID da empresa", required = true, defaultValue = "1")
            @RequestHeader("empresaId") Integer empresaId,
            @ApiParam(value = "Código da pessoa", required = true, defaultValue = "1")
            @PathVariable Integer codigoPessoa,
            @ApiIgnore PaginadorDTO paginadorDTO,
            HttpServletRequest request) {
        Integer[] filtro = verificarFiltroProfessorDash(filter);
        idProfessor = filtro[0] != null ? filtro[0] : 0;
        codigoPessoa = filtro[1] == 0 ? codigoPessoa : filtro[1];
        if (idProfessor != 0) {
            idProfessor = verificarIdProfessor(idProfessor, empresaId, codigoPessoa);
        }
        return listaGenericaRequest(idProfessor, empresaId, IndicadorDashboardEnum.SEM_ACOMPANHAMENTO, paginadorDTO, filter, request);
    }

    @ApiOperation(value = "Listar alunos em acompanhamento", notes = "Retorna lista paginada de alunos que possuem acompanhamento ativo")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.<br/> Ordena por um atributo contido no objeto da resposta." +
                    "<strong>Ordens disponíveis</strong>" +
                    "<ul>" +
                    "<li>ASC - Ordena pelo atributo escolhido de forma ascendente</li>" +
                    "<li>DESC - Ordena pelo atributo escolhido de forma descendente</li>" +
                    "</ul>" +
                    "Deve ser informado como atributo,ordem", defaultValue = "nome,asc", paramType = "query", dataType = "string"),
    })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "Lista de alunos em acompanhamento obtida com sucesso", response = ExemploRespostaListMapStringObjectPaginacao.class)
    })
    @ResponseBody
    @RequestMapping(value = "/alunos-em-acompanhamento/{codigoPessoa}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alunosEmAcompanhamento(
            @ApiParam(value = "ID do professor (opcional)", required = false, defaultValue = "1")
            @RequestParam(value = "professorId", required = false) Integer idProfessor,
            @ApiParam(value = "Filtros de busca em formato JSON", required = false, defaultValue = "{\"professorId\":1}")
            @RequestParam(value = "filters", required = false) String filter,
            @ApiParam(value = "ID da empresa", required = true, defaultValue = "1")
            @RequestHeader("empresaId") Integer empresaId,
            @ApiParam(value = "Código da pessoa", required = true, defaultValue = "1")
            @PathVariable Integer codigoPessoa,
            @ApiIgnore PaginadorDTO paginadorDTO,
            HttpServletRequest request) {
        Integer[] filtro = verificarFiltroProfessorDash(filter);
        idProfessor = filtro[0] != null ? filtro[0] : 0;
        codigoPessoa = filtro[1] == 0 ? codigoPessoa : filtro[1];
        if (idProfessor != 0) {
            idProfessor = verificarIdProfessor(idProfessor, empresaId, codigoPessoa);
        }
        return listaGenericaRequest(idProfessor, empresaId, IndicadorDashboardEnum.EM_ACOMPANHAMENTO, paginadorDTO, filter, request);
    }

    @ApiOperation(value = "Iniciar acompanhamento de aluno", notes = "Inicia o acompanhamento de um aluno por um professor específico")
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "Acompanhamento iniciado com sucesso", response = br.com.pacto.swagger.respostas.ExemploRespostaVazia.class)
    })
    @ResponseBody
    @RequestMapping(value = "/alunos-sem-acompanhamento/{codigoPessoa}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> iniciarAcompanhamento(
            @ApiParam(value = "ID da empresa", required = true, defaultValue = "1")
            @RequestHeader("empresaId") Integer empresaId,
            @ApiParam(value = "Código da pessoa (aluno)", required = true, defaultValue = "1")
            @PathVariable Integer codigoPessoa,
            @ApiParam(value = "ID do professor que iniciará o acompanhamento", required = true)
            @RequestBody Integer idProfessor,
            @ApiParam(value = "Tipo de acompanhamento (1=Padrão)", required = false, defaultValue = "1")
            @RequestParam(required = false, defaultValue = "1") Integer tipoAcompamanhento) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            TiposAcompanhamentoEnum tipoAcompanhamentoEnum = TiposAcompanhamentoEnum.getFromCodigo(tipoAcompamanhento);
            if (tipoAcompanhamentoEnum == null) {
                return ResponseEntityFactory.erroInterno("TIPO_ACOMPANHAMENTO_INVALIDO", "Tipo de acompanhamento inválido");
            }
            if (idProfessor != 0) {
                idProfessor = verificarIdProfessor(idProfessor, empresaId, codigoPessoa);
            }
            ClienteSintetico cliente = clienteSinteticoService.obterPorId(ctx, codigoPessoa);
            ProfessorSintetico professor = professorSinteticoService.obterPorId(ctx, idProfessor);
            clienteSinteticoService.iniciarAcompanhamento(ctx, cliente, professor, tipoAcompanhamentoEnum);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(BITreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar iniciar acompanhamento", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(value = "Iniciar acompanhamento de aluno (v2)", notes = "Versão 2 do endpoint para iniciar acompanhamento de um aluno por um professor específico")
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "Acompanhamento iniciado com sucesso", response = br.com.pacto.swagger.respostas.ExemploRespostaVazia.class)
    })
    @ResponseBody
    @RequestMapping(value = "/iniciar-acompanhamento-aluno/v2/{codigoCliente}/{codigoProfessor}", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> iniciarAcompanhamentoV2(
            @ApiParam(value = "ID da empresa", required = true, defaultValue = "1")
            @RequestHeader("empresaId") Integer empresaId,
            @ApiParam(value = "Código do cliente (aluno)", required = true, defaultValue = "1")
            @PathVariable Integer codigoCliente,
            @ApiParam(value = "Código do professor", required = true, defaultValue = "1")
            @PathVariable Integer codigoProfessor,
            @ApiParam(value = "Tipo de acompanhamento (1=Padrão)", required = false, defaultValue = "1")
            @RequestParam(required = false, defaultValue = "1") Integer tipoAcompamanhento) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            TiposAcompanhamentoEnum tipoAcompanhamentoEnum = TiposAcompanhamentoEnum.getFromCodigo(tipoAcompamanhento);

            if (tipoAcompanhamentoEnum == null) {
                return ResponseEntityFactory.erroInterno("TIPO_ACOMPANHAMENTO_INVALIDO", "Tipo de acompanhamento inválido");
            }

            ClienteSintetico cliente = clienteSinteticoService.obterPorId(ctx, codigoCliente);
            if (cliente == null) {
                throw new ServiceException("ALUNO_INEXISTENTE", "Aluno inexistente");
            }

            ProfessorSintetico professor = professorSinteticoService.obterPorId(ctx, codigoProfessor);
            if (professor == null) {
                throw new ServiceException("PROFESSOR_INEXISTENTE", "Professor inexistente");
            }

            clienteSinteticoService.iniciarAcompanhamento(ctx, cliente, professor, tipoAcompanhamentoEnum);

            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(BITreinoController.class.getName())
                    .log(Level.SEVERE, "Erro ao tentar iniciar acompanhamento", e);
            return ResponseEntityFactory.erroInterno(
                    e.getChaveExcecao(),
                    e.getMessage()
            );
        }
    }

    @ApiOperation(value = "Finalizar acompanhamento de aluno", notes = "Finaliza o acompanhamento de um aluno específico")
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "Acompanhamento finalizado com sucesso", response = ExemploRespostaVazia.class)
    })
    @ResponseBody
    @RequestMapping(value = "/alunos-com-acompanhamento/{codigoPessoa}/{programaId}/{fichaId}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> finalizarAcompanhamento(
            @ApiParam(value = "ID da empresa", required = true, defaultValue = "1")
            @RequestHeader("empresaId") Integer empresaId,
            @ApiParam(value = "Código da pessoa (aluno)", required = true, defaultValue = "1")
            @PathVariable Integer codigoPessoa,
            @ApiParam(value = "ID do programa de treino", required = true, defaultValue = "1")
            @PathVariable Integer programaId,
            @ApiParam(value = "ID da ficha de treino", required = true, defaultValue = "1")
            @PathVariable Integer fichaId,
            @ApiParam(value = "ID do professor responsável", required = true)
            @RequestBody Integer idProfessor) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            if (idProfessor != 0) {
                idProfessor = verificarIdProfessor(idProfessor, empresaId, codigoPessoa);
            }
            ClienteSintetico cliente = clienteSinteticoService.obterPorId(ctx, codigoPessoa);
            ProfessorSintetico professor = professorSinteticoService.obterPorId(ctx, idProfessor);
            clienteSinteticoService.finalizarAcompanhamento(ctx, cliente, professor, programaId, fichaId);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(BITreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar iniciar acompanhamento", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(value = "Finalizar acompanhamento de aluno (v2)", notes = "Versão 2 do endpoint para finalizar acompanhamento de um aluno específico")
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "Acompanhamento finalizado com sucesso", response = br.com.pacto.swagger.respostas.ExemploRespostaVazia.class)
    })
    @ResponseBody
    @RequestMapping(value = "/finalizar-acompanhamento-aluno/v2/{codigoCliente}/{codigoProfessor}/{programaId}/{fichaId}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> finalizarAcompanhamentoV2(
            @ApiParam(value = "ID da empresa", required = true, defaultValue = "1")
            @RequestHeader("empresaId") Integer empresaId,
            @ApiParam(value = "Código do cliente (aluno)", required = true, defaultValue = "1")
            @PathVariable Integer codigoCliente,
            @ApiParam(value = "Código do professor", required = true, defaultValue = "1")
            @PathVariable Integer codigoProfessor,
            @ApiParam(value = "ID do programa de treino", required = true, defaultValue = "1")
            @PathVariable Integer programaId,
            @ApiParam(value = "ID da ficha de treino", required = true, defaultValue = "1")
            @PathVariable Integer fichaId) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();

            ClienteSintetico cliente = clienteSinteticoService.obterPorId(ctx, codigoCliente);
            if (cliente == null) {
                throw new ServiceException("ALUNO_INEXISTENTE", "Aluno inexistente");
            }

            ProfessorSintetico professor = professorSinteticoService.obterPorId(ctx, codigoProfessor);
            if (professor == null) {
                throw new ServiceException("PROFESSOR_INEXISTENTE", "Professor inexistente");
            }

            clienteSinteticoService.finalizarAcompanhamento(ctx, cliente, professor, programaId, fichaId);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(BITreinoController.class.getName())
                    .log(Level.SEVERE, "Erro ao tentar finalizar acompanhamento", e);
            return ResponseEntityFactory.erroInterno(
                    e.getChaveExcecao(),
                    e.getMessage()
            );
        }
    }

    @ApiOperation(value = "Listar alunos visitantes", notes = "Retorna lista paginada de alunos com situação visitante")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.<br/> Ordena por um atributo contido no objeto da resposta." +
                    "<strong>Ordens disponíveis</strong>" +
                    "<ul>" +
                    "<li>ASC - Ordena pelo atributo escolhido de forma ascendente</li>" +
                    "<li>DESC - Ordena pelo atributo escolhido de forma descendente</li>" +
                    "</ul>" +
                    "Deve ser informado como atributo,ordem", defaultValue = "nome,asc", paramType = "query", dataType = "string"),
    })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "Lista de alunos visitantes obtida com sucesso", response = ExemploRespostaListMapStringObjectPaginacao.class)
    })
    @ResponseBody
    @RequestMapping(value = "/alunos-visitantes/{codigoPessoa}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alunosVisitantes(
            @ApiParam(value = "ID do professor (opcional)", required = false, defaultValue = "1")
            @RequestParam(value = "professorId", required = false) Integer idProfessor,
            @ApiParam(value = "Filtros de busca em formato JSON", required = false, defaultValue = "{\"professorId\":1}")
            @RequestParam(value = "filters", required = false) String filter,
            @ApiParam(value = "ID da empresa", required = true, defaultValue = "1")
            @RequestHeader("empresaId") Integer empresaId,
            @ApiParam(value = "Código da pessoa", required = true, defaultValue = "1")
            @PathVariable Integer codigoPessoa,
            @ApiIgnore PaginadorDTO paginadorDTO) {
        Integer[] filtro = verificarFiltroProfessorDash(filter);
        idProfessor = filtro[0] != null ? filtro[0] : 0;
        codigoPessoa = filtro[1] == 0 ? codigoPessoa : filtro[1];
        if (idProfessor != 0) {
            idProfessor = verificarIdProfessor(idProfessor, empresaId, codigoPessoa);
        }
        return listaGenerica(idProfessor, empresaId, IndicadorDashboardEnum.VISITANTES, paginadorDTO, filter);
    }

    /// treino-bi/alunos-renovaram
    @ResponseBody
    @RequestMapping(value = "/alunos-renovaram/{codigoPessoa}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alunosrenovaram(@RequestParam(value = "professorId", required = false) Integer idProfessor,
                                                               @RequestParam(value = "filters", required = false) String filter,
                                                               @RequestHeader("empresaId") Integer empresaId,
                                                               @PathVariable Integer codigoPessoa, PaginadorDTO paginadorDTO) {
        Integer[] filtro = verificarFiltroProfessorDash(filter);
        idProfessor = filtro[0] != null ? filtro[0] : 0;
        codigoPessoa = filtro[1] == 0 ? codigoPessoa : filtro[1];
        if (idProfessor != 0) {
            idProfessor = verificarIdProfessor(idProfessor, empresaId, codigoPessoa);
        }
        return listaGenerica(idProfessor, empresaId, IndicadorDashboardEnum.RENOVARAM, paginadorDTO, filter);
    }

    /// treino-bi/alunos-nao-renovaram
    @ResponseBody
    @RequestMapping(value = "/alunos-nao-renovaram", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alunosnaorenovaram(@RequestParam(value = "professorId", required = false) Integer idProfessor,
                                                                  @RequestParam(value = "filters", required = false) String filter,
                                                                  @RequestHeader("empresaId") Integer empresaId, PaginadorDTO paginadorDTO) {
        idProfessor = verificarFiltroProfessor(idProfessor, filter);
        return listaGenerica(idProfessor, empresaId, IndicadorDashboardEnum.NAO_RENOVARAM, paginadorDTO, filter);
    }

    /// treino-bi/alunos-vencer-30-dias
    @ResponseBody
    @RequestMapping(value = "/alunos-vencer-30-dias/{codigoPessoa}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alunosvencer30dias(@RequestParam(value = "professorId", required = false) Integer idProfessor,
                                                                  @RequestParam(value = "filters", required = false) String filter,
                                                                  @RequestHeader("empresaId") Integer empresaId,
                                                                  @PathVariable Integer codigoPessoa, PaginadorDTO paginadorDTO) {
        Integer[] filtro = verificarFiltroProfessorDash(filter);
        idProfessor = filtro[0] != null ? filtro[0] : 0;
        codigoPessoa = filtro[1] == 0 ? codigoPessoa : filtro[1];
        if (idProfessor != 0) {
            idProfessor = verificarIdProfessor(idProfessor, empresaId, codigoPessoa);
        }
        return listaGenerica(idProfessor, empresaId, IndicadorDashboardEnum.ALUNOS_A_VENCER, paginadorDTO, filter);
    }

    /// treino-bi/entrada-carteira/novos
    @ResponseBody
    @RequestMapping(value = "/entrada-carteira/novos", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> entradacarteiranovos(@RequestParam(value = "professorId", required = false) Integer idProfessor,
                                                                    @RequestParam(value = "filters", required = false) String filter,
                                                                    @RequestHeader("empresaId") Integer empresaId, PaginadorDTO paginadorDTO) {
        idProfessor = verificarFiltroProfessor(idProfessor, filter);
        return listaGenerica(idProfessor, empresaId, IndicadorDashboardEnum.NOVOS_CARTEIRA, paginadorDTO, filter);
    }

    /// treino-bi/entrada-carteira/trocaram
    @ResponseBody
    @RequestMapping(value = "/entrada-carteira/trocaram", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> entradacarteiratrocaram(@RequestParam(value = "professorId", required = false) Integer idProfessor,
                                                                       @RequestParam(value = "filters", required = false) String filter,
                                                                       @RequestHeader("empresaId") Integer empresaId, PaginadorDTO paginadorDTO) {
        idProfessor = verificarFiltroProfessor(idProfessor, filter);
        return listaGenerica(idProfessor, empresaId, IndicadorDashboardEnum.NOVOS_CARTEIRA_TROCARAM, paginadorDTO, filter);
    }

    /// treino-bi/saida-carteira
    @ResponseBody
    @RequestMapping(value = "/saida-carteira", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> saidacarteira(@RequestParam(value = "professorId", required = false) Integer idProfessor,
                                                             @RequestParam(value = "filters", required = false) String filter,
                                                             @RequestHeader("empresaId") Integer empresaId, PaginadorDTO paginadorDTO) {
        idProfessor = verificarFiltroProfessor(idProfessor, filter);
        return listaGenerica(idProfessor, empresaId, IndicadorDashboardEnum.TROCARAM_CARTEIRA, paginadorDTO, filter);
    }

    @ApiOperation(value = "Obter avaliações de treino por tipo", notes = "Retorna lista paginada de avaliações de treino filtradas por tipo de busca (estrelas)")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.<br/> Ordena por um atributo contido no objeto da resposta." +
                    "<strong>Ordens disponíveis</strong>" +
                    "<ul>" +
                    "<li>ASC - Ordena pelo atributo escolhido de forma ascendente</li>" +
                    "<li>DESC - Ordena pelo atributo escolhido de forma descendente</li>" +
                    "</ul>" +
                    "Deve ser informado como atributo,ordem", defaultValue = "nome,asc", paramType = "query", dataType = "string"),
    })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "Lista de avaliações de treino obtida com sucesso", response = ExemploRespostaListMapStringObjectPaginacao.class)
    })
    @ResponseBody
    @RequestMapping(value = "/avaliacao-treino/{tipoBusca}/{codigoPessoa}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> avaliacaoTreino(
            @ApiParam(value = "ID do professor (opcional)", required = false, defaultValue = "1")
            @RequestParam(value = "professorId", required = false) Integer idProfessor,
            @ApiParam(value = "Filtros de busca em formato JSON", required = false, defaultValue = "{\"professorId\":1}")
            @RequestParam(value = "filters", required = false) String filter,
            @ApiParam(value = "ID da empresa", required = true, defaultValue = "1")
            @RequestHeader("empresaId") Integer empresaId,
            @ApiParam(value = "Código da pessoa", required = true, defaultValue = "1")
            @PathVariable("codigoPessoa") Integer codigoPessoa,
            @ApiParam(value = "Tipo de busca por avaliação (0=Todas, 1=1 estrela, 2=2 estrelas, 3=3 estrelas, 4=4 estrelas, 5=5 estrelas)", required = true, defaultValue = "0")
            @PathVariable Integer tipoBusca,
            @ApiIgnore PaginadorDTO paginadorDTO) {
        Integer[] filtro = verificarFiltroProfessorDash(filter);
        idProfessor = filtro[0] != null ? filtro[0] : 0;
        codigoPessoa = filtro[1] == 0 ? codigoPessoa : filtro[1];
        if (idProfessor != 0) {
            idProfessor = verificarIdProfessor(idProfessor, empresaId, codigoPessoa);
        }

        switch (tipoBusca) {
            case 0:
                return listaGenerica(idProfessor, empresaId, IndicadorDashboardEnum.AVALIACOES, paginadorDTO, filter);
            case 1:
                return listaGenerica(idProfessor, empresaId, IndicadorDashboardEnum.ESTRELAS_1, paginadorDTO, filter);
            case 2:
                return listaGenerica(idProfessor, empresaId, IndicadorDashboardEnum.ESTRELAS_2, paginadorDTO, filter);
            case 3:
                return listaGenerica(idProfessor, empresaId, IndicadorDashboardEnum.ESTRELAS_3, paginadorDTO, filter);
            case 4:
                return listaGenerica(idProfessor, empresaId, IndicadorDashboardEnum.ESTRELAS_4, paginadorDTO, filter);
            default:
                return listaGenerica(idProfessor, empresaId, IndicadorDashboardEnum.ESTRELAS_5, paginadorDTO, filter);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/avaliacao-acompanhamento/{tipoBusca}/{codigoPessoa}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> avaliacaoAcompanhamento(@RequestParam(value = "professorId", required = false) Integer idProfessor,
                                                                       @RequestParam(value = "filters", required = false) String filter,
                                                                       @RequestHeader("empresaId") Integer empresaId,
                                                                       @PathVariable("codigoPessoa") Integer codigoPessoa,
                                                                       @PathVariable Integer tipoBusca, PaginadorDTO paginadorDTO) {
        try {
            Integer[] filtro = verificarFiltroProfessorDash(filter);
            idProfessor = filtro[0] != null ? filtro[0] : (idProfessor != null ? idProfessor : 0);
            codigoPessoa = filtro[1] == 0 ? codigoPessoa : filtro[1];
            if (idProfessor != 0) {
                idProfessor = verificarIdProfessor(idProfessor, empresaId, codigoPessoa);
            }

            List<AvaliacaoAcompanhamentoDTO> lista = dashboardBIService.obterAvaliacoesAcompanhamento(
                    sessaoService.getUsuarioAtual().getChave(),
                    idProfessor,
                    null,
                    null,
                    paginadorDTO,
                    filter,
                    empresaId,
                    tipoBusca
            );

            return ResponseEntityFactory.ok(lista, paginadorDTO);

        } catch (Exception e) {
            Logger.getLogger(BITreinoController.class.getName()).log(Level.SEVERE, "Erro ao obter lista de avaliação de acompanhamento", e);
            return ResponseEntityFactory.erroInterno("ERRO_BI_AVALIACAO_ACOMPANHAMENTO", e.getMessage());
        }
    }

    ///treino-bi/alunos-vencer-30-dias
    @ResponseBody
    @RequestMapping(value = "/lista-alunos-vencer-30-dias/{codigoPessoa}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listaAlunosVencer30Dias(@RequestParam(value = "professorId", required = false) Integer idProfessor,
                                                                       @RequestParam(value = "filters", required = false) String filter,
                                                                       @RequestHeader("empresaId") Integer empresaId,
                                                                       @PathVariable Integer codigoPessoa, PaginadorDTO paginadorDTO) {
        Integer[] filtro = verificarFiltroProfessorDash(filter);
        idProfessor = filtro[0] != null ? filtro[0] : 0;
        codigoPessoa = filtro[1] == 0 ? codigoPessoa : filtro[1];
        if (idProfessor != 0) {
            idProfessor = verificarIdProfessor(idProfessor, empresaId, codigoPessoa);
        }
        return listaGenerica(idProfessor, empresaId, IndicadorDashboardEnum.ALUNOS_A_VENCER, paginadorDTO, filter);
    }

    private Integer verificarIdProfessor(Integer idProfessor, Integer empresa, Integer pessoa) {
        try {
            return biService.codigoProfessor(idProfessor, empresa, pessoa);
        } catch (ServiceException e) {
            Uteis.logar(e, BITreinoController.class);
            e.printStackTrace();
        }
        return 0;
    }

    @ApiOperation(value = "Verificar pendências de Business Intelligence", notes = "Verifica se existem pendências relacionadas a treinos vencidos ou alunos sem treino")
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "Verificação realizada com sucesso", response = ExemploRespostaBoolean.class)
    })
    @ResponseBody
    @RequestMapping(value = "/verificar-pendencias", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> verificarPendencia(
            @ApiParam(value = "ID do professor", required = true, defaultValue = "1")
            @RequestParam(value = "idProfessor") Integer idProfessor,
            @ApiParam(value = "ID da empresa", required = true, defaultValue = "1")
            @RequestHeader(value = "empresaId") Integer empresaId,
            @ApiParam(value = "Código da pessoa", required = true, defaultValue = "1")
            @RequestParam(value = "codigoPessoa") Integer codigoPessoa) {

        try {

            boolean temPendencias = verificarPendencias(idProfessor, empresaId, codigoPessoa);
            EnvelopeRespostaDTO resposta = new EnvelopeRespostaDTO();
            resposta.setContent(temPendencias);
            return ResponseEntityFactory.ok(resposta);

        } catch (Exception e) {
            Uteis.logar(e, BITreinoController.class);
            e.printStackTrace();
        }
        return ResponseEntityFactory.erroInterno("erro.verificar.pendencias", "Erro ao verificar pendências");
    }

    @ResponseBody
    @RequestMapping(value = "/importar-aluno-fora-treino", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> importarAlunoForaTreino(
            @RequestParam(value = "idProfessor") Integer idProfessor,
            @RequestHeader(value = "empresaId") Integer empresaId) {

        try {
            biService.importarAlunosForaTreino(idProfessor, empresaId);
            return ResponseEntityFactory.ok();
        } catch (Exception e) {
            Uteis.logar(e, BITreinoController.class);
            e.printStackTrace();
        }
        return ResponseEntityFactory.erroInterno("erro.importar.aluno.fora.treino", "Erro ao iniciar processor para importar alunos fora do treino!");
    }

    private Integer verificarFiltroProfessor(Integer idProfessor, String filter) {
        try {
            if (idProfessor == null && filter != null && filter.contains("professorId")) {
                JSONObject objJson = new JSONObject(filter);
                idProfessor = objJson.getInt("professorId");
                if (idProfessor == (-1)) {
                    idProfessor = 0;
                }
                if (idProfessor != 0) {
                    idProfessor = biService.codigoProfessor(idProfessor, null, null);
                }
                return idProfessor;
            }
        } catch (Exception e) {
            Uteis.logar(e, BITreinoController.class);
            e.printStackTrace();
        }
        return null;
    }

    private Integer[] verificarFiltroProfessorDash(String filter) {
        Integer[] retorno = new Integer[2];
        Integer codigoPessoa;
        Integer idProfessor;
        try {
            if (filter != null && filter.contains("professorId") && filter.contains("codigoPessoa")) {
                JSONObject objJson = new JSONObject(filter);
                idProfessor = objJson.getInt("professorId");
                codigoPessoa = objJson.getInt("codigoPessoa");
                if (idProfessor == (-1)) {
                    idProfessor = 0;
                }
                if (codigoPessoa == (-1)) {
                    codigoPessoa = 0;
                }
                retorno[0] = idProfessor;
                retorno[1] = codigoPessoa;
            } else {
                retorno[0] = 0;
                retorno[1] = 0;
            }
            return retorno;
        } catch (Exception e) {
            Uteis.logar(e, BITreinoController.class);
            e.printStackTrace();
        }
        return null;
    }

    public String processarUrl(HttpServletRequest request, String url, String format, String urlTreino, List<LinkedHashMap<String, Object>> lista, String tituloRel) throws Exception {
        String key = sessaoService.getUsuarioAtual().getChave();
        if (format.equals("PDF")) {
            ExportarPDF geradorPDF = new ExportarPDF();
            url = geradorPDF.visualizarRelatorioPDF(key, lista, request,
                    tituloRel, "relatorio-pdf", urlTreino);
        } else if (format.equals("XLS")) {
            ExportarXLS exportarXLS = new ExportarXLS();
            url = exportarXLS.visualizarRelatorioExcel(key, lista, request,
                    tituloRel, "relatorio-excel", urlTreino);
        }
        return url;
    }

    public boolean verificarPendencias(Integer idProfessor, Integer empresaId, Integer codigoPessoa) {
        JSONObject filtroJson = new JSONObject();
        try {
            filtroJson.put("professorId", idProfessor != null ? idProfessor : JSONObject.NULL);
            filtroJson.put("codigoPessoa", codigoPessoa != null ? codigoPessoa : JSONObject.NULL);
        } catch (JSONException e) {
            e.printStackTrace();
            return false;
        }
        String filtro = filtroJson.toString();

        ResponseEntity<EnvelopeRespostaDTO> respostaTreinosVencidos = alunostreinovencido(idProfessor, filtro, empresaId, codigoPessoa, new PaginadorDTO());
        ResponseEntity<EnvelopeRespostaDTO> respostaAlunosSemTreino = alunosativosemtreino(idProfessor, filtro, empresaId, codigoPessoa, new PaginadorDTO());

        boolean temTreinosVencidos = verificaSeTemResposta(respostaTreinosVencidos);
        boolean temAlunosSemTreino = verificaSeTemResposta(respostaAlunosSemTreino);

        return temTreinosVencidos || temAlunosSemTreino;

    }

    private static boolean verificaSeTemResposta(ResponseEntity<EnvelopeRespostaDTO> response) {
        if (response != null && response.getBody() != null && response.getBody().getContent() != null) {
            Object content = response.getBody().getContent();
            if (content instanceof Collection<?>) {
                return !((Collection<?>) content).isEmpty();
            } else if (content.getClass().isArray()) {
                return Array.getLength(content) > 0;
            }
            return false;
        }
        return false;
    }
}
