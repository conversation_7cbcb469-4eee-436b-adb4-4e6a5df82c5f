package br.com.pacto.controller.json.professor;

import io.swagger.annotations.ApiModel;

/**
 * Created paulo 01/11/2018
 */
@ApiModel(description = "Situação do programa de treino.\n\n" +
        "<strong>Valores disponíveis:</strong>\n" +
        "- NOVOS - Programas novos criados\n" +
        "- RENOVADOS - Programas que foram renovados\n" +
        "- REVISADOS - Programas que foram revisados\n" +
        "- NAO_REVISADOS - Programas que não foram revisados")
public enum SituacaoProgramaEnum {

    NOVOS("NOVOS"),
    RENOVADOS("RENOVADOS"),
    REVISADOS("REVISADOS"),
    NAO_REVISADOS("NAO_REVISADOS"),
    ;

    private String codigo;

    private SituacaoProgramaEnum(String codigo) {
        this.codigo = codigo;
    }

    public String getCodigo() {
        return codigo;
    }

    public void setCodigo(String codigo) {
        this.codigo = codigo;
    }
}
