package br.com.pacto.controller.json.programa;

import br.com.pacto.bean.programa.ProgramaTreino;
import br.com.pacto.controller.json.programa.read.ProgramaTreinoJSON;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Dados de consulta de treinos gerados por Inteligência Artificial")
public class ConsultaTreinoDTO {

    @ApiModelProperty(value = "Código da empresa proprietária dos treinos", example = "1")
    private Integer codEmpresa;

    @ApiModelProperty(value = "Lista de programas de treino gerados por IA para o aluno")
    private List<ProgramaTreinoJSON> programasGerados;

    @ApiModelProperty(value = "Data e hora de geração dos programas no formato string", example = "2024-07-24 14:30:00")
    private String dataGerado;

    @ApiModelProperty(value = "Número da matrícula do aluno", example = "12345")
    private String matricula;

    @ApiModelProperty(value = "Data e hora em que o programa será aprovado automaticamente", example = "2024-07-24 16:30:00")
    private String vaiSerAprovadoAutomaticamenteEm;

    @ApiModelProperty(value = "Nome completo do aluno", example = "João Silva Santos")
    private String nomeAluno;

    @ApiModelProperty(value = "Foto do aluno em formato base64 ou URL", example = "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ...")
    private String fotoAluno;

    @ApiModelProperty(value = "Identificador único do aluno no sistema", example = "1001")
    private Long idAluno;

    @ApiModelProperty(value = "Configuração de tempo máximo em minutos para aprovação pelo professor", example = "120")
    private Integer configTempoMaxProfAprovar;

    @ApiModelProperty(value = "Configuração de tempo máximo em minutos para aprovação automática", example = "60")
    private Integer configTempoMaxAprovAut;

    public Integer getCodEmpresa() {
        return codEmpresa;
    }

    public void setCodEmpresa(Integer codEmpresa) {
        this.codEmpresa = codEmpresa;
    }

    public List<ProgramaTreinoJSON> getProgramasGerados() {
        return programasGerados;
    }

    public void setProgramasGerados(List<ProgramaTreinoJSON> programasGerados) {
        this.programasGerados = programasGerados;
    }

    public String getDataGerado() {
        return dataGerado;
    }

    public void setDataGerado(String dataGerado) {
        this.dataGerado = dataGerado;
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public String getVaiSerAprovadoAutomaticamenteEm() {
        return vaiSerAprovadoAutomaticamenteEm;
    }

    public void setVaiSerAprovadoAutomaticamenteEm(String vaiSerAprovadoAutomaticamenteEm) {
        this.vaiSerAprovadoAutomaticamenteEm = vaiSerAprovadoAutomaticamenteEm;
    }

    public String getNomeAluno() {
        return nomeAluno;
    }

    public void setNomeAluno(String nomeAluno) {
        this.nomeAluno = nomeAluno;
    }

    public String getFotoAluno() {
        return fotoAluno;
    }

    public void setFotoAluno(String fotoAluno) {
        this.fotoAluno = fotoAluno;
    }

    public Long getIdAluno() {
        return idAluno;
    }

    public void setIdAluno(Long idAluno) {
        this.idAluno = idAluno;
    }

    public Integer getConfigTempoMaxProfAprovar() {
        return configTempoMaxProfAprovar;
    }

    public void setConfigTempoMaxProfAprovar(Integer configTempoMaxProfAprovar) {
        this.configTempoMaxProfAprovar = configTempoMaxProfAprovar;
    }

    public Integer getConfigTempoMaxAprovAut() {
        return configTempoMaxAprovAut;
    }

    public void setConfigTempoMaxAprovAut(Integer configTempoMaxAprovAut) {
        this.configTempoMaxAprovAut = configTempoMaxAprovAut;
    }
}
