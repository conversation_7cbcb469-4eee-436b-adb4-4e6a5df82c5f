package br.com.pacto.controller.json.professor;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.List;

@ApiModel(description = "Detalhes completos de um professor no ranking")
public class DetalhesProfessorRankingDTO {

    @ApiModelProperty(value = "Data de cadastro do professor", example = "2023-01-15")
    private String dataCadastro;

    @ApiModelProperty(value = "Modalidade principal do professor", example = "Musculação")
    private String modalidade;

    @ApiModelProperty(value = "Carga horária semanal do professor", example = "40")
    private Integer cargaHoraria;

    @ApiModelProperty(value = "Indicadores de linha do professor")
    private List<InfoProfessorRankingDTO> linha = new ArrayList<>();

    @ApiModelProperty(value = "Indicadores relacionados a treino")
    private List<InfoProfessorRankingDTO> treino = new ArrayList<>();

    @ApiModelProperty(value = "Indicadores relacionados à agenda")
    private List<InfoProfessorRankingDTO> agenda = new ArrayList<>();

    @ApiModelProperty(value = "Indicadores relacionados aos alunos")
    private List<InfoProfessorRankingDTO> alunos = new ArrayList<>();

    @ApiModelProperty(value = "Conteúdo geral dos indicadores")
    private List<InfoProfessorRankingDTO> content = new ArrayList<>();

    public Integer getCargaHoraria() { return cargaHoraria; }

    public void setCargaHoraria(Integer cargaHoraria) { this.cargaHoraria = cargaHoraria; }

    public String getModalidade() {
        return modalidade;
    }

    public void setModalidade(String modalidade) {
        this.modalidade = modalidade;
    }

    public List<InfoProfessorRankingDTO> getLinha() {
        return linha;
    }

    public void setLinha(List<InfoProfessorRankingDTO> linha) {
        this.linha = linha;
    }

    public List<InfoProfessorRankingDTO> getTreino() {
        return treino;
    }

    public void setTreino(List<InfoProfessorRankingDTO> treino) {
        this.treino = treino;
    }

    public List<InfoProfessorRankingDTO> getAgenda() {
        return agenda;
    }

    public void setAgenda(List<InfoProfessorRankingDTO> agenda) {
        this.agenda = agenda;
    }

    public List<InfoProfessorRankingDTO> getAlunos() {
        return alunos;
    }

    public void setAlunos(List<InfoProfessorRankingDTO> alunos) {
        this.alunos = alunos;
    }

    public String getDataCadastro() {
        return dataCadastro;
    }

    public void setDataCadastro(String dataCadastro) {
        this.dataCadastro = dataCadastro;
    }

    public List<InfoProfessorRankingDTO> getContent() { return content; }

    public void setContent(List<InfoProfessorRankingDTO> content) { this.content = content; }
}
