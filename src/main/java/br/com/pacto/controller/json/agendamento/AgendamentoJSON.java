/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.agendamento;

import br.com.pacto.controller.json.base.SuperJSON;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 *
 * <AUTHOR>
 */
@ApiModel(description = "Dados de um agendamento para exibição em aplicativos móveis")
public class AgendamentoJSON extends SuperJSON {

    @ApiModelProperty(value = "Código único do agendamento", example = "123")
    private Integer id;

    @ApiModelProperty(value = "Data do agendamento em formato timestamp (milissegundos)", example = "1703520000000")
    private long dataLong;

    @ApiModelProperty(value = "Data e hora do agendamento formatada", example = "15/12/2024 14:00")
    private String data;

    @ApiModelProperty(value = "Horário de início do agendamento", example = "14:00")
    private String hora;

    @ApiModelProperty(value = "Horário de término do agendamento", example = "15:00")
    private String horaFinal;

    @ApiModelProperty(value = "Nome da atividade ou tipo de evento agendado", example = "Avaliação Física")
    private String nome;

    @ApiModelProperty(value = "Código numérico do status do agendamento", example = "1")
    private Integer statusCod;

    @ApiModelProperty(value = "Descrição do status do agendamento", example = "Confirmado")
    private String status;

    @ApiModelProperty(value = "Cor hexadecimal associada ao status do agendamento", example = "#1F7740")
    private String statusCor;

    @ApiModelProperty(value = "Cor adicional para exibição", example = "#1F7740")
    private String cor;

    @ApiModelProperty(value = "Nome do professor responsável pelo agendamento", example = "João Silva")
    private String nomeProfessor;

    @ApiModelProperty(value = "Código do professor responsável", example = "456")
    private Integer codProfessor;

    @ApiModelProperty(value = "URL da foto do professor", example = "https://academia.com/fotos/professor456.jpg")
    private String fotoProfessor;

    public AgendamentoJSON(Integer id, final String data, final String hora, final String horaFinal, final String nome, final String statusAgendamento,
            final String nomeProfessor, final String statusCor, long dataLong) {
        this.id = id;
        this.data = data;
        this.hora = hora;
        this.horaFinal = horaFinal;
        this.nome = nome;
        this.status = statusAgendamento;
        this.nomeProfessor = nomeProfessor;
        this.statusCor = statusCor;
        this.dataLong = dataLong;
    }

    public String getCor() {
        return cor;
    }

    public void setCor(String cor) {
        this.cor = cor;
    }

    public String getFotoProfessor() {
        return fotoProfessor;
    }

    public void setFotoProfessor(String fotoProfessor) {
        this.fotoProfessor = fotoProfessor;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getHora() {
        return hora;
    }

    public void setHora(String hora) {
        this.hora = hora;
    }

    public String getHoraFinal() {
        return horaFinal;
    }

    public void setHoraFinal(String horaFinal) {
        this.horaFinal = horaFinal;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getNomeProfessor() {
        return nomeProfessor;
    }

    public void setNomeProfessor(String nomeProfessor) {
        this.nomeProfessor = nomeProfessor;
    }

    public String getStatusCor() {
        return statusCor;
    }

    public void setStatusCor(String statusCor) {
        this.statusCor = statusCor;
    }

    public long getDataLong() {
        return dataLong;
    }

    public void setDataLong(long dataLong) {
        this.dataLong = dataLong;
    }

    public Integer getCodProfessor() {
        return codProfessor;
    }

    public void setCodProfessor(Integer codProfessor) {
        this.codProfessor = codProfessor;
    }

    public Integer getStatusCod() {
        return statusCod;
    }

    public void setStatusCod(Integer statusCod) {
        this.statusCod = statusCod;
    }
}
