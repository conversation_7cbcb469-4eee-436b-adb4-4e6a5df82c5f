package br.com.pacto.controller.json.termoaceite;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Dados de uma assinatura de termo de aceite")
public class TermoAceiteAssinaturaDTO {

    @ApiModelProperty(value = "Código identificador único da assinatura do termo", example = "123")
    private Integer codigo;

    @ApiModelProperty(value = "Data e hora da assinatura do termo no formato dd/MM/yyyy HH:mm:ss", example = "15/06/2024 14:30:25")
    private String data;

    @ApiModelProperty(value = "Endereço IP do dispositivo utilizado para assinar o termo", example = "*************")
    private String ip;

    @ApiModelProperty(value = "Nome completo da pessoa que assinou o termo", example = "<PERSON>")
    private String nome;

    @ApiModelProperty(value = "CPF da pessoa que assinou o termo (opcional)", example = "123.456.789-00")
    private String cpf;

    @ApiModelProperty(value = "Dados do termo de aceite que foi assinado")
    private TermoAceiteDTO termo;

    @ApiModelProperty(value = "Endereço de email da pessoa que assinou o termo (opcional)", example = "<EMAIL>")
    private String email;

    @ApiModelProperty(value = "Código de matrícula do aluno que assinou o termo", example = "456")
    private Integer codigoMatricula;

    public Integer getCodigo() {
        return codigo;
    }

    public Integer getCodigoMatricula() {
        return codigoMatricula;
    }

    public void setCodigoMatricula(Integer codigoMatricula) {
        this.codigoMatricula = codigoMatricula;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getCpf() {
        return cpf;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public TermoAceiteDTO getTermo() {
        return termo;
    }

    public void setTermo(TermoAceiteDTO termo) {
        this.termo = termo;
    }
}
