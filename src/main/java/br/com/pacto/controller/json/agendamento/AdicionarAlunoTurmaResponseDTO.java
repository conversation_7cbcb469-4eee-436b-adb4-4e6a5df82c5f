package br.com.pacto.controller.json.agendamento;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by paulo 13/08/2019
 */

@ApiModel(description = "Informações da resposta ao adicionar um aluno na turma")
public class AdicionarAlunoTurmaResponseDTO {

    @ApiModelProperty(value = "Status da operação")
    private SituacaoAdicionarAlunoTurmaDTO status;
    @ApiModelProperty(value = "Contéudo da resposta")
    private Map<String, Object> conteudo = new HashMap<>();

    public SituacaoAdicionarAlunoTurmaDTO getStatus() {
        return status;
    }

    public void setStatus(SituacaoAdicionarAlunoTurmaDTO status) {
        this.status = status;
    }

    public Map<String, Object> getConteudo() {
        return conteudo;
    }

    public void setConteudo(Map<String, Object> conteudo) {
        this.conteudo = conteudo;
    }
}
