package br.com.pacto.controller.json.usuario;

import br.com.pacto.controller.json.base.SuperJSON;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 *
 * <AUTHOR>
 */
@ApiModel(description = "Dados de um dependente do usuário")
public class UsuarioDependenteDTO extends SuperJSON {

    @ApiModelProperty(value = "Código do usuário dependente no sistema de treino", example = "67890")
    private Integer codigoUsuarioTreino;

    @ApiModelProperty(value = "Nome completo do dependente", example = "Ana Silva Santos")
    private String nome;

    @ApiModelProperty(value = "Nome de usuário do dependente", example = "<EMAIL>")
    private String username;

    @ApiModelProperty(value = "URL da foto do dependente", example = "https://cdn.academia.com/fotos/dependente123.jpg")
    private String foto;

    public Integer getCodigoUsuarioTreino() {
        return codigoUsuarioTreino;
    }

    public void setCodigoUsuarioTreino(Integer codigoUsuarioTreino) {
        this.codigoUsuarioTreino = codigoUsuarioTreino;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getFoto() {
        return foto;
    }

    public void setFoto(String foto) {
        this.foto = foto;
    }
}
