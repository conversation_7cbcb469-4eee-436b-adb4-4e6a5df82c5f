package br.com.pacto.controller.json.agendamento;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.controller.json.avaliacao.BIAvalicaoFisicaController;
import br.com.pacto.service.intf.gestao.BIAgendaService;
import br.com.pacto.swagger.respostas.ExemploRespostaVazia;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.logging.Level;
import java.util.logging.Logger;

@Controller
@RequestMapping("/psec/bi-agenda")
public class BiAgendaController {

    private BIAgendaService service;

    @Autowired
    public BiAgendaController(BIAgendaService service) {
        this.service = service;
    }


    @ResponseBody
    @RequestMapping(method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> bi(@ApiParam(value = "Código identificador da empresa que o BI da Agenda está vinculado", example = "1")
                                                  @RequestHeader("empresaId") Integer empresaId,
                                                  @ApiParam(value = "")
                                                  @RequestParam(value = "filtros", required = false) JSONObject filtros) {
        try {
            return ResponseEntityFactory.ok(service.obterBI(filtros, empresaId));
        } catch (Exception e) {
            Logger.getLogger(BIAvalicaoFisicaController.class.getName()).log(Level.SEVERE, "Erro ao consultar Dash de app", e);
            return ResponseEntityFactory.erroInterno("erro_obter_dash_app", e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/listagem-aulas", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> lista(@RequestHeader("empresaId") Integer empresaId,
                                                     PaginadorDTO paginadorDTO,
                                                     @RequestParam(value = "filters", required = false) JSONObject filtros) {
        try {
            return ResponseEntityFactory.ok(service.listagemAulas(filtros, paginadorDTO, empresaId), paginadorDTO);
        } catch (Exception e) {
            Logger.getLogger(BIAvalicaoFisicaController.class.getName()).log(Level.SEVERE, "Erro ao consultar Dash de app", e);
            return ResponseEntityFactory.erroInterno("erro_obter_dash_app", e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/listagem-bonificacao", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listaBonus(@RequestHeader("empresaId") Integer empresaId,
                                                          PaginadorDTO paginadorDTO,
                                                          @RequestParam(value = "filters", required = false) JSONObject filtros) {
        try {
            return ResponseEntityFactory.ok(service.listagemBonus(filtros, paginadorDTO, empresaId), paginadorDTO);
        } catch (Exception e) {
            Logger.getLogger(BIAvalicaoFisicaController.class.getName()).log(Level.SEVERE, "Erro ao consultar Dash de app", e);
            return ResponseEntityFactory.erroInterno("erro_obter_dash_app", e.getMessage());
        }
    }


    @ResponseBody
    @RequestMapping(value = "/listagem-bonificacao-professor", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listaBonusProfessor(@RequestHeader("empresaId") Integer empresaId,
                                                                   PaginadorDTO paginadorDTO,
                                                                   @RequestParam(value = "filters", required = false) JSONObject filtros) {
        try {
            return ResponseEntityFactory.ok(service.listagemBonusProfessor(filtros, paginadorDTO, empresaId), paginadorDTO);
        } catch (Exception e) {
            Logger.getLogger(BIAvalicaoFisicaController.class.getName()).log(Level.SEVERE, "Erro ao consultar Dash de app", e);
            return ResponseEntityFactory.erroInterno("erro_obter_dash_app", e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/listagem-modalidades", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listaModalidade(@RequestHeader("empresaId") Integer empresaId,
                                                               PaginadorDTO paginadorDTO,
                                                               @RequestParam(value = "filters", required = false) JSONObject filtros) {
        try {
            return ResponseEntityFactory.ok(service.listagemModalidades(filtros, paginadorDTO, empresaId), paginadorDTO);
        } catch (Exception e) {
            Logger.getLogger(BiAgendaController.class.getName()).log(Level.SEVERE, "Erro ao buscar lista de modalidades", e);
            return ResponseEntityFactory.erroInterno("erro_obter_modalidades_dash", e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/listagem-professores", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listaProfessores(@RequestHeader("empresaId") Integer empresaId,
                                                                PaginadorDTO paginadorDTO,
                                                                @RequestParam(value = "filters", required = false) JSONObject filtros) {
        try {
            return ResponseEntityFactory.ok(service.listagemProfessores(filtros, paginadorDTO, empresaId), paginadorDTO);
        } catch (Exception e) {
            Logger.getLogger(BiAgendaController.class.getName()).log(Level.SEVERE, "Erro ao buscar lista de professores", e);
            return ResponseEntityFactory.erroInterno("erro_obter_professores_dash", e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/listagem-frequencia-alunos", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listaFrequenciaAlunos(@RequestHeader("empresaId") Integer empresaId,
                                                                     PaginadorDTO paginadorDTO,
                                                                     @RequestParam(value = "filters", required = false) JSONObject filtros) {
        try {
            return ResponseEntityFactory.ok(service.listagemFrequenciaAlunos(filtros, paginadorDTO, empresaId), paginadorDTO);
        } catch (Exception e) {
            Logger.getLogger(BiAgendaController.class.getName()).log(Level.SEVERE, "Erro ao buscar lista de frequencia dos alunos", e);
            return ResponseEntityFactory.erroInterno("erro_obter_frequencia_alunos_dash", e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/listagem-presenca", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listaPresenca(@RequestHeader("empresaId") Integer empresaId,
                                                             PaginadorDTO paginadorDTO,
                                                             @RequestParam(value = "filters", required = false) JSONObject filtros) {
        try {
            return ResponseEntityFactory.ok(service.listagemPresenca(filtros, paginadorDTO, empresaId), paginadorDTO);
        } catch (Exception e) {
            Logger.getLogger(BiAgendaController.class.getName()).log(Level.SEVERE, "Erro ao buscar lista de frequencia dos alunos", e);
            return ResponseEntityFactory.erroInterno("erro_obter_frequencia_alunos_dash", e.getMessage());
        }
    }

}
