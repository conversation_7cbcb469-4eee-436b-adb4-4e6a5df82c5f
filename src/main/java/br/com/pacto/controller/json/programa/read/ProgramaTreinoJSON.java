/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.programa.read;

import br.com.pacto.controller.json.programa.read.OrigemProgramaTreinoEnum;
import br.com.pacto.controller.json.atividade.read.AtividadeJSON;
import br.com.pacto.controller.json.ficha.read.FichaJSON;
import br.com.pacto.controller.json.base.SuperJSON;
import br.com.pacto.controller.json.serialization.JsonDateSerializerYYYYMMDD;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.util.impl.JSFUtilities;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
@ApiModel(description = "Dados completos do programa de treino do aluno")
public class ProgramaTreinoJSON extends SuperJSON {

    @ApiModelProperty(value = "Código único identificador do programa de treino", example = "1001")
    private Integer cod;

    @ApiModelProperty(value = "Nome descritivo do programa de treino", example = "Programa de Musculação Iniciante")
    private String nome;

    @ApiModelProperty(value = "Objetivos do programa de treino separados por quebra de linha", example = "Ganho de massa muscular\nMelhoria do condicionamento físico")
    private String objetivos;

    @ApiModelProperty(value = "Restrições e limitações do programa separadas por quebra de linha", example = "Não realizar exercícios de impacto\nEvitar sobrecarga excessiva")
    private String restricoes;

    @ApiModelProperty(value = "Nome do método de treinamento utilizado", example = "Método Tradicional")
    private String nomeMetodo;

    @ApiModelProperty(value = "Descrição detalhada do método de treinamento", example = "Treinamento baseado em séries e repetições tradicionais")
    private String descricaoMetodo;

    @ApiModelProperty(value = "Versão atual do programa de treino", example = "1.0")
    private String versao;

    @ApiModelProperty(value = "Data de início do programa de treino", example = "2024-01-15T00:00:00.000Z")
    private Date dataInicio;

    @ApiModelProperty(value = "Data prevista para término do programa de treino", example = "2024-04-15T00:00:00.000Z")
    private Date dataTerminoPrevisto;

    @ApiModelProperty(value = "Nome do professor responsável pela carteira do aluno", example = "Prof. Carlos Silva")
    private String professorCarteira;

    @ApiModelProperty(value = "Nome do professor que montou o programa de treino", example = "Prof. Ana Santos")
    private String professorMontou;

    @ApiModelProperty(value = "Indica se deve usar a nova interface de montagem de fichas", example = "true")
    private Boolean usarNovaMontagemFicha;

    @ApiModelProperty(value = "Lista de fichas que compõem o programa de treino")
    private List<FichaJSON> fichas = new ArrayList<FichaJSON>();

    @ApiModelProperty(value = "Lista de todas as atividades/exercícios do programa")
    private List<AtividadeJSON> atividades = new ArrayList<AtividadeJSON>();

    @ApiModelProperty(value = "Nome de usuário do aluno proprietário do programa", example = "joao.silva")
    private String userName;

    @ApiModelProperty(value = "CREF (Conselho Regional de Educação Física) do professor responsável", example = "CREF 123456-G/SP")
    private String cref;

    @ApiModelProperty(value = "CREF do professor que montou o programa", example = "CREF 789012-G/SP")
    private String crefProfessorMontou;

    @ApiModelProperty(value = "Data da próxima revisão programada do treino", example = "2024-02-15T00:00:00.000Z")
    private Date dataProximaRevisao;

    @ApiModelProperty(value = "Indica se o programa já foi revisado pelo professor", example = "Sim")
    private String revisado;

    @ApiModelProperty(value = "Mensagem de aviso específica para o aluno sobre o programa", example = "Lembre-se de manter a hidratação durante os exercícios")
    private String mensagemAviso;

    @ApiModelProperty(value = "Indica se o programa está em revisão pelo professor", example = "false")
    private Boolean emRevisaoProfessor;

    @ApiModelProperty(value = "Indica se o programa foi gerado por Inteligência Artificial", example = "false")
    private Boolean isGeradoPorIA;

    @ApiModelProperty(value = "Origem do programa de treino")
    private OrigemProgramaTreinoEnum origem;

    public ProgramaTreinoJSON() {
        this.cod = 0;
        this.nome = "";
        this.objetivos = "";
        this.restricoes = "";
        this.nomeMetodo = "";
        this.descricaoMetodo = "";
        this.versao = "";
        this.dataInicio = null;
        this.dataTerminoPrevisto = null;
        this.professorCarteira = "";
        this.professorMontou = "";
        this.usarNovaMontagemFicha = null;
        this.userName = "";
        this.cref = "";
        this.crefProfessorMontou = "";
        this.mensagemAviso = "";
    }
    
    public Integer getCod() {
        return cod;
    }

    public void setCod(Integer cod) {
        this.cod = cod;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getObjetivos() {
        return objetivos;
    }

    public void setObjetivos(String objetivos) {
        this.objetivos = objetivos;
    }

    public String getNomeMetodo() {
        return nomeMetodo;
    }

    public void setNomeMetodo(String nomeMetodo) {
        this.nomeMetodo = nomeMetodo;
    }

    public String getDescricaoMetodo() {
        return descricaoMetodo;
    }

    public void setDescricaoMetodo(String descricaoMetodo) {
        this.descricaoMetodo = descricaoMetodo;
    }

    public String getVersao() {
        return versao;
    }

    public void setVersao(String versao) {
        this.versao = versao;
    }

    public List<FichaJSON> getFichas() {
        return fichas;
    }

    public void setFichas(List<FichaJSON> fichas) {
        this.fichas = fichas;
    }

    public List<AtividadeJSON> getAtividades() {
        return atividades;
    }

    public void setAtividades(List<AtividadeJSON> atividades) {
        this.atividades = atividades;
    }

    @JsonSerialize(using = JsonDateSerializerYYYYMMDD.class)
    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    @JsonSerialize(using = JsonDateSerializerYYYYMMDD.class)
    public Date getDataTerminoPrevisto() {
        return dataTerminoPrevisto;
    }

    public void setDataTerminoPrevisto(Date dataTerminoPrevisto) {
        this.dataTerminoPrevisto = dataTerminoPrevisto;
    }

    public String getProfessorCarteira() {
        return professorCarteira;
    }

    public Boolean getEmRevisaoProfessor() {
        return emRevisaoProfessor;
    }

    public void setEmRevisaoProfessor(Boolean emRevisaoProfessor) {
        this.emRevisaoProfessor = emRevisaoProfessor;
    }

    public Boolean getGeradoPorIA() {
        return isGeradoPorIA;
    }

    public void setGeradoPorIA(Boolean geradoPorIA) {
        isGeradoPorIA = geradoPorIA;
    }

    public void setProfessorCarteira(String professorCarteira) {
        this.professorCarteira = professorCarteira;
    }

    public String getProfessorMontou() {
        return professorMontou;
    }

    public void setProfessorMontou(String professorMontou) {
        this.professorMontou = professorMontou;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public void setCref(String cref) {
        this.cref = cref;
    }

    public String getCref() {
        return cref;
    }

    public String getRestricoes() {
        return restricoes;
    }

    public void setRestricoes(String restricoes) {
        this.restricoes = restricoes;
    }

    @JsonSerialize(using = JsonDateSerializerYYYYMMDD.class)
    public Date getDataProximaRevisao() {
        return dataProximaRevisao;
    }

    public void setDataProximaRevisao(Date dataProximaRevisao) {
        this.dataProximaRevisao = dataProximaRevisao;
    }

    public String getRevisado() {
        return revisado;
    }

    public void setRevisado(String revisado) {
        this.revisado = revisado;
    }

    public Boolean getUsarNovaMontagemFicha() {
        return usarNovaMontagemFicha;
    }

    public void setUsarNovaMontagemFicha(Boolean usarNovaMontagemFicha) {
        this.usarNovaMontagemFicha = usarNovaMontagemFicha;
    }

    public String getMensagemAviso() {
        return mensagemAviso;
    }

    public void setMensagemAviso(String mensagemAviso) {
        this.mensagemAviso = mensagemAviso;
    }

    public String getCrefProfessorMontou() {
        return crefProfessorMontou;
    }

    public void setCrefProfessorMontou(String crefProfessorMontou) {
        this.crefProfessorMontou = crefProfessorMontou;
    }

    public OrigemProgramaTreinoEnum getOrigem() {
        return origem;
    }

    public void setOrigem(OrigemProgramaTreinoEnum origem) {
        this.origem = origem;
    }
}
