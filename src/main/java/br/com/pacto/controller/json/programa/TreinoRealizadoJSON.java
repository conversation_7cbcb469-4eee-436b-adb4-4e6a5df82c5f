/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.programa;

import br.com.pacto.controller.json.base.SuperJSON;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 *
 * <AUTHOR>
 */
@ApiModel(description = "Dados completos do treino realizado pelo aluno")
public class TreinoRealizadoJSON extends SuperJSON {

    @ApiModelProperty(value = "Nome de usuário do aluno que realizou o treino", example = "maria.santos")
    private String username;

    @ApiModelProperty(value = "Identificador único do programa de treino", example = "1001")
    private String idPrograma;

    @ApiModelProperty(value = "Identificador único da ficha de treino executada", example = "2001")
    private Integer idFicha;

    @ApiModelProperty(value = "Dia de execução do treino (número inteiro)", example = "15")
    private Integer dia;

    @ApiModelProperty(value = "Nota de avaliação do treino atribuída pelo aluno (escala de 1 a 10)", example = "8")
    private String nota;

    @ApiModelProperty(value = "Tempo total de execução do treino em minutos", example = "45")
    private Integer tempo;

    @ApiModelProperty(value = "Comentários adicionais sobre o treino realizado", example = "Treino muito bom, consegui completar todas as séries")
    private String comentario;

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getIdPrograma() {
        return idPrograma;
    }

    public void setIdPrograma(String idPrograma) {
        this.idPrograma = idPrograma;
    }

    public Integer getIdFicha() {
        return idFicha;
    }

    public void setIdFicha(Integer idFicha) {
        this.idFicha = idFicha;
    }

    public Integer getDia() {
        return dia;
    }

    public void setDia(Integer dia) {
        this.dia = dia;
    }

    public String getNota() {
        return nota;
    }

    public void setNota(String nota) {
        this.nota = nota;
    }

    public Integer getTempo() {
        return tempo;
    }

    public void setTempo(Integer tempo) {
        this.tempo = tempo;
    }

    public String getComentario() {
        return comentario;
    }

    public void setComentario(String comentario) {
        this.comentario = comentario;
    }
}
