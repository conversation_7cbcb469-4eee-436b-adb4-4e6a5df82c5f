package br.com.pacto.controller.json.avaliacao;

import io.swagger.annotations.ApiModelProperty;

public class SugestaoHorarioJSON {

    @ApiModelProperty(value = "Código do tipo de evento de avaliação física disponível para agendamento",
                     example = "1")
    public Integer tipoEvento;

    @ApiModelProperty(value = "Horário sugerido para agendamento da avaliação física no formato HH:mm - HH:mm",
                     example = "09:00 - 10:00")
    public String horario;

    public SugestaoHorarioJSON(Integer tipoEvento, String horario) {
        this.tipoEvento = tipoEvento;
        this.horario = horario;
    }

    public Integer getTipoEvento() {
        return tipoEvento;
    }

    public void setTipoEvento(Integer tipoEvento) {
        this.tipoEvento = tipoEvento;
    }

    public String getHorario() {
        return horario;
    }

    public void setHorario(String horario) {
        this.horario = horario;
    }

}
