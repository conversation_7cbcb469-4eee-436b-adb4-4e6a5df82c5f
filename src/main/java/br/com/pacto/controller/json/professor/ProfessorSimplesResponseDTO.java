package br.com.pacto.controller.json.professor;

import br.com.pacto.bean.professor.ProfessorSintetico;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Created by paulo 31/10/2018
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Dados básicos de um professor")
public class ProfessorSimplesResponseDTO {

    @ApiModelProperty(value = "Identificador único do professor", example = "123")
    private Integer id;

    @ApiModelProperty(value = "Nome completo do professor", example = "<PERSON>")
    private String nome;

    public ProfessorSimplesResponseDTO() {
    }

    public ProfessorSimplesResponseDTO(ProfessorSintetico professorSintetico) {
        this.id = professorSintetico.getCodigo();
        this.nome = professorSintetico.getNome();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }
}
