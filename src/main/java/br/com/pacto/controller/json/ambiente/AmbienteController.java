package br.com.pacto.controller.json.ambiente;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.ambiente.AmbienteService;
import br.com.pacto.swagger.respostas.ambiente.ExemploRespostaAmbienteResponseTO;
import br.com.pacto.swagger.respostas.ambiente.ExemploRespostaListAmbienteResponseTO;
import br.com.pacto.swagger.respostas.ambiente.ExemploRespostaListAmbienteResponseTOPaginacao;
import br.com.pacto.swagger.respostas.coletor.ExemploRespostaListColetorResponseTO;
import br.com.pacto.swagger.respostas.niveis.ExemploRespostaListNivelTurmaResponseTO;
import io.swagger.annotations.*;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by Joao Moita on 26/09/2018.
 */
@Controller
@RequestMapping("/psec/ambientes")
public class AmbienteController {

    private AmbienteService ambienteService;

    @Autowired
    public AmbienteController(AmbienteService ambienteService) {
        Assert.notNull(ambienteService, "O serviço de ambiente não foi injetado corretamente");
        this.ambienteService = ambienteService;
    }

    @ApiOperation(
            value = "Cadastrar ambiente da academia",
            notes = "Cadastra um novo ambiente da academia. Os ambientes podem ser descritos de acordo com a academia e as atividades realizadas através dele. Por exemplo, uma academia que possui piscina pode cadastrar dois ambientes: PISCINA e ACADEMIA.",
            tags = "Ambiente"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaAmbienteResponseTO.class)
    })
    @ResponseBody
//    @Permissao(recursos = RecursoEnum.AMBIENTES)
    @RequestMapping(value = "", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cadastroAmbiente(
            @ApiParam(value = "Dados do ambiente que será cadastrado", required = true)
            @RequestBody AmbienteTO ambienteTO) {
        try {
            return ResponseEntityFactory.ok(
                    ambienteService.cadastroAmbiente(ambienteTO)
            );
        } catch (ServiceException e) {
            Logger.getLogger(AmbienteController.class.getName()).log(Level.SEVERE, "Erro ao tentar cadastrar aluno", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ApiOperation(
            value = "Consultar ambientes da academia",
            notes = "Consulta os ambientes da academia. Os ambientes podem ser descritos de acordo com a academia e as atividades realizadas através dele. Por exemplo, uma academia que possui piscina pode cadastrar dois ambientes: PISCINA e ACADEMIA.",
            tags = "Ambiente"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.<br/> Ordena por um atributo contido no objeto da resposta." +
                    "<strong>Ordens disponíveis</strong>" +
                    "<ul>" +
                    "<li>ASC - Ordena pelo atributo escolhido de forma ascendente</li>" +
                    "<li>DESC - Ordena pelo atributo escolhido de forma descendente</li>" +
                    "</ul>" +
                    "Deve ser informado como atributo,ordem", defaultValue = "nome,asc", paramType = "query", dataType = "string"),
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaListAmbienteResponseTOPaginacao.class)
    })
    @ResponseBody
    @RequestMapping(value = "", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarAmbiente(
            @ApiParam(value = "Filtros de busca, deve ser informado como um JSON." +
                    "<br/><strong>É necessário fazer o ENCONDE na URL para realizar a requisição com os filtros.</strong></br>" +
                    "<br/><strong>Filtros disponíveis:</strong>" +
                    "<ul>" +
                    "<li><strong>quicksearchValue:</strong>Filtra pelo nome do ambiente</li>" +
                    "</ul>", defaultValue = "{\"quicksearchValue\":\"spinning\"}")
            @RequestParam(value = "filters", required = false) JSONObject filtros,
            @ApiParam(value = "Código identificador da empresa", defaultValue = "1")
            @RequestHeader(value = "empresaId", required = true) Integer empresaId,
            @ApiIgnore PaginadorDTO paginadorDTO) throws JSONException {
        try {
            FiltroAmbienteJSON filtroAmbienteJSON = new FiltroAmbienteJSON(filtros);

            return ResponseEntityFactory.ok(ambienteService.listaAmbientes(filtroAmbienteJSON, paginadorDTO, empresaId), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(AmbienteController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar as atividades", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Alterar informações de um ambiente da academia",
            notes = "Altera as informaçoes de um ambiente da academia. Os ambientes podem ser descritos de acordo com a academia e as atividades realizadas através dele. Por exemplo, uma academia que possui piscina pode cadastrar dois ambientes: PISCINA e ACADEMIA.",
            tags = "Ambiente"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaAmbienteResponseTO.class)
    })
    @ResponseBody
//    @Permissao(recursos = RecursoEnum.AMBIENTES)
    @RequestMapping(value = "{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alterarAmbiente(
            @ApiParam(value = "Código único identificador do ambiente que será alterado", defaultValue = "2", example = "2", required = true)
            @PathVariable("id") Integer id,
            @ApiParam(value = "Dados do ambiente que será cadastrado", required = true)
            @RequestBody AmbienteTO ambienteTO) {
        try {
            return ResponseEntityFactory.ok(
                    ambienteService.alterarAmbiente(id, ambienteTO)
            );
        } catch (ServiceException e) {
            Logger.getLogger(AmbienteController.class.getName()).log(Level.SEVERE, "Erro ao tentar cadastrar aluno", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ApiOperation(
            value = "Deletar de um ambiente da academia",
            notes = "Exclui as informaçoes de um ambiente da academia. Os ambientes podem ser descritos de acordo com a academia e as atividades realizadas através dele. Por exemplo, uma academia que possui piscina pode cadastrar dois ambientes: PISCINA e ACADEMIA.",
            tags = "Ambiente"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)")
    })
    @ResponseBody
//    @Permissao(recursos = RecursoEnum.AMBIENTES)
    @RequestMapping(value = "{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> removerAmbiente(
            @ApiParam(value = "Código único identificador do ambiente que será deletado", defaultValue = "2", example = "2", required = true)
            @PathVariable("id") Integer id) {
        try {

            ambienteService.removerAmbiente(id);
            return ResponseEntityFactory.ok(
            );
        } catch (ServiceException e) {
            Logger.getLogger(AmbienteController.class.getName()).log(Level.SEVERE, "Erro ao tentar cadastrar aluno", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar todos os ambientes da academia",
            notes = "Consulta as informações de todos os ambientes da academia. Os ambientes podem ser descritos de acordo com a academia e as atividades realizadas através dele. Por exemplo, uma academia que possui piscina pode cadastrar dois ambientes: PISCINA e ACADEMIA.",
            tags = "Ambiente"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaListAmbienteResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.AMBIENTES)
    @RequestMapping(value = "/all", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarTodosAmbientes(
            @ApiParam(value = "Código identificador da empresa que será consultado os ambiente", defaultValue = "1")
            @RequestHeader(value = "empresaId", required = true) Integer empresaId,
            @ApiParam(value = "Informa se o ambiente tem que ser validado no aplicativo treino", defaultValue = "false", allowableValues = "true,false")
            @RequestParam(value = "validarNoTreino", required = false) Boolean validarNoTreino
    ) {
        try {
            return ResponseEntityFactory.ok(ambienteService.obterTodos(empresaId, validarNoTreino));
        } catch (ServiceException e) {
            Logger.getLogger(AmbienteController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar ambientes", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar todos os ambientes ativos da academia",
            notes = "Consulta as informações de todos os ambientes ativos da academia. Os ambientes podem ser descritos de acordo com a academia e as atividades realizadas através dele. Por exemplo, uma academia que possui piscina pode cadastrar dois ambientes: PISCINA e ACADEMIA.",
            tags = "Ambiente"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaAmbienteResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.AMBIENTES)
    @RequestMapping(value = "/all/ativo", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarTodosAmbientesAtivos(
            @ApiParam(value = "Filtros de busca, deve ser informado como um JSON." +
                    "<br/><strong>É necessário fazer o ENCONDE na URL para realizar a requisição com os filtros.</strong></br>" +
                    "<br/><strong>Filtros disponíveis:</strong>" +
                    "<ul>" +
                    "<li><strong>quicksearchValue:</strong>Filtra pelo nome do ambiente</li>" +
                    "</ul>", defaultValue = "{\"quicksearchValue\":\"spinning\"}")
            @RequestParam(value = "filters", required = false) JSONObject filters) {
        try {
            FiltroAmbienteJSON filtros = new FiltroAmbienteJSON(filters);
            return ResponseEntityFactory.ok(ambienteService.obterTodosAtivos(filtros));
        } catch (ServiceException e) {
            Logger.getLogger(AmbienteController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar ambientes", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception ex) {
            Logger.getLogger(AmbienteController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar ambientes", ex);
            return ResponseEntityFactory.erroInterno("", ex.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar todos os ambientes ativos da academia",
            notes = "Consulta as informações de todos os ambientes ativos da academia. Os ambientes podem ser descritos de acordo com a academia e as atividades realizadas através dele. Por exemplo, uma academia que possui piscina pode cadastrar dois ambientes: PISCINA e ACADEMIA.",
            tags = "Ambiente"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaListAmbienteResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.AMBIENTES)
    @RequestMapping(value = "tiposAmbiente/all", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarTodosTiposAmbientes() {
        try {
            return ResponseEntityFactory.ok(ambienteService.obterTodosTiposAmbiente());
        } catch (ServiceException e) {
            Logger.getLogger(AmbienteController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar ambientes", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception ex) {
            Logger.getLogger(AmbienteController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar tipos ambientes", ex);
            return ResponseEntityFactory.erroInterno("", ex.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar coletores de acesso dos ambientes da academia",
            notes = "Consulta as informações de todos os coletores de acesso da academia. Os ambientes podem ser descritos de acordo com a academia e as atividades realizadas através dele. Por exemplo, uma academia que possui piscina pode cadastrar dois ambientes: PISCINA e ACADEMIA.",
            tags = "Ambiente"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaListColetorResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.AMBIENTES)
    @RequestMapping(value = "coletores/all", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarTodosColetoresAmbiente() {
        try {
            return ResponseEntityFactory.ok(ambienteService.obterColetoresAmbiente());
        } catch (ServiceException e) {
            Logger.getLogger(AmbienteController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar coletores", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception ex) {
            Logger.getLogger(AmbienteController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar coletores", ex);
            return ResponseEntityFactory.erroInterno("", ex.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar níveis de turmas por ambiente",
            notes = "Consulta os níveis de turma permitidos por ambiente..",
            tags = "Níveis"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaListNivelTurmaResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.AMBIENTES)
    @RequestMapping(value = "niveisTurma/all", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarTodosNiveisTurmaAmbiente() {
        try {
            return ResponseEntityFactory.ok(ambienteService.obterNiveisTurmaAmbiente());
        } catch (ServiceException e) {
            Logger.getLogger(AmbienteController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar niveis turma", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception ex) {
            Logger.getLogger(AmbienteController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar niveis turma", ex);
            return ResponseEntityFactory.erroInterno("", ex.getMessage());
        }
    }
}
