package br.com.pacto.controller.json.termoaceite;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Dados de um termo de aceite")
public class TermoAceiteDTO {

    @ApiModelProperty(value = "Código identificador único do termo de aceite", example = "1")
    private Integer codigo;

    @ApiModelProperty(value = "Descrição completa do conteúdo do termo de aceite", example = "Termo de aceite para uso das instalações da academia, incluindo normas de segurança e responsabilidades do usuário")
    private String descricao;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}
