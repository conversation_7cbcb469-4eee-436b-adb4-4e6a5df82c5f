package br.com.pacto.controller.json.avaliacao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * Created by alcides on 21/09/2017.
 */
@ApiModel(description = "Dados da avaliação postural do aluno")
public class AvaliacaoPosturalJSON {

    @ApiModelProperty(value = "Código identificador da avaliação postural", example = "501")
    private Integer codigo;

    @ApiModelProperty(value = "Observações gerais sobre a postura do aluno", example = "Leve desvio de coluna, recomenda-se fortalecimento do core")
    private String observacao;

    @ApiModelProperty(value = "Análise postural do lado direito", example = "Ombro direito ligeiramente elevado")
    private String direito;

    @ApiModelProperty(value = "Análise postural do lado esquerdo", example = "Postura normal do lado esquerdo")
    private String esquerdo;

    @ApiModelProperty(value = "Análise postural vista pelas costas", example = "Leve escoliose em S")
    private String costas;

    @ApiModelProperty(value = "Análise postural vista de frente", example = "Cabeça ligeiramente inclinada para direita")
    private String frente;
    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public String getDireito() {
        return direito;
    }

    public void setDireito(String direito) {
        this.direito = direito;
    }

    public String getEsquerdo() {
        return esquerdo;
    }

    public void setEsquerdo(String esquerdo) {
        this.esquerdo = esquerdo;
    }

    public String getCostas() {
        return costas;
    }

    public void setCostas(String costas) {
        this.costas = costas;
    }

    public String getFrente() {
        return frente;
    }

    public void setFrente(String frente) {
        this.frente = frente;
    }
}
