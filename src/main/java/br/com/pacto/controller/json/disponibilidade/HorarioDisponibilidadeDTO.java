package br.com.pacto.controller.json.disponibilidade;

import br.com.pacto.bean.programa.ProfessorResponseTO;
import br.com.pacto.controller.json.turma.AmbienteDTO;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@ApiModel(description = "Informações dos horários disponíveis para agendamento")
public class HorarioDisponibilidadeDTO {

    @ApiModelProperty(value = "Código único identificador do horário disponível", example = "454")
    private Integer codigo;
    @ApiModelProperty(value = "Professor responsável pelo horário disponível")
    private Professor<PERSON>esponse<PERSON> professor;
    @ApiModelProperty(value = "Ambiente que o agendamento pode ser realizado")
    private AmbienteDTO ambiente;
    @ApiModelProperty(value = "Hora inicial do horário disponível", example = "19:00")
    private String horaInicial;
    @ApiModelProperty(value = "Hora final do horário disponível", example = "20:00")
    private String horaFinal;
    @ApiModelProperty(value = "Dia da semana que está disponível", example = "Quarta-feira")
    private String diaSemana;
    @ApiModelProperty(value = "Indica se permite realizar o agendamento através do aplicativo do Treino", example = "true")
    private Boolean permieAgendarAppTreino;
    @ApiModelProperty(value = "Indica se o horário está disponível apenas para Alunos Carteira", example = "false")
    private Boolean apenasAlunosCarteira;
    @ApiModelProperty(value = "Indica se o horário está ativo ou não", example = "false")
    private Boolean ativo;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public ProfessorResponseTO getProfessor() {
        return professor;
    }

    public void setProfessor(ProfessorResponseTO professor) {
        this.professor = professor;
    }

    public AmbienteDTO getAmbiente() {
        return ambiente;
    }

    public void setAmbiente(AmbienteDTO ambiente) {
        this.ambiente = ambiente;
    }

    public String getHoraInicial() {
        return horaInicial;
    }

    public void setHoraInicial(String horaInicial) {
        this.horaInicial = horaInicial;
    }

    public String getHoraFinal() {
        return horaFinal;
    }

    public void setHoraFinal(String horaFinal) {
        this.horaFinal = horaFinal;
    }

    public String getDiaSemana() {
        return diaSemana;
    }

    public void setDiaSemana(String diaSemana) {
        this.diaSemana = diaSemana;
    }

    public Boolean getPermieAgendarAppTreino() {
        return permieAgendarAppTreino;
    }

    public void setPermieAgendarAppTreino(Boolean permieAgendarAppTreino) {
        this.permieAgendarAppTreino = permieAgendarAppTreino;
    }

    public Boolean getApenasAlunosCarteira() {
        return apenasAlunosCarteira;
    }

    public void setApenasAlunosCarteira(Boolean apenasAlunosCarteira) {
        this.apenasAlunosCarteira = apenasAlunosCarteira;
    }

    public Boolean getAtivo() {
        return ativo;
    }

    public void setAtivo(Boolean ativo) {
        this.ativo = ativo;
    }
}
