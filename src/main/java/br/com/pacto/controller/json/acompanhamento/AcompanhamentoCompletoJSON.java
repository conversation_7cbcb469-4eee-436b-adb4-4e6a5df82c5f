/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.acompanhamento;

import br.com.pacto.controller.json.base.SuperJSON;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
@ApiModel(description = "Dados completos do acompanhamento de treino do aluno")
public class AcompanhamentoCompletoJSON extends SuperJSON {

    @ApiModelProperty(value = "Data de referência do acompanhamento no formato dd/MM/yyyy", example = "20/06/2024")
    private String data;

    @ApiModelProperty(value = "Dados estatísticos do acompanhamento do aluno")
    private AcompanhamentoDadosJSON dados;

    @ApiModelProperty(value = "Lista de dados de desempenho por ficha ou treino realizado")
    private List<DesempenhoJSON> desempenho = new ArrayList<DesempenhoJSON>();

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public AcompanhamentoDadosJSON getDados() {
        return dados;
    }

    public void setDados(AcompanhamentoDadosJSON dados) {
        this.dados = dados;
    }

    public List<DesempenhoJSON> getDesempenho() {
        return desempenho;
    }

    public void setDesempenho(List<DesempenhoJSON> desempenho) {
        this.desempenho = desempenho;
    }
}
