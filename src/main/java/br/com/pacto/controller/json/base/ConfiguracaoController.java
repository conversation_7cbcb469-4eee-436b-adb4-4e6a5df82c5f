package br.com.pacto.controller.json.base;


import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ExecuteRequestHttpService;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.configuracoes.ConfiguracaoDobras;
import br.com.pacto.controller.json.SuperController;
import br.com.pacto.controller.json.gogood.ConfigGoGoodDTO;
import br.com.pacto.controller.json.gympass.ConfigGymPassDTO;
import br.com.pacto.controller.json.log.EntidadeLogEnum;
import br.com.pacto.controller.json.mgb.ConfigMgb;
import br.com.pacto.controller.json.mqv.MqvDTO;
import br.com.pacto.controller.json.selfloops.SelfloopsDTO;
import br.com.pacto.controller.json.totalpass.TotalPassDTO;
import br.com.pacto.dao.intf.log.LogDao;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.configuracoes.ConfiguracaoSistemaService;
import br.com.pacto.service.intf.gogood.ConfigGoGoodService;
import br.com.pacto.service.intf.gympass.ConfigGymPassService;
import br.com.pacto.service.intf.log.LogService;
import br.com.pacto.service.intf.selfloops.SelfloopsConfiguracoesService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.swagger.respostas.configuracao.*;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.bean.GenericoTO;
import br.com.pacto.util.impl.UtilReflection;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import io.swagger.annotations.*;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import servicos.integracao.zw.IntegracaoCadastrosWSConsumer;
import springfox.documentation.annotations.ApiIgnore;

import javax.faces.model.SelectItem;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

import static br.com.pacto.objeto.Uteis.incluirLog;
import static java.util.Arrays.asList;


@Controller
@RequestMapping("/psec/configuracoes")
public class ConfiguracaoController extends SuperController {

    private ConfiguracaoSistemaService configuracaoSistemaService;

    private transient IntegracaoCadastrosWSConsumer integracaoWS = (IntegracaoCadastrosWSConsumer) UtilContext.getBean(IntegracaoCadastrosWSConsumer.class);

    @Autowired
    private SessaoService sessaoService;

    @Autowired
    private UsuarioService usuarioService;

    @Autowired
    private HttpServletRequest httpServletRequest;

    @Autowired
    private LogDao logDao;

    @Autowired
    private LogService logService;

    @Autowired
    private SelfloopsConfiguracoesService selfloopsConfiguracoesService;

    @Autowired
    public ConfiguracaoController(ConfiguracaoSistemaService configuracaoSistemaService) {
        Assert.notNull(configuracaoSistemaService, "O serviço de configuração não foi injetado corretamente");
        this.configuracaoSistemaService = configuracaoSistemaService;
    }

    private ResponseEntity<EnvelopeRespostaDTO> send(Class classe, Object obj) {
        try {
            Object objantes = UtilReflection.copy(obj);
            Object objAntesAlteracao = configuracaoSistemaService.configsDTO(classe, objantes);
            configuracaoSistemaService.gravarCfgsDTO(null, classe, obj);
            try {
                configuracaoSistemaService.purgeCache(sessaoService.getUsuarioAtual().getChave());
                configuracaoSistemaService.notificarOuvintes(sessaoService.getUsuarioAtual().getChave(), httpServletRequest);
            } catch (Exception e) {
                e.printStackTrace();
            }
            incluirLog(sessaoService.getUsuarioAtual().getChave(), classe.getSimpleName().toUpperCase(), null, configuracaoSistemaService.getDescricaoParaLog(objAntesAlteracao, obj),
                    configuracaoSistemaService.getDescricaoParaLog(obj, objAntesAlteracao),
                    "ALTERAÇÃO",
                    "ALTERAÇÃO DE CONFIGURAÇÃO "+classe.getSimpleName().toUpperCase(),
                    EntidadeLogEnum.CONFIGURACAOSISTEMA, "Configuração Sistema", sessaoService, logDao, null, null);
            return ResponseEntityFactory.ok("Salvo com sucesso!");
        } catch (ServiceException e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao alterar a configuração", e);
            switch (e.getCodigoError()) {
                case 400:
                    return ResponseEntityFactory.erroConhecido(e.getChaveExcecao(), e.getMessage());
                default:
                    return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            }
        }
    }

    @ApiOperation(
            value = "Alterar configurações gerais do sistema relacionadas ao treino",
            notes = "Altera as configurações gerais do sistema, incluindo validações de agenda, duração do aluno na academia e outras configurações básicas.",
            tags = "Configurações Treino"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Configurações Treino", response = ExemploRespostaConfiguracaoSucesso.class)
    })
    @ResponseBody
    @RequestMapping(value = "/gerais", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cfgGerais(@ApiParam(value = "Dados das configurações gerais a serem alteradas")
                                                         @RequestBody ConfiguracoesGeraisDTO cfgDTO) {
        return send(cfgDTO.getClass(), cfgDTO);
    }

    @ApiOperation(
            value = "Consultar log de configurações gerais relacionadas ao treino",
            notes = "Consulta o histórico de alterações das configurações gerais do sistema, permitindo rastrear quem alterou o que e quando.",
            tags = "Logs"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Número da página a ser retornada", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Número de itens por página", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Campo para ordenação dos resultados, seguido de 'ASC' ou 'DESC' (ex: 'dataAlteracao,DESC'). " +
                    "<br/><strong>Campos disponíveis para ordenação:</strong>" +
                    "<ul>" +
                    "<li><strong>dataAlteracao</strong> - Data da alteração</li>" +
                    "<li><strong>usuario</strong> - Usuário que realizou a alteração</li>" +
                    "<li><strong>operacao</strong> - Tipo de operação realizada</li>" +
                    "</ul>", defaultValue = "dataAlteracao,DESC", paramType = "query", dataType = "string")
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Log consultado com sucesso", response = ExemploRespostaLogConfiguracao.class)
    })
    @ResponseBody
    @RequestMapping(value = "/gerais/log", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> atividadesCrossfit(@ApiParam(value = "Filtros de busca para o log. " +
                                                                          "<br/><strong>Filtros disponíveis:</strong>" +
                                                                          "<ul>" +
                                                                          "<li><strong>quicksearchValue:</strong> Busca textual no log</li>" +
                                                                          "<li><strong>dataInicio:</strong> Data inicial do período (timestamp em milissegundos)</li>" +
                                                                          "<li><strong>dataFim:</strong> Data final do período (timestamp em milissegundos)</li>" +
                                                                          "<li><strong>tipo:</strong> Tipo de operação (ex: ['ALTERAÇÃO'])</li>" +
                                                                          "</ul>",
                                                                          defaultValue = "{\"dataInicio\":1735689600000, \"dataFim\":1749599999000}")
                                                                  @RequestParam(value = "filters", required = false) JSONObject filtros,
                                                                  @ApiIgnore PaginadorDTO paginadorDTO) throws JSONException {
        try {
            return ResponseEntityFactory.ok(logService.listarLog(EntidadeLogEnum.CONFIGURACAOSISTEMA, filtros, paginadorDTO,
                    "'" + ConfiguracoesGeraisDTO.class.getSimpleName().toUpperCase() + "'",
                    "", ""));
        } catch (ServiceException e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao buscar log de configuração", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar log de configurações de integrações relacionadas ao treino",
            notes = "Consulta o histórico de alterações das configurações de integrações do sistema, incluindo alterações em GymPass, GoGood, MGB e outras integrações.",
            tags = "Logs"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Número da página a ser retornada", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Número de itens por página", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Campo para ordenação dos resultados, seguido de 'ASC' ou 'DESC' (ex: 'dataAlteracao,DESC'). " +
                    "<br/><strong>Campos disponíveis para ordenação:</strong>" +
                    "<ul>" +
                    "<li><strong>dataAlteracao</strong> - Data da alteração</li>" +
                    "<li><strong>usuario</strong> - Usuário que realizou a alteração</li>" +
                    "<li><strong>operacao</strong> - Tipo de operação realizada</li>" +
                    "</ul>", defaultValue = "dataAlteracao,DESC", paramType = "query", dataType = "string")
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Log consultado com sucesso", response = ExemploRespostaLogConfiguracao.class)
    })
    @ResponseBody
    @RequestMapping(value = "/integracoes/log", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> integracoesLog(@ApiParam(value = "Filtros de busca para o log. " +
                                                                      "<br/><strong>Filtros disponíveis:</strong>" +
                                                                      "<ul>" +
                                                                      "<li><strong>quicksearchValue:</strong> Busca textual no log</li>" +
                                                                      "<li><strong>dataInicio:</strong> Data inicial do período (timestamp em milissegundos)</li>" +
                                                                      "<li><strong>dataFim:</strong> Data final do período (timestamp em milissegundos)</li>" +
                                                                      "<li><strong>tipo:</strong> Tipo de operação (ex: ['ALTERAÇÃO'])</li>" +
                                                                      "</ul>",
                                                                      defaultValue = "{\"dataInicio\":1735689600000, \"dataFim\":1749599999000}")
                                                              @RequestParam(value = "filters", required = false) JSONObject filtros,
                                                              @ApiIgnore PaginadorDTO paginadorDTO) throws JSONException {
        try {
            return ResponseEntityFactory.ok(logService.listarLog(EntidadeLogEnum.CONFIGURACAOSISTEMA,
                    filtros, paginadorDTO, "'" + ConfiguracaoIntegracoesGravarEnumDTO.class.getSimpleName().toUpperCase() + "'", "", ""));
        } catch (ServiceException e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao buscar log de configuração", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Alterar configurações de aplicativo Treino",
            notes = "Altera as configurações relacionadas aos aplicativos móveis Treino, incluindo módulos habilitados, reagendamentos e outras funcionalidades do app.",
            tags = "Configurações Treino"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Configurações alteradas com sucesso", response = ExemploRespostaConfiguracaoSucesso.class)
    })
    @ResponseBody
    @RequestMapping(value = "/aplicativos", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cfgGerias(@ApiParam(value = "Dados das configurações de aplicativos a serem alteradas")
                                                         @RequestBody ConfiguracoesAplicativosDTO cfgDTO) {
        return send(cfgDTO.getClass(), cfgDTO);
    }

    @ApiOperation(
            value = "Consultar log de configurações de aplicativo Treino",
            notes = "Consulta o histórico de alterações das configurações de aplicativos móveis, incluindo módulos habilitados e funcionalidades do app.",
            tags = "Logs"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Número da página a ser retornada", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Número de itens por página", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Campo para ordenação dos resultados, seguido de 'ASC' ou 'DESC' (ex: 'dataAlteracao,DESC'). " +
                    "<br/><strong>Campos disponíveis para ordenação:</strong>" +
                    "<ul>" +
                    "<li><strong>dataAlteracao</strong> - Data da alteração</li>" +
                    "<li><strong>usuario</strong> - Usuário que realizou a alteração</li>" +
                    "<li><strong>operacao</strong> - Tipo de operação realizada</li>" +
                    "</ul>", defaultValue = "dataAlteracao,DESC", paramType = "query", dataType = "string")
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Log consultado com sucesso", response = ExemploRespostaLogConfiguracao.class)
    })
    @ResponseBody
    @RequestMapping(value = "/aplicativos/log", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> logApps(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                                  PaginadorDTO paginadorDTO) throws JSONException {
        try {
            return ResponseEntityFactory.ok(logService.listarLog(EntidadeLogEnum.CONFIGURACAOSISTEMA,
                    filtros, paginadorDTO, "'" + ConfiguracoesAplicativosDTO.class.getSimpleName().toUpperCase() + "'", "", ""));
        } catch (ServiceException e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao buscar log de configuração", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @ApiOperation(
            value = "Alterar configurações de treino",
            notes = "Altera as configurações relacionadas aos treinos, incluindo emissão de fichas, bloqueios e visualizações de treino.",
            tags = "Configurações Treino"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Configurações alteradas com sucesso", response = ExemploRespostaConfiguracaoSucesso.class)
    })
    @ResponseBody
    @RequestMapping(value = "/treino", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cfgGerias(@ApiParam(value = "Dados das configurações de treino a serem alteradas")
                                                         @RequestBody ConfiguracoesTreinoDTO cfgDTO) {
        return send(cfgDTO.getClass(), cfgDTO);
    }

    @ApiOperation(
            value = "Consultar log de configurações de treino",
            notes = "Consulta o histórico de alterações das configurações de treino, incluindo emissão de fichas e bloqueios.",
            tags = "Logs"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Número da página a ser retornada", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Número de itens por página", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Campo para ordenação dos resultados, seguido de 'ASC' ou 'DESC' (ex: 'dataAlteracao,DESC'). " +
                    "<br/><strong>Campos disponíveis para ordenação:</strong>" +
                    "<ul>" +
                    "<li><strong>dataAlteracao</strong> - Data da alteração</li>" +
                    "<li><strong>usuario</strong> - Usuário que realizou a alteração</li>" +
                    "<li><strong>operacao</strong> - Tipo de operação realizada</li>" +
                    "</ul>", defaultValue = "dataAlteracao,DESC", paramType = "query", dataType = "string")
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Log consultado com sucesso", response = ExemploRespostaLogConfiguracao.class)
    })
    @ResponseBody
    @RequestMapping(value = "/treino/log", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> logTreino(@ApiParam(value = "Filtros de busca para o log. " +
                                                                 "<br/><strong>Filtros disponíveis:</strong>" +
                                                                 "<ul>" +
                                                                 "<li><strong>quicksearchValue:</strong> Busca textual no log</li>" +
                                                                 "<li><strong>dataInicio:</strong> Data inicial do período (timestamp em milissegundos)</li>" +
                                                                 "<li><strong>dataFim:</strong> Data final do período (timestamp em milissegundos)</li>" +
                                                                 "<li><strong>tipo:</strong> Tipo de operação (ex: ['ALTERAÇÃO'])</li>" +
                                                                 "</ul>",
                                                                 defaultValue = "{\"dataInicio\":1735689600000, \"dataFim\":1749599999000}")
                                                         @RequestParam(value = "filters", required = false) JSONObject filtros,
                                                         @ApiIgnore PaginadorDTO paginadorDTO) throws JSONException {
        try {
            return ResponseEntityFactory.ok(logService.listarLog(EntidadeLogEnum.CONFIGURACAOSISTEMA, filtros, paginadorDTO, "'" + ConfiguracoesTreinoDTO.class.getSimpleName().toUpperCase() + "'", "", ""));
        } catch (ServiceException e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao buscar log de configuração", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Alterar configurações de CrossFit",
            notes = "Altera as configurações específicas do módulo CrossFit, incluindo produtos de inscrição e nomenclaturas.",
            tags = "Configurações Treino"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Configurações alteradas com sucesso", response = ExemploRespostaConfiguracaoSucesso.class)
    })
    @ResponseBody
    @RequestMapping(value = "/crossfit", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cfgCrossfit(@ApiParam(value = "Dados das configurações de CrossFit a serem alteradas")
                                                           @RequestBody ConfiguracoesCrossfitDTO cfgDTO) {
        return send(cfgDTO.getClass(), cfgDTO);
    }

    @ApiOperation(
            value = "Alterar configurações de aulas",
            notes = "Altera as configurações relacionadas às aulas coletivas, incluindo agendamentos, validações e reposições.",
            tags = "Configurações Treino"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Configurações alteradas com sucesso", response = ExemploRespostaConfiguracaoSucesso.class)
    })
    @ResponseBody
    @RequestMapping(value = "/aulas", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cfgAulas(@RequestBody ConfiguracoesAulasDTO cfgDTO) {
        cfgDTO.setMinutos_agendar_com_antecedencia(Uteis.removerTudoMenosNumero(cfgDTO.getMinutos_agendar_com_antecedencia()));
        cfgDTO.setMinutos_desmarcar_com_antecedencia(Uteis.removerTudoMenosNumero(cfgDTO.getMinutos_desmarcar_com_antecedencia()));
        cfgDTO.setMinutos_alterar_equipamento_com_antecedencia(Uteis.removerTudoMenosNumero(cfgDTO.getMinutos_alterar_equipamento_com_antecedencia()));
        return send(cfgDTO.getClass(), cfgDTO);
    }

    @ApiOperation(
            value = "Consultar log de configurações de aulas",
            notes = "Consulta o histórico de alterações das configurações de aulas coletivas, incluindo agendamentos, validações e reposições.",
            tags = "Logs"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Número da página a ser retornada", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Número de itens por página", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Campo para ordenação dos resultados, seguido de 'ASC' ou 'DESC' (ex: 'dataAlteracao,DESC'). " +
                    "<br/><strong>Campos disponíveis para ordenação:</strong>" +
                    "<ul>" +
                    "<li><strong>dataAlteracao</strong> - Data da alteração</li>" +
                    "<li><strong>usuario</strong> - Usuário que realizou a alteração</li>" +
                    "<li><strong>operacao</strong> - Tipo de operação realizada</li>" +
                    "</ul>", defaultValue = "dataAlteracao,DESC", paramType = "query", dataType = "string")
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Log consultado com sucesso", response = ExemploRespostaLogConfiguracao.class)
    })
    @ResponseBody
    @RequestMapping(value = "/aulas/log", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> logAulas(@ApiParam(value = "Filtros de busca para o log. " +
                                                                "<br/><strong>Filtros disponíveis:</strong>" +
                                                                "<ul>" +
                                                                "<li><strong>quicksearchValue:</strong> Busca textual no log</li>" +
                                                                "<li><strong>dataInicio:</strong> Data inicial do período (timestamp em milissegundos)</li>" +
                                                                "<li><strong>dataFim:</strong> Data final do período (timestamp em milissegundos)</li>" +
                                                                "<li><strong>tipo:</strong> Tipo de operação (ex: ['ALTERAÇÃO'])</li>" +
                                                                "</ul>",
                                                                defaultValue = "{\"dataInicio\":1735689600000, \"dataFim\":1749599999000}")
                                                        @RequestParam(value = "filters", required = false) JSONObject filtros,
                                                        @ApiIgnore PaginadorDTO paginadorDTO) throws JSONException {
        try {
            return ResponseEntityFactory.ok(logService.listarLog(EntidadeLogEnum.CONFIGURACAOSISTEMA, filtros, paginadorDTO, "'" + ConfiguracoesAulasDTO.class.getSimpleName().toUpperCase() + "'", "", ""));
        } catch (ServiceException e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao buscar log de configuração", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Alterar configurações de avaliação física",
            notes = "Altera as configurações relacionadas às avaliações físicas, incluindo anamnese, dobras cutâneas, perimetria e testes físicos.",
            tags = "Configurações Treino"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Configurações alteradas com sucesso", response = ExemploRespostaConfiguracaoSucesso.class)
    })
    @ResponseBody
    @RequestMapping(value = "/avaliacao", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cfgAvaliacoes(@ApiParam(value = "Dados das configurações de avaliação física a serem alteradas")
                                                             @RequestBody ConfiguracoesAvaliacaoDTO cfgDTO) {
        String key = sessaoService.getUsuarioAtual().getChave();

        if (cfgDTO.getCfg_dobras_cutaneas()) {
            Gson gson = new Gson();
            List<ConfiguracaoDobras> cfgsDobras = gson.fromJson(cfgDTO.getOrdens_dobras(), new TypeToken<ArrayList<ConfiguracaoDobras>>() {
            }.getType());
            try {
                configuracaoSistemaService.gravarCfgsDobras(key, cfgsDobras);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return send(cfgDTO.getClass(), cfgDTO);
    }

    @ApiOperation(
            value = "Consultar log de configurações de avaliação física",
            notes = "Consulta o histórico de alterações das configurações de avaliação física, incluindo anamnese, dobras cutâneas e testes físicos.",
            tags = "Logs"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Número da página a ser retornada", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Número de itens por página", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Campo para ordenação dos resultados, seguido de 'ASC' ou 'DESC' (ex: 'dataAlteracao,DESC'). " +
                    "<br/><strong>Campos disponíveis para ordenação:</strong>" +
                    "<ul>" +
                    "<li><strong>dataAlteracao</strong> - Data da alteração</li>" +
                    "<li><strong>usuario</strong> - Usuário que realizou a alteração</li>" +
                    "<li><strong>operacao</strong> - Tipo de operação realizada</li>" +
                    "</ul>", defaultValue = "dataAlteracao,DESC", paramType = "query", dataType = "string")
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Log consultado com sucesso", response = ExemploRespostaLogConfiguracao.class)
    })
    @ResponseBody
    @RequestMapping(value = "/avaliacao/log", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> logAvaliacao(@ApiParam(value = "Filtros de busca para o log. " +
                                                                    "<br/><strong>Filtros disponíveis:</strong>" +
                                                                    "<ul>" +
                                                                    "<li><strong>quicksearchValue:</strong> Busca textual no log</li>" +
                                                                    "<li><strong>dataInicio:</strong> Data inicial do período (timestamp em milissegundos)</li>" +
                                                                    "<li><strong>dataFim:</strong> Data final do período (timestamp em milissegundos)</li>" +
                                                                    "<li><strong>tipo:</strong> Tipo de operação (ex: ['ALTERAÇÃO'])</li>" +
                                                                    "</ul>",
                                                                    defaultValue = "{\"dataInicio\":1735689600000, \"dataFim\":1749599999000}")
                                                            @RequestParam(value = "filters", required = false) JSONObject filtros,
                                                            @ApiIgnore PaginadorDTO paginadorDTO) throws JSONException {
        try {
            return ResponseEntityFactory.ok(logService.listarLog(EntidadeLogEnum.CONFIGURACAOSISTEMA, filtros, paginadorDTO, "'" + ConfiguracoesAvaliacaoDTO.class.getSimpleName().toUpperCase() + "'", "", ""));
        } catch (ServiceException e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao buscar log de configuração", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Alterar configurações de gestão",
            notes = "Altera as configurações relacionadas à gestão do sistema, incluindo períodos de análise de BI e critérios de alunos inativos.",
            tags = "Configurações Treino"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Configurações alteradas com sucesso", response = ExemploRespostaConfiguracaoSucesso.class)
    })
    @ResponseBody
    @RequestMapping(value = "/gestao", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cfgGestao(@ApiParam(value = "Dados das configurações de gestão a serem alteradas")
                                                         @RequestBody ConfiguracoesGestaoDTO cfgDTO) {
        return send(cfgDTO.getClass(), cfgDTO);
    }

    @ApiOperation(
            value = "Consultar log de configurações de gestão",
            notes = "Consulta o histórico de alterações das configurações de gestão, incluindo períodos de BI e critérios de alunos inativos.",
            tags = "Logs"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Número da página a ser retornada", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Número de itens por página", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Campo para ordenação dos resultados, seguido de 'ASC' ou 'DESC' (ex: 'dataAlteracao,DESC'). " +
                    "<br/><strong>Campos disponíveis para ordenação:</strong>" +
                    "<ul>" +
                    "<li><strong>dataAlteracao</strong> - Data da alteração</li>" +
                    "<li><strong>usuario</strong> - Usuário que realizou a alteração</li>" +
                    "<li><strong>operacao</strong> - Tipo de operação realizada</li>" +
                    "</ul>", defaultValue = "dataAlteracao,DESC", paramType = "query", dataType = "string")
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Log consultado com sucesso", response = ExemploRespostaLogConfiguracao.class)
    })
    @ResponseBody
    @RequestMapping(value = "/gestao/log", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> logGestao(@ApiParam(value = "Filtros de busca para o log. " +
                                                                 "<br/><strong>Filtros disponíveis:</strong>" +
                                                                 "<ul>" +
                                                                 "<li><strong>quicksearchValue:</strong> Busca textual no log</li>" +
                                                                 "<li><strong>dataInicio:</strong> Data inicial do período (timestamp em milissegundos)</li>" +
                                                                 "<li><strong>dataFim:</strong> Data final do período (timestamp em milissegundos)</li>" +
                                                                 "<li><strong>tipo:</strong> Tipo de operação (ex: ['ALTERAÇÃO'])</li>" +
                                                                 "</ul>",
                                                                 defaultValue = "{\"dataInicio\":1735689600000, \"dataFim\":1749599999000}")
                                                         @RequestParam(value = "filters", required = false) JSONObject filtros,
                                                         @ApiIgnore PaginadorDTO paginadorDTO) throws JSONException {
        try {
            return ResponseEntityFactory.ok(logService.listarLog(EntidadeLogEnum.CONFIGURACAOSISTEMA, filtros, paginadorDTO, "'" + ConfiguracoesGestaoDTO.class.getSimpleName().toUpperCase() + "'", "", ""));
        } catch (ServiceException e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao buscar log de configuração", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Alterar configurações de integrações",
            notes = "Altera as configurações de integrações do sistema, incluindo GymPass, GoGood, Myzone e outras integrações.",
            tags = "Configurações Treino"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Configurações alteradas com sucesso", response = ExemploRespostaConfiguracaoSucesso.class)
    })
    @ResponseBody
    @RequestMapping(value = "/integracoes", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cfgIntegracoes(@ApiParam(value = "Dados das configurações de integrações a serem alteradas")
                                                              @RequestBody ConfiguracaoIntegracoesDTO cfgDTOIntegracao,
                                                              @ApiParam(value = "ID da empresa", required = true, example = "123")
                                                              @RequestHeader(value = "empresaId", required = true) Integer empresaId) throws Exception {
        ConfiguracaoIntegracoesGravarEnumDTO cfgDTO = new ConfiguracaoIntegracoesGravarEnumDTO();
        cfgDTO = cfgDTO.constructor(cfgDTOIntegracao);
        ConfigGymPassService configService = UtilContext.getBean(ConfigGymPassService.class);
        String ctx = sessaoService.getUsuarioAtual().getChave();
        ConfigGymPassDTO dto = new ConfigGymPassDTO();
        dto.setEmpresa(empresaId);
        popularConfiguracoesGympass(dto, cfgDTOIntegracao, ctx, empresaId);
        if (dto.getEmpresa() != null) {
            configService.alterarDTO(ctx, dto);
        }
        return send(cfgDTO.getClass(), cfgDTO);
    }

    @ApiOperation(
            value = "Alterar lista de configurações GymPass",
            notes = "Altera múltiplas configurações de integração GymPass para diferentes empresas.",
            tags = "Configurações Treino"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Configurações alteradas com sucesso", response = ExemploRespostaConfiguracaoSucesso.class)
    })
    @ResponseBody
    @RequestMapping(value = "/integracoeslista", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cfgIntegracoesLista(@ApiParam(value = "Lista de configurações GymPass a serem alteradas")
                                                                   @RequestBody List<ConfigGymPassDTO> cfgDTOIntegracaoLista,
                                                                   @ApiParam(value = "ID da empresa", required = true, example = "123")
                                                                   @RequestHeader(value = "empresaId", required = true) Integer empresaId) throws Exception {
        ConfigGymPassService configService = UtilContext.getBean(ConfigGymPassService.class);
        String ctx = sessaoService.getUsuarioAtual().getChave();
        for (ConfigGymPassDTO dto : cfgDTOIntegracaoLista) {
            ConfiguracaoIntegracoesDTO cfgDTOIntegracao = new ConfiguracaoIntegracoesDTO();
            cfgDTOIntegracao = cfgDTOIntegracao.constructor(dto);
            popularConfiguracoesGympass(dto, cfgDTOIntegracao, ctx, empresaId);
            configService.alterarDTO(ctx, dto);
        }

        return ResponseEntityFactory.ok("OK");
    }

    @ApiOperation(
            value = "Alterar lista de configurações GoGood",
            notes = "Altera múltiplas configurações de integração GoGood para diferentes empresas.",
            tags = "Configurações Treino"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Configurações alteradas com sucesso", response = ExemploRespostaConfiguracaoSucesso.class)
    })
    @ResponseBody
    @RequestMapping(value = "/integracoeslistaGoGood", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cfgIntegracoesListaGoGood(@ApiParam(value = "Lista de configurações GoGood a serem alteradas")
                                                                         @RequestBody List<ConfigGoGoodDTO> cfgDTOIntegracaoLista,
                                                                         @ApiParam(value = "ID da empresa", required = true, example = "123")
                                                                         @RequestHeader(value = "empresaId", required = true) Integer empresaId) throws Exception {
        ConfigGoGoodService configService = UtilContext.getBean(ConfigGoGoodService.class);
        String ctx = sessaoService.getUsuarioAtual().getChave();
        for (ConfigGoGoodDTO dto : cfgDTOIntegracaoLista) {
            ConfiguracaoIntegracoesDTO cfgDTOIntegracao = new ConfiguracaoIntegracoesDTO();
            cfgDTOIntegracao = cfgDTOIntegracao.constructor(dto);
            popularConfiguracoesGoGood(dto, cfgDTOIntegracao, ctx, empresaId);
            configService.alterarDTO(ctx, dto);
        }

        return ResponseEntityFactory.ok("OK");
    }

    @ApiOperation(
            value = "Alterar lista de configurações MGB",
            notes = "Altera múltiplas configurações de integração MGB para diferentes empresas.",
            tags = "Configurações Treino"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Configurações alteradas com sucesso", response = ExemploRespostaConfiguracaoSucesso.class)
    })
    @ResponseBody
    @RequestMapping(value = "/integracoeslistamgb", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cfgIntegracoesListaMGB(@ApiParam(value = "Lista de configurações MGB a serem alteradas")
                                                                      @RequestBody List<ConfigMgb> configMGB,
                                                                      @ApiParam(value = "ID da empresa", required = true, example = "123")
                                                                      @RequestHeader(value = "empresaId", required = true) Integer empresaId) throws Exception {
        String key = sessaoService.getUsuarioAtual().getChave();
        try {
            configuracaoSistemaService.inserirConfigsMGB(key, configMGB);
            return ResponseEntityFactory.ok("OK");
        } catch (ServiceException e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao executar o processo", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Atualizar número de aulas experimentais",
            notes = "Processo de manutenção para atualizar o número de aulas experimentais para todos os alunos.",
            tags = "Configurações Treino"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Processo executado com sucesso", response = ExemploRespostaConfiguracaoSucesso.class)
    })
    @ResponseBody
    @RequestMapping(value = "/processos/manutencao/aulasexperimentais", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> pcrAtualizarNumeroAulasExperimentaisTodos(@ApiParam(value = "Número de aulas experimentais", required = true, example = "3")
                                                                                         @RequestParam("nraulasexperimentais") Integer nraulasexperimentais) {
        try {
            configuracaoSistemaService.atualizarManutencao(nraulasexperimentais);
            return ResponseEntityFactory.ok("OK");
        } catch (ServiceException e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao alterar a configuração", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }

    }

    @ApiOperation(
            value = "Consultar configurações de manutenção",
            notes = "Consulta as configurações de manutenção do sistema, incluindo aplicativos personalizados.",
            tags = "Configurações Treino"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Configurações consultadas com sucesso", response = ExemploRespostaConfiguracoesManutencaoDTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "/manutencao", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cfgManutencao() {
        try {
            return ResponseEntityFactory.ok(configuracaoSistemaService.configsDTO(ConfiguracoesManutencaoDTO.class, new ConfiguracoesManutencaoDTO()));
        } catch (ServiceException e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao consultar a configuração", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Alterar configurações de manutenção",
            notes = "Altera as configurações de manutenção do sistema, incluindo aplicativos personalizados.",
            tags = "Configurações Treino"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Configurações alteradas com sucesso", response = ExemploRespostaConfiguracaoSucesso.class)
    })
    @ResponseBody
    @RequestMapping(value = "/manutencao", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cfgGeriasManutencao(@ApiParam(value = "Dados das configurações de manutenção a serem alteradas")
                                                                   @RequestBody ConfiguracoesManutencaoDTO cfgDTO) {
        return send(cfgDTO.getClass(), cfgDTO);
    }

    @ApiOperation(
            value = "Consultar configurações gerais do sistema",
            notes = "Consulta as configurações gerais do sistema relacionadas ao treino, incluindo validações de agenda e duração do aluno na academia.",
            tags = "Configurações Treino"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Configurações consultadas com sucesso", response = ExemploRespostaConfiguracoesGeraisDTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "/gerais", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cfgGerais() {
        try {
            return ResponseEntityFactory.ok(configuracaoSistemaService.configsDTO(ConfiguracoesGeraisDTO.class, new ConfiguracoesGeraisDTO()));
        } catch (ServiceException e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao consultar a configuração", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar configurações de aulas",
            notes = "Consulta as configurações relacionadas às aulas coletivas, incluindo agendamentos, validações e reposições.",
            tags = "Configurações Treino"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Configurações consultadas com sucesso", response = ExemploRespostaConfiguracoesAulasDTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "/aulas", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cfgAulas() {
        try {
            return ResponseEntityFactory.ok(configuracaoSistemaService.configsDTO(ConfiguracoesAulasDTO.class, new ConfiguracoesAulasDTO()));
        } catch (ServiceException e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao consultar a configuração", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar configurações de aplicativos",
            notes = "Consulta as configurações relacionadas aos aplicativos móveis Treino, incluindo módulos habilitados e funcionalidades do app.",
            tags = "Configurações Treino"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Configurações consultadas com sucesso", response = ExemploRespostaConfiguracoesAplicativosDTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "/aplicativos", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cfgAplicativos() {
        try {
            return ResponseEntityFactory.ok(configuracaoSistemaService.configsDTO(ConfiguracoesAplicativosDTO.class, new ConfiguracoesAplicativosDTO()));
        } catch (ServiceException e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao consultar a configuração", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @ApiOperation(
            value = "Consultar configurações de treino",
            notes = "Consulta as configurações relacionadas aos treinos, incluindo emissão de fichas, bloqueios e visualizações de treino.",
            tags = "Configurações Treino"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Configurações consultadas com sucesso", response = ExemploRespostaConfiguracoesTreinoDTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "/treino", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cfgTreino() {
        try {
            return ResponseEntityFactory.ok(configuracaoSistemaService.configsDTO(ConfiguracoesTreinoDTO.class, new ConfiguracoesTreinoDTO()));
        } catch (ServiceException e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao consultar a configuração", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar configurações de CrossFit",
            notes = "Consulta as configurações específicas do módulo CrossFit, incluindo produtos de inscrição e nomenclaturas.",
            tags = "Configurações Treino"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Configurações consultadas com sucesso", response = ExemploRespostaConfiguracoesCrossfitDTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "/crossfit", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cfgCrossfit() {
        try {
            return ResponseEntityFactory.ok(configuracaoSistemaService.configsDTO(ConfiguracoesCrossfitDTO.class, new ConfiguracoesCrossfitDTO()));
        } catch (ServiceException e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao consultar a configuração", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar configurações de avaliação física",
            notes = "Consulta as configurações relacionadas às avaliações físicas, incluindo anamnese, dobras cutâneas, perimetria e testes físicos.",
            tags = "Configurações Treino"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Configurações consultadas com sucesso", response = ExemploRespostaConfiguracoesAvaliacaoDTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "/avaliacao", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cfgAvaliacao() {
        List<SelectItem> produtos;
        Gson gson = new Gson();
        try {
            String key = sessaoService.getUsuarioAtual().getChave();
            produtos = new ArrayList<SelectItem>();

            if (!SuperControle.independente(key)) {
                List<GenericoTO> genericoTOS = integracaoWS.produtosServicosZW(Aplicacao.getProp(key, Aplicacao.urlZillyonWebInteg), key);
                produtos.add(new SelectItem(0, ""));
                for (GenericoTO g : genericoTOS) {
                    produtos.add(new SelectItem(g.getCodigo(), g.getLabel()));
                }
            }
            ConfiguracoesAvaliacaoDTO configuracoesAvaliacaoDTO = (ConfiguracoesAvaliacaoDTO) configuracaoSistemaService.configsDTO(ConfiguracoesAvaliacaoDTO.class, new ConfiguracoesAvaliacaoDTO());
            configuracoesAvaliacaoDTO.setProdutos(gson.toJson(produtos));
            List<ConfiguracaoDobras> dobras = configuracaoSistemaService.obterCfgsDobras(key);
            configuracoesAvaliacaoDTO.setOrdens_dobras(gson.toJson(dobras));
            return ResponseEntityFactory.ok(configuracoesAvaliacaoDTO);

        } catch (ServiceException e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao consultar a configuração", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar configurações de gestão",
            notes = "Consulta as configurações relacionadas à gestão do sistema, incluindo períodos de análise de BI e critérios de alunos inativos.",
            tags = "Configurações Treino"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Configurações consultadas com sucesso", response = ExemploRespostaConfiguracoesGestaoDTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "/gestao", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cfgGestao() {
        try {
            return ResponseEntityFactory.ok(configuracaoSistemaService.configsDTO(ConfiguracoesGestaoDTO.class, new ConfiguracoesGestaoDTO()));
        } catch (ServiceException e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao consultar a configuração", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar configurações de integrações",
            notes = "Consulta as configurações de integrações do sistema, incluindo GymPass, GoGood, Myzone e outras integrações.",
            tags = "Configurações Treino"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Configurações consultadas com sucesso", response = ExemploRespostaConfiguracoesIntegracoesDTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "/integracoes", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cfgIntegracoes(@ApiParam(value = "ID da empresa", required = true, example = "123")
                                                              @RequestHeader(value = "empresaId", required = true) Integer empresaId) {
        try {
            ConfiguracaoIntegracoesDTO configs = (ConfiguracaoIntegracoesDTO) configuracaoSistemaService.configsDTO(ConfiguracaoIntegracoesDTO.class, new ConfiguracaoIntegracoesDTO());
            String ctx = sessaoService.getUsuarioAtual().getChave();
            configs = configuracaoSistemaService.obterConfigsBookingGympass(ctx, empresaId, configs);
            return ResponseEntityFactory.ok(configs);
        } catch (ServiceException e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao consultar a configuração", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar lista de configurações GymPass",
            notes = "Consulta todas as configurações de integração GymPass disponíveis no sistema.",
            tags = "Configurações Treino"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Lista consultada com sucesso", response = ExemploRespostaListaConfigGymPassDTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "/integracoeslista", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cfgIntegracoesLista(@ApiParam(value = "ID da empresa", required = true, example = "123")
                                                                   @RequestHeader(value = "empresaId", required = true) Integer empresaId) {
        try {
            ConfiguracaoIntegracoesDTO configs = (ConfiguracaoIntegracoesDTO) configuracaoSistemaService.configsDTO(ConfiguracaoIntegracoesDTO.class, new ConfiguracaoIntegracoesDTO());
            String ctx = sessaoService.getUsuarioAtual().getChave();
            ConfigGymPassService configService = UtilContext.getBean(ConfigGymPassService.class);
            List<ConfigGymPassDTO> listaConfigGymPass = configService.obterTodosDTO(ctx);
            return ResponseEntityFactory.ok(listaConfigGymPass);
        } catch (ServiceException e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao consultar a configuração", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    private void popularConfiguracoesGympass(ConfigGymPassDTO dto, ConfiguracaoIntegracoesDTO cfgDTOIntegracao, String ctx, Integer empresaId) throws Exception {
        dto.setCodigoGymPass(cfgDTOIntegracao.getCodigo_gympass_booking());
        dto.setUsarGymPassBooking(cfgDTOIntegracao.getUsar_gympass_booking());
        dto.setEmpresa(dto.getEmpresa());
    }

    private void popularConfiguracoesGoGood(ConfigGoGoodDTO dto, ConfiguracaoIntegracoesDTO cfgDTOIntegracao, String ctx, Integer empresaId) throws Exception {
        dto.setTokenAcademyGoGood(cfgDTOIntegracao.getToken_academy_gogood());
        dto.setEmpresa(dto.getEmpresa());
    }

    @ApiOperation(
            value = "Executar processo de exclusão de registros não existentes no ZW",
            notes = "Processo de manutenção que exclui registros que não existem mais no sistema ZW.",
            tags = "Configurações Treino"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Processo executado com sucesso", response = ExemploRespostaConfiguracaoSucesso.class)
    })
    @ResponseBody
    @RequestMapping(value = "/processos/excluirNaoExisteZw/{empresa}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> pcrExcluirNaoExisteZw(@ApiParam(value = "Código da empresa", required = true, example = "123")
                                                                     @PathVariable("empresa") Integer empresa) {
        String key = sessaoService.getUsuarioAtual().getChave();
        try {
            configuracaoSistemaService.executarExclusaoCliNaoExisteZw(key, empresa);
            return ResponseEntityFactory.ok("OK");
        } catch (ServiceException e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao executar o processo", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar lista de configurações MGB",
            notes = "Consulta todas as configurações de integração MGB disponíveis no sistema.",
            tags = "Configurações Treino"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Lista consultada com sucesso", response = ExemploRespostaListaConfigMgbDTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "/integracoeslistamgb", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cfgIntegracoesListaMGB(@ApiParam(value = "ID da empresa", required = true, example = "123")
                                                                      @RequestHeader(value = "empresaId", required = true) Integer empresaId) {
        String key = sessaoService.getUsuarioAtual().getChave();
        try {
            return ResponseEntityFactory.ok(configuracaoSistemaService.obterConfigsMGB(key));
        } catch (ServiceException e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao executar o processo", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Sincronizar dados de booking",
            notes = "Processo que sincroniza os dados de booking com sistemas externos para uma empresa específica.",
            tags = "Configurações Treino"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Sincronização executada com sucesso", response = ExemploRespostaConfiguracaoSucesso.class)
    })
    @ResponseBody
    @RequestMapping(value = "/processos/sincronizarBooking/{empresa}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> sincronizarBooking(@ApiParam(value = "Código da empresa", required = true, example = "123")
                                                                  @PathVariable("empresa") Integer empresa) {
        String key = sessaoService.getUsuarioAtual().getChave();
        try {
            configuracaoSistemaService.sincronizarBooking(key, empresa);
            return ResponseEntityFactory.ok("OK");
        } catch (Exception e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao executar o processo", e);
            return ResponseEntityFactory.erroInterno("erro_sincronizar", e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar configurações de notificação",
            notes = "Consulta as configurações de notificações do sistema, incluindo notificações de treino, agenda e SMS.",
            tags = "Configurações Treino"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Configurações consultadas com sucesso", response = ExemploRespostaConfiguracoesNotificacaoDTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "/notificacao", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cfgNotificacao() {
        try {
            return ResponseEntityFactory.ok(configuracaoSistemaService.configsDTO(ConfiguracoesNotificacaoDTO.class, new ConfiguracoesNotificacaoDTO()));
        } catch (ServiceException e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao consultar a configuração", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Alterar configurações de notificação",
            notes = "Altera as configurações de notificações do sistema, incluindo notificações de treino, agenda e SMS.",
            tags = "Configurações Treino"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Configurações alteradas com sucesso", response = ExemploRespostaConfiguracaoSucesso.class)
    })
    @ResponseBody
    @RequestMapping(value = "/notificacao", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cfgNotificacao(@ApiParam(value = "Dados das configurações de notificação a serem alteradas")
                                                              @RequestBody ConfiguracoesNotificacaoDTO cfgDTO) {
        try {
            configuracaoSistemaService.gravarCfgsNotificacaoDTO(null, cfgDTO.getClass(), cfgDTO);
            return ResponseEntityFactory.ok("Salvo com sucesso!");
        } catch (ServiceException e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao alterar a configuração", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Obter configurações de integração MQV",
            notes = "Consulta as configurações de integração com o sistema MQV para todas as empresas.",
            tags = "Configurações Treino"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Integrações MQV consultadas com sucesso", response = ExemploRespostaListaMqvDTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "/obter-integracoes-mqv", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterIntegracoesMQV() {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            return ResponseEntityFactory.ok(configuracaoSistemaService.obterIntegracoesMQV(ctx));
        } catch (ServiceException e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao executar o processo", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Obter configurações de integração Selfloops",
            notes = "Consulta as configurações de integração com o sistema Selfloops para todas as empresas.",
            tags = "Configurações Treino"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Integrações Selfloops consultadas com sucesso", response = ExemploRespostaListaSelfloopsDTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "/obter-integracoes-selfloops", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterIntegracoesSelfloops() {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            return ResponseEntityFactory.ok(selfloopsConfiguracoesService.obterIntegracoesSelfloops(ctx));
        } catch (ServiceException e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao executar o processo", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Salvar configurações de integração MQV",
            notes = "Salva as configurações de integração com o sistema MQV para múltiplas empresas.",
            tags = "Configurações Treino"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Integrações MQV salvas com sucesso", response = ExemploRespostaConfiguracaoSucesso.class)
    })
    @ResponseBody
    @RequestMapping(value = "/salvar-integracoes-mqv", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> salvarIntegracoesMQV(@ApiParam(value = "Lista de configurações MQV a serem salvas")
                                                                    @RequestBody List<MqvDTO> mqvDTOList) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            configuracaoSistemaService.salvarIntegracoesMQV(ctx, mqvDTOList);
            return ResponseEntityFactory.ok("OK");
        } catch (ServiceException e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao executar o processo", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Salvar configurações de integração Selfloops",
            notes = "Salva as configurações de integração com o sistema Selfloops para múltiplas empresas.",
            tags = "Configurações Treino"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Integrações Selfloops salvas com sucesso", response = ExemploRespostaConfiguracaoSucesso.class)
    })
    @ResponseBody
    @RequestMapping(value = "/salvar-integracoes-selfloops", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> salvarIntegracoesSelfloops(@ApiParam(value = "Lista de configurações Selfloops a serem salvas")
                                                                          @RequestBody List<SelfloopsDTO> selfloopsDTOList) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            selfloopsConfiguracoesService.salvarIntegracoesSelfloops(ctx, selfloopsDTOList);
            return ResponseEntityFactory.ok("OK");
        } catch (ServiceException e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao executar o processo", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @ApiOperation(
            value = "Sincronizar alunos com MGB",
            notes = "Processo que sincroniza os dados de alunos com o sistema MGB para uma empresa específica.",
            tags = "Configurações Treino"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Sincronização executada com sucesso", response = ExemploRespostaSincronizacaoMgbDTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "/processos/sincronizar-alunos-mgb/{empresa}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> pcrSincronizarAlunosMgb(@ApiParam(value = "Código da empresa", required = true, example = "123")
                                                                       @PathVariable("empresa") Integer empresa) {
        String key = sessaoService.getUsuarioAtual().getChave();
        try {
            return ResponseEntityFactory.ok(configuracaoSistemaService.sincronizarAlunosMgb(key, empresa));
        } catch (ServiceException e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao executar o processo", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Definir PAR-Q como falso para aluno",
            notes = "Processo que define o questionário PAR-Q como falso para um aluno específico.",
            tags = "Configurações Treino"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Processo executado com sucesso", response = ExemploRespostaProcessoParqDTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "/processos/set-aluno-parq-false/{matricula}", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> setAlunoParqFalse(@ApiParam(value = "Número da matrícula do aluno", required = true, example = "12345")
                                                                 @PathVariable("matricula") Integer matricula) {
        String key = sessaoService.getUsuarioAtual().getChave();
        try {
            String retorno = configuracaoSistemaService.setAlunoParqFalse(key, matricula);
            return ResponseEntityFactory.ok(retorno);
        } catch (Exception e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao executar o processo", e);
            return ResponseEntityFactory.erroInterno(e.getCause().toString(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Corrigir datas de matrícula em horário de turma",
            notes = "Processo que corrige as datas de início e fim de uma matrícula de aluno em horário de turma.",
            tags = "Configurações Treino"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Processo executado com sucesso", response = ExemploRespostaProcessoCorrecaoDataDTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "/processos/corrigir-datas-matriculaalunohorarioturma", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> corrigirDatasMatriculaalunohorarioturma(@ApiParam(value = "Código da matrícula", required = true, example = "12345")
                                                                                       @RequestParam("codigoMatricula") Integer codigoMatricula,
                                                                                       @ApiParam(value = "Data de início (formato: dd/MM/yyyy)", required = true, example = "01/01/2024")
                                                                                       @RequestParam("dataInicio") String dataInicio,
                                                                                       @ApiParam(value = "Data de fim (formato: dd/MM/yyyy)", required = true, example = "31/12/2024")
                                                                                       @RequestParam("dataFim") String dataFim) {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        try {
            String retorno = "";
            if (ctx != null) {
                Map<String, String> params = new HashMap<>();
                params.put("chave", ctx);
                params.put("codMAHT", codigoMatricula.toString());
                params.put("dataInicio", dataInicio);
                params.put("dataFim", dataFim);
                String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWeb);
                String urlParams = ExecuteRequestHttpService.obterUrlComParams(url + "/prest/manutencao/corrigir-datas-matriculaalunohorarioturma", params, "UTF-8");

                String retornoZw = ExecuteRequestHttpService.executeRequestGET(urlParams);
                if (new JSONObject(retornoZw).has("content")) {
                    if (new JSONObject(retornoZw).getString("content").equals("ok")) {
                        retorno = "Datas da matricula do aluno atualizadas com sucesso!";
                    } else {
                        retorno = new JSONObject(retornoZw).getString("content");
                    }
                } else {
                    retorno = "Ocorreu um problema ao tentar atualizar as datas da matricula do aluno!";
                }
            } else {
                retorno = "Chave não encontrada!";
            }

            return ResponseEntityFactory.ok(retorno);
        } catch (Exception e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao executar o processo", e);
            return ResponseEntityFactory.erroInterno(e.getCause().toString(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Sincronizar professores com sistema TW",
            notes = "Processo que sincroniza os dados de professores com o sistema TW (TreinoWeb) para uma empresa específica.",
            tags = "Configurações Treino"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Sincronização executada com sucesso", response = ExemploRespostaSincronizarProfessorTW.class)
    })
    @ResponseBody
    @RequestMapping(value = "/processos/sync-professortw/{empresaZW}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> sincronizarProfessorTW(@ApiParam(value = "Código da empresa no sistema ZW", required = true, example = "456")
                                                                      @PathVariable("empresaZW") Integer empresaZW) {
        String key = sessaoService.getUsuarioAtual().getChave();
        try {
            return ResponseEntityFactory.ok(configuracaoSistemaService.sincronizarProfessoresTW(key, empresaZW));
        } catch (ServiceException e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao executar o processo", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Obter configurações de integração TotalPass",
            notes = "Consulta todas as configurações de integração com o sistema TotalPass para todas as empresas.",
            tags = "Configurações Treino"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Integrações TotalPass consultadas com sucesso", response = ExemploRespostaListaTotalPassDTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "/obter-integracoes-totalpass", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterIntegracoesTotalPass() {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            return ResponseEntityFactory.ok(configuracaoSistemaService.obterIntegracoesTotalPass(ctx));
        } catch (ServiceException e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao executar o processo", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Obter configurações de integração GoGood",
            notes = "Consulta as configurações de integração com o sistema GoGood para uma empresa específica.",
            tags = "Configurações Treino"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Integrações GoGood consultadas com sucesso", response = ExemploRespostaListaConfigGoGoodDTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "/obter-integracoes-gogood", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterIntegracoesGoGood(@ApiParam(value = "ID da empresa", required = true, example = "123")
                                                                      @RequestHeader(value = "empresaId", required = true) Integer empresaId) {
        try {
            ConfiguracaoIntegracoesDTO configs = (ConfiguracaoIntegracoesDTO) configuracaoSistemaService.configsDTO(ConfiguracaoIntegracoesDTO.class, new ConfiguracaoIntegracoesDTO());
            String ctx = sessaoService.getUsuarioAtual().getChave();
            ConfigGoGoodDTO dto = configuracaoSistemaService.obterConfigsGoGood(ctx, empresaId, configs);
            return ResponseEntityFactory.ok(asList(dto));
        } catch (ServiceException e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao consultar a configuração", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @ApiOperation(
            value = "Salvar configurações de integração TotalPass",
            notes = "Salva as configurações de integração com o sistema TotalPass para múltiplas empresas.",
            tags = "Configurações Treino"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Integrações TotalPass salvas com sucesso", response = ExemploRespostaConfiguracaoSucesso.class)
    })
    @ResponseBody
    @RequestMapping(value = "/salvar-integracoes-totalpass", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> salvarIntegracoesTotalPass(@ApiParam(value = "Lista de configurações TotalPass a serem salvas")
                                                                          @RequestBody List<TotalPassDTO> totalPassDTOList) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            configuracaoSistemaService.salvarIntegracoesTotalPass(ctx, totalPassDTOList);
            return ResponseEntityFactory.ok("OK");
        } catch (ServiceException e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao executar o processo", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Verificar se usa plano compartilhado",
            notes = "Verifica se o sistema está configurado para permitir planos recorrentes compartilhados (dependentes).",
            tags = "Configurações Treino"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Verificação executada com sucesso", response = ExemploRespostaVerificaPlanoCompartilhado.class)
    })
    @ResponseBody
    @RequestMapping(value = "/usa-plano-compartilhado", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> verificaPermiteDependente() {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            return ResponseEntityFactory.ok(configuracaoSistemaService.verificaPermiteDependente(ctx));
        } catch (ServiceException e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao executar o processo", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            return ResponseEntityFactory.erroInterno(e.getCause().toString(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Alterar configurações de Inteligência Artificial",
            notes = "Altera as configurações relacionadas à Inteligência Artificial do sistema, incluindo criação automática de treinos e aprovações.",
            tags = "Configurações Treino"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Configurações alteradas com sucesso", response = ExemploRespostaConfiguracaoSucesso.class)
    })
    @ResponseBody
    @RequestMapping(value = "/ia", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cfgGerias(@ApiParam(value = "Dados das configurações de IA a serem alteradas")
                                                         @RequestBody ConfiguracoesIaDTO cfgDTO) {
        return send(cfgDTO.getClass(), cfgDTO);
    }

    @ApiOperation(
            value = "Consultar configurações de Inteligência Artificial",
            notes = "Consulta as configurações relacionadas à Inteligência Artificial do sistema, incluindo criação automática de treinos e aprovações.",
            tags = "Configurações Treino"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Configurações consultadas com sucesso", response = ExemploRespostaConfiguracoesIaDTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "/ia", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cfgIa() {
        try {
            return ResponseEntityFactory.ok(configuracaoSistemaService.configsDTO(ConfiguracoesIaDTO.class, new ConfiguracoesIaDTO()));
        } catch (ServiceException e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao consultar a configuração", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar log de configurações de Inteligência Artificial",
            notes = "Consulta o histórico de alterações das configurações de IA, incluindo criação automática de treinos e aprovações.",
            tags = "Logs"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Número da página a ser retornada", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Número de itens por página", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Campo para ordenação dos resultados, seguido de 'ASC' ou 'DESC' (ex: 'dataAlteracao,DESC'). " +
                    "<br/><strong>Campos disponíveis para ordenação:</strong>" +
                    "<ul>" +
                    "<li><strong>dataAlteracao</strong> - Data da alteração</li>" +
                    "<li><strong>usuario</strong> - Usuário que realizou a alteração</li>" +
                    "<li><strong>operacao</strong> - Tipo de operação realizada</li>" +
                    "</ul>", defaultValue = "dataAlteracao,DESC", paramType = "query", dataType = "string")
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Log consultado com sucesso", response = ExemploRespostaLogConfiguracao.class)
    })
    @ResponseBody
    @RequestMapping(value = "/ia/log", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> logIa(@ApiParam(value = "Filtros de busca para o log. " +
                                                             "<br/><strong>Filtros disponíveis:</strong>" +
                                                             "<ul>" +
                                                             "<li><strong>quicksearchValue:</strong> Busca textual no log</li>" +
                                                             "<li><strong>dataInicio:</strong> Data inicial do período (timestamp em milissegundos)</li>" +
                                                             "<li><strong>dataFim:</strong> Data final do período (timestamp em milissegundos)</li>" +
                                                             "<li><strong>tipo:</strong> Tipo de operação (ex: ['ALTERAÇÃO'])</li>" +
                                                             "</ul>",
                                                             defaultValue = "{\"dataInicio\":1735689600000, \"dataFim\":1749599999000}")
                                                     @RequestParam(value = "filters", required = false) JSONObject filtros,
                                                     @ApiIgnore PaginadorDTO paginadorDTO) throws JSONException {
        try {
            return ResponseEntityFactory.ok(logService.listarLog(EntidadeLogEnum.CONFIGURACAOSISTEMA, filtros, paginadorDTO, "'" + ConfiguracoesIaDTO.class.getSimpleName().toUpperCase() + "'", "", ""));
        } catch (ServiceException e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao buscar log de configuração", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Sincronizar atividades de professores com sistema TW",
            notes = "Processo que sincroniza as atividades dos professores com o sistema TW (TreinoWeb) para uma empresa específica.",
            tags = "Configurações Treino"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Sincronização executada com sucesso", response = ExemploRespostaSincronizarAtividadesProfessoresTW.class)
    })
    @ResponseBody
    @RequestMapping(value = "/processos/sync-atividades-professorestw/{empresaZW}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> sincronizarAtividadesProfessoresTW(@ApiParam(value = "Código da empresa no sistema ZW", required = true, example = "456")
                                                                                  @PathVariable("empresaZW") Integer empresaZW) {
        String key = sessaoService.getUsuarioAtual().getChave();
        try {
            return ResponseEntityFactory.ok(configuracaoSistemaService.sincronizarAtividadesProfessoresTW(key, empresaZW));
        } catch (ServiceException e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao executar o processo", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

}
