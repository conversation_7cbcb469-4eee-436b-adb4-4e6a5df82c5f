package br.com.pacto.controller.json.parceiros;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * Created by paulo on 15/10/2018.
 */

@ApiModel(description = "Dados para criação e atualização de parceiros em lote")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ParceiroCreateTO {

    @ApiModelProperty(value = "Lista de parceiros a serem criados ou atualizados no sistema", required = true)
    private List<ParceiroTO> parceiros;

    @ApiModelProperty(value = "Lista de IDs dos parceiros que devem ser excluídos do sistema", example = "[1, 2, 3]")
    private List<Integer> parceirosExcluidosIds;

    public List<ParceiroTO> getParceiros() {
        return parceiros;
    }

    public void setParceiros(List<ParceiroTO> parceiros) {
        this.parceiros = parceiros;
    }

    public List<Integer> getParceirosExcluidosIds() {
        return parceirosExcluidosIds;
    }

    public void setParceirosExcluidosIds(List<Integer> parceirosExcluidosIds) {
        this.parceirosExcluidosIds = parceirosExcluidosIds;
    }
}
