package br.com.pacto.controller.json.aluno;

        import br.com.pacto.base.dto.EnvelopeRespostaDTO;
        import br.com.pacto.base.util.ResponseEntityFactory;
        import br.com.pacto.bean.perfil.permissao.RecursoEnum;
        import br.com.pacto.security.aspecto.Permissao;
        import br.com.pacto.service.exception.ServiceException;
        import br.com.pacto.service.intf.cliente.ClienteSinteticoService;
        import br.com.pacto.swagger.respostas.niveis.ExemploRespostaListNivelAlunoResponseDTO;
        import io.swagger.annotations.ApiOperation;
        import io.swagger.annotations.ApiResponse;
        import io.swagger.annotations.ApiResponses;
        import org.springframework.beans.factory.annotation.Autowired;
        import org.springframework.http.MediaType;
        import org.springframework.http.ResponseEntity;
        import org.springframework.stereotype.Controller;
        import org.springframework.util.Assert;
        import org.springframework.web.bind.annotation.RequestMapping;
        import org.springframework.web.bind.annotation.RequestMethod;
        import org.springframework.web.bind.annotation.ResponseBody;

        import java.util.logging.Level;
        import java.util.logging.Logger;

/**
 * Created by ulisses on 27/08/2018.
 */
@Controller
@RequestMapping("/psec/niveis-aluno")
public class NivelAlunoController {

    private final ClienteSinteticoService clienteSinteticoService;

    @Autowired
    public NivelAlunoController(ClienteSinteticoService clienteSinteticoService){
        Assert.notNull(clienteSinteticoService, "O serviço de cliente sintético não foi injetado corretamente");
        this.clienteSinteticoService = clienteSinteticoService;
    }

    @ApiOperation(
            value = "Consultar níveis dos alunos",
            notes = "Consulta os níveis dos alunos",
            tags = "Níveis"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaListNivelAlunoResponseDTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.NIVEIS)
    @RequestMapping(value = "", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterNiveisAluno() {
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.obterNiveisAluno());
        } catch (ServiceException e) {
            Logger.getLogger(NivelAlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os níveis dos alunos", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

}
