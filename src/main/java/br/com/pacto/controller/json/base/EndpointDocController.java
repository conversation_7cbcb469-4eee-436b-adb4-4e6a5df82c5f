/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.base;

import br.com.pacto.base.impl.EntityManagerFactoryService;
import br.com.pacto.base.jpa.MigracaoDadosJPAService;
import br.com.pacto.base.jpa.service.intf.PovoadorService;
import br.com.pacto.base.scheduling.agendamento.TaskNotificationAgendamento;
import br.com.pacto.bean.Agendamento;
import br.com.pacto.bean.agenda.TipoLembreteEnum;
import br.com.pacto.bean.atividade.AtividadeAnimacao;
import br.com.pacto.bean.configuracoes.ConfiguracaoSistema;
import br.com.pacto.bean.configuracoes.ConfiguracoesEnum;
import br.com.pacto.bean.ficha.Ficha;
import br.com.pacto.bean.notificacao.TipoNotificacaoEnum;
import br.com.pacto.bean.programa.ProgramaTreino;
import br.com.pacto.bean.programa.ProgramaTreinoAndamento;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.agendamento.AgendamentoJSON;
import br.com.pacto.controller.json.animacao.AnimacaoJSON;
import br.com.pacto.controller.json.atividade.read.AtividadeJSON;
import br.com.pacto.controller.json.ficha.read.FichaJSON;
import br.com.pacto.controller.json.programa.read.ProgramaTreinoJSON;
import br.com.pacto.controller.json.programa.ProgramaTreinoJSONControle;
import br.com.pacto.controller.json.stats.StatsJSON;
import br.com.pacto.controller.json.usuario.FotosJSONControle;
import br.com.pacto.dao.intf.atividade.AtividadeAnimacaoDao;
import br.com.pacto.dao.intf.cliente.ClienteSinteticoDao;
import br.com.pacto.dao.intf.usuario.UsuarioDao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.agenda.AgendamentoService;
import br.com.pacto.service.intf.configuracoes.ConfiguracaoSistemaService;
import br.com.pacto.service.intf.creditopersonal.CreditoPersonalService;
import br.com.pacto.service.intf.ficha.FichaService;
import br.com.pacto.service.intf.notificacao.NotificacaoService;
import br.com.pacto.service.intf.programa.ProgramaTreinoService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.ViewUtils;
import br.com.pacto.util.impl.Ordenacao;
import br.com.pacto.util.impl.UtilReflection;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.logging.Level;
import java.util.logging.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.mvc.method.RequestMappingInfo;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;
import servicos.integracao.adm.AdmWSConsumer;
import servicos.integracao.adm.client.EmpresaWS;
import servicos.integracao.admapp.AdmAppWSConsumer;
import servicos.integracao.admapp.client.Midia;

/**
 *
 * <AUTHOR>
 */
@Api(tags = "Administração")
@Controller
@RequestMapping("/EndpointControl")
public class EndpointDocController extends SuperControle {

    private RequestMappingHandlerMapping handlerMapping;
    @Autowired
    private ClienteSinteticoDao clienteSinteticoDao;
    @Autowired
    private AgendamentoService agendamentoService;
    @Autowired
    private NotificacaoService notificacaoService;
    @Autowired
    private UsuarioService usuarioService;
    @Autowired
    private ProgramaTreinoService programaTreinoService;
    @Autowired
    private FichaService fichaService;
    @Autowired
    private ViewUtils viewUtils;
    @Autowired
    private UsuarioDao usuarioDao;
    @Autowired
    private ConfiguracaoSistemaService configService;

    @Autowired
    public EndpointDocController(RequestMappingHandlerMapping handlerMapping) {
        this.handlerMapping = handlerMapping;
    }

    @ApiOperation(value = "Listar endpoints mapeados", notes = "Obtém todos os endpoints mapeados no sistema com seus métodos HTTP correspondentes")
    @ApiResponses(value = {
        @ApiResponse(code = 200, message = "Lista de endpoints obtida com sucesso", response = br.com.pacto.swagger.respostas.administracao.ExemploEndpointsMapeados.class)
    })
    @RequestMapping(value = "/show", method = RequestMethod.GET)
    public @ResponseBody
    ModelMap mostrar() {
        ModelMap mm = new ModelMap();
        Map<RequestMappingInfo, HandlerMethod> mapa = this.handlerMapping.getHandlerMethods();
        for (Map.Entry<RequestMappingInfo, HandlerMethod> entry : mapa.entrySet()) {
            RequestMappingInfo requestMappingInfo = entry.getKey();
            HandlerMethod handlerMethod = entry.getValue();
            mm.addAttribute(handlerMethod.getMethod().toGenericString(), requestMappingInfo.getPatternsCondition().toString());
        }
        return mm;
    }

    @ApiOperation(value = "Listar empresas cadastradas", notes = "Obtém todas as empresas cadastradas no sistema")
    @ApiResponses(value = {
        @ApiResponse(code = 200, message = "Lista de empresas obtida com sucesso", response = br.com.pacto.swagger.respostas.administracao.ExemploEmpresasCadastradas.class)
    })
    @RequestMapping(value = "/empresas", method = RequestMethod.GET)
    public @ResponseBody
    ModelMap empresas() {
        ModelMap mm = new ModelMap();
        mm.addAttribute(EntityManagerFactoryService.getEmpresas());
        return mm;
    }

    @ApiOperation(value = "Buscar clientes por similaridade fonética", notes = "Realiza busca de clientes utilizando algoritmo de similaridade fonética (soundex) no nome")
    @ApiResponses(value = {
        @ApiResponse(code = 200, message = "Busca realizada com sucesso", response = br.com.pacto.swagger.respostas.administracao.ExemploClientesSoundex.class)
    })
    @RequestMapping(value = "{ctx}/soundex", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap soundex(
            @ApiParam(value = "Contexto da empresa", required = true, defaultValue = "empresa1")
            @PathVariable String ctx,
            @ApiParam(value = "Valor para busca fonética no nome do cliente", required = true, defaultValue = "Silva")
            @RequestParam String valor) {
        ModelMap mm = new ModelMap();
        try {
//            String hql = "select o from ClienteSintetico o where convert(nome,'UTF-8','SQL_ASCII') like convert('%" + valor + "%','UTF-8','SQL_ASCII')";
//            mm.addAttribute("clientes", clienteSinteticoDao.findByParam(ctx, hql, new HashMap<String, Object>()));
            mm.addAttribute("clientes", clienteSinteticoDao.listOfObjects(ctx,
                    "select convert_to(nome, 'SQL_ASCII') from ClienteSintetico where convert_to(upper(nome), 'SQL_ASCII') like ('%' || convert_to(upper('" + valor + "'),'SQL_ASCII') || '%')"));
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(EndpointDocController.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(value = "Notificar agendamentos próximos", notes = "Consulta e notifica agendamentos previstos nos próximos minutos conforme tipo de lembrete especificado")
    @ApiResponses(value = {
        @ApiResponse(code = 200, message = "Notificações enviadas com sucesso", response = br.com.pacto.swagger.respostas.administracao.ExemploAgendamentosProximos.class)
    })
    @RequestMapping(value = "{ctx}/pushProximosAgendados", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap pushProximosAgendados(
            @ApiParam(value = "Contexto da empresa", required = true, defaultValue = "empresa1")
            @PathVariable String ctx,
            @ApiParam(value = "Data e hora atual no formato dd/MM/yyyy HH:mm:ss", required = true, defaultValue = "15/12/2024 14:30:00")
            @RequestParam final String dataAtual,
            @ApiParam(value = "Tipo de lembrete para notificação.<br/>" +
                    "<strong>Valores disponíveis:</strong><br/>" +
                    "- QUINZE_MINUTOS (15 minutos antes)<br/>" +
                    "- DUAS_HORAS (2 horas antes)<br/>" +
                    "- UM_DIA (1 dia antes)<br/>" +
                    "- SETE_DIAS (7 dias antes)<br/>" +
                    "- AGENDAMENTO_NOVO (agendamento novo)<br/>" +
                    "- AGENDAMENTO_ALTERADO (agendamento alterado)", required = true, defaultValue = "DUAS_HORAS")
            @RequestParam final String tipoLembrete) {
        ModelMap mm = new ModelMap();
        try {
            TipoLembreteEnum tEnum = TipoLembreteEnum.valueOf(tipoLembrete);

            List<Agendamento> l = agendamentoService.consultarPrevistosNosProximosMinutos(ctx,
                    Calendario.getDate(Calendario.MASC_DATAHORA, dataAtual), null, null,
                    tEnum.equals(TipoLembreteEnum.DUAS_HORAS) ? 0 : tEnum.getnMinutos() - 120,
                    tEnum);
            List<AgendamentoJSON> arr = new ArrayList<AgendamentoJSON>();
            for (Agendamento agendamento : l) {
                agendamento = notificacaoService.loadLazyAttributes(ctx, agendamento);
                AgendamentoJSON json = new AgendamentoJSON(agendamento.getCodigo(),
                        Calendario.getData(agendamento.getInicio(), Calendario.MASC_DATAHORA),
                        Calendario.getHora(agendamento.getInicio(), Calendario.MASC_HORA),
                        Calendario.getHora(agendamento.getFim(), Calendario.MASC_HORA),
                        agendamento.getTipoEvento().getNome() + " com " + agendamento.getNomeProfessor(),
                        agendamento.getStatus().getDescricao(), agendamento.getNomeProfessor(),
                        agendamento.getStatus().getCor(), agendamento.getInicio().getTime());
                arr.add(json);
                Usuario u = usuarioService.obterPorAtributo(ctx, "cliente.codigo", agendamento.getCliente().getCodigo());
                if (u != null) {
                    notificacaoService.notificarLembreteAgendamento(ctx, agendamento, TipoLembreteEnum.valueOf(tipoLembrete));
                }
            }
            mm.addAttribute(arr);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(EndpointDocController.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    private void notificar(final String ctx, Agendamento agendamento, TipoLembreteEnum tipoLembrete) throws ServiceException {
        notificacaoService.notificarLembreteAgendamento(ctx, agendamento, tipoLembrete);
    }

    private void notificar(final String ctx, Agendamento agendamento, final TipoLembreteEnum tipoLembrete,
            final TipoNotificacaoEnum tipoNotificacao,
            final String idMensagem) throws ServiceException {
        notificacaoService.notificarAgendamentoNovoOuAlterado(ctx, agendamento, tipoLembrete, tipoNotificacao, idMensagem);
    }

    private void notificar(final String ctx, ProgramaTreino programa, TipoLembreteEnum tipoLembrete,
            TipoNotificacaoEnum tipoNotificacao, final String msg) throws ServiceException {
        notificacaoService.notificarPrograma(ctx, programa, Calendario.hoje(), tipoLembrete, tipoNotificacao, msg);
    }

    @ApiOperation(value = "Executar task de notificações de agendamento", notes = "Executa task automática que processa e envia notificações para agendamentos próximos (2 horas e 1 dia), novos e alterados")
    @ApiResponses(value = {
        @ApiResponse(code = 200, message = "Task executada com sucesso", response = br.com.pacto.swagger.respostas.administracao.ExemploTaskSucesso.class)
    })
    @RequestMapping(value = "{ctx}/pushTaskAgendamento", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap pushTaskAgendamento(
            @ApiParam(value = "Contexto da empresa", required = true, defaultValue = "empresa1")
            @PathVariable String ctx) {
        ModelMap mm = new ModelMap();
        try {
            Uteis.logar(null, "Started -> " + TaskNotificationAgendamento.class.getSimpleName() + " " + ctx);
            //
            List<Agendamento> listaProximas2Horas = agendamentoService.consultarPrevistosNosProximosMinutos(ctx,
                    Calendario.hoje(), null, null,0, TipoLembreteEnum.DUAS_HORAS);
            for (Agendamento agendamento : listaProximas2Horas) {
                notificar(ctx,agendamento, TipoLembreteEnum.DUAS_HORAS);
            }

            List<Agendamento> listaProximasUmDia = agendamentoService.consultarPrevistosNosProximosMinutos(ctx,
                    Calendario.hoje(), null, null, TipoLembreteEnum.UM_DIA.getnMinutos() - 120, TipoLembreteEnum.UM_DIA);
            for (Agendamento agendamento : listaProximasUmDia) {
                notificar(ctx,agendamento, TipoLembreteEnum.UM_DIA);
            }
            //
            List<Agendamento> listaNovos = agendamentoService.consultarAgendamentosNovosOuAlterados(ctx,
                    null, null, TipoLembreteEnum.AGENDAMENTO_NOVO);
            for (Agendamento agendamento : listaNovos) {
                notificar(ctx, agendamento, TipoLembreteEnum.AGENDAMENTO_NOVO,
                        TipoNotificacaoEnum.AGENDAMENTO_NOVO, "lembrete.agendamento.novo");
            }
            List<Agendamento> listaAlterados = agendamentoService.consultarAgendamentosNovosOuAlterados(ctx,
                    null, null, TipoLembreteEnum.AGENDAMENTO_ALTERADO);
            for (Agendamento agendamento : listaAlterados) {
                notificar(ctx, agendamento, TipoLembreteEnum.AGENDAMENTO_ALTERADO,
                        TipoNotificacaoEnum.AGENDAMENTO_ALTERADO, "lembrete.agendamento.alterado");
            }
            mm.addAttribute(STATUS_SUCESSO);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(EndpointDocController.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(value = "Notificar agendamentos novos ou alterados", notes = "Consulta e notifica agendamentos que foram criados ou alterados recentemente")
    @ApiResponses(value = {
        @ApiResponse(code = 200, message = "Notificações enviadas com sucesso", response = br.com.pacto.swagger.respostas.administracao.ExemploAgendamentosNovosAlterados.class)
    })
    @RequestMapping(value = "{ctx}/pushNovosAgendamentoOuAlterados", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap pushNovosAgendamentoOuAlterados(
            @ApiParam(value = "Contexto da empresa", required = true, defaultValue = "empresa1")
            @PathVariable String ctx,
            @ApiParam(value = "Tipo de lembrete para notificação.<br/>" +
                    "<strong>Valores disponíveis:</strong><br/>" +
                    "- AGENDAMENTO_NOVO (agendamento novo)<br/>" +
                    "- AGENDAMENTO_ALTERADO (agendamento alterado)", required = true, defaultValue = "AGENDAMENTO_NOVO")
            @RequestParam final String tipoLembrete,
            @ApiParam(value = "Tipo de notificação a ser enviada.<br/>" +
                    "<strong>Valores disponíveis:</strong><br/>" +
                    "- AGENDAMENTO_NOVO<br/>" +
                    "- AGENDAMENTO_ALTERADO", required = true, defaultValue = "AGENDAMENTO_NOVO")
            @RequestParam final String tipoNotf,
            @ApiParam(value = "ID da mensagem de template para notificação", required = true, defaultValue = "lembrete.agendamento.novo")
            @RequestParam final String idMensagem) {
        ModelMap mm = new ModelMap();
        try {
            List<Agendamento> l = agendamentoService.consultarAgendamentosNovosOuAlterados(ctx, null, null,
                    TipoLembreteEnum.valueOf(tipoLembrete));
            List<AgendamentoJSON> arr = new ArrayList<AgendamentoJSON>();
            for (Agendamento agendamento : l) {
                agendamento = notificacaoService.loadLazyAttributes(ctx, agendamento);
                AgendamentoJSON json = new AgendamentoJSON(agendamento.getCodigo(),
                        Calendario.getData(agendamento.getInicio(), Calendario.MASC_DATAHORA),
                        Calendario.getHora(agendamento.getInicio(), Calendario.MASC_HORA),
                        Calendario.getHora(agendamento.getFim(), Calendario.MASC_HORA),
                        agendamento.getTipoEvento().getNome() + " com " + agendamento.getNomeProfessor(),
                        agendamento.getStatus().getDescricao(), agendamento.getNomeProfessor(),
                        agendamento.getStatus().getCor(), agendamento.getInicio().getTime());
                arr.add(json);
                Usuario u = usuarioService.obterPorAtributo(ctx, "cliente.codigo", agendamento.getCliente().getCodigo());
                if (u != null) {
                    notificacaoService.notificarAgendamentoNovoOuAlterado(ctx,
                            agendamento, TipoLembreteEnum.valueOf(tipoLembrete),
                            TipoNotificacaoEnum.valueOf(tipoNotf), idMensagem);
                }
            }
            mm.addAttribute(arr);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(EndpointDocController.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(value = "Solicitar agendamento de renovação de programas", notes = "Consulta programas previstos para renovação nos próximos 7 dias e envia notificações solicitando agendamento de renovação")
    @ApiResponses(value = {
        @ApiResponse(code = 200, message = "Solicitações de renovação enviadas com sucesso", response = br.com.pacto.swagger.respostas.administracao.ExemploProgramasRenovacao.class)
    })
    @RequestMapping(value = "{ctx}/pushSolicitarAgendamentoRenovacao", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap pushSolicitarAgendamentoRenovacao(
            @ApiParam(value = "Contexto da empresa", required = true, defaultValue = "empresa1")
            @PathVariable String ctx,
            @ApiParam(value = "Data atual no formato dd/MM/yyyy", required = true, defaultValue = "15/12/2024")
            @RequestParam final String dataAtual) {
        ModelMap mm = new ModelMap();
        try {
            Date d = Calendario.getDate(Calendario.MASC_DATA, dataAtual);
            ConfiguracaoSistema configSeriesSet = configService.consultarPorTipo(ctx, ConfiguracoesEnum.AGRUPAMENTO_SERIES_SET);
            List<ProgramaTreino> lista7Dias = programaTreinoService.consultarPrevistosRenovarNosProximosDias(ctx,
                    Calendario.getDate(Calendario.MASC_DATA, dataAtual), null, null, TipoLembreteEnum.SETE_DIAS);
            List<ProgramaTreinoJSON> arr = new ArrayList<ProgramaTreinoJSON>();
            for (ProgramaTreino programa : lista7Dias) {
                programa = programaTreinoService.obterPorId(ctx, programa.getCodigo());
                ProgramaTreinoJSON p = ProgramaTreinoJSONControle.preencherProgramaJSON(programa, ctx, null, configSeriesSet.getValorAsBoolean(), "", null);
                arr.add(p);
                notificacaoService.notificarPrograma(ctx, programa, d, TipoLembreteEnum.SETE_DIAS,
                        TipoNotificacaoEnum.SOLICITAR_RENOVACAO,
                        viewUtils.getMensagem("programa.agendarRenovacao"));
            }
            mm.addAttribute(STATUS_SUCESSO, arr);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(EndpointDocController.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(value = "Lembrar compromisso de treino", notes = "Consulta programas atrasados e envia notificações lembrando alunos sobre seus compromissos de treino")
    @ApiResponses(value = {
        @ApiResponse(code = 200, message = "Lembretes enviados com sucesso", response = br.com.pacto.swagger.respostas.administracao.ExemploLembretesCompromisso.class)
    })
    @RequestMapping(value = "{ctx}/pushLembrarCompromisso", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap pushLembrarCompromisso(
            @ApiParam(value = "Contexto da empresa", required = true, defaultValue = "empresa1")
            @PathVariable String ctx,
            @ApiParam(value = "Data atual no formato dd/MM/yyyy HH:mm:ss", required = true, defaultValue = "15/12/2024 14:30:00")
            @RequestParam final String dataAtual) {
        ModelMap mm = new ModelMap();
        try {
            Date d = Calendario.getDate(Calendario.MASC_DATAHORA, dataAtual);
            List<ProgramaTreino> lista7Dias = programaTreinoService.consultarAtrasados(ctx,
                    Calendario.getDate(Calendario.MASC_DATA, dataAtual), null, null, TipoNotificacaoEnum.LEMBRAR_ALUNO_COMPROMISSO,
                    TipoLembreteEnum.SETE_DIAS);
            List<String> arr = new ArrayList<String>();
            for (ProgramaTreino programa : lista7Dias) {
                programa = programaTreinoService.obterPorId(ctx, programa.getCodigo());
                ProgramaTreinoAndamento programaTreinoAndamento = programaTreinoService.obterAndamento(ctx, programa);
                if (programaTreinoAndamento != null) {
                    if (programaTreinoAndamento.getUltimoTreino() != null) {
                        final String msg = String.format(viewUtils.getMensagem("programa.lembrarCompromisso"), new Object[]{
                            programa.getDiasPorSemana(),
                            Calendario.getData(programaTreinoAndamento.getUltimoTreino(), Calendario.MASC_DATAHORA)});
                        notificacaoService.notificarPrograma(ctx, programa, d, TipoLembreteEnum.SETE_DIAS,
                                TipoNotificacaoEnum.LEMBRAR_ALUNO_COMPROMISSO, msg);
                        arr.add(msg);
                    }
                } else {
                    final String msg = String.format(viewUtils.getMensagem("programa.comecarCompromisso"), new Object[]{
                        programa.getDiasPorSemana()});
                    notificacaoService.notificarPrograma(ctx, programa, d, TipoLembreteEnum.SETE_DIAS,
                            TipoNotificacaoEnum.LEMBRAR_ALUNO_COMPROMISSO, msg);
                    arr.add(msg);
                }
            }
            mm.addAttribute(STATUS_SUCESSO, arr);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(EndpointDocController.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(value = "Obter animações de atividades", notes = "Obtém lista de animações associadas às atividades, removendo registros órfãos e ordenando por nome da atividade")
    @ApiResponses(value = {
        @ApiResponse(code = 200, message = "Lista de animações obtida com sucesso", response = br.com.pacto.swagger.respostas.administracao.ExemploAnimacoesAtividades.class)
    })
    @RequestMapping(value = "{ctx}/obterNomeFotos", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap obterNomeFotos(
            @ApiParam(value = "Contexto da empresa", required = true, defaultValue = "empresa1")
            @PathVariable String ctx) {
        ModelMap mm = new ModelMap();
        try {
            AtividadeAnimacaoDao atividadeAnimacaoDao = (AtividadeAnimacaoDao) UtilContext.getBean(AtividadeAnimacaoDao.class);
            List<AtividadeAnimacao> lista = atividadeAnimacaoDao.findAll(ctx);
            List<AnimacaoJSON> arrJson = new ArrayList<AnimacaoJSON>();
            for (AtividadeAnimacao atvAnim : lista) {
                if (atvAnim.getAtividade() == null || atvAnim.getAnimacao() == null) {
                    atividadeAnimacaoDao.delete(ctx, atvAnim);
                    Uteis.logar(null, "Excluiu órfão -> '" + atvAnim.getCodigo());
                } else {
                    AnimacaoJSON img = new AnimacaoJSON(atvAnim.getAnimacao().getUrl(), atvAnim.getAtividade().getNome());
                    Uteis.logar(null, ctx + " -> update midia set titulo = '" + img.getAtividade() + "' where tipo = 0 and nome = '" + img.getUrl() + "';");
                    arrJson.add(img);
                }
            }
            Ordenacao.ordenarLista(arrJson, "atividade");
            mm.addAttribute(arrJson);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(EndpointDocController.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(value = "Obter fichas predefinidas", notes = "Obtém lista de fichas de treino predefinidas disponíveis no sistema, incluindo suas atividades e configurações")
    @ApiResponses(value = {
        @ApiResponse(code = 200, message = "Lista de fichas predefinidas obtida com sucesso", response = br.com.pacto.swagger.respostas.administracao.ExemploFichasPredefinidas.class)
    })
    @RequestMapping(value = "{ctx}/obterFichasPredefinidas", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap obterFichasPredefinidas(
            @ApiParam(value = "Contexto da empresa", required = true, defaultValue = "empresa1")
            @PathVariable String ctx) {
        ModelMap mm = new ModelMap();
        try {
            boolean treinoIndependente = false;
            try {
                treinoIndependente = SuperControle.independente(ctx);
            } catch (Exception e) {
                e.printStackTrace();
            }
            ConfiguracaoSistema configSeriesSet = configService.consultarPorTipo(ctx, ConfiguracoesEnum.AGRUPAMENTO_SERIES_SET);
            List<Ficha> l = fichaService.obterFichasPredefinidas(ctx, true);
            List<FichaJSON> arr = new ArrayList<FichaJSON>();
            Map<Integer, AtividadeJSON> mapaTodasAtividades = new HashMap<Integer, AtividadeJSON>();
            for (Ficha ficha : l) {
                FichaJSON fichaJSON = FichaJSON.obterJSON(treinoIndependente, null, null, ficha,
                        SuperControle.getUrlImagem(ctx), mapaTodasAtividades, configSeriesSet.getValorAsBoolean(), ctx, null);
                arr.add(fichaJSON);
            }
            mm.addAttribute(arr);
            mm.addAttribute(mapaTodasAtividades.values());
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(EndpointDocController.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(value = "Resetar senha de usuário", notes = "Reseta a senha de um usuário específico no sistema")
    @ApiResponses(value = {
        @ApiResponse(code = 200, message = "Senha resetada com sucesso", response = br.com.pacto.swagger.respostas.administracao.ExemploSenhaResetada.class)
    })
    @RequestMapping(value = "{ctx}/resetPwd", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap resetPwd(
            @ApiParam(value = "Contexto da empresa", required = true, defaultValue = "empresa1")
            @PathVariable String ctx,
            @ApiParam(value = "Nome de usuário para resetar a senha", required = true, defaultValue = "usuario123")
            @RequestParam final String userName,
            @ApiParam(value = "Nova senha para o usuário", required = true, defaultValue = "novaSenha123")
            @RequestParam final String pwd) {
        ModelMap mm = new ModelMap();
        try {
            Usuario u = usuarioDao.findObjectByAttribute(ctx, "userName", userName);
            if (u != null) {
                u.setSenha(Uteis.encriptar(pwd));
                usuarioDao.update(ctx, u);
                mm.addAttribute(STATUS_SUCESSO);
            }
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(EndpointDocController.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(value = "Povoar dados no sistema", notes = "Executa o serviço de povoamento de dados iniciais no sistema para a empresa especificada")
    @ApiResponses(value = {
        @ApiResponse(code = 200, message = "Dados povoados com sucesso", response = br.com.pacto.swagger.respostas.administracao.ExemploPovoamentoSucesso.class)
    })
    @RequestMapping(value = "{ctx}/povoar", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap povoar(
            @ApiParam(value = "Contexto da empresa", required = true, defaultValue = "empresa1")
            @PathVariable String ctx) {
        ModelMap mm = new ModelMap();
        try {
            PovoadorService povoador = (PovoadorService) UtilContext.getBean(PovoadorService.class);
            povoador.run(ctx);
            mm.addAttribute(STATUS_SUCESSO);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(EndpointDocController.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(value = "Povoar andamento de treinos", notes = "Executa o povoamento dos dados de andamento dos programas de treino no sistema")
    @ApiResponses(value = {
        @ApiResponse(code = 200, message = "Andamento de treinos povoado com sucesso", response = br.com.pacto.swagger.respostas.administracao.ExemploAndamentoPovoado.class)
    })
    @RequestMapping(value = "{ctx}/povoarAndamento", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap povoarAndamento(
            @ApiParam(value = "Contexto da empresa", required = true, defaultValue = "empresa1")
            @PathVariable String ctx) {
        ModelMap mm = new ModelMap();
        try {
            programaTreinoService.povoarAndamentoTreino(ctx);
            mm.addAttribute(STATUS_SUCESSO);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(EndpointDocController.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(value = "Obter mídias remotas", notes = "Obtém lista de mídias disponíveis no serviço remoto de administração de aplicativos")
    @ApiResponses(value = {
        @ApiResponse(code = 200, message = "Lista de mídias remotas obtida com sucesso", response = br.com.pacto.swagger.respostas.administracao.ExemploMidiasRemotas.class)
    })
    @RequestMapping(value = "{ctx}/obterMidiasRemotas", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap obterMidiasRemotas(
            @ApiParam(value = "Contexto da empresa", required = true, defaultValue = "empresa1")
            @PathVariable String ctx) {
        ModelMap mm = new ModelMap();
        List<Midia> resultado = new ArrayList<Midia>();
        try {
            List<Midia> lista = AdmAppWSConsumer.obterMidias(ctx);
            for (Midia midia: lista) {
                resultado.add(midia);
            }

            mm.addAttribute(RETURN, resultado);
        } catch (Exception e) {
            mm.addAttribute(STATUS_ERRO, e.getMessage());
            Logger.getLogger(EndpointDocController.class.getName()).log(Level.SEVERE, null, e);
        }
        return mm;
    }

    @ApiOperation(value = "Obter estatísticas de URLs", notes = "Obtém estatísticas de URLs acessadas no sistema (implementação comentada - retorna lista vazia)")
    @ApiResponses(value = {
        @ApiResponse(code = 200, message = "Estatísticas obtidas (implementação vazia)", response = br.com.pacto.swagger.respostas.administracao.ExemploEstatisticasURLs.class)
    })
    @RequestMapping(value = "/obterURLs", method = {RequestMethod.POST, RequestMethod.GET})
    public @ResponseBody
    ModelMap obterURLs(
            @ApiParam(value = "Filtrar apenas URLs de login e status", required = true, defaultValue = "true")
            @RequestParam Boolean clean) {
        ModelMap mm = new ModelMap();
        try {
//            List<StatsJSON> nova = new ArrayList<StatsJSON>();
//            if (clean) {
//                for (StatsJSON statsJSON : tmp) {
//                    if (statsJSON.getUrl().contains("/login") || statsJSON.getUrl().contains("/status")) {
//                        nova.add(statsJSON);
//                    }
//                }
//            }
//            Ordenacao.ordenarLista(clean ? nova : tmp, "url");
//            mm.addAttribute(clean ? nova : tmp);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(EndpointDocController.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(value = "Auditoria de dados", notes = "Endpoint de auditoria para consulta de alterações em entidades (implementação vazia - retorna lista vazia)")
    @ApiResponses(value = {
        @ApiResponse(code = 200, message = "Auditoria executada (implementação vazia)", response = br.com.pacto.swagger.respostas.administracao.ExemploAuditoria.class)
    })
    @RequestMapping(value = "{ctx}/audit", method = {RequestMethod.POST})
    public @ResponseBody
    ModelMap audit(
            @ApiParam(value = "Contexto da empresa", required = true, defaultValue = "empresa1")
            @PathVariable String ctx,
            @ApiParam(value = "Nome canônico da classe para auditoria", required = true, defaultValue = "br.com.pacto.bean.usuario.Usuario")
            @RequestParam final String classCanonical,
            @ApiParam(value = "Atributo da classe para filtrar auditoria", required = true, defaultValue = "nome")
            @RequestParam final String attribute,
            @ApiParam(value = "Valor do atributo para filtrar auditoria", required = true, defaultValue = "João Silva")
            @RequestParam Object value) {
        ModelMap mm = new ModelMap();
        try {

            mm.addAttribute(new ArrayList<>());
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(EndpointDocController.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(value = "Simular web service administrativo", notes = "Simula chamada ao web service administrativo para obter lista de empresas")
    @ApiResponses(value = {
        @ApiResponse(code = 200, message = "Simulação executada com sucesso", response = br.com.pacto.swagger.respostas.administracao.ExemploSimulacaoAdmWS.class)
    })
    @RequestMapping(value = "{ctx}/simularAdmWS", method = {RequestMethod.POST})
    public @ResponseBody
    ModelMap simularAdmWS(
            @ApiParam(value = "Contexto da empresa", required = true, defaultValue = "empresa1")
            @PathVariable String ctx,
            @ApiParam(value = "URL do web service para simulação", required = true, defaultValue = "http://localhost:8080/admws")
            @RequestParam final String url) {
        ModelMap mm = new ModelMap();
        try {
            AdmWSConsumer service = UtilContext.getBean(AdmWSConsumer.class);
            List<EmpresaWS> l = service.obterEmpresas(ctx);
            if (l != null && !l.isEmpty()) {
                mm.addAttribute(l);
            } else {
                mm.addAttribute("Empresas não encontradas");
            }
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(EndpointDocController.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(value = "Executar migração de dados", notes = "Executa migração de dados utilizando reflexão para chamar métodos específicos do serviço de migração")
    @ApiResponses(value = {
        @ApiResponse(code = 200, message = "Migração executada", response = br.com.pacto.swagger.respostas.administracao.ExemploMigracao.class)
    })
    @RequestMapping(value = "/executarMigracao", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap executarMigracao(
            @ApiParam(value = "Chave da empresa para migração ou 'todas' para todas as empresas", required = true, defaultValue = "empresa1")
            @RequestParam String key,
            @ApiParam(value = "Nome do método de migração a ser executado", required = true, defaultValue = "migrarDados")
            @RequestParam final String metodo) {
        ModelMap mm = new ModelMap();
        List<String> resultado = new ArrayList();
        try {
            MigracaoDadosJPAService ms = (MigracaoDadosJPAService) UtilContext.getBean(MigracaoDadosJPAService.class);
            if (key != null && metodo != null) {
                if (key.equals("todas")) {
                    Set<String> keys = EntityManagerFactoryService.getEmpresas().keySet();
                    for (String k : keys) {
                        try {
                            UtilReflection.invoke(ms, metodo, new Class[]{String.class}, new Object[]{k});
                            resultado.add(k + " " + STATUS_SUCESSO);
                        } catch (Exception e) {
                            resultado.add(STATUS_ERRO + " " + k + " " + e.getMessage());
                        }
                    }
                } else if (!key.isEmpty()) {
                    UtilReflection.invoke(ms, metodo, new Class[]{String.class}, new Object[]{key});
                    resultado.add(key + " " + STATUS_SUCESSO);
                }
            }
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(FotosJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        mm.addAttribute(resultado);
        return mm;
    }
    
    @ApiOperation(value = "Expirar créditos de personal", notes = "Executa a expiração de créditos de personal trainer vencidos na data atual")
    @ApiResponses(value = {
        @ApiResponse(code = 200, message = "Créditos expirados com sucesso", response = br.com.pacto.swagger.respostas.administracao.ExemploCreditosExpirados.class)
    })
    @RequestMapping(value = "{ctx}/expirarCreditos", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap expirarCreditos(
            @ApiParam(value = "Contexto da empresa", required = true, defaultValue = "empresa1")
            @PathVariable String ctx) {
        ModelMap mm = new ModelMap();
        try {
            CreditoPersonalService creditoService = (CreditoPersonalService) UtilContext.getBean(CreditoPersonalService.class);
            creditoService.expirarCreditos(ctx, Calendario.hoje());
            mm.addAttribute(STATUS_SUCESSO);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(EndpointDocController.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(value = "Atualizar professor carteira de programa vigente", notes = "Atualiza o professor responsável pela carteira de programas de treino vigentes para uma matrícula específica")
    @ApiResponses(value = {
        @ApiResponse(code = 200, message = "Professor carteira atualizado com sucesso", response = br.com.pacto.swagger.respostas.administracao.ExemploProfessorCarteiraAtualizado.class)
    })
    @RequestMapping(value = "{ctx}/atualizarProfessorCarteiraProgramaVirgente", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap atualizarProfessorCarteiraProgramaVirgente(
            @ApiParam(value = "Contexto da empresa", required = true, defaultValue = "empresa1")
            @PathVariable String ctx,
            @ApiParam(value = "Matrícula do aluno para atualizar professor carteira", required = true, defaultValue = "12345")
            @RequestParam String matricula) {
        ModelMap mm = new ModelMap();
        try {
            programaTreinoService.atualizarProfessorCarteiraProgramaVirgente(ctx, matricula);
            mm.addAttribute(STATUS_SUCESSO);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(EndpointDocController.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }
}
