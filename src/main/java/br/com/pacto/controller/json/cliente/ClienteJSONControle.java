/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.cliente;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.cliente.ClienteObservacao;
import br.com.pacto.bean.cliente.ClienteObservacaoJSON;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.agendamento.PerfilDISCDTO;
import br.com.pacto.controller.json.aluno.AlunoCadastroSimplesDTO;
import br.com.pacto.controller.json.aluno.AlunoController;
import br.com.pacto.controller.json.aluno.AlunoDTO;
import br.com.pacto.controller.json.aluno.AvaliacaoProfessorDTO;
import br.com.pacto.controller.json.aluno.HistoricoContatoAlunoVO;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.controller.json.gestao.ClienteDadosTotalPassDTO;
import br.com.pacto.controller.json.programa.ProgramaTreinoJSONControle;
import br.com.pacto.controller.json.usuario.UsuarioJSONControle;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.cliente.ClienteObservacaoService;
import br.com.pacto.service.intf.cliente.ClienteSinteticoService;
import br.com.pacto.service.intf.usuario.FotoService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.swagger.respostas.ExemploRespostaString;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import br.com.pacto.swagger.respostas.cliente.ExemploRespostaConsultarObservacoesAlunoApp;
import br.com.pacto.swagger.respostas.cliente.ExemploRespostaConsultarObservacoesCliente;
import br.com.pacto.swagger.respostas.cliente.ExemploRespostaConsultarLocalAcessoPorNFC;
import br.com.pacto.swagger.respostas.cliente.ExemploRespostaLocaisAcesso;
import br.com.pacto.swagger.respostas.cliente.ExemploRespostaHistoricoContato;
import br.com.pacto.swagger.respostas.cliente.ExemploRespostaHistoricoPresenca;
import br.com.pacto.swagger.respostas.ExemploRespostaVazia;
import br.com.pacto.swagger.respostas.cliente.ExemploRespostaGravarObservacaoCorrigida;
import br.com.pacto.swagger.respostas.cliente.ExemploRespostaAtualizarFrequenciaSemanal;
import br.com.pacto.swagger.respostas.cliente.ExemploRespostaConsultarQuantidadeAcessosAgrupadosDia;
import br.com.pacto.swagger.respostas.cliente.ExemploRespostaCadastroAlunoSimplificado;
import br.com.pacto.swagger.respostas.cliente.ExemploRespostaSincronizarClienteSintetico;
import br.com.pacto.swagger.respostas.cliente.ExemploRespostaSincronizarMatriculaClienteSintetico;
import br.com.pacto.swagger.respostas.cliente.ExemploRespostaAssinaturaDigitalContratosAluno;
import br.com.pacto.swagger.respostas.cliente.ExemploRespostaAssinaturaDigitalContratosAlunoCrypt;
import br.com.pacto.swagger.respostas.cliente.ExemploRespostaBuscarAssinaturaDigitalPorContrato;
import br.com.pacto.swagger.respostas.cliente.ExemploRespostaIncluirAssinaturaDigitalContrato;
import br.com.pacto.swagger.respostas.cliente.ExemploRespostaAvaliarMultiplosProfessores;
import br.com.pacto.swagger.respostas.cliente.ExemploRespostaAvaliarProfessor;
import br.com.pacto.swagger.respostas.cliente.ExemploRespostaAvaliarProfessorV2;
import springfox.documentation.annotations.ApiIgnore;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import servicos.integracao.zw.IntegracaoCadastrosWSConsumer;
import servicos.integracao.zw.json.ColetorJSON;
import br.com.pacto.swagger.respostas.cliente.ExemploRespostaAssinaturaDigitalContratosAlunoCrypt;
import br.com.pacto.swagger.respostas.cliente.ExemploRespostaBuscarAssinaturaDigitalPorContratoCrypt;
import br.com.pacto.swagger.respostas.cliente.ExemploRespostaIncluirAssinaturaDigitalContratoAluno;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR> Felipe
 */
@Api(tags = "Clientes")
@Controller
@RequestMapping("/cliente")
public class ClienteJSONControle extends SuperControle {

    private final ClienteSinteticoService clienteSinteticoService;
    private final ClienteObservacaoService clienteObservacaoService;
    private final UsuarioService usuarioService;
    private final FotoService fotoService;

    @Autowired
    public ClienteJSONControle(ClienteSinteticoService clienteSinteticoService, ClienteObservacaoService clienteObservacaoService,
                               UsuarioService usuarioService, FotoService fotoService) {
        this.clienteSinteticoService = clienteSinteticoService;
        this.clienteObservacaoService = clienteObservacaoService;
        this.usuarioService = usuarioService;
        this.fotoService = fotoService;
    }

    @ApiOperation(
            value = "Atualizar frequência semanal de clientes",
            notes = "Atualiza a frequência semanal de treino para um ou mais clientes no sistema. " +
                    "Recebe um JSON com os códigos dos clientes e suas respectivas frequências semanais para atualização no banco de dados.",
            tags = "Clientes"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaAtualizarFrequenciaSemanal.class)
    })
    @RequestMapping(value = "{ctx}/atualizarFrequenciaSemanal", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap atualizarFrequenciaSemanal(
            @ApiParam(value = "Contexto da empresa", defaultValue = "empresa1", required = true)
            @PathVariable String ctx,
            @ApiParam(value = "JSON contendo array com códigos dos clientes e suas frequências semanais",
                     defaultValue = "[{\"codigosClientes\":\"1,2,3\",\"frequenciaSemanal\":3}]", required = true)
            @RequestParam String dadosJSON) {
        ModelMap mm = new ModelMap();
        try {
            JSONArray lista = new JSONArray(dadosJSON);
            for (int e = 0; e < lista.length(); e++) {
                JSONObject obj = lista.getJSONObject(e);
                String sql = "update ClienteSintetico set frequenciaSemanal = " + obj.getInt("frequenciaSemanal") + " where codigoCliente in (" + obj.getString("codigosClientes") + ")";
                clienteSinteticoService.executeNativeSQL(ctx, sql);
            }
            mm.addAttribute(RETURN, "Sucesso");
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ClienteJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(
            value = "Consultar observações do cliente",
            notes = "Consulta todas as observações cadastradas para um cliente específico através de sua matrícula. " +
                    "Retorna uma lista com as observações incluindo dados do usuário que as cadastrou.",
            tags = "Clientes"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaConsultarObservacoesCliente.class)
    })
    @ResponseBody
    @RequestMapping(value = "{contexto}/observacoes", method = RequestMethod.POST)
    public ModelMap observacoes(
            @ApiParam(value = "Contexto da empresa", defaultValue = "empresa1", required = true)
            @PathVariable String contexto,
            @ApiParam(value = "Matrícula do cliente para consulta das observações", defaultValue = "12345", required = true)
            @RequestParam("matricula") String matriculaCliente,
            HttpServletRequest httpRequest) {
        ModelMap modelMap = new ModelMap();
        try {
            final List<ClienteObservacao> observacoes =
                    clienteObservacaoService.consultarObservacoesPorMatriculaCliente(contexto, matriculaCliente);

            modelMap.addAttribute(RETURN, mapearObservacoes(contexto, observacoes, httpRequest));

        } catch (Exception ex) {
            modelMap.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ClienteJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return modelMap;
    }

    @ApiOperation(
            value = "Gravar nova observação do cliente",
            notes = "Cadastra uma nova observação para um cliente específico. A observação é vinculada ao usuário que a está cadastrando " +
                    "e pode ser marcada como importante. Retorna os dados da observação criada.",
            tags = "Clientes"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaGravarObservacaoCorrigida.class)
    })
    @ResponseBody
    @RequestMapping(value = "{contexto}/gravarobservacao", method = RequestMethod.POST)
    public ModelMap gravarObservacao(
            @ApiParam(value = "Contexto da empresa", defaultValue = "empresa1", required = true)
            @PathVariable String contexto,
            @ApiParam(value = "Nome de usuário do profissional que está cadastrando a observação", defaultValue = "professor1", required = true)
            @RequestParam final String userName,
            @ApiParam(value = "Matrícula do cliente para o qual será cadastrada a observação", defaultValue = "12345", required = true)
            @RequestParam String matricula,
            @ApiParam(value = "Texto da observação a ser cadastrada", defaultValue = "Cliente apresentou boa evolução no treino de membros superiores", required = true)
            @RequestParam String observacao,
            @ApiParam(value = "Indica se a observação deve ser marcada como importante", defaultValue = "false", required = true)
            @RequestParam final Boolean importante,
            HttpServletRequest request) {
        ModelMap modelMap = new ModelMap();
        try {
            final Usuario usuario = usuarioService.obterPorAtributo(contexto, "username", userName);
            if (usuario == null) {
                return reportarErro(modelMap, "mobile.usuarioinvalido");
            }
            ClienteSintetico cliente = clienteSinteticoService.consultarPorMatricula(contexto, matricula);
            ClienteObservacao clienteObservacao = new ClienteObservacao();
            clienteObservacao.setCliente(cliente);
            clienteObservacao.setDataObservacao(Calendario.hoje());
            clienteObservacao.setUsuario_codigo(usuario.getCodigo());
            clienteObservacao.setImportante(importante);
            clienteObservacao.setObservacao(observacao);
            if (UteisValidacao.emptyString(observacao)) {
                return reportarErro(modelMap, "É necessário preencher a observação.");
            }
            clienteSinteticoService.gravarObservacaoCliente(contexto, clienteObservacao);
            modelMap.addAttribute(RETURN, mapearObservacao(contexto, request, clienteObservacao, usuario));
        } catch (Exception ex) {
            modelMap.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ClienteJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return modelMap;
    }

    @ApiOperation(
            value = "Excluir observação do cliente",
            notes = "Remove uma observação específica do cliente através do ID da observação. " +
                    "A exclusão é permanente e não pode ser desfeita.",
            tags = "Clientes"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaVazia.class)
    })
    @ResponseBody
    @RequestMapping(value = "{contexto}/observacoes/delete/{id}", method = RequestMethod.DELETE)
    public ModelMap excluir(
            @ApiParam(value = "Contexto da empresa", defaultValue = "empresa1", required = true)
            @PathVariable("contexto") String contexto,
            @ApiParam(value = "ID único da observação a ser excluída", defaultValue = "123", required = true)
            @PathVariable("id") Integer idObservacao) {
        ModelMap modelMap = new ModelMap();
        try {
            clienteObservacaoService.excluir(contexto, idObservacao);
        } catch (Exception ex) {
            modelMap.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ClienteJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return modelMap;
    }

    private List<Map<String, Object>> mapearObservacoes(String contexto, List<ClienteObservacao> observacoes,
                                                        HttpServletRequest request) throws Exception {
        final List<Map<String, Object>> observacoesMapeadas = new ArrayList<Map<String, Object>>();
        for (final ClienteObservacao clienteObservacao : observacoes) {
            Usuario usu = usuarioService.obterPorId(contexto, clienteObservacao.getUsuario_codigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            observacoesMapeadas.add(mapearObservacao(contexto, request, clienteObservacao, usu));
        }
        return observacoesMapeadas;
    }

    private Map<String, Object> mapearObservacao(String contexto, HttpServletRequest request, ClienteObservacao clienteObservacao, Usuario usu) throws Exception {
        final Map<String, Object> observacaoMapeada = new HashMap<String, Object>();
        // dados da observacao
        observacaoMapeada.put("codigo", clienteObservacao.getCodigo());
        observacaoMapeada.put("observacao", clienteObservacao.getObservacao());
        observacaoMapeada.put("data", clienteObservacao.getDataObservacaoApresentar());
        observacaoMapeada.put("dataLong", clienteObservacao.getDataObservacao().getTime());
        observacaoMapeada.put("importante", clienteObservacao.getImportante());

        // dados do professor
        observacaoMapeada.put("nome", usu.getNomeApresentar());
        observacaoMapeada.put("username",usu.getUserName());
        observacaoMapeada.put("srcImg", fotoService.carregarFoto(contexto, usu, false, request));
        return observacaoMapeada;
    }

    @ApiOperation(
            value = "Consultar locais de acesso por empresa",
            notes = "Consulta todos os locais de acesso (coletores) disponíveis para uma empresa específica. " +
                    "Retorna uma lista com os coletores configurados para controle de acesso dos clientes.",
            tags = "Local de Acesso"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaLocaisAcesso.class)
    })
    @RequestMapping(value = "{ctx}/consultarLocaisAcesso", method = {RequestMethod.POST, RequestMethod.GET})
    public @ResponseBody
    ModelMap consultarLocaisAcesso(
            @ApiParam(value = "Contexto da empresa", defaultValue = "empresa1", required = true)
            @PathVariable String ctx,
            @ApiParam(value = "Código da empresa para consulta dos locais de acesso", defaultValue = "1", required = true)
            @RequestParam Integer empresa) {

        ModelMap mm = new ModelMap();
        try {
            List<ColetorJSON> lista = clienteSinteticoService.consultarLocaisAcesso(ctx, empresa);
            mm.addAttribute("locaisAcesso", lista);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ClienteJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(
            value = "Consultar local de acesso por NFC",
            notes = "Consulta um local de acesso específico através do código NFC do coletor. " +
                    "Utilizado para identificar o local onde o cliente está tentando acessar.",
            tags = "Local de Acesso"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaConsultarLocalAcessoPorNFC.class)
    })
    @RequestMapping(value = "{ctx}/consultarLocalAcessoPorNFC", method = {RequestMethod.POST, RequestMethod.GET})
    public @ResponseBody
    ModelMap consultarLocalAcessoPorNFC(
            @ApiParam(value = "Contexto da empresa", defaultValue = "empresa1", required = true)
            @PathVariable String ctx,
            @ApiParam(value = "Código NFC do coletor para identificação do local", defaultValue = "NFC123456", required = true)
            @RequestParam String nfc) {

        ModelMap mm = new ModelMap();
        try {
            ColetorJSON local = clienteSinteticoService.consultarLocalAcessoPorNFC(ctx, nfc);
            mm.addAttribute(local);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ClienteJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(
            value = "Consultar observações do aluno (App)",
            notes = "Consulta as observações de um aluno específico com suporte a paginação para uso em aplicativos móveis. " +
                    "Retorna as observações formatadas com dados do usuário e foto do profissional que as cadastrou.",
            tags = "Clientes"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaConsultarObservacoesAlunoApp.class)
    })
    @RequestMapping(value = "{ctx}/app/consultarObservacoesDeAluno", method = RequestMethod.GET)
    public @ResponseBody
    ModelMap consultarObservacoesDeAlunoApp(
            @ApiParam(value = "Contexto da empresa", defaultValue = "empresa1", required = true)
            @PathVariable String ctx,
            @ApiParam(value = "Matrícula do aluno para consulta das observações", defaultValue = "12345", required = true)
            @RequestParam String matriculaAluno,
            @ApiParam(value = "Índice inicial para paginação", defaultValue = "0")
            @RequestParam(required = false) Integer index,
            @ApiParam(value = "Número máximo de resultados por página", defaultValue = "10")
            @RequestParam(value = "max", required = false) Integer maxResult,
            HttpServletRequest request){

        ModelMap modelMap = new ModelMap();
        try {
            final List<ClienteObservacao> observacoes =
                    clienteObservacaoService.consultarObservacoesPorMatriculaClienteApp(ctx, matriculaAluno, maxResult == null ? 0: maxResult, index == null ? 0 : index);

            List<ClienteObservacaoJSON> observacaoJSONS = new ArrayList<ClienteObservacaoJSON>();
            for(ClienteObservacao co: observacoes)
            {   Usuario usu = usuarioService.obterPorId(ctx, co.getUsuario_codigo());
                ClienteObservacaoJSON observacaoJSON = new ClienteObservacaoJSON(co, usu);
                String foto = null;
                if(co.getUsuario_codigo() != null)
                {
                    foto = usu.getFotoKeyApp();
                    if(UteisValidacao.emptyString(foto))
                    {
                        foto = fotoService.defineURLFotoPessoa(request, "", usu.getIdPessoa(), true, ctx, true);
                    }
                }
                observacaoJSON.setFoto(foto);
                observacaoJSONS.add(observacaoJSON);
            }
            modelMap.addAttribute("sucesso", observacaoJSONS);

        } catch (Exception ex) {
            modelMap.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ClienteJSONControle.class.getName()).log(Level.SEVERE, ex.getMessage(), ex);
        }
        return modelMap;
    }


    @ApiOperation(
            value = "Remover observação (App)",
            notes = "Remove uma observação específica do cliente através do código da observação. " +
                    "Endpoint específico para uso em aplicativos móveis.",
            tags = "Clientes"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaVazia.class)
    })
    @RequestMapping(value = "{ctx}/app/removerObservacao", method = RequestMethod.DELETE)
    public @ResponseBody ModelMap removerObservacaoApp(
            @ApiParam(value = "Contexto da empresa", defaultValue = "empresa1", required = true)
            @PathVariable String ctx,
            @ApiParam(value = "Código único da observação a ser removida", defaultValue = "123", required = true)
            @RequestParam Integer codigoObservacao) {
        ModelMap modelMap = new ModelMap();
        try {
            clienteObservacaoService.excluir(ctx, codigoObservacao);
        } catch (Exception ex) {
            modelMap.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ClienteJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        modelMap.addAttribute(STATUS_SUCESSO, "Observação removida com sucesso");
        return modelMap;
    }

    @ApiOperation(
            value = "Consultar acessos de um cliente agrupados por dia da semana",
            notes = "Consulta a quantidade de acessos/treinos realizados por um aluno agrupados por dia da semana em um período específico. " +
                    "Retorna estatísticas de frequência semanal do cliente para análise de padrões de treino.",
            tags = "Clientes"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaConsultarQuantidadeAcessosAgrupadosDia.class)
    })
    @RequestMapping(value = "{ctx}/app/consultarQuantidadeAcessosClientesAgrupadosDia", method = RequestMethod.GET)
    public @ResponseBody
    ModelMap consultarQuantidadeAcessosClientesAgrupadosDia(
            @ApiParam(value = "Contexto da empresa", defaultValue = "empresa1", required = true)
            @PathVariable String ctx,
            @ApiParam(value = "Código do aluno para consulta dos acessos", defaultValue = "123", required = true)
            @RequestParam Integer codigoAluno,
            @ApiParam(value = "Nome de usuário para validação", defaultValue = "professor1", required = true)
            @RequestParam final String username,
            @ApiParam(value = "Data inicial do período em timestamp (milissegundos)", defaultValue = "1704067200000", required = true)
            @RequestParam Long dataInicial,
            @ApiParam(value = "Data final do período em timestamp (milissegundos)", defaultValue = "1735689599000", required = true)
            @RequestParam Long dataFinal) throws ServiceException {
        ModelMap modelMap = new ModelMap();
        ModelMap resultado = new ModelMap();

        if (SuperControle.independente(ctx)) {
            resultado.addAttribute(RETURN, clienteSinteticoService.consultarTreinosRealizadosPorDiaSemana(ctx, dataInicial, dataFinal, codigoAluno, username));
        } else {
            try {
                IntegracaoCadastrosWSConsumer integracaoWS = UtilContext.getBean(IntegracaoCadastrosWSConsumer.class);
                final String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg);
                JSONObject json = new JSONObject(integracaoWS.consultarQuantidadeAcessosClientesAgrupadosDia(url, ctx, codigoAluno, dataInicial, dataFinal));

                if (json.has("acessosDiarios")) {
                    JSONObject frequencia = (JSONObject) json.get("acessosDiarios");
                    modelMap.addAttribute("SEG", frequencia.has("SEG") ? frequencia.get("SEG") : 0);
                    modelMap.addAttribute("TER", frequencia.has("TER") ? frequencia.get("TER") : 0);
                    modelMap.addAttribute("QUA", frequencia.has("QUA") ? frequencia.get("QUA") : 0);
                    modelMap.addAttribute("QUI", frequencia.has("QUI") ? frequencia.get("QUI") : 0);
                    modelMap.addAttribute("SEX", frequencia.has("SEX") ? frequencia.get("SEX") : 0);
                    modelMap.addAttribute("SAB", frequencia.has("SAB") ? frequencia.get("SAB") : 0);
                    modelMap.addAttribute("DOM", frequencia.has("DOM") ? frequencia.get("DOM") : 0);
                    resultado.addAttribute(STATUS_SUCESSO, modelMap);
                } else {
                    resultado.addAttribute(STATUS_ERRO, "Não foi possivel obter a frequência do aluno.");
                }
            } catch (Exception ex) {
                resultado.addAttribute(STATUS_ERRO, ex.getMessage());
            }
        }
        return resultado;
    }

    @ApiOperation(
            value = "Cadastrar aluno simplificado",
            notes = "Cadastra um novo aluno no sistema utilizando um formulário simplificado com os dados essenciais. " +
                    "Utilizado para cadastros rápidos de alunos com informações básicas.",
            tags = "Clientes"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaCadastroAlunoSimplificado.class)
    })
    @RequestMapping(value = "{ctx}/simplificado", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap submitTreino(
            @ApiParam(value = "Contexto da empresa", defaultValue = "empresa1", required = true)
            @PathVariable String ctx,
            @ApiParam(value = "Dados do aluno para cadastro simplificado", required = true)
            @RequestBody AlunoCadastroSimplesDTO alunoDTO) {
        ModelMap mm = new ModelMap();
        try {
            mm.addAttribute(RETURN, clienteSinteticoService.cadastrarAlunoPersonalFit(new AlunoDTO(alunoDTO), ctx));
        } catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            leaveAcao();
        }
        return mm;
    }

    @ApiOperation(
            value = "Sincronizar dados do cliente sintético",
            notes = "Sincroniza os dados dos clientes sintéticos com o sistema externo ZW. " +
                    "Realiza a atualização das informações dos clientes para manter a consistência entre os sistemas.",
            tags = "Clientes"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaSincronizarClienteSintetico.class)
    })
    @RequestMapping(value = "{ctx}/sincronizar-dados-cliente-sintetico", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap sincronizarDadosClienteSintetico(
            @ApiParam(value = "Contexto da empresa", defaultValue = "empresa1", required = true)
            @PathVariable String ctx) {
        ModelMap mm = new ModelMap();
        try {
            return mm.addAttribute(RETURN, clienteSinteticoService.sincronizarDadosClienteSintetico(ctx));
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(UsuarioJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(
            value = "Sincronizar matrícula do cliente sintético",
            notes = "Sincroniza as matrículas dos clientes sintéticos com o sistema ZW para uma empresa específica. " +
                    "Garante que as matrículas estejam atualizadas e consistentes entre os sistemas.",
            tags = "Clientes"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaSincronizarMatriculaClienteSintetico.class)
    })
    @RequestMapping(value = "{ctx}/sincronizar-matricula-clientesintetico/{empresaZW}", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap sincronizarMatriculaClienteSintetico(
            @ApiParam(value = "Contexto da empresa", defaultValue = "empresa1", required = true)
            @PathVariable String ctx,
            @ApiParam(value = "Código da empresa ZW para sincronização", defaultValue = "1", required = true)
            @PathVariable Integer empresaZW) {
        ModelMap mm = new ModelMap();
        try {
            return mm.addAttribute(RETURN, clienteSinteticoService.sincronizarMatriculaClienteSintetico(ctx, empresaZW));
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(UsuarioJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(
            value = "Buscar assinatura digital de contratos do aluno",
            notes = "Consulta as assinaturas digitais dos contratos de um aluno específico. " +
                    "Permite validação opcional das assinaturas encontradas.",
            tags = "Clientes"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaAssinaturaDigitalContratosAluno.class)
    })
    @RequestMapping(value = "{ctx}/aluno-contrato-assinatura-digital/{matricula}/{empresa}", method = RequestMethod.GET)
    public @ResponseBody
    ModelMap assinaturaDigitalContratosAluno(
            @ApiParam(value = "Contexto da empresa", defaultValue = "empresa1", required = true)
            @PathVariable String ctx,
            @ApiParam(value = "Matrícula do aluno", defaultValue = "12345", required = true)
            @PathVariable String matricula,
            @ApiParam(value = "Código da empresa", defaultValue = "1", required = true)
            @PathVariable String empresa,
            @ApiParam(value = "Indica se deve validar as assinaturas", defaultValue = "false")
            @RequestParam(value = "validar", required = false) Boolean validar) {
        ModelMap mm = new ModelMap();
        try {
            if(Aplicacao.isTrue(Aplicacao.consultaContratoAssinaturaDigital)){
                return mm.addAttribute(RETURN, clienteSinteticoService.buscarAssinaturaDigitalContratosAluno(ctx, matricula, empresa, validar));
            }
            return mm.addAttribute(RETURN, "[]");
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(UsuarioJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(value = "Consultar assinaturas digitais de contratos do aluno (criptografado)",
                  notes = "<strong>Permissões:</strong> Requer autenticação via chave de empresa (ctx)<br/>" +
                          "<strong>Funcionalidade:</strong> Consulta as assinaturas digitais dos contratos de um aluno específico retornando os dados em formato criptografado para maior segurança<br/>" +
                          "<strong>Comportamento:</strong> Descriptografa os dados recebidos no corpo da requisição, consulta as assinaturas digitais dos contratos do aluno e retorna as informações criptografadas<br/>" +
                          "<strong>Retorno:</strong>Dados criptografados das assinaturas digitais ou mensagem de erro criptografada",
                  tags = "Contratos")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Consulta realizada com sucesso", response = ExemploRespostaAssinaturaDigitalContratosAlunoCrypt.class)
    })
    @RequestMapping(value = "{ctx}/rB3mU7oX8eS4eA6iC4lH9eY2fU6sF5kR", method = RequestMethod.PATCH)
    public @ResponseBody
    ModelMap assinaturaDigitalContratosAlunoCrypt(@ApiParam(value = "Chave de identificação da empresa no sistema", defaultValue = "empresa123") @PathVariable String ctx,
                                                  @ApiParam(value = "Dados criptografados contendo as informações necessárias para consulta das assinaturas digitais. Deve conter um JSON criptografado com os campos: matricula (matrícula do aluno), empresa (código da empresa) e validar (boolean para validação das assinaturas). Os dados devem ser criptografados usando o método de criptografia padrão do sistema.",
                                                           defaultValue = "U2FsdGVkX1+vupppZksvRf5pq5g5XjFRIipRkwB0K1Y96Qsv2Lm+31cmzaAILwyt") @RequestBody String content) {
        ModelMap mm = new ModelMap();
        try {
            JSONObject o = new JSONObject(Uteis.decryptUserData(content));
            String matricula = o.optString("matricula");
            String empresa = o.optString("empresa");
            boolean validar = o.optBoolean("validar");

            String returnAssinatura = "[]";
            if(Aplicacao.isTrue(Aplicacao.consultaContratoAssinaturaDigital)){
                returnAssinatura = clienteSinteticoService.buscarAssinaturaDigitalContratosAluno(ctx, matricula, empresa, validar);
            }

            mm.addAttribute(RETURN, Uteis.encryptUserData(returnAssinatura));
        } catch (Exception ex) {
            String erro = ex.getMessage();
            try {
                erro = Uteis.encryptUserData(ex.getMessage());
            } catch (Exception ignored) {}
            mm.addAttribute(STATUS_ERRO, erro);
            Logger.getLogger(UsuarioJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(
            value = "Buscar assinatura digital por código do contrato",
            notes = "Consulta a assinatura digital de um contrato específico através do código do contrato. " +
                    "Permite localizar assinaturas digitais usando o identificador único do contrato.",
            tags = "Clientes"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaBuscarAssinaturaDigitalPorContrato.class)
    })
    @RequestMapping(value = "{ctx}/aluno-contrato-assinatura-digital-by-contrato/{contrato}/{empresa}", method = RequestMethod.GET)
    public @ResponseBody
    ModelMap assinaturaDigitalContratosAlunoByContrato(
            @ApiParam(value = "Contexto da empresa", defaultValue = "empresa1", required = true)
            @PathVariable String ctx,
            @ApiParam(value = "Código do contrato", defaultValue = "CONT123", required = true)
            @PathVariable String contrato,
            @ApiParam(value = "Código da empresa", defaultValue = "1", required = true)
            @PathVariable String empresa) {
        ModelMap mm = new ModelMap();
        try {
            if(Aplicacao.isTrue(Aplicacao.consultaContratoAssinaturaDigital)){
                Logger.getLogger("CONSULTA_CONTRATO_ASSINATURA").log(Level.SEVERE, null, "teste");
                return mm.addAttribute(RETURN, clienteSinteticoService.buscarAssinaturaDigitalContratosAlunoByContrato(ctx, contrato, empresa));
            }
            return mm.addAttribute(RETURN, "[]");
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(UsuarioJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(value = "Buscar assinatura digital por contrato específico (criptografado)",
                  notes = "Busca a assinatura digital de um contrato específico do aluno através de dados criptografados. " +
                         "Este endpoint recebe os parâmetros de consulta (código do contrato e empresa) em formato criptografado " +
                         "no corpo da requisição e retorna os dados da assinatura digital também criptografados para maior segurança. " +
                         "Utilizado para consultas seguras de assinaturas digitais de contratos de matrícula, renovação ou outros tipos " +
                         "de contratos da academia. Retorna array vazio criptografado se o contrato não possuir assinatura digital " +
                         "ou se a funcionalidade estiver desabilitada no sistema.",
                  tags = "Contratos")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Consulta realizada com sucesso",
                        response = ExemploRespostaBuscarAssinaturaDigitalPorContratoCrypt.class)
    })
    @RequestMapping(value = "{ctx}/kC8zT9lL2xH6qG9wB0mP9vR1mM2bA7aE", method = RequestMethod.PATCH)
    public @ResponseBody
    ModelMap assinaturaDigitalContratosAlunoByContratoCrypt(@ApiParam(value = "Contexto da aplicação (chave de identificação da empresa/unidade)",
                                                                      defaultValue = "academia123")
                                                            @PathVariable String ctx,
                                                            @ApiParam(value = "Dados criptografados contendo as informações necessárias para consulta da assinatura digital. " +
                                                                             "Deve conter um JSON criptografado com os campos 'contrato' (código do contrato) e 'empresa' (código da empresa). " +
                                                                             "Os dados devem ser criptografados usando o método de criptografia padrão do sistema antes do envio.",
                                                                      defaultValue = "U2FsdGVkX1+vupppZksvRf5pq5g5XjFRIipRkwB0K1Y96Qsv2Lm+31cmzaAILwytX0dugpn1WE3wAN9AEh9jVnb+oq5gZNeCGjU=")
                                                            @RequestBody String content) {
        ModelMap mm = new ModelMap();
        try {
            JSONObject o = new JSONObject(Uteis.decryptUserData(content));
            String contrato = o.optString("contrato");
            String empresa = o.optString("empresa");

            String returnAssinatura = "[]";
            if(Aplicacao.isTrue(Aplicacao.consultaContratoAssinaturaDigital)){
                Logger.getLogger("CONSULTA_CONTRATO_ASSINATURA").log(Level.SEVERE, null, "teste");
                returnAssinatura = clienteSinteticoService.buscarAssinaturaDigitalContratosAlunoByContrato(ctx, contrato, empresa);
            }

            mm.addAttribute(RETURN, Uteis.encryptUserData(returnAssinatura));
        } catch (Exception ex) {
            String erro = ex.getMessage();
            try {
                erro = Uteis.encryptUserData(ex.getMessage());
            } catch (Exception ignored) {}
            mm.addAttribute(STATUS_ERRO, erro);
            Logger.getLogger(UsuarioJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(value = "Incluir assinatura digital em contrato do aluno",
                  notes = "Inclui uma assinatura digital em um contrato específico do aluno. " +
                          "A assinatura é processada e armazenada de forma segura no sistema, " +
                          "permitindo a validação posterior da autenticidade do documento. " +
                          "O sistema registra a data e hora da assinatura, além de gerar um hash " +
                          "de validação para garantir a integridade da assinatura digital.",
                  tags = "Contratos")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Assinatura digital incluída com sucesso no contrato",
                        response = ExemploRespostaIncluirAssinaturaDigitalContratoAluno.class)
    })
    @RequestMapping(value = "{ctx}/aluno-contrato-assinatura-digital-incluir/{contrato}", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap incluirAssinaturaDigitalContratoAluno(
            @ApiParam(value = "Chave de identificação da empresa no sistema",
                     defaultValue = "empresa123")
            @PathVariable String ctx,

            @ApiParam(value = "Código identificador único do contrato onde será incluída a assinatura digital. " +
                             "Este código é gerado automaticamente pelo sistema no momento da criação do contrato " +
                             "e é utilizado para localizar o documento específico que será assinado digitalmente.",
                     defaultValue = "12345")
            @PathVariable String contrato,

            @ApiParam(value = "Dados da assinatura digital em formato base64. Contém a representação digital " +
                             "da assinatura do aluno, incluindo informações biométricas e de validação. " +
                             "Os dados devem estar codificados em base64 para garantir a integridade " +
                             "durante a transmissão e armazenamento no sistema.",
                     defaultValue = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==")
            @RequestBody String assinatura) {
        ModelMap mm = new ModelMap();
        try {
            return mm.addAttribute(RETURN, clienteSinteticoService.incluirAssinaturaDigitalContratoAluno(ctx, contrato, assinatura));
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(UsuarioJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(
            value = "Avaliar múltiplos professores",
            notes = "Permite que um cliente avalie múltiplos professores simultaneamente. " +
                    "Recebe uma lista de avaliações com notas e comentários para cada professor.",
            tags = "Clientes"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)",
                        response = br.com.pacto.swagger.respostas.cliente.ExemploRespostaAvaliarMultiplosProfessores.class)
    })
    @ResponseBody
    @RequestMapping(value = "{ctx}/avaliacoes-professores", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> avaliaProfessores (
            @ApiParam(value = "Contexto da empresa", defaultValue = "empresa1", required = true)
            @PathVariable String ctx,
            @ApiParam(value = "Lista de avaliações dos professores", required = true)
            @RequestBody List<AvaliacaoProfessorDTO> avaliacaoProfessorDTO
    ) {
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.avaliaProfessores(avaliacaoProfessorDTO, ctx));
        } catch (Exception e) {
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Avaliar professor",
            notes = "Permite que um cliente avalie um professor específico. " +
                    "Recebe uma avaliação com nota e comentário para o professor.",
            tags = "Clientes"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)",
                        response = br.com.pacto.swagger.respostas.cliente.ExemploRespostaAvaliarProfessor.class)
    })
    @ResponseBody
    @RequestMapping(value = "{ctx}/avaliacao-professor", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> avaliaProfessor (
            @ApiParam(value = "Contexto da empresa", defaultValue = "empresa1", required = true)
            @PathVariable String ctx,
            @ApiParam(value = "Dados da avaliação do professor", required = true)
            @RequestBody AvaliacaoProfessorDTO avaliacaoProfessorDTO
    ) {
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.avaliaProfessor(avaliacaoProfessorDTO, ctx));
        } catch (Exception e) {
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Avaliar professor (App Treino v2)",
            notes = "Nova versão do endpoint para avaliação de professor específico para o aplicativo de treino. " +
                    "Inclui funcionalidades aprimoradas e notificações automáticas.",
            tags = "Clientes"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)",
                        response = br.com.pacto.swagger.respostas.cliente.ExemploRespostaAvaliarProfessorV2.class)
    })
    @ResponseBody
    @RequestMapping(value = "{ctx}/v2/avaliacao-professor", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> avaliaProfessorAPPTreino (
            @ApiParam(value = "Contexto da empresa", defaultValue = "empresa1", required = true)
            @PathVariable String ctx,
            @ApiParam(value = "Dados da avaliação do professor", required = true)
            @RequestBody AvaliacaoProfessorDTO avaliacaoProfessorDTO
    ) {
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.novaAvaliacaoProfessor(avaliacaoProfessorDTO, ctx));
        } catch (Exception e) {
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Buscar avaliações feitas por um cliente",
            notes = "Consulta todas as avaliações de professores realizadas por um cliente específico. " +
                    "Retorna o histórico de avaliações do cliente para análise e acompanhamento.",
            tags = "Clientes"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = br.com.pacto.swagger.respostas.cliente.ExemploRespostaBuscaAvaliacaoPorCliente.class)
    })
    @ResponseBody
    @RequestMapping(value = "{ctx}/avaliacao-professor-por-cliente", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> buscaAvaliacaoPorCliente (
            @ApiParam(value = "Contexto da empresa", defaultValue = "empresa1", required = true)
            @PathVariable String ctx,
            @ApiParam(value = "Código do usuário cliente", defaultValue = "123", required = true)
            @RequestParam Integer codUsuario
    ) {
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.buscaAvaliacaoCliente(codUsuario, ctx));
        } catch (Exception e) {
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Buscar avaliações de professores",
            notes = "Consulta avaliações de professores específicos ou de todos os professores. " +
                    "Permite filtrar por códigos de professores ou retornar todas as avaliações.",
            tags = "Clientes"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = br.com.pacto.swagger.respostas.cliente.ExemploRespostaBuscaAvaliacaoProfessor.class)
    })
    @ResponseBody
    @RequestMapping(value = "{ctx}/avaliacao-professor", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> buscaAvaliacao (
            @ApiParam(value = "Contexto da empresa", defaultValue = "empresa1", required = true)
            @PathVariable String ctx,
            @ApiParam(value = "Códigos dos professores separados por vírgula", defaultValue = "1,2,3")
            @RequestParam(required = false) String codProfessores
    ) {
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.buscaAvaliacao(codProfessores, ctx));
        } catch (Exception e) {
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Autorizar acesso TotalPass",
            notes = "Autoriza o acesso de um cliente através da plataforma TotalPass. " +
                    "Valida os dados do cliente e cria o período de acesso necessário no sistema ZW.",
            tags = "Clientes"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = br.com.pacto.swagger.respostas.cliente.ExemploRespostaAutorizaAcessoTotalPass.class)
    })
    @ResponseBody
    @RequestMapping(value = "{ctx}/autoriza-acesso-totalpass", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> autorizaAcessoTotalPass(
            @ApiParam(value = "Contexto da empresa", defaultValue = "empresa1", required = true)
            @PathVariable String ctx,
            @ApiParam(value = "Dados do cliente TotalPass para autorização de acesso", required = true)
            @RequestBody ClienteDadosTotalPassDTO clienteDadosTotalPassDTO) {
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.autorizaAcessoTotalPass(clienteDadosTotalPassDTO, ctx));
        } catch (Exception e) {
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Buscar dados de acompanhamento do cliente",
            notes = "Consulta dados consolidados de acompanhamento dos clientes em um período específico. " +
                    "Retorna informações sobre evolução, frequência e estatísticas de treino dos clientes.",
            tags = "Clientes"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = br.com.pacto.swagger.respostas.cliente.ExemploRespostaBuscaClienteAcompanhamento.class)
    })
    @ResponseBody
    @RequestMapping(value = "{ctx}/cliente-acompanhamento", method = {RequestMethod.POST, RequestMethod.GET}, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> buscaClienteAcompanhamento (
            @ApiParam(value = "Contexto da empresa", defaultValue = "empresa1", required = true)
            @PathVariable String ctx,
            @ApiParam(value = "Data de início do período no formato YYYYMMDD", defaultValue = "20240101", required = true)
            @RequestParam String dtInicio,
            @ApiParam(value = "Data de fim do período no formato YYYYMMDD", defaultValue = "20241231", required = true)
            @RequestParam String dtFim) {
        try {
            Date dtInicioDate = Calendario.getDate("yyyyMMdd", dtInicio);
            Date dtFimDate = Calendario.getDate("yyyyMMdd", dtFim);
            return ResponseEntityFactory.ok(clienteSinteticoService.consultarClienteAcompanhamentoJSON(ctx, dtInicioDate, dtFimDate));
        } catch (Exception e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao carregar linha de tempo do aluno", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }
    @ApiOperation(
            value = "Consultar histórico de contatos do cliente",
            notes = "Consulta o histórico completo de contatos e observações de um cliente específico. " +
                    "Permite filtrar entre contatos do CRM e observações do sistema de treino.",
            tags = "Clientes"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaHistoricoContato.class)
    })
    @ResponseBody
    @RequestMapping(value = "{contexto}/historico-contato", method = RequestMethod.GET)
    public ModelMap historicoContato(
            @ApiParam(value = "Contexto da empresa", defaultValue = "empresa1", required = true)
            @PathVariable String contexto,
            @ApiParam(value = "Matrícula do cliente para consulta do histórico", defaultValue = "12345", required = true)
            @RequestParam("matricula") String matriculaCliente,
            @ApiParam(value = "Incluir contatos do CRM no histórico", defaultValue = "true")
            @RequestParam(required = false, defaultValue = "true") Boolean contatoCRM,
            @ApiParam(value = "Incluir observações do sistema no histórico", defaultValue = "true")
            @RequestParam(required = false, defaultValue = "true") Boolean observacoes,
            HttpServletRequest httpRequest) {
        ModelMap modelMap = new ModelMap();
        try {
            final List<HistoricoContatoAlunoVO> historicoContatoAlunoVOS =
                    clienteObservacaoService.consultarHistoricoDeContatos(contexto, matriculaCliente, contatoCRM, observacoes);

            modelMap.addAttribute(RETURN, historicoContatoAlunoVOS);

        } catch (Exception ex) {
            modelMap.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ClienteJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return modelMap;
    }

    @ApiOperation(
            value = "Salvar perfil DISC do cliente",
            notes = "Cadastra o perfil DISC (Dominância, Influência, Estabilidade, Conformidade) de um cliente. " +
                    "O perfil DISC é utilizado para análise comportamental e personalização do atendimento.",
            tags = "Clientes"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = br.com.pacto.swagger.respostas.cliente.ExemploRespostaSalvarPerfilDISC.class)
    })
    @ResponseBody
    @RequestMapping(value = "{ctx}/perfilDISC", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> salvarPerfilDISC(
            @ApiParam(value = "Contexto da empresa", defaultValue = "empresa1", required = true)
            @PathVariable String ctx,
            @ApiParam(value = "Dados do perfil DISC do cliente", required = true)
            @RequestBody PerfilDISCDTO perfilDISCDTO) {
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.salvarPerfilDISC(ctx, perfilDISCDTO));
        } catch (Exception e) {
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Buscar perfil DISC do cliente",
            notes = "Consulta o perfil DISC cadastrado para um cliente específico através de sua matrícula. " +
                    "Retorna os valores de Dominância, Influência, Estabilidade e Conformidade do cliente.",
            tags = "Clientes"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = br.com.pacto.swagger.respostas.cliente.ExemploRespostaBuscarPerfilDISC.class)
    })
    @ResponseBody
    @RequestMapping(value = "{ctx}/perfilDISC", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> buscarPerfilDISC(
            @ApiParam(value = "Contexto da empresa", defaultValue = "empresa1", required = true)
            @PathVariable String ctx,
            @ApiParam(value = "Matrícula do cliente para consulta do perfil DISC", defaultValue = "12345", required = true)
            @RequestParam Integer matricula) {
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.buscarPerfilDISC(ctx, matricula));
        } catch (Exception e) {
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Atualizar perfil DISC do cliente",
            notes = "Atualiza o perfil DISC existente de um cliente através do código do perfil. " +
                    "Permite modificar os valores de Dominância, Influência, Estabilidade e Conformidade.",
            tags = "Clientes"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = br.com.pacto.swagger.respostas.cliente.ExemploRespostaAtualizarPerfilDISC.class)
    })
    @ResponseBody
    @RequestMapping(value = "{ctx}/perfilDISC", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> atualizarPerfilDISC(
            @ApiParam(value = "Contexto da empresa", defaultValue = "empresa1", required = true)
            @PathVariable String ctx,
            @ApiParam(value = "Dados atualizados do perfil DISC", required = true)
            @RequestBody PerfilDISCDTO perfilDISCDTO,
            @ApiParam(value = "Código do perfil DISC a ser atualizado", defaultValue = "123", required = true)
            @RequestParam Integer codigo) {
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.atualizarPerfilDISC(ctx, perfilDISCDTO, codigo));
        } catch (Exception e) {
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Obter retrospectiva anual do cliente",
            notes = "Gera uma retrospectiva anual completa do cliente com estatísticas de treino, evolução e conquistas. " +
                    "Inclui dados como frequência, objetivos alcançados e progresso ao longo do ano.",
            tags = "Clientes"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = br.com.pacto.swagger.respostas.cliente.ExemploRespostaRetrospectiva.class)
    })
    @ResponseBody
    @RequestMapping(value = "{ctx}/retrospectiva", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> retrospectiva(
            @ApiParam(value = "Contexto da empresa", defaultValue = "empresa1", required = true)
            @PathVariable String ctx,
            @ApiParam(value = "Ano para geração da retrospectiva", defaultValue = "2024", required = true)
            @RequestParam Integer ano,
            @ApiParam(value = "Matrícula do cliente para geração da retrospectiva", defaultValue = "12345", required = true)
            @RequestParam Integer matricula,
            @ApiParam(value = "Forçar atualização do cache de dados", defaultValue = "false")
            @RequestParam(required = false, defaultValue = "false") Boolean atualizaCache) {
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.obterRetrospectiva(ctx, ano, matricula, atualizaCache));
        } catch (Exception e) {
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar pontos e saldo de brindes do cliente",
            notes = "Consulta o saldo atual de pontos do cliente no programa de fidelidade. " +
                    "Retorna informações sobre pontos acumulados e disponíveis para troca por brindes.",
            tags = "Clientes"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = br.com.pacto.swagger.respostas.cliente.ExemploRespostaPontosSaldo.class)
    })
    @ResponseBody
    @RequestMapping(value = "{ctx}/pontos-saldo", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> pontosSaldoBrindes(
            @ApiParam(value = "Contexto da empresa", defaultValue = "empresa1", required = true)
            @PathVariable String ctx,
            @ApiParam(value = "Matrícula do cliente para consulta dos pontos", defaultValue = "12345", required = true)
            @RequestParam Integer matricula) {
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.obterPontosSaldo(ctx, matricula));
        } catch (Exception e) {
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar brindes disponíveis",
            notes = "Consulta a lista de brindes disponíveis no programa de fidelidade da empresa. " +
                    "Permite filtrar apenas brindes ativos ou incluir também os inativos.",
            tags = "Clientes"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = br.com.pacto.swagger.respostas.cliente.ExemploRespostaBrindes.class)
    })
    @ResponseBody
    @RequestMapping(value = "{ctx}/brindes", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> brindes(
            @ApiParam(value = "Contexto da empresa", defaultValue = "empresa1", required = true)
            @PathVariable String ctx,
            @ApiParam(value = "Filtrar apenas brindes ativos", defaultValue = "true")
            @RequestParam(required = false, defaultValue = "true") Boolean ativo) {
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.obterBrindes(ctx, ativo));
        } catch (Exception e) {
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar histórico de presença do cliente",
            notes = "Consulta o histórico completo de presença e frequência de um cliente na academia. " +
                    "Retorna dados detalhados sobre aulas frequentadas, faltas e padrões de comparecimento.",
            tags = "Clientes"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = br.com.pacto.swagger.respostas.cliente.ExemploRespostaHistoricoPresenca.class)
    })
    @ResponseBody
    @RequestMapping(value = "{ctx}/historico-presenca", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> dadosAulas(
            @ApiParam(value = "Contexto da empresa", defaultValue = "empresa1", required = true)
            @PathVariable String ctx,
            @ApiParam(value = "Matrícula do cliente para consulta do histórico", defaultValue = "12345", required = true)
            @RequestParam Integer matricula,
            @ApiParam(value = "Código da empresa do cliente", defaultValue = "1", required = true)
            @RequestParam Integer empresa,
            @ApiParam(value = "Forçar atualização do cache de dados", defaultValue = "false")
            @RequestParam (defaultValue = "false") Boolean atualizaCache) {
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.historicoPresenca(ctx, matricula, empresa, atualizaCache));
        } catch (Exception e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao carregar linha de tempo do aluno", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }
}

