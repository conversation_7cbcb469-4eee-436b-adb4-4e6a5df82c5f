package br.com.pacto.controller.json.aulaExcluida;


import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.aulaExcluida.AulaExcluidaService;
import br.com.pacto.swagger.respostas.ExemploRespostaVazia;
import br.com.pacto.swagger.respostas.aula.excluida.ExemploRespostaAulaExcluidaDTO;
import io.swagger.annotations.*;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import java.util.logging.Level;
import java.util.logging.Logger;

@Controller
@RequestMapping("/psec/aula-excluida")
public class AulaExcluidaController {

    private AulaExcluidaService aulaExcluidaService;

    @Autowired
    public AulaExcluidaController(AulaExcluidaService aulaExcluidaService) {
        Assert.notNull(aulaExcluidaService, "O serviço de aula excçuiída não foi injetado corretamente");
        this.aulaExcluidaService = aulaExcluidaService;
    }

    @ApiOperation(
            value = "Consultar aulas excluídas",
            notes = "Consulta as aulas coletivas que foram excluídas.",
            tags = "Aulas Excluídas"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.\n\n Ordena por um atributo contido no objeto da resposta.\n\n" +
                    "<strong>Ordens disponíveis</strong>\n\n" +
                    "- <strong>ASC</strong> - Ordena pelo atributo escolhido de forma ascendente\n" +
                    "- <strong>DESC</strong> - Ordena pelo atributo escolhido de forma descendente\n\n" +
                    "Deve ser informado como atributo,ordem", defaultValue = "codigo,desc", paramType = "query", dataType = "string"),
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaAulaExcluidaDTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarAulaExcluida(@ApiParam(value = "Filtros de busca, deve ser informado como um JSON.\n\n" +
                                                                             "<strong>É necessário fazer o ENCODE na URL para realizar a requisição com os filtros.</strong>\n\n" +
                                                                             "<strong>Filtros disponíveis:</strong>\n" +
                                                                             "- <strong>dataInicio:</strong> Filtra aulas excluídas a partir desta data (Deve ser informado como um timestamp em milissegundos).\n" +
                                                                             "- <strong>dataFim:</strong> Filtra aulas excluídas até esta data (Deve ser informado como um timestamp em milissegundos).\n" +
                                                                             "- <strong>professoresIds:</strong> Filtra por um ou mais professores (Deve ser informado como uma lista de códigos/IDs ex: [10, 25]).\n" +
                                                                             "- <strong>professorId:</strong> Filtra por um professor específico (Deve ser informado como um código/ID ex: 10).",
                                                                             defaultValue = "{\"dataInicio\":1735689600000, \"dataFim\":1749599999000, \"professoresIds\":[10, 25], \"professorId\":10}")
                                                                     @RequestParam(value = "filters", required = false) JSONObject filtros,
                                                                     @ApiParam(value = "Código da empresa que a aula está vinculada", example = "3", required = true)
                                                                     @RequestHeader(value = "empresaId", required = true) Integer empresaId,
                                                                     @ApiIgnore PaginadorDTO paginadorDTO, HttpServletRequest request) throws JSONException {
        try {
            FiltroAulaExcluidaJSON filtroAulaExcluidaJSON = new FiltroAulaExcluidaJSON(filtros);

            return ResponseEntityFactory.ok(aulaExcluidaService.listaAulaExcluida(filtroAulaExcluidaJSON, paginadorDTO, empresaId, request), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(AulaExcluidaController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar as atividades", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Reverter exclusão de aula coletiva",
            notes = "Reverte a exclusão de uma aula coletiva, ou seja, ela volta para a agenda de aulas.",
            tags = "Aulas Excluídas"
    )
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida). Não retorna corpo de resposta.", response = ExemploRespostaVazia.class)
    })
    @ResponseBody
    @RequestMapping(value = "{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> removerAulaExcluida(
            @ApiParam(value = "Código da aula que será revertida a exclusão", example = "232", required = true)
            @PathVariable("id") Integer id) {
        try {
            aulaExcluidaService.removerAulaExcluida(id);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(AulaExcluidaController.class.getName()).log(Level.SEVERE, "Erro ao tentar desfazer a aula excluída", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

}
