package br.com.pacto.objeto.to;

import br.com.pacto.bean.atividade.MetodoExecucaoEnum;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * Created by ulisses on 05/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Dados para criação ou atualização de atividade da ficha")
public class AtividadeFichaEndpointTO {

    @ApiModelProperty(value = "ID único da atividade da ficha", example = "1")
    private Integer id;

    @ApiModelProperty(value = "Lista de IDs das atividades da ficha relacionadas", example = "[1, 2, 3]")
    private List<Integer> atividadesFichaId;

    @ApiModelProperty(value = "Sequência/ordem da atividade na ficha", example = "1")
    private Integer sequencia;

    @ApiModelProperty(value = "ID da atividade física a ser executada", example = "15", required = true)
    private Integer atividadeId;

    @ApiModelProperty(value = "Nível de esforço/intensidade da atividade (1-10)", example = "7")
    private Integer esforco;

    @ApiModelProperty(value = "Método de execução da atividade. \n\n" +
            "<strong>Valores disponíveis</strong>\n" +
            "- PIRAMIDE_DECRESCENTE (Pirâmide Decrescente)\n" +
            "- PIRAMIDE_CRESCENTE (Pirâmide Crescente)\n" +
            "- CIRCUITO (Circuito)\n" +
            "- ISOMETRICO (Isométrico)\n" +
            "- SUPER_SERIE (Super-Série)\n" +
            "- BI_SET (BI-Set)\n" +
            "- TRI_SET (TRI-Set)\n" +
            "- DROP_SET (Drop-Set)\n" +
            "- ONDULATORIO (Ondulatório)\n" +
            "- PROGRESSAO_DUPLA (Progressão Dupla)\n" +
            "- DE_LORME (De Lorme)\n" +
            "- ERPAD (Erpad)\n" +
            "- PARCELADO (Parcelado)\n" +
            "- DUPLAMENTE_PARCELADO (Duplamente Parcelado)\n" +
            "- TRIPLADO_PARCELADO (Triplamente Parcelado)\n" +
            "- PUXE_EMPURRE (Puxe-Empurre)\n" +
            "- REPETICAO_ROUBADA (Repetição Roubada)\n" +
            "- REPETICAO_FORCADA (Repetição Forçada)\n" +
            "- DTA (D.T.A.)\n" +
            "- REPETICAO_PARCIAL (Repetição Parcial)\n" +
            "- PICO_CONTRACAO (Pico de Contração)\n" +
            "- TENSAO_LENTA_CONTINUA (Tensão Lenta e Contínua)\n" +
            "- SET_DESCENDENTE (Set Descendente)\n" +
            "- ISOLAMENTO (Isolamento)\n" +
            "- SUPER_SET (Super-Set)\n" +
            "- SERIE_COMPOSTA (Série Composta)\n" +
            "- SUPERSET_MULTIPLO (Super-Set Múltiplo)\n" +
            "- PRE_EXAUSTAO (Pré-Exaustão)\n" +
            "- SERIE_GIGANTE (Série Gigante)\n" +
            "- PHA (P.H.A.)\n" +
            "- SUPER_CIRCUITO (Super-Circuito)\n" +
            "- MUSCULACAO_INTERVALADA (Musculação Intervalada)\n" +
            "- PLIOMETRICO (Pliométrico)\n" +
            "- REPETICAO_NEGATIVA (Repetição Negativa)\n" +
            "- NAUTILUS (Nautilus)\n" +
            "- HEAVY_DUTY (Heavy-Duty)\n" +
            "- REST_PAUSE (Rest Pause)\n" +
            "- POS_EXAUSTAO (Pós-Exaustão)\n" +
            "- EXAUSTAO (Exaustão)\n" +
            "- STRIP_SET (Strip Set)\n" +
            "- SET_21 (Set 21)\n" +
            "- SUPER_DROP_SET (Super Drop Set)\n" +
            "- FLUSHING (Flushing)\n" +
            "- CONTRACAO_ISOMETRICA (Contração Isométrica)\n" +
            "- CONTINUO (Contínuo)\n" +
            "- COMBINADO (Combinado)\n" +
            "- ALTERNADO (Alternado)\n" +
            "- ALTERNADO_SIMULTANEO (Alternado + Simultâneo)\n" +
            "- FST (FST-7)\n" +
            "- SST (SST)\n" +
            "- HIIT (HIIT)\n" +
            "- TABATA (Tabata)\n" +
            "- MET_6_20 (6/20)\n" +
            "- TEMPOS_2 (2 Tempos)\n" +
            "- TEMPOS_3 (3 Tempos)\n" +
            "- NONSTOP (Nonstop)\n" +
            "- CLUSTER (Cluster)\n" +
            "- PONTO_ZERO (Ponto Zero)\n" +
            "- DEADSTOP (Deadstop)\n" +
            "- NAO_ATRIBUIDO (Não atribuído)", example = "PIRAMIDE_DECRESCENTE")
    private MetodoExecucaoEnum metodoExecucao;

    @ApiModelProperty(value = "ID da ficha de treino onde a atividade será inserida", example = "5", required = true)
    private Integer fichaId;

    @ApiModelProperty(value = "Número de séries a serem executadas", example = "3")
    private Integer series;

    @ApiModelProperty(value = "Número de repetições por série (pode conter faixas como '8-12')", example = "10")
    private String repeticoes;

    @ApiModelProperty(value = "Carga a ser utilizada (pode conter valores como '20kg' ou 'peso corporal')", example = "25kg")
    private String carga;

    @ApiModelProperty(value = "Cadência de execução do movimento (tempo de cada fase)", example = "2-1-2-1")
    private String cadencia;

    @ApiModelProperty(value = "Tempo de descanso entre séries em segundos", example = "60")
    private Integer descanso = 0;

    @ApiModelProperty(value = "Observações complementares sobre a execução da atividade", example = "Manter postura ereta durante todo o movimento")
    private String complemento;

    @ApiModelProperty(value = "Velocidade para atividades cardiovasculares (km/h)", example = "8.5")
    private Double velocidade = 0.0;

    @ApiModelProperty(value = "Duração da atividade em minutos", example = "30")
    private Integer duracao = 0;

    @ApiModelProperty(value = "Distância a ser percorrida em metros", example = "5000")
    private Integer distancia = 0;

    @ApiModelProperty(value = "Lista de IDs das atividades para métodos BI-SET ou TRI-SET", example = "[2, 3]")
    private List<Integer> setAtividadesIds;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public List<Integer> getAtividadesFichaId() {
        return atividadesFichaId;
    }

    public void setAtividadesFichaId(List<Integer> atividadesFichaId) {
        this.atividadesFichaId = atividadesFichaId;
    }

    public Integer getSequencia() {
        return sequencia;
    }

    public void setSequencia(Integer sequencia) {
        this.sequencia = sequencia;
    }

    public Integer getAtividadeId() {
        return atividadeId;
    }

    public void setAtividadeId(Integer atividadeId) {
        this.atividadeId = atividadeId;
    }

    public Integer getEsforco() {
        return esforco;
    }

    public void setEsforco(Integer esforco) {
        this.esforco = esforco;
    }

    public MetodoExecucaoEnum getMetodoExecucao() {
        return metodoExecucao;
    }

    public void setMetodoExecucao(MetodoExecucaoEnum metodoExecucao) {
        this.metodoExecucao = metodoExecucao;
    }

    public Integer getFichaId() {
        return fichaId;
    }

    public void setFichaId(Integer fichaId) {
        this.fichaId = fichaId;
    }

    public Integer getSeries() {
        return series;
    }

    public void setSeries(Integer series) {
        this.series = series;
    }

    public String getRepeticoes() {
        return repeticoes == null ? "" : repeticoes;
    }

    public void setRepeticoes(String repeticoes) {
        this.repeticoes = repeticoes;
    }

    public String getCarga() {
        return carga == null ? "" : carga;
    }

    public void setCarga(String carga) {
        this.carga = carga;
    }

    public String getCadencia() {
        return cadencia;
    }

    public void setCadencia(String cadencia) {
        this.cadencia = cadencia;
    }

    public Integer getDescanso() {
        return descanso == null ? 0 : descanso;
    }

    public void setDescanso(Integer descanso) {
        this.descanso = descanso;
    }

    public String getComplemento() {
        return complemento;
    }

    public void setComplemento(String complemento) {
        this.complemento = complemento;
    }

    public Double getVelocidade() {
        return velocidade == null ? 0.0 : velocidade;
    }

    public void setVelocidade(Double velocidade) {
        this.velocidade = velocidade;
    }

    public Integer getDuracao() {
        return duracao == null ? 0 : duracao;
    }

    public void setDuracao(Integer duracao) {
        this.duracao = duracao;
    }

    public Integer getDistancia() {
        return distancia == null ? 0 : distancia;
    }

    public void setDistancia(Integer distancia) {
        this.distancia = distancia;
    }

    public List<Integer> getSetAtividadesIds() {
        return setAtividadesIds;
    }

    public void setSetAtividadesIds(List<Integer> setAtividadesIds) {
        this.setAtividadesIds = setAtividadesIds;
    }
}
