package br.com.pacto.objeto.to;

import br.com.pacto.bean.atividade.MetodoExecucaoEnum;
import br.com.pacto.bean.serie.SerieEndpointTO;
import br.com.pacto.util.UteisValidacao;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by paulo 21/01/2020
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@ApiModel(description = "Dados da atividade dentro de uma ficha de treino")
public class AtividadeFichaDTO {

    @ApiModelProperty(value = "ID único da atividade na ficha", example = "1")
    private Integer id;

    @ApiModelProperty(value = "Sequência/ordem da atividade na ficha", example = "1")
    private Integer sequencia;

    @ApiModelProperty(value = "ID da atividade física a ser executada", example = "15")
    private Integer atividadeId;

    @ApiModelProperty(value = "Nível de esforço da atividade (1-10)", example = "7")
    private Integer esforco;

    @ApiModelProperty(value = "Método de execução da atividade", example = "PIRAMIDE_CRESCENTE")
    private String metodoExecucao;

    @ApiModelProperty(value = "ID da ficha à qual a atividade pertence", example = "5")
    private Integer fichaId;

    @ApiModelProperty(value = "Lista das séries da atividade")
    private List<SerieEndpointTO> series;

    @ApiModelProperty(value = "Lista das sequências das atividades para métodos BI-SET ou TRI-SET", example = "[2, 3]")
    private List<Integer> atividadesSequenciaSet;

    @ApiModelProperty(value = "Lista dos IDs das atividades para métodos BI-SET ou TRI-SET", example = "[10, 15]")
    private List<Integer> atividadesIdSet;

    @ApiModelProperty(value = "Complemento personalizado para o nome da atividade", example = "Com pegada supinada")
    private String complementoNomeAtividade;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getSequencia() {
        return sequencia;
    }

    public void setSequencia(Integer sequencia) {
        this.sequencia = sequencia;
    }

    public Integer getAtividadeId() {
        return atividadeId;
    }

    public void setAtividadeId(Integer atividadeId) {
        this.atividadeId = atividadeId;
    }

    public Integer getEsforco() {
        return esforco;
    }

    public void setEsforco(Integer esforco) {
        this.esforco = esforco;
    }

    public String getMetodoExecucao() {
        return metodoExecucao;
    }

    public MetodoExecucaoEnum getMetodoExecucaoEnum() {

        if(UteisValidacao.emptyString(getMetodoExecucao())){
            return null;
        }
        return MetodoExecucaoEnum.valueOf(getMetodoExecucao());
    }


    public void setMetodoExecucao(String metodoExecucao) {
        this.metodoExecucao = metodoExecucao;
    }

    public Integer getFichaId() {
        return fichaId;
    }

    public void setFichaId(Integer fichaId) {
        this.fichaId = fichaId;
    }

    public List<SerieEndpointTO> getSeries() {
        if (series == null) {
            series = new ArrayList<>();
        }
        return series;
    }

    public void setSeries(List<SerieEndpointTO> series) {
        getSeries().addAll(series);
    }

    public List<Integer> getAtividadesSequenciaSet() {
        if (atividadesSequenciaSet == null) {
            atividadesSequenciaSet = new ArrayList<>();
        }
        return atividadesSequenciaSet;
    }

    public void setAtividadesSequenciaSet(List<Integer> atividadesSequenciaSet) {
        this.atividadesSequenciaSet = atividadesSequenciaSet;
    }

    public List<Integer> getAtividadesIdSet() {
        if (atividadesIdSet == null) {
            atividadesIdSet = new ArrayList<>();
        }
        return atividadesIdSet;
    }

    public void setAtividadesIdSet(List<Integer> atividadesIdSet) {
        this.atividadesIdSet = atividadesIdSet;
    }

    public String getComplementoNomeAtividade() {
        return complementoNomeAtividade;
    }

    public void setComplementoNomeAtividade(String complementoNomeAtividade) {
        this.complementoNomeAtividade = complementoNomeAtividade;
    }
}
