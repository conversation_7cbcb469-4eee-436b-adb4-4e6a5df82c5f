/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.impl.gympass.json;

import br.com.pacto.bean.aula.Aula;
import br.com.pacto.controller.json.base.SuperJSON;
import br.com.pacto.objeto.Calendario;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 30/03/2020
 */
@ApiModel(description = "Dados de uma turma para integração com GymPass")
public class TurmaGymPassJSON extends SuperJSON {

    @ApiModelProperty(value = "Código identificador único da turma", example = "123")
    private Integer codigo;

    @ApiModelProperty(value = "Nome da turma", example = "Yoga Avançado")
    private String nome;

    @ApiModelProperty(value = "Descrição detalhada da turma", example = "Turma de yoga para praticantes avançados")
    private String descricao;

    @ApiModelProperty(value = "Observações adicionais sobre a turma", example = "Necessário experiência prévia")
    private String observacao;

    @ApiModelProperty(value = "Indica se a turma está ativa", example = "true")
    private boolean ativo;

    @ApiModelProperty(value = "Código da empresa no sistema ZW", example = "1")
    private Integer empresaZW;

    @ApiModelProperty(value = "Código da empresa no sistema TR", example = "1")
    private Integer empresaTR;

    @ApiModelProperty(value = "Código do produto GymPass associado", example = "789")
    private Integer produtoGymPass;

    @ApiModelProperty(value = "Código identificador da classe no GymPass", example = "456")
    private Integer idClasseGymPass;

    @ApiModelProperty(value = "URL para acesso à turma virtual", example = "https://meet.google.com/abc-defg-hij")
    private String urlTurmaVirtual;

    @ApiModelProperty(value = "Lista de horários da turma")
    private List<HorarioTurmaGymPassJSON> horarios;

    public TurmaGymPassJSON() {
    }

    public TurmaGymPassJSON(ResultSet rs) throws Exception {
        this.codigo = rs.getInt("codigo");
        this.nome = rs.getString("identificador");
        this.descricao = rs.getString("identificador");
        this.observacao = "";
        this.empresaZW = rs.getInt("empresa");
        this.produtoGymPass = rs.getInt("produtogympass");
        this.idClasseGymPass =  rs.getInt("idclassegympass");
        this.urlTurmaVirtual = rs.getString("urlturmavirtual");
        Date fimVigencia = rs.getDate("datafinalvigencia");
        this.ativo = Calendario.maiorOuIgual(fimVigencia, Calendario.hoje());
        this.horarios = new ArrayList<>();
    }

    public TurmaGymPassJSON(Aula aula, Integer empresaTR) {
        this.codigo = aula.getCodigo();
        this.nome = aula.getNome();
        this.descricao = aula.getNome();
        this.observacao = "";
        this.empresaTR = empresaTR;
        this.produtoGymPass = aula.getProdutoGymPass();
        this.idClasseGymPass = aula.getIdClasseGymPass();
        this.urlTurmaVirtual = aula.getUrlTurmaVirtual();
        this.ativo = Calendario.igual(Calendario.hoje(), aula.getDataInicio()) ||
                Calendario.igual(Calendario.hoje(), aula.getDataFim()) ||
                Calendario.entre(Calendario.hoje(), aula.getDataInicio(), aula.getDataFim());
        this.horarios = new ArrayList<>();
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public Integer getProdutoGymPass() {
        return produtoGymPass;
    }

    public void setProdutoGymPass(Integer produtoGymPass) {
        this.produtoGymPass = produtoGymPass;
    }

    public Integer getIdClasseGymPass() {
        return idClasseGymPass;
    }

    public void setIdClasseGymPass(Integer idClasseGymPass) {
        this.idClasseGymPass = idClasseGymPass;
    }

    public List<HorarioTurmaGymPassJSON> getHorarios() {
        if (horarios == null) {
            horarios = new ArrayList<>();
        }
        return horarios;
    }

    public void setHorarios(List<HorarioTurmaGymPassJSON> horarios) {
        this.horarios = horarios;
    }

    public boolean isAtivo() {
        return ativo;
    }

    public void setAtivo(boolean ativo) {
        this.ativo = ativo;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getUrlTurmaVirtual() {
        return urlTurmaVirtual;
    }

    public void setUrlTurmaVirtual(String urlTurmaVirtual) {
        this.urlTurmaVirtual = urlTurmaVirtual;
    }

    public Integer getEmpresaZW() {
        return empresaZW;
    }

    public void setEmpresaZW(Integer empresaZW) {
        this.empresaZW = empresaZW;
    }

    public Integer getEmpresaTR() {
        return empresaTR;
    }

    public void setEmpresaTR(Integer empresaTR) {
        this.empresaTR = empresaTR;
    }
}
