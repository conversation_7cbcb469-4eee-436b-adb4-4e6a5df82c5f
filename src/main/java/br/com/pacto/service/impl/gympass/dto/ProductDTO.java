package br.com.pacto.service.impl.gympass.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.json.JSONObject;

@ApiModel(description = "Informações do produto")
public class ProductDTO {
    @ApiModelProperty(value = "Código identificador do produto", example = "1")
    private Integer id;
    @ApiModelProperty(value = "Nome do produto", example = "MUSCULAÇÃO")
    private String nome;

    public ProductDTO() { }

    public ProductDTO(JSONObject json) {
        this.id = json.optInt("product_id");
        this.nome = json.optString("name");
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

}
