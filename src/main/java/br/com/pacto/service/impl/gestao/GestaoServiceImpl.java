/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.impl.gestao;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.atividade.AtividadeFicha;
import br.com.pacto.bean.bi.DashboardBI;
import br.com.pacto.bean.cliente.ClienteAcompanhamento;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.configuracoes.ConfiguracaoSistema;
import br.com.pacto.bean.configuracoes.ConfiguracoesEnum;
import br.com.pacto.bean.gestao.*;
import br.com.pacto.bean.notificacao.GravidadeNotificacaoEnum;
import br.com.pacto.bean.notificacao.Notificacao;
import br.com.pacto.bean.notificacao.TipoNotificacaoEnum;
import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.bean.programa.ExecucoesTreinoTO;
import br.com.pacto.bean.programa.HistoricoRevisaoProgramaTreino;
import br.com.pacto.bean.programa.ProgramaTreino;
import br.com.pacto.bean.programa.ProgramaTreinoAndamentoTO;
import br.com.pacto.bean.serie.TreinoRealizado;
import br.com.pacto.bean.tipoEvento.TipoEvento;
import br.com.pacto.controller.to.FiltrosGestaoTO;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.controller.json.gestao.FiltroGestaoProgramaDTO;
import br.com.pacto.dao.intf.agenda.AgendamentoDao;
import br.com.pacto.dao.intf.atividade.AtividadeFichaDao;
import br.com.pacto.dao.intf.cliente.ClienteAcompanhamentoDao;
import br.com.pacto.dao.intf.cliente.ClienteSinteticoDao;
import br.com.pacto.dao.intf.dashboardbi.DashboardBIDao;
import br.com.pacto.dao.intf.notificacao.NotificacaoDao;
import br.com.pacto.dao.intf.professor.ProfessorSinteticoDao;
import br.com.pacto.dao.intf.programa.HistoricoRevisaoProgramaTreinoDao;
import br.com.pacto.dao.intf.programa.ProgramaTreinoDao;
import br.com.pacto.dao.intf.serie.TreinoRealizadoDao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.ColecaoUtils;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.configuracoes.ConfiguracaoSistemaService;
import br.com.pacto.service.intf.gestao.GestaoService;
import br.com.pacto.service.intf.programa.ProgramaTreinoService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.ViewUtils;
import br.com.pacto.util.bean.GenericoTO;
import br.com.pacto.util.impl.Ordenacao;
import com.google.common.collect.Iterables;
import org.apache.commons.collections.Predicate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.faces.model.SelectItem;
import javax.persistence.TypedQuery;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import static br.com.pacto.bean.gestao.IndicadorEnum.N_ATIVIDADE_PROF_TREINO_ACOMPANHADO;

/**
 *
 * <AUTHOR>
 */
@Service
public class GestaoServiceImpl implements GestaoService {

    @Autowired
    private ViewUtils viewUtils;
    @Autowired
    private AgendamentoDao agendamentoDao;
    @Autowired
    private TreinoRealizadoDao treinoRealizadoDao;
    @Autowired
    private DashboardBIDao dashboardBIDao;
    @Autowired
    private ProgramaTreinoDao programaTreinoDao;
    @Autowired
    private ProfessorSinteticoDao professorDao;
    @Autowired
    private ProgramaTreinoDao programaDao;
    @Autowired
    private NotificacaoDao notificacaoDao;
    @Autowired
    private ClienteSinteticoDao clienteDao;
    @Autowired
    private ClienteAcompanhamentoDao clienteAcompanhamentyDao;
    @Autowired
    private ConfiguracaoSistemaService configSistemaService;
    @Autowired
    private ProgramaTreinoService programaService;
    @Autowired
    private HistoricoRevisaoProgramaTreinoDao historicoRevisaoDao;
    @Autowired
    private ProfessorSinteticoDao professorSinteticoDao;
    @Autowired
    private SessaoService sessaoService;
    @Autowired
    private AtividadeFichaDao atividadeFichaDao;
    public ViewUtils getViewUtils() {
        return viewUtils;
    }

    public void setViewUtils(ViewUtils viewUtils) {
        this.viewUtils = viewUtils;
    }

    public List<AgrupadorIndicadores> calcularIndicadores(HttpServletRequest request,final String ctx, PaginadorDTO paginadorDTO, final Integer empresaZW,
                                                          FiltrosGestaoTO filtro,
                                                          boolean ignorarZerados, CategoriaIndicadorEnum categoria, Boolean treinoIndependente, Boolean colaboradorId) throws ServiceException {
        try {
            List<AgrupadorIndicadores> agrupadores = new ArrayList<AgrupadorIndicadores>();
            boolean todosProfessores = filtro.getProfessoresSelecionados().isEmpty();
            List<SelectItem> listaProfessores;
            String professoresSelecionados = "";


            if (todosProfessores) {
                listaProfessores = filtro.getProfessores();
            } else {
                listaProfessores = new ArrayList<SelectItem>();
                for (String cod : filtro.getProfessoresSelecionados()) {
                    final Integer codPesq;
                    if(colaboradorId && !SuperControle.independente(ctx)){
                        ProfessorSintetico professorSintetico = obterPorIdColaborador(ctx, Integer.valueOf(cod));
                        if (professorSintetico != null) {
                            codPesq = professorSintetico.getCodigo();
                        } else {
                            codPesq = 0;
                        }
                    }else {
                        codPesq = Integer.valueOf(cod);
                    }
                    SelectItem selecionado = (SelectItem) ColecaoUtils.find(filtro.getProfessores(), new Predicate() {
                        public boolean evaluate(Object o) {
                            Integer codProfessor = (Integer) ((SelectItem) o).getValue();
                            return codProfessor.intValue() == codPesq;
                        }
                    });
                    if (selecionado != null) {
                        listaProfessores.add(selecionado);
                        professoresSelecionados += "," + ((Integer) selecionado.getValue()).toString();
                    }
                }
            }
            List<IndicadorEnum> indicadoresCategoria = new ArrayList<IndicadorEnum>();
            for (IndicadorEnum ind : IndicadorEnum.values()) {
                if (categoria != null && ind.getCategoria().equals(categoria)) {
                    indicadoresCategoria.add(ind);
                }
            }
            switch (categoria) {
                case AGENDA:
                    montarIndicadores(empresaZW, listaProfessores, categoria, ctx,
                            filtro, ignorarZerados, agrupadores);
                    break;
                case ATIVIDADES_PROF:
                    agrupadores.addAll(montarIndicadoresAtividadesProfessores(ctx,
                            empresaZW, filtro, indicadoresCategoria, professoresSelecionados.replaceFirst(",", "")));
                    break;
                case CARTEIRAS_PROF:
                    agrupadores.addAll(montarIndicadoresCarteiraProfessores(request, paginadorDTO, ctx, empresaZW,
                            filtro, indicadoresCategoria, professoresSelecionados.replaceFirst(",", ""), treinoIndependente));
                    break;
            }

            agrupadores = Ordenacao.ordenarLista(agrupadores, "nomeProfessor");
            return agrupadores;
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    public ProfessorSintetico obterPorIdColaborador(final String ctx, Integer id) throws ServiceException {
        try {
            return professorSinteticoDao.findObjectByAttribute(ctx, "codigoColaborador", id);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public Indicador calcularIndicador(final String ctx, final Integer empresaZW,
            IndicadorEnum tipo, final FiltrosGestaoTO filtro,
            final Integer codProfessor, final String nomeProfessor, ResultEnum result) throws ServiceException {
        try {
            filtro.resolveTempo();
            Indicador ind = new Indicador(codProfessor, nomeProfessor, tipo, filtro.getInicio(), filtro.getFim());
            Map<String, Object> p = new HashMap<String, Object>();
            p.put("inicio", filtro.getInicio());
            p.put("fim", filtro.getFim());
            StringBuilder where = new StringBuilder();
            switch (tipo.getCategoria()) {
                case AGENDA:
                    montarIndicadoresAgenda(where, empresaZW, tipo, codProfessor, filtro);
                    if (result == ResultEnum.COUNT) {
                        ind.setTotal(agendamentoDao.countWithParam(ctx, "codigo", where, p));
                    } else {
                        ind.setObjetos(agendamentoDao.findByParam(ctx, where, p));
                    }
                    break;
            }

            return ind;
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    private void montarIndicadoresAgenda(StringBuilder where, final Integer empresaZW,
            IndicadorEnum tipo, final Integer codProfessor, final FiltrosGestaoTO filtro) {
        where.append(" WHERE obj.professor.empresa.codZW = ").append(empresaZW).append(" and obj.inicio BETWEEN :inicio and  :fim ");
        where.append(" AND obj.disponibilidade = false ");
        where.append(" AND obj.professor.ativo = true ");
        if (tipo != null && tipo.getStatus() != null) {
            where.append(" AND obj.status = ").append(tipo.getStatus().getId());
        }
        if (codProfessor != null) {
            where.append(" AND obj.professor.codigo = ").append(codProfessor);
        }
        if (!filtro.getTiposEvento().isEmpty()) {
            Iterable<TipoEvento> selecionados = Iterables.filter(filtro.getTiposEvento(), new com.google.common.base.Predicate<TipoEvento>() {
                public boolean apply(TipoEvento t) {
                    return t.getEscolhido() == true;
                }
            });
            if (selecionados != null && selecionados.iterator().hasNext()) {
                where.append(" AND obj.tipoEvento.codigo in (");
                for (Iterator<TipoEvento> it = selecionados.iterator(); it.hasNext();) {
                    TipoEvento tipoEvento = it.next();
                    where.append(tipoEvento.getCodigo()).append(",");
                }
                int r = where.lastIndexOf(",");
                where.replace(r, r + 1, "");
                where.append(")");
            }
        }
    }

    private List<AgrupadorIndicadores> montarIndicadoresCarteiraProfessores(HttpServletRequest request,PaginadorDTO paginadorDTO, final String ctx,
            final Integer empresaZW, final FiltrosGestaoTO filtro,
            List<IndicadorEnum> indicadoresEnum, final String professoresSelecionados, Boolean treinoIndependente) throws ServiceException, Exception {

        Map<Integer, AgrupadorIndicadores> agrupadores = new HashMap<Integer, AgrupadorIndicadores>();
        filtro.setInicio(Calendario.getDataComHoraZerada(filtro.getInicio()));
        filtro.setFim(Calendario.getDataComHora(filtro.getFim(), "23:59:59"));

        Map<IndicadorEnum, Map<Integer, Indicador>> indicadores = new HashMap<IndicadorEnum, Map<Integer, Indicador>>();

        int maxResults = 50;
        int indiceInicial = 0;
        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? 50 : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
            paginadorDTO.setSize((long) maxResults);
            paginadorDTO.setPage((long) indiceInicial);
        }


        for (IndicadorEnum tipo : indicadoresEnum) {
            Map<Integer, Indicador> professorIndic = new HashMap<Integer, Indicador>();
            StringBuilder where = new StringBuilder();
            Map<String, Object> p = new HashMap<String, Object>();


            switch (tipo) {
                case N_PROF_CARTEIRA:
                    List<Integer> clientesAdd = new ArrayList<Integer>();
                    StringBuilder queryProfCarteira = new StringBuilder();

                    queryProfCarteira.append("select distinct obj.codigo as distinct , obj.* ");
                    queryProfCarteira.append("from ClienteSintetico obj ");
                    if (!professoresSelecionados.isEmpty()) {
                        queryProfCarteira.append("inner join professorsintetico p on p.codigo = obj.professorsintetico_codigo ");
                        queryProfCarteira.append("inner join empresa e on e.codigo = p.empresa_codigo ");
                    }
                    queryProfCarteira.append("inner join programatreino p2 on p2.cliente_codigo = obj.codigo ");

                    if (SuperControle.independente(ctx)) {
                        queryProfCarteira.append(" WHERE p.empresa_codigo = ").append(empresaZW);
                    } else {
                        queryProfCarteira.append(" WHERE e.codzw = ").append(empresaZW);
                    }

                    if (!professoresSelecionados.isEmpty()) {
                        if (!professoresSelecionados.matches("^[0-9,]+$")) {
                            throw new IllegalArgumentException("IDs de professores inválidos");
                        }
                        queryProfCarteira.append(" and p.codigo IN (").append(professoresSelecionados).append(")");
                    }

                    if (filtro.isApenasProfessoresAtivos()) {
                        queryProfCarteira.append(" and p.ativo is true ");
                    }

                    List<ClienteSintetico> resultados = clienteDao.findClienteSinteticoQueryNativa(ctx,queryProfCarteira.toString());

                    if (paginadorDTO != null) {
                        paginadorDTO.setQuantidadeTotalElementos(Long.valueOf(resultados.size()));
                    }

                    for (ClienteSintetico pro : resultados) {
                        if (!clientesAdd.contains(pro.getCodigo()) ) {
                            addIndicador(ctx, agrupadores, professorIndic, pro.getProfessorSintetico(),
                                    tipo, filtro, 1, pro, true);
                        }
                        clientesAdd.add(pro.getCodigo());
                    }

                    break;
                case N_PROF_CARTEIRA_SEM_TREINO:
                    if (filtro.isNaoRevisado() || filtro.isNovo() || filtro.isRevisado() || filtro.isRenovado()) {
                        break;
                    }
                    if (SuperControle.independente(ctx)) {
                        where.append(" WHERE professorSintetico.empresa.codigo = ").append(empresaZW);
                    } else {
                        where.append(" WHERE professorSintetico.empresa.codZW = ").append(empresaZW);
                    }
                    where.append(" AND NOT EXISTS (SELECT pro.cliente.codigo FROM ProgramaTreino pro WHERE pro.cliente.codigo = obj.codigo) ");
                    if (!professoresSelecionados.isEmpty()) {
                        where.append(" and professorSintetico.codigo IN (").append(professoresSelecionados).append(") ");
                    }
                    if (filtro.isApenasProfessoresAtivos()) {
                        where.append(" and professorSintetico.ativo is true ");
                    }
                    where.append(montarFiltrosCliente(request,ctx, filtro, p, "obj"));
                    if (paginadorDTO != null) {
                        paginadorDTO.setQuantidadeTotalElementos(clienteDao.countWithParam(ctx, "codigo", where, p).longValue());
                    }

                    StringBuilder queryCarteiraSemTreino = new StringBuilder();
                    queryCarteiraSemTreino.append("select distinct obj.codigo as distinct , obj.* ");
                    queryCarteiraSemTreino.append("from ClienteSintetico obj ");
                    if (!professoresSelecionados.isEmpty()) {
                        queryCarteiraSemTreino.append("inner join professorsintetico p on p.codigo = obj.professorsintetico_codigo ");
                        queryCarteiraSemTreino.append("inner join empresa e on e.codigo = p.empresa_codigo ");
                    }

                    queryCarteiraSemTreino.append("left join programatreino p2 on p2.cliente_codigo = obj.codigo ");
                    if (SuperControle.independente(ctx)) {
                        queryCarteiraSemTreino.append(" WHERE p.empresa_codigo = ").append(empresaZW);
                    } else {
                        queryCarteiraSemTreino.append(" WHERE e.codzw = ").append(empresaZW);
                    }
                    if (!professoresSelecionados.isEmpty()) {
                        queryCarteiraSemTreino.append(" and p.codigo IN (").append(professoresSelecionados).append(")");
                    }
                    if (filtro.isApenasProfessoresAtivos()) {
                        queryCarteiraSemTreino.append(" and p.ativo is true ");
                    }
                    queryCarteiraSemTreino.append(" and p2.cliente_codigo is null ");
                    List<ClienteSintetico> resultadosCarteiraSemTreino = clienteDao.findClienteSinteticoQueryNativa(ctx,queryCarteiraSemTreino.toString());

                    if (paginadorDTO != null) {
                        paginadorDTO.setQuantidadeTotalElementos(Long.valueOf(resultadosCarteiraSemTreino.size()));
                    }

                    for (ClienteSintetico cli : resultadosCarteiraSemTreino) {
                        addIndicador(ctx, agrupadores, professorIndic, cli.getProfessorSintetico(),
                                tipo, filtro, 1, cli, false);
                    }

                    break;
                case N_PROF_CARTEIRA_PROX_VENCIMENTO:
                    ConfiguracaoSistema diasAntes = configSistemaService.consultarPorTipo(ctx, ConfiguracoesEnum.DIAS_ANTES_VENCIMENTO);
                    Date depois = Uteis.somarCampoData(filtro.getInicio(), Calendar.DAY_OF_MONTH, diasAntes.getValorAsInteger());
                    p.put("antes", Calendario.getDataComHoraZerada(filtro.getInicio()));
                    p.put("depois", depois);
                    if (SuperControle.independente(ctx)) {
                        where.append(" WHERE professorCarteira.empresa.codigo = ").append(empresaZW);
                    } else {
                        where.append(" WHERE professorCarteira.empresa.codZW = ").append(empresaZW);
                    }

                    where.append(" and obj.dataTerminoPrevisto between :antes and :depois ");
                    where.append(" AND NOT EXISTS(SELECT pro.codigo FROM ProgramaTreino pro  WHERE pro.cliente.codigo = obj.cliente.codigo  AND obj.dataTerminoPrevisto < pro.dataTerminoPrevisto) ");
                    if (!professoresSelecionados.isEmpty()) {
                        where.append(" and professorCarteira.codigo IN (").append(professoresSelecionados).append(") ");
                    }
                    if (filtro.isApenasProfessoresAtivos()) {
                        where.append(" and professorCarteira.ativo is true ");
                    }
                    where.append(montarFiltrosCliente(request, ctx, filtro, p, "obj.cliente"));
                    if (paginadorDTO != null) {
                        paginadorDTO.setQuantidadeTotalElementos(programaDao.countWithParam(ctx, "codigo", where, p).longValue());
                    }


                    List<ProgramaTreino> resultadosVenc = programaDao.findByParam(ctx, where, p);
                    List<Integer> clientesAddVenc = new ArrayList<Integer>();

                    for (ProgramaTreino pro : resultadosVenc) {
                        if (!clientesAddVenc.contains(pro.getCliente().getCodigo()) && filtrosPrograma(ctx, filtro, pro)) {
                            addIndicador(ctx, agrupadores, professorIndic, pro.getProfessorCarteira(),
                                    tipo, filtro, 1, pro.getCliente(), true);
                        }
                        clientesAddVenc.add(pro.getCliente().getCodigo());
                    }

                    break;
                case N_PROF_CARTEIRA_VENCIDOS:
                    p.put("fim", filtro.getInicio());
                    if (SuperControle.independente(ctx)) {
                        where.append(" WHERE professorSintetico.empresa.codigo = ").append(empresaZW);
                    } else {
                        where.append(" WHERE professorSintetico.empresa.codZW = ").append(empresaZW);
                    }
                    if (!treinoIndependente) {
                        where.append(" AND obj.situacao = 'AT' ");
                    }
                    where.append(" and obj.codigo in (SELECT pro.cliente.codigo FROM ProgramaTreino pro ");
                    where.append(" WHERE pro.dataTerminoPrevisto < :fim  ");
                    where.append(" AND NOT EXISTS(SELECT proN.codigo FROM ProgramaTreino proN ");
                    where.append(" WHERE proN.cliente.codigo = pro.cliente.codigo  AND ");
                    where.append("  proN.dataTerminoPrevisto > pro.dataTerminoPrevisto)) ");
                    where.append(montarFiltrosCliente(request, ctx, filtro, p, "obj"));
                    if (!professoresSelecionados.isEmpty()) {
                        where.append(" and professorSintetico.codigo IN (").append(professoresSelecionados).append(") ");
                    }
                    if (filtro.isApenasProfessoresAtivos()) {
                        where.append(" and professorSintetico.ativo is true ");
                    }
                    if (paginadorDTO != null) {
                        paginadorDTO.setQuantidadeTotalElementos(clienteDao.countWithParam(ctx, "codigo", where, p).longValue());
                    }

                    List<ClienteSintetico> resultadosCliVenc = clienteDao.findByParam(ctx, where, p);

                    for (ClienteSintetico cli : resultadosCliVenc) {

                        if ((filtro.isNaoRevisado() || filtro.isNovo() || filtro.isRevisado() || filtro.isRenovado())
                                && !filtrosProgramaCliente(ctx, filtro, cli)) {
                            continue;
                        }
                        addIndicador(ctx, agrupadores, professorIndic, cli.getProfessorSintetico(),
                                tipo, filtro, 1, cli, true);
                    }

                    break;
                case N_PROF_CARTEIRA_MEDIA_AVALIACAO:
                    p.put("inicio", Calendario.inicioMes(Calendario.anterior(Calendar.MONTH, filtro.getInicio())));
                    p.put("fim", Calendario.fimMes(filtro.getInicio()));
                    if (SuperControle.independente(ctx)) {
                        where.append(" WHERE professor.empresa.codigo = ").append(empresaZW);
                    } else {
                        where.append(" WHERE professor.empresa.codZW = ").append(empresaZW);
                    }
                    where.append(" and dataInicio BETWEEN :inicio AND :fim ");
                    if (!professoresSelecionados.isEmpty()) {
                        where.append(" and professor.codigo IN (").append(professoresSelecionados).append(") ");
                    }
                    if (filtro.isApenasProfessoresAtivos()) {
                        where.append(" and professor.ativo is true ");
                    }

                    where.append(" and nota <> null and cast(nota as integer) > 0 ");

                    where.append(montarFiltrosCliente(request, ctx, filtro, p, "obj.cliente"));

                    if (paginadorDTO != null) {
                        paginadorDTO.setQuantidadeTotalElementos(treinoRealizadoDao.countWithParam(ctx, "codigo", where, p).longValue());
                    }


                    List<TreinoRealizado> resultadosTre = treinoRealizadoDao.findByParam(ctx, where, p);
                    for (TreinoRealizado tr : resultadosTre) {
                        if (filtrosPrograma(ctx, filtro, tr.getProgramaTreinoFicha().getPrograma())
                                && tr.getNota() != null && Integer.valueOf(tr.getNota()) > 0
                                ) {
                            addIndicador(ctx, agrupadores, professorIndic, tr.getProfessor(),
                                    tipo, filtro,
                                    Integer.valueOf(tr.getNota()), tr, false);
                        }
                    }
                    for (Integer key : professorIndic.keySet()) {
                        Indicador indicador = professorIndic.get(key);
                        if (indicador.getObjetos().isEmpty()) {
                            continue;
                        }
                        indicador.setTotal(((indicador.getTotal().intValue() / indicador.getObjetos().size()) * 100 / 5));
                    }
                    break;
                case N_PROF_CARTEIRA_2_ESTRELAS:
                    p.put("inicio", Calendario.inicioMes(Calendario.anterior(Calendar.MONTH, filtro.getInicio())));
                    p.put("fim", Calendario.fimMes(filtro.getInicio()));
                    if (SuperControle.independente(ctx)) {
                        where.append(" WHERE professor.empresa.codigo = ").append(empresaZW);
                    } else {
                        where.append(" WHERE professor.empresa.codZW = ").append(empresaZW);
                    }
                    where.append(" and dataInicio BETWEEN :inicio AND :fim ");
                    where.append(" AND nota <> null AND cast(nota as integer) > 0 AND cast(nota as integer) <= 2");
                    if (!professoresSelecionados.isEmpty()) {
                        where.append(" and professor.codigo IN (").append(professoresSelecionados).append(") ");
                    }
                    if (filtro.isApenasProfessoresAtivos()) {
                        where.append(" and professor.ativo is true ");
                    }
                    where.append(montarFiltrosCliente(request, ctx, filtro, p, "obj.cliente"));

                    if (paginadorDTO != null) {
                        paginadorDTO.setQuantidadeTotalElementos(treinoRealizadoDao.countWithParam(ctx, "codigo", where, p).longValue());
                    }


                    List<TreinoRealizado> resultados2St = treinoRealizadoDao.findByParam(ctx, where, p);
                    for (TreinoRealizado tr : resultados2St) {
                        if (filtrosPrograma(ctx, filtro, tr.getProgramaTreinoFicha().getPrograma())) {
                            addIndicador(ctx, agrupadores, professorIndic, tr.getProfessor(),
                                    tipo, filtro, 1, tr, false);
                        }
                    }

                    break;
                case N_TEMPO_MEDIO_PERMANENCIA_TREINO:
                    Map<Integer, List<Integer>> professorTempoMap = new HashMap<>();
                    String[] ids = professoresSelecionados.split(",");

                    for (String id : ids) {
                        StringBuilder sql = new StringBuilder();
                        sql.append("SELECT cast(dataterminoprevisto as date) - cast(programatreino.datainicio  as date) FROM programatreino ");
                        sql.append("INNER JOIN ClienteSintetico cli on cli.codigo = programatreino.cliente_codigo WHERE ");
                        sql.append("cli.situacao = 'AT' ");
                        if (!professoresSelecionados.isEmpty()) {
                            sql.append(" and professorcarteira_codigo = ").append(id.trim()).append(" ");
                        }
                        sql.append("AND '").append(Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "yyyy-MM-dd"));
                        sql.append("' BETWEEN cast(programatreino.datainicio as date) AND cast(dataterminoprevisto as date) ");

                        List<Integer> valoresInt = programaTreinoDao.listOfObjects(ctx, sql.toString());

                        professorTempoMap.put(Integer.parseInt(id), valoresInt);
                    }

                    // Calcula a média para cada professor e adiciona indicador
                    for (Integer professorCode : professorTempoMap.keySet()) {
                        List<Integer> tempoList = professorTempoMap.get(professorCode);
                        int sum = 0;
                        for (Integer tempo : tempoList) {
                            sum += tempo;
                        }
                        int average = tempoList.isEmpty() ? 0 : sum / tempoList.size();

                        addIndicador(ctx, agrupadores, professorIndic,
                                professorDao.obterPorId(ctx, professorCode),
                                tipo, filtro, average, average, false);
                    }

                    break;
            }

            indicadores.put(tipo, professorIndic);
        }
        for (Integer key : agrupadores.keySet()) {
            AgrupadorIndicadores agrup = agrupadores.get(key);
            for (IndicadorEnum ind : indicadoresEnum) {
                Map<Integer, Indicador> indProfessores = indicadores.get(ind);
                agrup.getIndicadores().add(indProfessores.get(key));
            }
        }

        if (paginadorDTO != null) {
            paginadorDTO.setQuantidadeTotalElementos((long) agrupadores.values().size());
        }
        return new ArrayList<AgrupadorIndicadores>(agrupadores.values());
    }

    @Override
    public List<ClienteSintetico> carregarAlunosPorIndicadoresCarteiraProfessor(HttpServletRequest request, String ctx, PaginadorDTO paginadorDTO, Integer empresaZW, IndicadorEnum tipo, final String professoresSelecionados, final FiltrosGestaoTO filtro, Boolean treinoIndependente) throws Exception {
            StringBuilder where = new StringBuilder();
            Map<String, Object> p = new HashMap<String, Object>();
            List<ClienteSintetico> listReturn = new ArrayList<>();
            filtro.setInicio(Calendario.getDataComHoraZerada(filtro.getInicio()));
            filtro.setFim(Calendario.getDataComHora(filtro.getFim(), "23:59:59"));

        int maxResults = 50;
        int indiceInicial = 0;
        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? 50 : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
        }
        paginadorDTO.setSize((long) maxResults);
        paginadorDTO.setPage((long) indiceInicial);
            switch (tipo) {
                case N_PROF_CARTEIRA:
                    where.append(" WHERE obj.cliente.empresa = ").append(empresaZW);
                    where.append(" AND NOT EXISTS(SELECT pro.codigo FROM ProgramaTreino pro  WHERE pro.cliente.codigo = obj.cliente.codigo  AND obj.dataTerminoPrevisto < pro.dataTerminoPrevisto) ");
                    if (!professoresSelecionados.isEmpty()) {
                        where.append(" and obj.cliente.professorSintetico.codigo IN (").append(professoresSelecionados.replaceFirst(",", "")).append(")");
                    }

                    if (filtro.isApenasProfessoresAtivos()) {
                        where.append(" and obj.cliente.professorSintetico.ativo is true ");
                    }

                    where.append(montarFiltrosCliente(request, ctx, filtro, p, "obj.cliente"));
                    if (paginadorDTO != null) {
                        paginadorDTO.setQuantidadeTotalElementos(programaDao.countWithParam(ctx, "codigo", where, p).longValue());
                    }

                    where.append(paginadorDTO.getSQLOrderByUse().replace("terminoProgramaVigente", "dataTerminoPrevisto"));
                    List<ProgramaTreino> resultadosVenc = programaDao.findByParam(ctx, where, p, maxResults, indiceInicial);

                    for (ProgramaTreino pr : resultadosVenc) {
                        if (pr.getCliente() != null && !UteisValidacao.emptyNumber(pr.getCliente().getCodigo())) {
                            listReturn.add(pr.getCliente());
                        }
                    }

                    break;
                case N_PROF_CARTEIRA_SEM_TREINO:
                    if (filtro.isNaoRevisado() || filtro.isNovo() || filtro.isRevisado() || filtro.isRenovado()) {
                        break;
                    }
                    where.append(" WHERE obj.empresa = ").append(empresaZW);
                    where.append(" AND  obj.codigo NOT IN (SELECT pro.cliente.codigo FROM ProgramaTreino pro WHERE pro.cliente.codigo = obj.codigo) ");
                    if (!professoresSelecionados.isEmpty()) {
                        where.append(" and obj.professorSintetico.codigo IN (").append(professoresSelecionados.replaceFirst(",", "")).append(") ");
                    }
                    if (filtro.isApenasProfessoresAtivos()) {
                        where.append(" and obj.professorSintetico.ativo is true ");
                    }

                    where.append(montarFiltrosCliente(request, ctx, filtro, p, "obj"));

                    if (paginadorDTO != null) {
                        paginadorDTO.setQuantidadeTotalElementos(clienteDao.countWithParam(ctx, "codigo", where, p).longValue());
                    }

                    listReturn = clienteDao.findByParam(ctx, where, p, maxResults, indiceInicial);
                    break;
                case N_PROF_CARTEIRA_PROX_VENCIMENTO:
                    ConfiguracaoSistema diasAntes = configSistemaService.consultarPorTipo(ctx, ConfiguracoesEnum.DIAS_ANTES_VENCIMENTO);
                    Date depois = Uteis.somarCampoData(filtro.getInicio(), Calendar.DAY_OF_MONTH, diasAntes.getValorAsInteger());
                    p.put("antes", Calendario.getDataComHoraZerada(filtro.getInicio()));
                    p.put("depois", depois);
                    where.append(" WHERE obj.cliente.empresa = ").append(empresaZW).append(" and obj.dataTerminoPrevisto between :antes and :depois ");
                    where.append(" AND NOT EXISTS(SELECT pro.codigo FROM ProgramaTreino pro  WHERE pro.cliente.codigo = obj.cliente.codigo  AND obj.dataTerminoPrevisto < pro.dataTerminoPrevisto) ");
                    if (!professoresSelecionados.isEmpty()) {
                        where.append(" and obj.professorCarteira.codigo IN (").append(professoresSelecionados.replaceFirst(",", "")).append(") ");
                    }
                    if (filtro.isApenasProfessoresAtivos()) {
                        where.append(" and obj.professorCarteira.ativo is true ");
                    }

                    where.append(montarFiltrosCliente(request, ctx, filtro, p, "obj.cliente"));
                    if (paginadorDTO != null) {
                        paginadorDTO.setQuantidadeTotalElementos(programaDao.countWithParam(ctx, "codigo", where, p).longValue());
                    }
                    where.append(paginadorDTO.getSQLOrderByUse().replace("terminoProgramaVigente", "dataTerminoPrevisto"));
                    List<ProgramaTreino> resultadosCl = programaDao.findByParam(ctx, where, p, maxResults, indiceInicial);

                    for (ProgramaTreino pr : resultadosCl) {
                        if (pr.getCliente() != null && !UteisValidacao.emptyNumber(pr.getCliente().getCodigo())) {
                            listReturn.add(pr.getCliente());
                        }
                    }
                    break;
                case N_PROF_CARTEIRA_VENCIDOS:
                    p.put("fim", filtro.getInicio());
                    where.append(" WHERE obj.cliente.empresa = ").append(empresaZW);
                    if (!treinoIndependente) {
                        where.append(" AND obj.cliente.situacao = 'AT' ");
                    }
                    where.append(" and obj.codigo in (SELECT pro.codigo FROM ProgramaTreino pro ");
                    where.append(" WHERE pro.dataTerminoPrevisto < :fim  ");
                    where.append(" AND NOT EXISTS(SELECT proN.codigo FROM ProgramaTreino proN ");
                    where.append(" WHERE proN.cliente.codigo = pro.cliente.codigo  AND ");
                    where.append("  proN.dataTerminoPrevisto > pro.dataTerminoPrevisto)) ");

                    where.append(montarFiltrosCliente(request, ctx, filtro, p, "obj.cliente"));

                    if (!professoresSelecionados.isEmpty()) {
                        where.append(" and obj.cliente.professorSintetico.codigo IN (").append(professoresSelecionados.replaceFirst(",", "")).append(") ");
                    }
                    if (filtro.isApenasProfessoresAtivos()) {
                        where.append(" and obj.cliente.professorSintetico.ativo is true ");
                    }
                    if (paginadorDTO != null) {
                        paginadorDTO.setQuantidadeTotalElementos(programaDao.countWithParam(ctx, "codigo", where, p).longValue());
                    }
                    where.append(paginadorDTO.getSQLOrderByUse().replace("terminoProgramaVigente", "dataTerminoPrevisto"));

                    List<ProgramaTreino> resultadosCliVenc = programaDao.findByParam(ctx, where, p, maxResults, indiceInicial);

                    for (ProgramaTreino pro : resultadosCliVenc) {

                        if ((filtro.isNaoRevisado() || filtro.isNovo() || filtro.isRevisado() || filtro.isRenovado())
                                && !filtrosProgramaCliente(ctx, filtro, pro.getCliente())) {
                            continue;
                        }
                        listReturn.add(pro.getCliente());
                    }

                    break;
                case N_PROF_CARTEIRA_MEDIA_AVALIACAO:
                    p.put("inicio", Calendario.inicioMes(Calendario.anterior(Calendar.MONTH, filtro.getInicio())));
                    p.put("fim", Calendario.fimMes(filtro.getInicio()));
                    where.append(" WHERE obj.cliente.empresa = ").append(empresaZW).append(" and obj.dataInicio BETWEEN :inicio AND :fim ");
                    if (!professoresSelecionados.isEmpty()) {
                        where.append(" and professor.codigo IN (").append(professoresSelecionados.replaceFirst(",", "")).append(") ");
                    }
                    if (filtro.isApenasProfessoresAtivos()) {
                        where.append(" and obj.professor.ativo is true ");
                    }

                    where.append(" and obj.nota <> null and cast(obj.nota as integer) > 0 ");

                    where.append(montarFiltrosCliente(request, ctx, filtro, p, "obj.cliente"));

                    if (paginadorDTO != null) {
                        paginadorDTO.setQuantidadeTotalElementos(treinoRealizadoDao.countWithParam(ctx, "codigo", where, p).longValue());
                    }
                    where.append(paginadorDTO.getSQLOrderByUse());

                    List<TreinoRealizado> resultadosTre = treinoRealizadoDao.findByParam(ctx, where, p, maxResults, indiceInicial);
                    for (TreinoRealizado tr : resultadosTre) {
                        if (filtrosPrograma(ctx, filtro, tr.getProgramaTreinoFicha().getPrograma())
                                && tr.getNota() != null && Integer.valueOf(tr.getNota()) > 0
                        ) {
                            listReturn.add(tr.getCliente());
                        }
                    }
                    break;
                case N_PROF_CARTEIRA_2_ESTRELAS:
                    p.put("inicio", Calendario.inicioMes(Calendario.anterior(Calendar.MONTH, filtro.getInicio())));
                    p.put("fim", Calendario.fimMes(filtro.getInicio()));
                    where.append(" WHERE obj.cliente.empresa = ").append(empresaZW).append(" and obj.dataInicio BETWEEN :inicio AND :fim ");
                    where.append(" AND nota <> null AND cast(nota as integer) > 0 AND cast(nota as integer) <= 2");
                    if (!professoresSelecionados.isEmpty()) {
                        where.append(" and obj.professor.codigo IN (").append(professoresSelecionados.replaceFirst(",", "")).append(") ");
                    }

                    where.append(montarFiltrosCliente(request, ctx, filtro, p, "obj.cliente"));

                    if (filtro.isApenasProfessoresAtivos()) {
                        where.append(" and obj.professor.ativo is true ");
                    }
                    if (paginadorDTO != null) {
                        paginadorDTO.setQuantidadeTotalElementos(treinoRealizadoDao.countWithParam(ctx, "codigo", where, p).longValue());
                    }
                    where.append(paginadorDTO.getSQLOrderByUse());

                    List<TreinoRealizado> resultados2St = treinoRealizadoDao.findByParam(ctx, where, p, maxResults, indiceInicial);
                    for (TreinoRealizado tr : resultados2St) {
                        if (filtrosPrograma(ctx, filtro, tr.getProgramaTreinoFicha().getPrograma())) {
                            listReturn.add(tr.getCliente());
                        }

                    }

                    break;
            }
            return listReturn;

    }

    private void addIndicador(final String ctx, final Map<Integer, AgrupadorIndicadores> agrupadores, final Map<Integer, Indicador> professorIndic,
            final ProfessorSintetico professor, IndicadorEnum tipo, final FiltrosGestaoTO filtro,
            final Integer valorSomar, final Object obj, boolean obterDataPrograma) throws Exception {
        if (professor == null) {
            return;
        }
        AgrupadorIndicadores agrup = agrupadores.computeIfAbsent(
                professor.getCodigo(),
                cod -> new AgrupadorIndicadores(cod, professor.getNome())
        );

        Indicador ind = professorIndic.computeIfAbsent(
                professor.getCodigo(),
                cod -> {
                    Indicador novo = new Indicador(cod, professor.getNome(), tipo, filtro.getInicio(), filtro.getFim());
                    novo.setObjetos(new ArrayList<>());
                    novo.setTotal(0);
                    return novo;
                }
        );

        if (tipo == N_ATIVIDADE_PROF_TREINO_ACOMPANHADO || !obterDataPrograma) {
            ind.getObjetos().add(obj);
        }
        if (obterDataPrograma) {
            ClienteSintetico cli = (ClienteSintetico) obj;
            ProgramaTreino pt = programaService.consultarSomenteDataTreinoAtual(ctx, cli);
            cli.setProgramaVigente(pt);
            ind.getObjetos().add(cli);
        }
        if (tipo == IndicadorEnum.N_PROF_TREINO_ACOMPANHADO && obj instanceof ClienteAcompanhamento) {
            ClienteAcompanhamento acompanhamento = (ClienteAcompanhamento) obj;
            ind.setDataInicio(acompanhamento.getInicio());
        }
        ind.setTotal(ind.getTotal().intValue() + valorSomar);
    }

    private List<AgrupadorIndicadores> montarIndicadoresAtividadesProfessores(final String ctx,
            final Integer empresaZW, final FiltrosGestaoTO filtro, List<IndicadorEnum> indicadoresEnum,
            final String professoresSelecionados) throws ServiceException, Exception {
        Map<Integer, AgrupadorIndicadores> agrupadores = new HashMap<Integer, AgrupadorIndicadores>();
        Map<IndicadorEnum, Map<Integer, Indicador>> indicadores = new HashMap<IndicadorEnum, Map<Integer, Indicador>>();

        for (IndicadorEnum tipo : indicadoresEnum) {
            Map<Integer, Indicador> professorIndic = new HashMap<Integer, Indicador>();
            StringBuilder where = new StringBuilder(" WHERE obj.cliente.empresa = ").append(empresaZW);
            Map<String, Object> p = new HashMap<String, Object>();
            p.put("inicio", filtro.getInicio());
            p.put("fim", Calendario.fimDoDia(filtro.getFim()));
//            StringBuilder wherePadrao = new StringBuilder("WHERE obj.codigo IN (SELECT pro.cliente.codigo FROM ProgramaTreino pro ");
//            wherePadrao.append("  WHERE dataLancamento BETWEEN :inicio and :fim ");
            switch (tipo) {
                case N_PROF_TREINO_NOVO:
                    where.append(" and dataLancamento BETWEEN :inicio and :fim ");
                    where.append("  AND programaTreinoRenovacao IS NULL ");
                    if (!professoresSelecionados.isEmpty()) {
                        where.append(" and professorMontou.codigo IN (").append(professoresSelecionados).append(") ");
                    }
                    List<ProgramaTreino> resultadosCli = programaDao.findByParam(ctx, where, p);
                    for (ProgramaTreino pro : resultadosCli) {
                        addIndicador(ctx, agrupadores, professorIndic, pro.getProfessorMontou(),
                                tipo, filtro, 1, pro, false);
                    }

                    break;
                case N_PROF_TREINO_RENOVADO:
                    where.append("  and dataLancamento BETWEEN :inicio and :fim ");
                    where.append("  AND programaTreinoRenovacao IS NOT NULL ");
                    if (!professoresSelecionados.isEmpty()) {
                        where.append(" and professorMontou.codigo IN (").append(professoresSelecionados).append(") ");
                    }
                    List<ProgramaTreino> resultadosRen = programaDao.findByParam(ctx, where, p);
                    for (ProgramaTreino pro : resultadosRen) {
                        addIndicador(ctx, agrupadores, professorIndic, pro.getProfessorMontou(),
                                tipo, filtro, 1, pro, false);
                    }

                    break;
                case N_PROF_TREINO_ACOMPANHADO:
                    where.append(" and inicio BETWEEN :inicio AND :fim  ");
                    if (!professoresSelecionados.isEmpty()) {
                        where.append(" and professor.codigo IN (").append(professoresSelecionados).append(") ");
                    }
                    List<ClienteAcompanhamento> resultadosAc = clienteAcompanhamentyDao.findByParam(ctx, where, p);
                    for (ClienteAcompanhamento acom : resultadosAc) {
                        addIndicador(ctx, agrupadores, professorIndic, acom.getProfessor(),
                                tipo, filtro, 1, acom, false);
                    }
                    break;
                case N_PROF_TREINO_REVISADO:
                    where.append(" and dataRegistro BETWEEN :inicio AND :fim ");
                    if (!professoresSelecionados.isEmpty()) {
                        where.append(" and professorRevisou.codigo IN (").append(professoresSelecionados).append(") ");
                    }
                    List<HistoricoRevisaoProgramaTreino> resultadosRev = historicoRevisaoDao.findByParam(ctx, where, p);
                    for (HistoricoRevisaoProgramaTreino hrev : resultadosRev) {
                        addIndicador(ctx, agrupadores, professorIndic, hrev.getProfessorRevisou(),
                                tipo, filtro, 1, hrev, false);
                    }
                    break;
                case N_PROF_TREINO_REVISAR:
                    where.append("  and dataProximaRevisao BETWEEN :inicio and :fim ");
                    if (!professoresSelecionados.isEmpty()) {
                        where.append(" and professorMontou.codigo IN (").append(professoresSelecionados).append(") ");
                    }
                    List<ProgramaTreino> resultadosRevisar = programaDao.findByParam(ctx, where, p);
                    for (ProgramaTreino pro : resultadosRevisar) {
                        addIndicador(ctx, agrupadores, professorIndic, pro.getProfessorMontou(),
                                tipo, filtro, 1, pro, false);
                    }

                    break;
                case N_ATIVIDADE_PROF_TREINO_ACOMPANHADO:
                    StringBuilder sql = new StringBuilder();
                    sql.append(" where obj.codigo_professor_acompanhamento in (").append(professoresSelecionados.replaceFirst(",", "")).append(") ");

                    List<AtividadeFicha> resultadosRevisartr = atividadeFichaDao.findByParam(ctx, sql, new HashMap<String, Object>());
                    for (AtividadeFicha atividade : resultadosRevisartr) {
                        ProfessorSintetico professorSinteticoa = professorSinteticoDao.obterPorId(ctx,atividade.getCodigo_professor_acompanhamento());
                        addIndicador(ctx, agrupadores, professorIndic, professorSinteticoa,
                                tipo, filtro, 1, atividade, false);
                    }
                    break;
            }
            indicadores.put(tipo, professorIndic);
        }
        for (Integer key : agrupadores.keySet()) {
            AgrupadorIndicadores agrup = agrupadores.get(key);
            for (IndicadorEnum ind : indicadoresEnum) {
                Map<Integer, Indicador> indProfessores = indicadores.get(ind);
                agrup.getIndicadores().add(indProfessores.get(key));
            }
        }
        return new ArrayList<AgrupadorIndicadores>(agrupadores.values());

    }

    @Override
    public List<ClienteSintetico> carregarAlunosPorAtividadeProfessor(final String ctx,
                                                                     final Integer empresaZW, final FiltrosGestaoTO filtro, IndicadorEnum tipo,
                                                                     final String professoresSelecionados) throws Exception {
        StringBuilder where = new StringBuilder(" WHERE obj.cliente.empresa = ").append(empresaZW);
        Map<String, Object> p = new HashMap<String, Object>();
        List<ClienteSintetico> listReturn = new ArrayList<>();
        p.put("inicio", filtro.getInicio());
        p.put("fim", Calendario.fimDoDia(filtro.getFim()));

        switch (tipo) {
            case N_PROF_TREINO_NOVO:
                where.append(" and obj.dataLancamento BETWEEN :inicio and :fim ");
                where.append("  AND obj.programaTreinoRenovacao IS NULL ");
                if (!professoresSelecionados.isEmpty()) {
                    where.append(" and obj.professorMontou.codigo IN (").append(professoresSelecionados.replaceFirst(",", "")).append(") ");
                }
                List<ProgramaTreino> resultadosCli = programaDao.findByParam(ctx, where, p);
                for (ProgramaTreino pro : resultadosCli) {
                    listReturn.add(pro.getCliente());
                }

                break;
            case N_PROF_TREINO_RENOVADO:
                where.append("  and obj.dataLancamento BETWEEN :inicio and :fim ");
                where.append("  AND obj.programaTreinoRenovacao IS NOT NULL ");
                if (!professoresSelecionados.isEmpty()) {
                    where.append(" and obj.professorMontou.codigo IN (").append(professoresSelecionados.replaceFirst(",", "")).append(") ");
                }
                List<ProgramaTreino> resultadosRen = programaDao.findByParam(ctx, where, p);
                for (ProgramaTreino pro : resultadosRen) {
                    listReturn.add(pro.getCliente());
                }

                break;
            case N_PROF_TREINO_ACOMPANHADO:
                where.append(" and obj.inicio BETWEEN :inicio AND :fim  ");
                if (!professoresSelecionados.isEmpty()) {
                    where.append(" and obj.professor.codigo IN (").append(professoresSelecionados.replaceFirst(",", "")).append(") ");
                }
                List<ClienteAcompanhamento> resultadosAc = clienteAcompanhamentyDao.findByParam(ctx, where, p);
                for (ClienteAcompanhamento acom : resultadosAc) {
                    acom.getCliente().setDia(acom.getInicio());
                    listReturn.add(acom.getCliente());
                }
                break;
            case N_PROF_TREINO_REVISADO:
                where.append(" and obj.dataRegistro BETWEEN :inicio AND :fim ");
                if (!professoresSelecionados.isEmpty()) {
                    where.append(" and obj.professorRevisou.codigo IN (").append(professoresSelecionados.replaceFirst(",", "")).append(") ");
                }
                List<HistoricoRevisaoProgramaTreino> resultadosRev = historicoRevisaoDao.findByParam(ctx, where, p);
                for (HistoricoRevisaoProgramaTreino hrev : resultadosRev) {
                    listReturn.add(hrev.getCliente());
                }
                break;
            case N_PROF_TREINO_REVISAR:
                where.append("  and obj.dataProximaRevisao BETWEEN :inicio and :fim ");
                if (!professoresSelecionados.isEmpty()) {
                    where.append(" and obj.professorMontou.codigo IN (").append(professoresSelecionados.replaceFirst(",", "")).append(") ");
                }
                List<ProgramaTreino> resultadosRevisar = programaDao.findByParam(ctx, where, p);
                for (ProgramaTreino pro : resultadosRevisar) {
                    listReturn.add(pro.getCliente());
                }

                break;
            case N_ATIVIDADE_PROF_TREINO_ACOMPANHADO:

                StringBuilder sql = new StringBuilder();
                sql.append(" where obj.codigo_professor_acompanhamento in (").append(professoresSelecionados.replaceFirst(",", "")).append(") ");

                List<AtividadeFicha> resultadosRevisartr = atividadeFichaDao.findByParam(ctx, sql, new HashMap<String, Object>());
                for (AtividadeFicha atividade : resultadosRevisartr) {
                    atividade.getFicha().getProgramas().forEach(programaficha -> {
                        ClienteSintetico cliente = programaficha.getPrograma().getCliente();
                        ClienteAcompanhamento acompanhamento = clienteAcompanhamentyDao.buscarUltimoAcompanhamentoDoCliente(
                                ctx, cliente.getCodigo(), atividade.getCodigo_professor_acompanhamento()
                        );
                        if (acompanhamento != null && acompanhamento.getInicio() != null) {
                            cliente.setDia(acompanhamento.getInicio());
                        }

                        listReturn.add(cliente);
                    });
                }
                break;
        }
        return listReturn;
    }

    public List<Notificacao> gestaoNotificacoes(String ctx, final Integer empresaZW,
            final FiltrosGestaoTO filtro) throws ServiceException {
        try {
            String professores = "";
            for (String cod : filtro.getProfessoresSelecionados()) {
                professores += "," + cod;
            }
            String alunos = "";
            for (String cod : filtro.getAlunosSelecionados()) {
                alunos += "," + cod;
            }
            String gravidade = montarTiposGravidade(filtro);

            Map<String, Object> params = new HashMap<String, Object>();
            filtro.getProfessoresSelecionados();
            final StringBuilder sql = new StringBuilder("select obj from Notificacao obj ");
            sql.append(" WHERE obj.cliente.empresa = ").append(empresaZW).
                    append(" and obj.dataRegistro BETWEEN :inicio AND :fim ");

            if (!professores.isEmpty()) {
                sql.append(" AND obj.professor.codigo IN (").append(professores.replaceFirst(",", "")).append(") ");
            }
            if (!alunos.isEmpty()) {
                sql.append(" AND obj.cliente.codigo IN (").append(alunos.replaceFirst(",", "")).append(") ");
            }
            if (!gravidade.isEmpty()) {
                sql.append(" AND obj.tipo IN (").append(gravidade).append(") ");
            }
            sql.append(" order by obj.cliente.nome ");

            params.put("inicio", Calendario.getDataComHoraZerada(filtro.getInicio()));
            params.put("fim", Calendario.getDataComHora(filtro.getFim(), "23:59:59"));
//            params.put("professor", filtro.getp);
            if (filtro.getHoraInicio() == null
                    || filtro.getHoraInicio().isEmpty()
                    || filtro.getHoraFim() == null
                    || filtro.getHoraFim().isEmpty()
                    || (filtro.getHoraInicio().equals("00:00") && filtro.getHoraFim().equals("23:59"))) {
                return notificacaoDao.findByParam(ctx, sql.toString(), params);
            } else {
                //TODO: rever essa lógica
                String inicio = filtro.getHoraInicio();
                String fim = filtro.getHoraFim();
                Date datainicio = Calendario.getDataComHora(Calendario.hoje(), inicio + ":59");
                Date datafim = Calendario.getDataComHora(Calendario.hoje(), fim + ":59");

                List<Notificacao> nots = notificacaoDao.findByParam(ctx, sql.toString(), params);
                List<Notificacao> notResult = new ArrayList<Notificacao>();
                for (Notificacao notificacao : nots) {
                    Date dataComHora = Calendario.getDataComHora(Calendario.hoje(), Uteis.getDataAplicandoFormatacao(notificacao.getDataRegistro(), "HH:mm:ss"));
                    if (dataComHora.after(datainicio) && dataComHora.before(datafim)) {
                        notResult.add(notificacao);
                    }
                }
                return notResult;
            }

        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    private String montarTiposGravidade(FiltrosGestaoTO filtro) {
        String tipos = "";
        for (GenericoTO gen : filtro.getGravidades()) {
            if (gen.getEscolhido()) {
                for (TipoNotificacaoEnum tipo : TipoNotificacaoEnum.values()) {
                    if (tipo.getGravidade().equals(GravidadeNotificacaoEnum.valueOf(gen.getLabel()))) {
                        tipos += "," + tipo.ordinal();
                    }
                }
            }
        }
        return tipos.replaceFirst(",", "");
    }

    private String montarFiltrosCliente(HttpServletRequest request, String ctx, final FiltrosGestaoTO filtro, Map<String, Object> p, String name) {
        StringBuilder and = new StringBuilder();
        // SITUACAO ALUNO
        String situacoesAluno = "";
        boolean outros = false;
        boolean visitante = false;
        boolean inativo = false;
        boolean ativo = false;
        /* condição criada para verificar se é do treino antigo (JSF) ou do novo (SPA)
           futuramente sera refeita, quando o atributo request for null vem do treino antigo */
        if (request == null) {
            for (GenericoTO sit : filtro.getSituacoesAluno()) {
                if (sit.getEscolhido()) {
                    SituacaoAlunoEnum situacao = SituacaoAlunoEnum.valueOf(sit.getLabel());
                    if (situacao.equals(SituacaoAlunoEnum.OUTROS)) {
                        outros = true;
                    } else if (situacao.equals(SituacaoAlunoEnum.VISITANTE)) {
                        visitante = true;
                    } else {
                        situacoesAluno += ", '" + situacao.getCodigo() + "'";
                    }

                }
            }
            if (situacoesAluno.isEmpty()) {
                and.append(outros || visitante ? " and (" : "");
            } else {
                and.append(" and ").append(outros || visitante ? "(" : "").append(name).append(".situacaoContrato in (").append(situacoesAluno.replaceFirst(",", "")).append(")");
            }
            if (outros) {
                String not = "";
                for (SituacaoAlunoEnum sit : SituacaoAlunoEnum.values()) {
                    if (!sit.equals(SituacaoAlunoEnum.OUTROS)) {
                        not += ", '" + sit.getCodigo() + "'";
                    }
                }
                and.append(situacoesAluno.isEmpty() ? " (" : " OR (");
                and.append(name).append(".situacaoContrato not in (").append(not.replaceFirst(",", "")).append(")");
                and.append(" AND ").append(name).append(".situacao NOT LIKE 'VI' )").append(visitante ? "" : ")");

            }
            if (visitante) {
                if (situacoesAluno.isEmpty() && !outros) {
                    and.append(name).append(".situacao LIKE 'VI')");
                } else {
                    and.append(" OR (").append(name).append(".situacao LIKE 'VI'))");
                }
            }
            // SITUACAO CONTRATO
            String situacoesContrato = "";
            for (GenericoTO sit : filtro.getSituacoesContrato()) {
                if (sit.getEscolhido()) {
                    SituacaoContratoEnum situacao = SituacaoContratoEnum.valueOf(sit.getLabel());
                    situacoesContrato += ", '" + situacao.getCodigo() + "'";
                }
            }
            if (!situacoesContrato.isEmpty()) {
                and.append(" and ").append(name).append(".situacaoMatriculaContrato in (").append(situacoesContrato.replaceFirst(",", "")).append(")");
            }

        }else {
            if (SuperControle.independente(ctx)) {
                for (GenericoTO sit : filtro.getSituacoesAluno()) {
                    SituacaoAlunoEnum situacao = SituacaoAlunoEnum.valueOf(sit.getLabel());
                    if(sit.getLabel().equals("ATIVO")){
                        situacoesAluno += ", 'AT'";
                    } else {
                        situacoesAluno += ", '" + situacao.getCodigo() + "'";
                    }
                }
                if(!situacoesAluno.isEmpty()) {
                    and.append(" and (").append(name).append(".situacao in (").append(situacoesAluno.replaceFirst(",", "")).append("))");
                }
            }else {
                for (GenericoTO sit : filtro.getSituacoesAluno()) {
                    SituacaoAlunoEnum situacao = SituacaoAlunoEnum.valueOf(sit.getLabel());
                    situacoesAluno += ", '" + situacao.getCodigo() + "'";
                }

                if(!situacoesAluno.isEmpty()) {
                    and.append(" and ((").append(name).append(".situacaoContrato in (").append(situacoesAluno.replaceFirst(",", "")).append("))");
                    and.append(" or (").append(name).append(".situacao in (").append(situacoesAluno.replaceFirst(",", "")).append(")))");
                }
            }
        }
        return and.toString();
    }

    private boolean filtrosProgramaCliente(final String ctx, final FiltrosGestaoTO filtro, final ClienteSintetico cli) throws Exception {
        ProgramaTreino pro = programaService.consultarUltimoTreinoDataBaseAluno(ctx, cli, filtro.getInicio());
        return filtrosPrograma(ctx, filtro, pro);
    }

    private boolean filtrosPrograma(final String ctx, final FiltrosGestaoTO filtro, final ProgramaTreino programa) throws Exception {
        if ((!filtro.isNovo() && !filtro.isRenovado())
                || (filtro.isNovo() && filtro.isRenovado())) {
            return verificarProgramaRevisado(ctx, programa, filtro);
        }
        if (filtro.isNovo() && programa.getProgramaTreinoRenovacao() == null) {
            return verificarProgramaRevisado(ctx, programa, filtro);
        }
        if (programa.getProgramaTreinoRenovacao() != null && filtro.isRenovado()) {
            return verificarProgramaRevisado(ctx, programa, filtro);
        }
        return false;
    }

    private boolean verificarProgramaRevisado(final String ctx, final ProgramaTreino programa, FiltrosGestaoTO filtro) throws Exception {
        if ((!filtro.isNaoRevisado() && !filtro.isRevisado())
                || (filtro.isNaoRevisado() && filtro.isRevisado())) {
            return true;
        }
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("codprograma", programa.getCodigo());
        params.put("coddataRegistro", Calendario.getDataComHora(filtro.getInicio(), "23:59:59"));

        List<HistoricoRevisaoProgramaTreino> lista = historicoRevisaoDao.findByParam(ctx, new StringBuilder(" WHERE obj.programa.codigo = :codprograma AND obj.dataRegistro <= :coddataRegistro"), params);
        if (filtro.isNaoRevisado() && lista.isEmpty()) {
            return true;
        }
        if (filtro.isRevisado() && !lista.isEmpty()) {
            return true;
        }
        return false;
    }

    private void montarIndicadores(final Integer empresaZW,
            List<SelectItem> listaProfessores, CategoriaIndicadorEnum categoria,
            final String ctx, FiltrosGestaoTO filtro,
            boolean ignorarZerados, List<AgrupadorIndicadores> agrupadores) throws ServiceException {
        IndicadorEnum[] inds = IndicadorEnum.values();
        for (SelectItem item : listaProfessores) {
            List<Indicador> l = new ArrayList<Indicador>();
            Integer codProfessor = (Integer) item.getValue();
            String nomeProfessor = item.getLabel();
            for (int i = 0; i < inds.length; i++) {
                IndicadorEnum indicadorEnum = inds[i];
                if (categoria != null && indicadorEnum.getCategoria() != categoria) {
                    continue;
                }
                Indicador indicador = calcularIndicador(ctx, empresaZW, indicadorEnum, filtro,
                        codProfessor, nomeProfessor, ResultEnum.COUNT);
                if (ignorarZerados && indicador.getTotal().intValue() == 0) {
                    continue;
                } else {
                    l.add(indicador);
                }
            }
            if (ignorarZerados && l.isEmpty()) {
                continue;
            }
            AgrupadorIndicadores agrupador = new AgrupadorIndicadores(codProfessor, nomeProfessor);
            agrupador.setIndicadores(l);
            agrupadores.add(agrupador);
        }
    }


    @Override
    public List<ProgramaTreino> gestaoAndamento(String ctx, final Integer empresaZW, final FiltrosGestaoTO filtro) throws ServiceException {
        try {
            String professores = "";
            for (String cod : filtro.getProfessoresSelecionados()) {
                professores += "," + cod;
            }
            String alunos = "";
            for (String cod : filtro.getAlunosSelecionados()) {
                alunos += "," + cod;
            }
            Map<String, Object> params = new HashMap<String, Object>();
            final StringBuilder sql = new StringBuilder("select obj from ProgramaTreino obj ");
            sql.append(" WHERE obj.cliente.empresa = ").append(empresaZW);
            sql.append(" and (obj.dataInicio = :inicio or ");
            sql.append(" obj.dataTerminoPrevisto = :inicio or ");
            sql.append(" :inicio BETWEEN obj.dataInicio AND obj.dataTerminoPrevisto)");

            if (!professores.isEmpty()) {
                sql.append(" AND ( obj.professorCarteira.codigo IN (").append(professores.replaceFirst(",", "")).append(") ");
                sql.append(" OR obj.professorMontou.codigo IN (").append(professores.replaceFirst(",", "")).append(")) ");
            }
            if (!alunos.isEmpty()) {
                sql.append(" AND obj.cliente.codigo IN (").append(alunos.replaceFirst(",", "")).append(") ");
            }
            sql.append(" order by obj.nrTreinosRealizados DESC ");

            params.put("inicio", Calendario.getDataComHoraZerada(filtro.getInicio()));
            return programaDao.findByParam(ctx, sql.toString(), params);
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    @Override
    public List<ProgramaTreinoAndamentoTO> gestaoAndamento(String ctx, final Integer empresaZW, final FiltroGestaoProgramaDTO filtro, PaginadorDTO paginadorDTO) throws ServiceException {
        try {
            return programaDao.consultarAndamento(ctx, empresaZW, filtro, paginadorDTO);
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    @Override
    public List<ExecucoesTreinoTO> gestaoExecucoesTreino(String ctx, final Integer empresaZW, final FiltroGestaoProgramaDTO filtro, PaginadorDTO paginadorDTO) throws ServiceException {
        try {
            return programaDao.consultarExecucoesTreino(ctx, empresaZW, filtro, paginadorDTO);
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

}
