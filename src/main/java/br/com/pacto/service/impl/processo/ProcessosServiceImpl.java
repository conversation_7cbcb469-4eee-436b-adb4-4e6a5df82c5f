package br.com.pacto.service.impl.processo;

import br.com.pacto.dao.intf.avaliacao.ParQDao;
import br.com.pacto.dao.intf.configuracoes.ConfiguracaoSistemaDao;
import br.com.pacto.dao.intf.pessoa.PessoaDao;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.conexao.ConexaoZWServiceImpl;
import br.com.pacto.service.intf.processo.ProcessosService;
import br.com.pacto.util.UteisValidacao;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class ProcessosServiceImpl implements ProcessosService {

    @Autowired
    private ConfiguracaoSistemaDao configuracaoDao;
    @Autowired
    private ParQDao parQDao;
    @Autowired
    private ConexaoZWServiceImpl conexaoZWService;
    @Autowired
    private PessoaDao pessoaDao;

    @Override
    public String createUnaccent(String ctx) throws Exception {
        try {
            try (ResultSet rs = configuracaoDao.createStatement(ctx, "select exists (select * from pg_extension where upper(extname) like 'UNACCENT');")) {
                if (rs.next() && rs.getBoolean("exists")) {
                    return "Extensão unaccent já existe neste banco";
                } else {
                    configuracaoDao.executeNativeSQL(ctx, "create extension unaccent;");
                    return "Extensão unaccent criada com sucesso";
                }
            }
        } catch (Exception e) {
            Uteis.logar(e, ProcessosService.class);
            throw new ServiceException(e);
        }
    }

    @Override
    public List<String> replicarAssinaturaContratoParaParq(String ctx, Integer empresaZW, Boolean apenasValidar) throws Exception {
        System.out.println("INICIANDO PROCESSO DE REPLICAR ASSINATURA CONTRATO PARA ASSINATURA PARQ REPETIDAS PARA CHAVE: " + ctx);
        // consulta para identificar todas assinaturas parq repetidas
        StringBuilder sql = new StringBuilder();
        sql.append("WITH repetidos AS ( \n");
        sql.append("    SELECT urlassinatura, COUNT(*) AS total, COUNT(DISTINCT cliente_codigo) AS diferentes \n");
        sql.append("    FROM respostaclienteparq \n");
        sql.append("    GROUP BY urlassinatura \n");
        sql.append("    HAVING COUNT(*) > 1 AND COUNT(DISTINCT cliente_codigo) > 1 \n");
        sql.append(") \n");
        sql.append("SELECT c.nome, c.codigo as codigoclientetr, c.matricula, c.codigocliente as codigoclientezw, c.situacao, r.codigo, r.urlassinatura \n");
        sql.append("FROM respostaclienteparq r \n");
        sql.append("JOIN repetidos rep ON r.urlassinatura = rep.urlassinatura \n");
        sql.append("INNER JOIN clientesintetico c on c.codigo = r.cliente_codigo \n");
        sql.append("WHERE c.empresa = ").append(empresaZW).append(" \n");
        sql.append("ORDER by r.cliente_codigo, r.dataresposta desc");

        Map<Integer, JSONObject> mapAlunosAssinaturaParq = new HashMap<Integer, JSONObject>();
        List<String> listaLogRetorno = new ArrayList<>();
        listaLogRetorno.add("#### ALUNOS COM ASSINATURA REPETIDA ###############################################################");
        String assinaturaRepetida = "";
        String listaMatriculas = "";
        Integer qt = 0;

        try (ResultSet rs = parQDao.createStatement(ctx, sql.toString())) {
            while (rs.next()) {
                JSONObject objeto = new JSONObject();
                objeto.put("nome", rs.getString("nome"));
                objeto.put("matricula", rs.getString("matricula"));
                objeto.put("codigoclientetr", rs.getString("codigoclientetr"));
                objeto.put("codigoclientezw", rs.getString("codigoclientezw"));
                objeto.put("situacao", rs.getString("situacao"));
                objeto.put("codigoRespostaParq", rs.getString("codigo"));
                if (UteisValidacao.emptyString(assinaturaRepetida)) {
                    assinaturaRepetida = rs.getString("urlassinatura");
                }
                listaMatriculas += "," + rs.getString("matricula");
                listaLogRetorno.add(rs.getString("nome") + " - MAT: " + rs.getString("matricula") + " - SITUACAO: " + rs.getString("situacao"));
                mapAlunosAssinaturaParq.put(rs.getInt("codigoclientezw"), objeto);
                qt++;
            }
        }

        listaMatriculas = listaMatriculas.replaceFirst(",", "");
        System.out.println("MATRICULAS: " + listaMatriculas);
        listaLogRetorno.add("#### TOTAL: " + qt);
        listaLogRetorno.add("###################################################################################################");
        listaLogRetorno.add("");

        if (UteisValidacao.emptyString(listaMatriculas)) {
            listaLogRetorno.add("#### NÃO FOI LOCALIZADO NENHUM ALUNO COM ASSINATURA PARQ REPETIDA, PROCESSO ENCERRADO #############");
        } else {
            if (apenasValidar) {
                listaLogRetorno.add("#### ALUNOS COM ASSINATURA REPETIDA E POSSUEM CONTRATO ASSINADO ###############################");
            } else {
                listaLogRetorno.add("#### ALUNOS COM ASSINATURA DO CONTRATO REPLICADA PARA A ASSINATURA DO PARQ ####################");
            }

            // consulta para identificar contratos assinados dos alunos que estão com a assinatura parq repetida
            StringBuilder sqlZw = new StringBuilder();
            sqlZw.append("SELECT DISTINCT ON (cli.codigo) cli.codigo AS codigo_cliente, pes.nome, cli.codigomatricula, cad.assinatura, cad.lancamento,  con.datalancamento \n");
            sqlZw.append("FROM contratoassinaturadigital cad \n");
            sqlZw.append("INNER JOIN contrato con ON con.codigo = cad.contrato \n");
            sqlZw.append("INNER JOIN cliente cli ON cli.pessoa = con.pessoa \n");
            sqlZw.append("INNER JOIN pessoa pes ON pes.codigo = cli.pessoa \n");
            sqlZw.append("WHERE cli.codigomatricula IN (").append(listaMatriculas).append(") \n");
            sqlZw.append("ORDER BY cli.codigo, cad.lancamento DESC, con.datalancamento DESC");
            qt = 0;

            try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
                try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sqlZw.toString(), conZW)) {
                    while (rs.next()) {
                        if (apenasValidar) {
                            listaLogRetorno.add("APENAS VALIDAÇÃO! - " + rs.getString("nome") + " - MAT: " + rs.getString("codigomatricula"));
                        } else {
                            try {
                                // atualizar a assinatura do parq com a assinatura do contrato
                                Integer codCliTR = Integer.valueOf(mapAlunosAssinaturaParq.get(rs.getInt("codigo_cliente")).getString("codigoclientetr"));
                                String assinaturaContrato = rs.getString("assinatura");
                                parQDao.executeNativeSQL(ctx, "update respostaclienteparq set urlassinatura = '" + assinaturaContrato + "' where cliente_codigo = " + codCliTR + " and urlassinatura = '" + assinaturaRepetida + "'");
                                listaLogRetorno.add("ASSINATURA REPLICADA COM SUCESSO! - " + rs.getString("nome") + " - MAT: " + rs.getString("codigomatricula"));
                            } catch (Exception e) {
                                listaLogRetorno.add("ERRO AO TENTAR ATUALIZAR A ASSINATURA DO: " + rs.getString("nome") + " - MAT: " + rs.getString("codigomatricula"));
                                Uteis.logar(e, ProcessosServiceImpl.class);
                            }
                        }
                        qt++;
                    }
                }
            }

            listaLogRetorno.add("#### TOTAL: " + qt);
            listaLogRetorno.add("###################################################################################################");

            if (!apenasValidar) {
                qt = 0;
                listaLogRetorno.add("");
                listaLogRetorno.add("#### ALUNOS COM ASSINATURA REPETIDA MAS NÃO POSSUEM CONTRATO ASSINADO PARA REPLICAR A ASSINATURA E NÃO FORAM ATUALIZADOS ###############################");
                try (ResultSet rs = parQDao.createStatement(ctx, sql.toString())) {
                    while (rs.next()) {
                        listaLogRetorno.add(rs.getString("nome") + " - MAT: " + rs.getString("matricula") + " - SITUACAO: " + rs.getString("situacao"));
                        qt++;
                    }
                }
                listaLogRetorno.add("#### TOTAL: " + qt);
                listaLogRetorno.add("###################################################################################################");
            }
        }

        System.out.println("FINALIZADO PROCESSO DE REPLICAR ASSINATURA CONTRATO PARA ASSINATURA PARQ REPETIDAS PARA CHAVE: " + ctx);
        return listaLogRetorno;
    }

    @Override
    public String forcarSincronizacaoFotoKeyPessoaZwTr(String ctx) throws Exception {
        Uteis.logarDebug("#### INICIANDO PROCESSO DE SINCRONIZAR FOTO PESSOA ENTRE ZW E TREINO PARA CHAVE: " + ctx);
        // consultar todos registros de pessoa que fotokey não seja null e não seja vazio
        StringBuilder sqlZw = new StringBuilder();
        sqlZw.append(
                "select p.nome, p.fotokey, c.codigo as codclientezw, c.codigomatricula \n" +
                "from pessoa p \n" +
                "inner join cliente c on c.pessoa = p.codigo \n" +
                "where p.fotokey is not null \n" +
                "and p.fotokey <> '';"
        );
        Integer qtTotal = 0;
        Integer qtSucesso = 0;
        Integer qtIgual = 0;
        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
            try (ResultSet rsZw = ConexaoZWServiceImpl.criarConsulta(sqlZw.toString(), conZW)) {
                while (rsZw.next()) {
                    qtTotal++;
                    try {
                        // consultar registro de pessoa do banco do treino pelo codigo de cliente do banco do zw
                        Integer codClienteZw = rsZw.getInt("codclientezw");
                        String fotoKeyPessoaZw = rsZw.getString("fotokey");

                        StringBuilder sqlTr = new StringBuilder();
                        sqlTr.append(
                                "select p.codigo as codPessoaTR, p.fotokey\n" +
                                "from pessoa p \n" +
                                "inner join clientesintetico c on c.pessoa_codigo = p.codigo\n" +
                                "where c.codigocliente = " + codClienteZw + "; \n"
                        );

                        try (ResultSet rsTR = pessoaDao.createStatement(ctx, sqlTr.toString())) {
                            while (rsTR.next()) {
                                Integer codPessoaTr = rsTR.getInt("codPessoaTR");
                                String fotoKeyPessoaTr = rsTR.getString("fotokey");
                                // se for diferente atualizar com a fotoKeyPessoaZw no banco do treino
                                if (!fotoKeyPessoaZw.equals(fotoKeyPessoaTr)) {
                                    pessoaDao.executeNativeSQL(ctx, "update pessoa set fotokey = '" + fotoKeyPessoaZw + "' where codigo = " + codPessoaTr);
                                    qtSucesso++;
                                } else {
                                    qtIgual++;
                                }
                            }
                        }

                    } catch (Exception e) {
                        Uteis.logar(e, ProcessosServiceImpl.class);
                    }
                }
            }
        }

        Uteis.logarDebug("#### FINALIZANDO PROCESSO DE SINCRONIZAR FOTO PESSOA ENTRE ZW E TREINO PARA CHAVE: " + ctx);
        return "Processo concluído! - Total pessoas zw verificado: " + qtTotal + " - Total sucesso: " + qtSucesso + " - Total fotokey igual entre zw e treino: " + qtIgual;
    }

}
