package br.com.pacto.service.impl.cliente.perfil;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Informações sobre grupos musculares trabalhados pelo aluno, incluindo estatísticas de exercícios e percentual de trabalho.")
public class TreinoGruposMuscularesDTO {

    @ApiModelProperty(value = "Nome do grupo muscular trabalhado.", example = "Deltóide")
    private String grupo;

    @ApiModelProperty(value = "Número de exercícios realizados para este grupo muscular.", example = "155")
    private Integer nrExercicios;

    @ApiModelProperty(value = "Percentual de trabalho deste grupo muscular em relação ao treino total.", example = "16")
    private Integer percentual;

    public TreinoGruposMuscularesDTO(String grupo, Integer nrExercicios, Integer percentual) {
        this.grupo = grupo;
        this.nrExercicios = nrExercicios;
        this.percentual = percentual;
    }

    public TreinoGruposMuscularesDTO() {
    }

    public String getGrupo() {
        return grupo;
    }

    public void setGrupo(String grupo) {
        this.grupo = grupo;
    }

    public Integer getNrExercicios() {
        return nrExercicios;
    }

    public void setNrExercicios(Integer nrExercicios) {
        this.nrExercicios = nrExercicios;
    }

    public Integer getPercentual() {
        return percentual;
    }

    public void setPercentual(Integer percentual) {
        this.percentual = percentual;
    }
}
