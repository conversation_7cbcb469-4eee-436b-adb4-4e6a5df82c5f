package br.com.pacto.service.impl.gympass.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 22/03/2020
 */
@ApiModel(description = "Informações de um slot de horário disponível para agendamento no GymPass")
public class SlotsDTO {

    @ApiModelProperty(value = "Código identificador único do slot", example = "12345")
    private int id;

    @ApiModelProperty(value = "Data e hora de ocorrência do slot", example = "2025-06-20T10:00:00Z")
    private String occur_date;

    @ApiModelProperty(value = "Nome da sala onde ocorrerá a aula", example = "Sala de Yoga 1")
    private String room;

    @ApiModelProperty(value = "Status do slot de agendamento", example = "1")
    private int status;

    @ApiModelProperty(value = "Duração da aula em minutos", example = "60")
    private int length_in_minutes;

    @ApiModelProperty(value = "Capacidade total de alunos para o slot", example = "20")
    private int total_capacity;

    @ApiModelProperty(value = "Quantidade de agendamentos já realizados", example = "15")
    private int total_booked;

    @ApiModelProperty(value = "Código do produto associado ao slot", example = "789")
    private int product_id;

    @ApiModelProperty(value = "Data limite para cancelamento do agendamento", example = "2025-06-20T08:00:00Z")
    private String cancellable_until;

    @ApiModelProperty(value = "Avaliação média da aula", example = "4.5")
    private Double rating;

    @ApiModelProperty(value = "Janela de tempo para agendamento")
    private BookingWindowDTO booking_window;

    @ApiModelProperty(value = "Lista de instrutores responsáveis pela aula")
    private List<InstructorsDTO> instructors;

    @ApiModelProperty(value = "Indica se a aula é virtual", example = "false")
    private boolean virtual;


    public SlotsDTO() {
    }

    public SlotsDTO(JSONObject json) throws JSONException {
        this.id = json.optInt("id");
        this.occur_date = json.optString("occur_date");
        this.status = json.optInt("status");
        this.room = json.optString("room");
        this.length_in_minutes = json.optInt("length_in_minutes");
        this.total_capacity = json.optInt("total_capacity");
        this.total_booked = json.optInt("total_booked");
        this.product_id = json.optInt("product_id");
        this.booking_window = new BookingWindowDTO(json.getJSONObject("booking_window"));
        this.cancellable_until = json.optString("cancellable_until");
        this.instructors = new ArrayList<>();
        JSONArray array = json.optJSONArray("instructors");
        for (int e = 0; e < array.length(); e++) {
            JSONObject obj = array.getJSONObject(e);
            this.instructors.add(new InstructorsDTO(obj));
        }

        this.virtual = json.optBoolean("virtual");
        this.rating = json.optDouble("rating");
    }

    public String getOccur_date() {
        return occur_date;
    }

    public void setOccur_date(String occur_date) {
        this.occur_date = occur_date;
    }

    public String getRoom() {
        return room;
    }

    public void setRoom(String room) {
        this.room = room;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getLength_in_minutes() {
        return length_in_minutes;
    }

    public void setLength_in_minutes(int length_in_minutes) {
        this.length_in_minutes = length_in_minutes;
    }

    public int getTotal_capacity() {
        return total_capacity;
    }

    public void setTotal_capacity(int total_capacity) {
        this.total_capacity = total_capacity;
    }

    public int getTotal_booked() {
        return total_booked;
    }

    public void setTotal_booked(int total_booked) {
        this.total_booked = total_booked;
    }

    public int getProduct_id() {
        return product_id;
    }

    public void setProduct_id(int product_id) {
        this.product_id = product_id;
    }

    public String getCancellable_until() {
        return cancellable_until;
    }

    public void setCancellable_until(String cancellable_until) {
        this.cancellable_until = cancellable_until;
    }

    public Double getRating() {
        return rating;
    }

    public void setRating(Double rating) {
        this.rating = rating;
    }

    public BookingWindowDTO getBooking_window() {
        return booking_window;
    }

    public void setBooking_window(BookingWindowDTO booking_window) {
        this.booking_window = booking_window;
    }

    public List<InstructorsDTO> getInstructors() {
        return instructors;
    }

    public void setInstructors(List<InstructorsDTO> instructors) {
        this.instructors = instructors;
    }

    public boolean isVirtual() {
        return virtual;
    }

    public void setVirtual(boolean virtual) {
        this.virtual = virtual;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }
}
