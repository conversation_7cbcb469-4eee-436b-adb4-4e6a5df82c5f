package br.com.pacto.service.impl.gympass.dto;

import org.json.JSONObject;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 22/03/2020
 */
@ApiModel(description = "Dados básicos de uma aula/classe do GymPass")
public class ClassesDTO {

    @JsonIgnore
    @ApiModelProperty(value = "Código identificador interno da classe", example = "456")
    private int id;

    @ApiModelProperty(value = "Nome da aula/classe", example = "Yoga Matinal")
    private String name;

    @ApiModelProperty(value = "Identificador único da classe em formato slug", example = "yoga-matinal")
    private String slug;

    @ApiModelProperty(value = "Descrição detalhada da aula", example = "Aula de yoga focada em relaxamento e alongamento matinal")
    private String description;

    @ApiModelProperty(value = "Observações adicionais sobre a aula", example = "Trazer tapete próprio")
    private String notes;

    @ApiModelProperty(value = "Indica se a aula está disponível para agendamento", example = "true")
    private boolean bookable = true;

    @ApiModelProperty(value = "Indica se a aula está visível na plataforma", example = "true")
    private boolean visible = true;

    @ApiModelProperty(value = "Código do produto associado à aula", example = "789")
    private int product_id;

    @ApiModelProperty(value = "Código da academia/ginásio", example = "101")
    private int gym_id;

    @ApiModelProperty(value = "Referência externa da aula", example = "ZW-B-123")
    private String reference;

    public ClassesDTO() {
    }

    public ClassesDTO(JSONObject json) {
        this.id = json.optInt("id");
        this.name = json.optString("name");
        this.slug = json.optString("slug");
        this.description = json.optString("description");
        this.notes = json.optString("notes");
        this.bookable = json.optBoolean("bookable");
        this.visible = json.optBoolean("visible");
        this.product_id = json.optInt("product_id");
        this.gym_id = json.optInt("gym_id");
        this.reference = json.optString("reference");
    }


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public boolean isBookable() {
        return bookable;
    }

    public void setBookable(boolean bookable) {
        this.bookable = bookable;
    }

    public boolean isVisible() {
        return visible;
    }

    public void setVisible(boolean visible) {
        this.visible = visible;
    }

    public int getProduct_id() {
        return product_id;
    }

    public void setProduct_id(int product_id) {
        this.product_id = product_id;
    }

    public String getReference() {
        return reference;
    }

    public void setReference(String reference) {
        this.reference = reference;
    }

    @JsonIgnore
    public int getId() {
        return id;
    }

    @JsonIgnore
    public void setId(int id) {
        this.id = id;
    }

    public String getSlug() {
        return slug;
    }

    public void setSlug(String slug) {
        this.slug = slug;
    }

    public int getGym_id() {
        return gym_id;
    }

    public void setGym_id(int gym_id) {
        this.gym_id = gym_id;
    }
}
