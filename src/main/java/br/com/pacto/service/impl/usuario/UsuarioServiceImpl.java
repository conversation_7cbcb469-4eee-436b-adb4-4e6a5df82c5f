package br.com.pacto.service.impl.usuario;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.oamd.BetaTestersService;
import br.com.pacto.base.util.ExecuteRequestHttpService;
import br.com.pacto.bean.avaliacao.ItemAvaliacaoFisica;
import br.com.pacto.bean.avaliacao.ItemAvaliacaoFisicaEnum;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.colaborador.ColaboradorTO;
import br.com.pacto.bean.colaborador.EmailTO;
import br.com.pacto.bean.colaborador.FiltroColaboradorJSON;
import br.com.pacto.bean.colaborador.SituacaoColaboradorEnum;
import br.com.pacto.bean.colaborador.TelefoneTO;
import br.com.pacto.bean.colaborador.TipoUsuarioColaboradorEnum;
import br.com.pacto.bean.configuracoes.ConfiguracaoSistema;
import br.com.pacto.bean.configuracoes.ConfiguracoesEnum;
import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.notificacao.NotificacaoFimTreino;
import br.com.pacto.bean.perfil.Perfil;
import br.com.pacto.bean.perfil.permissao.Permissao;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.bean.perfil.permissao.TipoPermissaoEnum;
import br.com.pacto.bean.pessoa.Pessoa;
import br.com.pacto.bean.pessoa.SexoEnum;
import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.bean.usuario.StatusEnum;
import br.com.pacto.bean.usuario.TipoUsuarioEnum;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.bean.usuario.UsuarioBuilder;
import br.com.pacto.bean.usuario.UsuarioCacheTO;
import br.com.pacto.bean.usuario.UsuarioColaboradorResponseTO;
import br.com.pacto.bean.usuario.UsuarioColaboradorTO;
import br.com.pacto.bean.usuarioEmail.UsuarioEmail;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.controller.json.colaborador.UsuarioResponseTO;
import br.com.pacto.controller.json.log.EntidadeLogEnum;
import br.com.pacto.controller.json.usuario.UsuarioAppBasicoJSON;
import br.com.pacto.controller.json.usuario.UsuarioDependenteDTO;
import br.com.pacto.controller.json.usuario.UsuarioEmpresaApp;
import br.com.pacto.controller.json.usuario.UsuarioJSON;
import br.com.pacto.dao.intf.cliente.ClienteSinteticoDao;
import br.com.pacto.dao.intf.log.LogDao;
import br.com.pacto.dao.intf.notificacao.NotificacaoFimTreinoDao;
import br.com.pacto.dao.intf.perfil.PerfilDao;
import br.com.pacto.dao.intf.pessoa.StatusPessoaDao;
import br.com.pacto.dao.intf.professor.ProfessorSinteticoDao;
import br.com.pacto.dao.intf.usuario.UsuarioDao;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.dto.UsuarioAppDTO;
import br.com.pacto.security.dto.UsuarioLoginV2DTO;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.security.service.TokenService;
import br.com.pacto.security.service.impl.LoginServiceImpl;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.exception.ValidacaoException;
import br.com.pacto.service.impl.agenda.ColaboradorVO;
import br.com.pacto.service.impl.agenda.PessoaVO;
import br.com.pacto.service.impl.cliente.ClienteSinteticoServiceImpl;
import br.com.pacto.service.impl.conexao.ConexaoZWServiceImpl;
import br.com.pacto.service.impl.notificacao.excecao.ColaboradoresExcecoes;
import br.com.pacto.service.impl.notificacao.excecao.EmpresaExcecoes;
import br.com.pacto.service.impl.notificacao.excecao.UsuarioExcecoes;
import br.com.pacto.service.intf.Validacao;
import br.com.pacto.service.intf.aulapersonal.ConfiguracaoPersonalEmpresaService;
import br.com.pacto.service.intf.avaliacao.AvaliacaoFisicaService;
import br.com.pacto.service.intf.cliente.ClienteSinteticoService;
import br.com.pacto.service.intf.configuracoes.ConfiguracaoSistemaService;
import br.com.pacto.service.intf.empresa.EmpresaService;
import br.com.pacto.service.intf.login.UsuarioGeralService;
import br.com.pacto.service.intf.memcached.CachedManagerInterfaceFacade;
import br.com.pacto.service.intf.perfil.PerfilService;
import br.com.pacto.service.intf.pessoa.PessoaService;
import br.com.pacto.service.intf.professor.ProfessorSinteticoService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.service.intf.usuarioEmail.UsuarioEmailService;
import br.com.pacto.service.login.TokenDTO;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.UtilS3Base64Img;
import br.com.pacto.util.ViewUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.util.EntityUtils;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.web.context.ServletContextAware;
import servicos.integracao.adm.AdmWSConsumer;
import servicos.integracao.adm.client.EmpresaWS;
import servicos.integracao.admapp.AdmAppWSConsumer;
import servicos.integracao.zw.beans.UsuarioZW;

import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.math.BigInteger;
import java.security.SecureRandom;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.DecimalFormat;
import java.util.*;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.logging.Level;
import java.util.logging.Logger;

import static br.com.pacto.objeto.Uteis.incluirLog;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

@Service
@Qualifier(value = "usuarioService")
public class UsuarioServiceImpl implements UsuarioService, ServletContextAware {
    @Autowired
    private ConexaoZWServiceImpl conexaoZWService;
    @Autowired
    private UsuarioDao usuarioDao;
    @Autowired
    private ViewUtils viewUtils;
    @Autowired
    private Validacao validacao;
    @Autowired
    private PerfilService perfilService;
    @Autowired
    private PessoaService pessoaService;
    @Autowired
    private StatusPessoaDao statusPessoaDao;
    @Autowired
    private ClienteSinteticoDao clienteDao;
    @Autowired
    private ProfessorSinteticoService professorSinteticoService;
    @Autowired
    private SessaoService sessaoService;
    @Autowired
    private ProfessorSinteticoDao professorSinteticoDao;
    @Autowired
    private FotoServiceImpl fotoService;
    private ServletContext context;
    @Autowired
    private EmpresaService empresaService;
    @Autowired
    private NotificacaoFimTreinoDao notificacaoFimTreinoDao;
    @Autowired
    private UsuarioGeralService usuarioGeralService;
    @Autowired
    private ClienteSinteticoService clienteSinteticoService;
    @Autowired
    private PerfilDao perfilDao;
    @Autowired
    private CachedManagerInterfaceFacade memcached;
    @Autowired
    private UsuarioEmailService usuarioEmailService;
    @Autowired
    private TokenService tokenService;
    @Autowired
    private LogDao logDao;

    public UsuarioDao getUsuarioDao() {
        return usuarioDao;
    }

    public void setUsuarioDao(UsuarioDao usuarioDao) {
        this.usuarioDao = usuarioDao;
    }

    public StatusPessoaDao getStatusPessoaDao() {
        return statusPessoaDao;
    }

    public ViewUtils getViewUtils() {
        return viewUtils;
    }

    public void setViewUtils(ViewUtils viewUtils) {
        this.viewUtils = viewUtils;
    }

    public Validacao getValidacao() {
        return validacao;
    }

    public void setValidacao(Validacao validacao) {
        this.validacao = validacao;
    }

    public Usuario alterar(final String ctx, Usuario object) throws ServiceException {
        try {
            return getUsuarioDao().update(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public Usuario alterar(final String ctx, Usuario usuarioTreino, UsuarioZW zw) throws ServiceException {
        try {
            usuarioTreino.setCodigoExterno(zw.getCodigoExterno());
            usuarioTreino.setCliente(usuarioTreino.getCliente());
            usuarioTreino.setCodigoExterno(zw.getCodigoExterno());
            usuarioTreino.setNome(zw.getNome());
            usuarioTreino.setProfessor(zw.getProfessor());
            usuarioTreino.setSenha(zw.getSenha());
            usuarioTreino.setStatus(zw.getStatus());
            usuarioTreino.setUserName(zw.getUserName());
            usuarioTreino.setTipo(zw.getTipo());
            usuarioTreino.setUsuarioZW(zw.getUsuarioZW());
            usuarioTreino.setEmpresaZW(zw.getEmpresaZW());
            usuarioTreino.setCpf(zw.getCpf());
            if ((!usuarioTreino.getTipo().equals(TipoUsuarioEnum.ALUNO) && (getValidacao().isNull(usuarioTreino.getProfessor().getPessoa()) || getValidacao().emptyNumber(usuarioTreino.getProfessor().getPessoa().getCodigo())))
                    || (usuarioTreino.getTipo().equals(TipoUsuarioEnum.ALUNO) && (getValidacao().isNull(usuarioTreino.getCliente().getPessoa()) || getValidacao().emptyNumber(usuarioTreino.getCliente().getPessoa().getCodigo())))) {
                criarPessoaSimples(ctx, usuarioTreino);
            }
            return getUsuarioDao().update(ctx, usuarioTreino);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public void criarPessoaSimples(final String ctx, Usuario object) throws Exception {
        Pessoa pessoa = new Pessoa();
        pessoa.setNome(object.getNome());
        pessoa = pessoaService.inserirPessoa(ctx, pessoa);
        if (object.getCliente() != null) {
            object.getCliente().setPessoa(pessoa);
        } else if (object.getProfessor() != null) {
            object.getProfessor().setPessoa(pessoa);
        }
    }

    public Usuario consultarUsuarioSimples(final String ctx, final String userName) throws Exception {
        List<Usuario> usuarios = usuarioDao.findObjectsByAttributesSimple(ctx, new String[]{"codigo", "cliente.matricula"}, new String[]{"userName"}, new Object[]{userName}, "userName", 0);
        return usuarios == null || usuarios.isEmpty() ? null : usuarios.get(0);
    }

    public void excluir(final String ctx, Usuario object) throws ServiceException {
        try {
            excluirStatusPessoa(ctx, object.getCodigo());
            getUsuarioDao().delete(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    private void excluirStatusPessoa(String ctx, Integer usuario) throws Exception {
        getStatusPessoaDao().deleteComParam(ctx, new String[]{"usuario.codigo"}, new Object[]{usuario});
    }

    public Usuario inserir(final String ctx, Usuario object) throws ServiceException {
        try {
            return getUsuarioDao().insert(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public Usuario obterObjetoPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException {
        try {
            return getUsuarioDao().findObjectByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public Usuario obterPorId(final String ctx, Integer id, boolean aluno) throws ServiceException {
        if (!SuperControle.independente(ctx) && !aluno) {
            return obterPorId(ctx, id, Uteis.NIVELMONTARDADOS_TODOS);
        } else {
            return getUsuarioDao().obterPorId(ctx, id);
        }
    }

    public Usuario obterPorId(final String ctx, Integer id) throws ServiceException {
        return obterPorId(ctx, id, false);
    }

    public Usuario obterPorId(final String ctx, Integer id, Integer nivelMontarDados) throws ServiceException {
        if (!SuperControle.independente(ctx)) {
            try {
                String s = "SELECT *, u.codigo as codigo_usuario, col.empresa, col.situacao, ue.codigo as codigoUsuEmail, col.pessoa FROM usuario u " +
                        "inner join colaborador col on col.codigo = u.colaborador " +
                        "left join usuarioemail ue on ue.usuario = u.codigo " +
                        "where u.codigo = " + id;
                try (Connection con = conexaoZWService.conexaoZw(ctx)) {
                    try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(s, con)) {
                        if (rs.next()) {
                            return perfilUnificado(ctx, usuarioDao.montarDadosUsuario(ctx, rs, nivelMontarDados));
                        }
                    }
                }
                return null;
            } catch (Exception ex) {
                throw new ServiceException(ex);
            }
        } else {
            return getUsuarioDao().obterPorId(ctx, id);
        }
    }

    private Usuario perfilUnificado(final String ctx, Usuario usuario) throws Exception {
        if(this.temPerfilUnificado(ctx)){
            try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
                String sqlPerfil = "select p.codigo , p.nome, p.tipo  from usuarioperfilacesso u " +
                        "inner join perfilacesso p on p.codigo = u.perfilacesso " +
                        "where u.usuario = " + usuario.getUsuarioZW() + "  and u.empresa = " + (sessaoService.getUsuarioAtual().getEmpresaAtual() != null ? sessaoService.getUsuarioAtual().getEmpresaAtual() : usuario.getEmpresaZW()) + " ";

                try (ResultSet rsPerfil = ConexaoZWServiceImpl.criarConsulta(sqlPerfil, conZW)) {
                    while (rsPerfil.next()) {
                        usuario.getPerfil().setNome(rsPerfil.getString("nome"));
                        usuario.getPerfil().setCodigo(rsPerfil.getInt("codigo"));
                    }
                }

                String sql = "select codigorecurso, codigorecurso, permissoes ,codperfilacesso, p.tipo from permissao inner join perfilacesso p on p.codigo = permissao.codperfilacesso  where sistema ='bdmusc' and codperfilacesso = (select perfilacesso from usuarioperfilacesso where usuario =" + usuario.getUsuarioZW() + " and empresa = " + usuario.getEmpresaZW() + " and unificado)";
                try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql, conZW)) {

                    List<Permissao> permissoes = new ArrayList<>();
                    while (rs.next()) {
                        Permissao permissao = new Permissao();
                        permissao.setCodigo(rs.getInt("codigorecurso"));
                        permissao.setRecurso(RecursoEnum.getFromId(rs.getInt("codigorecurso")));

                        HashSet<TipoPermissaoEnum> tipoPermissao = new HashSet<>();
                        String[] result = rs.getString("permissoes").replace(")", "").split("\\(");
                        List<String> list = new ArrayList<String>(Arrays.asList(result));
                        list.remove("");
                        list.forEach(
                                p -> {
                                    int tipoDePermissao = Integer.valueOf(p);
                                    tipoPermissao.add(TipoPermissaoEnum.getFromOrdinal(tipoDePermissao));
                                }
                        );
                        permissao.setTipoPermissoes(tipoPermissao);
                        permissoes.add(permissao);
                    }
                    usuario.getPerfil().setPermissoes(permissoes);
                }
            }
        }
        return usuario;
    }

    public List<Usuario> obterPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException {
        try {
            return getUsuarioDao().findByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<Usuario> obterPorParam(final String ctx, String query,
                                       Map<String, Object> params, int max, int index)
            throws ServiceException {
        try {
            return getUsuarioDao().findByParam(ctx, query, params, max, index);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<Usuario> obterTodos(final String ctx, Integer empresa) throws ServiceException {
        try {

            if (UteisValidacao.emptyNumber(empresa)) {
                return getUsuarioDao().findAll(ctx);
            } else {
                return obterListaPorAtributo(ctx, "empresaZW", empresa);
            }


        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public Usuario validarUsuario(final String chave, final String userName, final String password, Boolean... isEncript) throws ServiceException {
        boolean encript = true;
        if (isEncript != null) {
            encript = isEncript.length <= 0 || isEncript[0];
        }
        return validarUsuario(chave, userName, password, false, encript);
    }

    @Override
    public Usuario validarUsuario(final String chave, final String userName, final String password, final boolean aluno, Boolean encript)
            throws ServiceException {
        try {
            Usuario u = validarUserSemPreparar(chave, userName, password, aluno, encript);
            return prepararUsuario(u, chave, password);
        } catch (Exception ex) {
            if (ex.getMessage().equals(viewUtils.getMensagem("comum.dadosIncorretos"))) {
                throw new ServiceException(ex);
            }
            Logger.getLogger(this.getClass().getName()).log(Level.SEVERE, null, ex);
            throw new ServiceException("Usuário não encontrado.");
        }
    }

    @Override
    public Usuario validarUsuarioApp(final String chave, final String userName, final String password, Boolean... isEncript)
            throws ServiceException {
        try {
            Map<String, Object> params = new HashMap<>();
            Boolean encript = true;
            if (isEncript != null) {
                encript = isEncript.length > 0 ? isEncript[0].booleanValue() : true;
            }
            String query = getQueryValidarUserSemPrepararApp(userName, password, params, encript);
            Usuario u = getUsuarioDao().findObjectByParam(chave, query, params);
            if (u == null) {
                throw new ServiceException("Usuário não encontrado");
            }
            if (u.getStatus() == StatusEnum.INATIVO) {
                throw new ServiceException("Usuário inativo");
            }
            return prepararUsuario(u, chave, password);
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    @Override
    public List<Usuario> validarUsuarioV2(final String chave, final String userName, final String password) throws ServiceException {
        try {
            List<Usuario> users = validarUserSemPrepararV2(chave, userName, password);
            for (Usuario u : users) {
                u = prepararUsuario(u, chave, password);
            }
            return users;

        } catch (Exception ex) {
            ex.printStackTrace();
            throw new ServiceException(ex.getMessage());
        }
    }

    private String getQueryValidarUserSemPreparar(final String ctx, final String userName, final String password, Map<String, Object> params, Boolean isEncript) throws ServiceException, UnsupportedEncodingException {
        return getQueryValidarUserSemPreparar(ctx, userName, password, params, false, isEncript);
    }

    private String getQueryValidarUserSemPreparar(final String ctx, final String userName, final String password, Map<String, Object> params, Boolean aluno, Boolean isEncript) throws ServiceException, UnsupportedEncodingException {
        if (validacao.isNull(userName) || validacao.isEmpty(userName)) {
            throw new ServiceException(viewUtils.getMensagem("login.usuarioInvalido"));
        }
        if (validacao.isNull(password) || validacao.isEmpty(password)) {
            throw new ServiceException(viewUtils.getMensagem("login.senhaInvalida"));
        }
        String senhaEncriptadaComUpperCase;
        String senhaEncriptadaSemUpperCase;
        if (isEncript && !(password.length() == 64)) {
            senhaEncriptadaComUpperCase = Uteis.encriptar(Uteis.removerEspacosInicioFimString(password.toUpperCase()));
            senhaEncriptadaSemUpperCase = Uteis.encriptar(Uteis.removerEspacosInicioFimString(password));
        } else {
            senhaEncriptadaComUpperCase = password;
            senhaEncriptadaSemUpperCase = password;
        }
        params.put("senhaEncriptadaComUpperCase", senhaEncriptadaComUpperCase); //SOLICITACAO MAXIMILIANO NÃO ALTERAR -- TICKET #3319!!!
        params.put("senhaEncriptadaSemUpperCase", senhaEncriptadaSemUpperCase);
        params.put("username", Uteis.removerEspacosInicioFimString(userName.toUpperCase())); //SOLICITACAO MAXIMILIANO NÃO ALTERAR -- TICKET #3319!!!
        String cpf = Uteis.tirarCaracteres(userName.toUpperCase(), true);

        StringBuilder query = new StringBuilder();
        if (SuperControle.independente(ctx) || aluno) {
            query.append("SELECT obj FROM Usuario obj\n");
            query.append("LEFT OUTER JOIN obj.usuarioEmail as usuarioEmail\n");
            query.append("LEFT OUTER JOIN obj.professor as prof\n");
            query.append("WHERE status = 0 and (\n");

            if (cpf.length() == 11) {
                params.put("cpf", cpf);
                query.append("replace(replace(obj.cpf, '.', ''), '-', '') = :cpf  or ");
            }
            query.append(" UPPER(username) = :username \n");
            query.append(" OR UPPER(usuarioEmail.email) = :username ) and senha in (:senhaEncriptadaComUpperCase, :senhaEncriptadaSemUpperCase) \n");
        } else {
            query.append("SELECT *, uE.codigo as codigoUsuEmail FROM Usuario obj\n");
            query.append("INNER JOIN colaborador c on c.codigo = obj.colaborador\n");
            query.append("INNER JOIN pessoa p on p.codigo = c.pessoa\n");
            query.append("LEFT JOIN usuarioEmail uE on uE.usuario = obj.codigo\n");
            query.append("WHERE obj.statusTw = 0 and (\n");

            if (cpf.length() == 11) {
                params.put("cpf", cpf);
                query.append("replace(replace(p.cfp, '.', ''), '-', '') = '" + params.get("cpf") + "'  or ");
            }
            query.append(" UPPER(obj.username) = '" + params.get("username") + "' \n");
            query.append(" OR UPPER(uE.email) = '" + params.get("username") + "' ) and obj.senha in ('" + params.get("senhaEncriptadaComUpperCase") + "', '" + params.get("senhaEncriptadaSemUpperCase") + "') \n");
        }

        return query.toString();
    }

    private String getQueryValidarUserSemPrepararApp(final String userName, final String password, Map<String, Object> params, Boolean isEncript) throws ServiceException, UnsupportedEncodingException {
        if (validacao.isNull(userName) || validacao.isEmpty(userName)) {
            throw new ServiceException(viewUtils.getMensagem("login.usuarioInvalido"));
        }
        if (validacao.isNull(password) || validacao.isEmpty(password)) {
            throw new ServiceException(viewUtils.getMensagem("login.senhaInvalida"));
        }
        String senhaEncriptadaComUpperCase;
        String senhaEncriptadaSemUpperCase;
        if (isEncript) {
            senhaEncriptadaComUpperCase = Uteis.encriptar(Uteis.removerEspacosInicioFimString(password.toUpperCase()));
            senhaEncriptadaSemUpperCase = Uteis.encriptar(Uteis.removerEspacosInicioFimString(password));
        } else {
            senhaEncriptadaComUpperCase = password;
            senhaEncriptadaSemUpperCase = password;
        }
        params.put("senhaEncriptadaComUpperCase", senhaEncriptadaComUpperCase); //SOLICITACAO MAXIMILIANO NÃO ALTERAR -- TICKET #3319!!!
        params.put("senhaEncriptadaSemUpperCase", senhaEncriptadaSemUpperCase);
        params.put("username", Uteis.removerEspacosInicioFimString(userName.toUpperCase())); //SOLICITACAO MAXIMILIANO NÃO ALTERAR -- TICKET #3319!!!

        StringBuilder query = new StringBuilder();
        query.append("SELECT obj FROM Usuario obj\n");
        query.append("WHERE UPPER(username) = :username \n");
        query.append("and senha in (:senhaEncriptadaComUpperCase, :senhaEncriptadaSemUpperCase)\n");

        return query.toString();
    }

    public Usuario validarUserSemPreparar(final String chave, final String userName, final String password, final boolean aluno, final Boolean... isEncript) throws Exception {
        Boolean encript = true;
        if (isEncript != null) {
            encript = isEncript.length > 0 ? isEncript[0].booleanValue() : true;
        }
        return validarUserSemPreparar(chave, userName, password, aluno, encript);
    }

    public Usuario validarUserSemPreparar(final String chave, final String userName, final String password, Boolean aluno, Boolean encript) throws Exception {
        Map<String, Object> params = new HashMap<>();

        String query = getQueryValidarUserSemPreparar(chave, userName, password, params, aluno, encript);
        if (SuperControle.independente(chave) || aluno) {
            return getUsuarioDao().findFirstObjectByParam(chave, query, params);
        } else {
            try (Connection con = conexaoZWService.conexaoZw(chave)) {
                try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(query, con)) {
                    if (rs.next()) {
                        return usuarioDao.montarDadosUsuario(chave, rs, aluno ? Uteis.NIVELMONTARDADOS_TODOS : Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    }
                }
            }
            return null;
        }
    }

    public List<Usuario> validarUserSemPrepararV2(final String chave, final String userName, final String password) throws Exception {
        Map<String, Object> params = new HashMap<>();
        String query = getQueryValidarUserSemPreparar(chave, userName, password, params, true);
        return getUsuarioDao().findByParam(chave, query, params);
    }

    private Usuario validarUsuarioColaboradorDuplicado(final String ctx, final String userName, final Integer usuarioId, final Integer empresaId) throws Exception {
        StringBuilder hql = new StringBuilder();

        hql.append("SELECT obj FROM Usuario obj ");
        hql.append("WHERE obj.empresaZW = :empresaId ");
        hql.append("AND obj.userName = :userName ");
        if (usuarioId != null) {
            hql.append("AND obj.codigo <> :usuarioId ");
        }

        HashMap<String, Object> params = new HashMap<>();

        params.put("empresaId", empresaId);
        params.put("userName", userName);
        if (usuarioId != null) {
            params.put("usuarioId", usuarioId);
        }
        return getUsuarioDao().findObjectByParam(ctx, hql.toString(), params);
    }

    @Override
    public Usuario prepararUsuario(Usuario u, final String chave, final String password) throws Exception {
        if (u != null) {
            u.setChave(chave);
            u.setPwdForCookie(password);
            //Esse cara não pode existir mais, ele faz uma HTTP connection no ZW.
//            u.setAvatar(Aplicacao.preencherFoto(chave, u.getIdPessoa(), true, false, false));
            u.setAvatarEmpresa(String.format("%s/imagens/%s.jpg", Aplicacao.getProp(Aplicacao.urlOAMDSegura), chave));
            montarUrlsImagem(chave, u);
            //obter informações do ZillyonWeb
            if (u.getUsuarioZW() != null && u.getUsuarioZW() > 0) {
                try {
                    u.setModulos(Aplicacao.getProp(chave, Aplicacao.modulos));
                    u.setEmpresasZW(UtilContext.getBean(AdmWSConsumer.class).obterEmpresasZW(chave, u.getUsuarioZW()));
                    if (u.getEmpresasZW() == null || u.getEmpresasZW().isEmpty()) {
                        u.setModulos("");
                    }
                    try {
                        if (u.getUsuarioEmail() != null && !UteisValidacao.emptyNumber(u.getUsuarioEmail().getCodigo())) {
                            u.setUsuarioEmail(usuarioEmailService.obterPorId(chave, u.getUsuarioEmail().getCodigo(), u.getTipo()));
                        }
                    } catch (Exception e) {
                        Uteis.logar(e, UsuarioServiceImpl.class);
                    }
                    u.setEmpresaLogada(u.getEmpresaDefault());
                    if (u.getEmpresaLogada() == null && u.getProfessor() != null) {
                        obterEmpresaWS(u, u.getProfessor().getEmpresa());
                    }
                    u.setTimeZone(u.getEmpresaDefault().getTimeZoneDefault());
                    u.setAvatarEmpresa(String.format("%s/imagens/foto-%s-%s.jpg", Aplicacao.getProp(Aplicacao.urlOAMDSegura), chave, u.getEmpresaLogada().getCodigo()));
                } catch (Exception e) {
                    Uteis.logar(null, "ERRO: Não foi possível obter informações do ZillyonWeb pelo erro: " + e.getMessage());
                }
            } else {
                Empresa e = (UtilContext.getBean(EmpresaService.class)).obterPorIdZW(chave, u.getEmpresaZW());
                obterEmpresaWS(u, e);

            }

            if (u.getTipo() != null &&
                    u.getTipo().equals(TipoUsuarioEnum.PROFESSOR) && !u.getProfessor().isAtivo()) {
                throw new ServiceException(viewUtils.getMensagem("login.usuario_inativo"));
            }

            /*u.setToken(
                    CriptografiaUtil.encrypt(
                            String.format("%s_%s_%s", chave, u.getCodigo(), Calendario.getInstance().getTimeInMillis()),
                            CriptografiaUtil.CHAVE_CRIPTO, CriptografiaUtil.ALGORITMO_AES));
            u.setToken(u.getToken().replaceAll("\n", "").replaceAll("\r", ""));*/

            u.setToken(tokenService.gerarToken(u, chave));

            if (u.getTipo() == TipoUsuarioEnum.ALUNO && u.getCliente() != null) {//Validar se a empresa pode Usar o App para Smartphone para seus alunos
                AvaliacaoFisicaService as = UtilContext.getBean(AvaliacaoFisicaService.class);
                ItemAvaliacaoFisica objs = as.obterItemAvaliacaoFisica(chave, u.getCliente().getCodigo(), ItemAvaliacaoFisicaEnum.OBJETIVOS, null, null);
                u.getCliente().setObjetivosLista(objs == null ? new ArrayList<>() : objs.getObjs());

                if (!validacao.emptyNumber(u.getEmpresaZW())) {
                    EmpresaService es = UtilContext.getBean(EmpresaService.class);
                    Empresa e = es.obterPorIdZW(chave, u.getEmpresaZW());
                    if (e != null) {
                        u.setNomeEmpresa(e.getNome());
                        u.setUrlSite(e.getUrlSite());
                        return u;
                    } else {
                        throw new ServiceException(viewUtils.getMensagem("login.empresaInvalida"));
                    }
                } else {
                    throw new ServiceException(viewUtils.getMensagem("login.empresaInvalida"));
                }
            }

            /**
             * LFDeus - 10/01/2019 - registrar no zw que o usuário fez login!
             * Necessário pois no ZW existe um recurso que identifica os usuário sem acessar o sistema.
             * Caso o usuário tenha acesso somente ao TR esse dado pode ficar incoerente
             */
            registrarZillyonWebLoginTreinoWeb(chave, u);

            /**
             * WM - 27/06/2017 - já deixar preenchido a URL base de midias para
             * evitar que isso seja feito indiretamente pela tela de Programa de
             * Treino, por exemplo.
             */
            AdmAppWSConsumer.obterUrlBase(chave);

            try {
                montarMensagemWhatsapp(u);
            } catch (Exception ex) {
                ex.printStackTrace();
            }
            LoginServiceImpl.MAPA_USUARIOS.put(chave, new UsuarioCacheTO(u));
            return u;
        } else {
            throw new ServiceException(viewUtils.getMensagem("comum.dadosIncorretos"));
        }
    }

    public List<UsuarioEmpresaApp> empresasApp(String ctx, Usuario u) {
        return new ArrayList() {{
            if (u.getEmpresasZW() != null) {
                for (EmpresaWS e : u.getEmpresasZW()) {
                    UsuarioEmpresaApp uea = new UsuarioEmpresaApp(e);
                    try {
                        try (ResultSet statement = usuarioDao.createStatement(ctx, "select codigo from professorsintetico where codigoColaborador = " + e.getCodigoColaborador()
                                + " limit 1")) {
                            if (statement.next()) {
                                uea.setCodigoProfessor(statement.getInt("codigo"));
                            } else {
                                uea.setCodigoProfessor(u.getProfessor().getCodigo());
                            }
                        }
                    } catch (Exception ez) {
                        Uteis.logarDebug(String.format("empresasApp chave: %s => usuario: %s - exception: %s", ctx, u.getCodigo(), ez.getMessage()));
                    }
                    add(uea);
                }

            }

        }};
    }

    private void obterEmpresaWS(Usuario usuario, Empresa empresa) {
        EmpresaWS ew = new EmpresaWS();
        if (usuario.getProfessor() != null) {
            ew.setCodigoColaborador(usuario.getProfessor().getCodigoColaborador());
        }
        if (empresa != null) {
            ew.setNome(empresa.getNome());
            ew.setCodigo(empresa.getCodZW());
            ew.setTimeZoneDefault(empresa.getTimeZoneDefault());
            ew.setTokenSMS(empresa.getTokenSMS());
            ew.setSite(empresa.getUrlSite());
            ew.setEmail(empresa.getEmail());
            ew.setCodigoFinanceiro(empresa.getCodFinanceiro());
            usuario.setEmpresasZW(new ArrayList<>());
            usuario.getEmpresasZW().add(ew);
            usuario.setEmpresaLogada(ew);
        }
    }

    private void montarMensagemWhatsapp(Usuario u) {
        StringBuilder whatsapp = new StringBuilder(sorteiaNumeroWhatsApp());
        whatsapp.append("&text=");
        if (u.getEmpresaLogada() == null) {
            whatsapp.append("Empresa: Não preenchida. ");
            whatsapp.append("Financeiro: Não preenchido. ");
        } else {
            whatsapp.append("Empresa: ").append(u.getEmpresaLogada().getNome()).append(". ");
            whatsapp.append("Financeiro: ").append(u.getEmpresaLogada().getCodigoFinanceiro()).append(". ");
        }
        whatsapp.append("Usuario: ").append(u.getNome()).append(". ");
        whatsapp.append("Conversa iniciada a partir da tela de bloqueio do sistema.");
        u.setMsgWhatsapp(whatsapp.toString());
    }

    private String sorteiaNumeroWhatsApp() {
        Random random = new Random();
        String[] numeros = Aplicacao.getProp(Aplicacao.NUMEROS_WHATSAPP_PACTO).split(",");
        try {
            return numeros[random.nextInt(numeros.length)];
        } catch (Exception e) {
            return numeros[0];
        }
    }

    @Override
    public Usuario verificarUsoModulos(final String ctx, Usuario usuario) {
        try {
            ConfiguracaoSistemaService css = (ConfiguracaoSistemaService) UtilContext.getBean(ConfiguracaoSistemaService.class);
            if (usuario.getEmpresaLogada() != null
                    && usuario.getEmpresaLogada().getCodigo() != null
                    && usuario.getEmpresaLogada().getCodigo() > 0) {

                ConfiguracaoSistema cfgUsarPersonal = css.consultarPorTipo(ctx,
                        ConfiguracoesEnum.USAR_GESTAO_PERSONAL);
                cfgUsarPersonal.setValorAsBoolean(usuario.getEmpresaLogada().isUsarGestaoCreditosPersonal());
                css.alterar(ctx, cfgUsarPersonal);
                ConfiguracaoPersonalEmpresaService cfgPersonalService = (ConfiguracaoPersonalEmpresaService) UtilContext.getBean(ConfiguracaoPersonalEmpresaService.class);
                cfgPersonalService.atualizarCfgEmpresa(ctx, usuario.getEmpresaLogada());
            }
            ConfiguracaoSistema cfgUsarSalaCheia = css.consultarPorTipo(ctx, ConfiguracoesEnum.USAR_SALA_CHEIA);
            if (usuario.getModulos() != null && !usuario.getModulos().isEmpty()) {
                cfgUsarSalaCheia.setValorAsBoolean(usuario.getModulos().toUpperCase().contains("SLC"));
                css.alterar(ctx, cfgUsarSalaCheia);
            }
            usuario.setUsaSalaCheia(cfgUsarSalaCheia.getValorAsBoolean());
        } catch (Exception e) {
            Uteis.logar(null, e.getMessage());
        }
        return usuario;
    }

    public Usuario obterPorAtributo(final String ctx, final String atributo, Object valor)
            throws ServiceException {
        return obterPorAtributo(ctx, atributo, valor, false);
    }

    public Usuario obterPorAtributo(final String ctx, final String atributo, Object valor, final boolean cliente)
            throws ServiceException {
        try {
            String identificador = "usuario-".concat(atributo).concat("-").concat(valor.toString().replaceAll(" ", "0_"));
            Usuario usuario = memcached.ler(ctx, identificador);
            if (usuario != null) {
                return usuario;
            }
            if (isNotBlank(atributo) && atributo.equalsIgnoreCase("username") && !cliente) {
                usuario = consultarPorUserName(ctx, String.valueOf(valor), true, false);
            } else {
                usuario = getUsuarioDao().findObjectByAttribute(ctx, atributo, valor);
            }
            try {
                if (usuario != null && usuario.getUsuarioEmail() != null && !UteisValidacao.emptyNumber(usuario.getUsuarioEmail().getCodigo())) {
                    usuario.setUsuarioEmail(usuarioEmailService.obterPorId(ctx, usuario.getUsuarioEmail().getCodigo(), usuario.getTipo()));
                }
            } catch (Exception e) {
                Uteis.logar(e, UsuarioServiceImpl.class);
            }
            if (usuario != null) {
                cachearUsuario(ctx, identificador, usuario);
            }
            return usuario;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    private void cachearUsuario(String ctx, String identificador, Usuario usuario) {
        try {
            if (memcached.getMemcachedClient() == null) {
                return;
            }
            if (usuario.getCliente() != null) {
                usuario.setCliente(clienteDao.findById(ctx, usuario.getCliente().getCodigo()));
            }
            memcached.gravar(ctx, identificador, usuario);
        } catch (Exception e) {
            Uteis.logar(e, UsuarioServiceImpl.class);
        }
    }


    @Override
    public UsuarioColaboradorResponseTO obterUsuarioColaborador(Integer id, HttpServletRequest request) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();

        try {
            Usuario usuario = obterPorId(ctx, id);
            if (usuario != null && usuario.getProfessor() != null) {
                usuario.getProfessor().setUriImagem(fotoService.defineURLFotoPessoa(request, usuario.getProfessor().getPessoa().getFotoKey(), usuario.getProfessor().getCodigoPessoa(), false, ctx, !SuperControle.independente(ctx)));
                try {
                    if (usuario.getUsuarioEmail() != null && !UteisValidacao.emptyNumber(usuario.getUsuarioEmail().getCodigo())) {
                        usuario.setUsuarioEmail(usuarioEmailService.obterPorId(ctx, usuario.getUsuarioEmail().getCodigo(), usuario.getTipo()));
                    }
                } catch (Exception e) {
                    Uteis.logar(e, UsuarioServiceImpl.class);
                }
            }
            return new UsuarioColaboradorResponseTO(usuario, SuperControle.independente(ctx),
                    SuperControle.independente(ctx) ? null : empresas(ctx, usuario));
        } catch (ServiceException e) {
            throw new ServiceException(UsuarioExcecoes.ERRO_BUSCAR_USUARIO);
        }
    }

    @Override
    public Usuario obterUsuarioPorColaborador(Integer colaboradorId) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            return usuarioDao.obterUsuarioPorColaborador(ctx, colaboradorId);
        } catch (ServiceException e) {
            throw new ServiceException(UsuarioExcecoes.ERRO_BUSCAR_USUARIO_POR_PROFESSOR);
        }
    }

    @Override
    public Usuario obterUsuarioPorColaborador(String ctx, Integer colaboradorId) throws ServiceException {
        try {
            return usuarioDao.obterUsuarioPorColaborador(ctx, colaboradorId);
        } catch (ServiceException e) {
            throw new ServiceException(UsuarioExcecoes.ERRO_BUSCAR_USUARIO_POR_PROFESSOR);
        }
    }

    @Override
    public UsuarioColaboradorResponseTO cadastrarUsuarioColaborador(String ctx, UsuarioColaboradorTO usuarioColaboradorTO, Integer empresaId,
                                                                    HttpServletRequest request) throws ServiceException {

        try {
            boolean importador = usuarioColaboradorTO.getAppUserName().equals("<EMAIL>")
                    || usuarioColaboradorTO.getAppUserName().equals("Pacto IA");
            if (SuperControle.independente(ctx) || importador) {
                validarUsuarioColaborador(ctx, usuarioColaboradorTO, empresaId);
                Usuario usuario = UsuarioBuilder.usuarioColaboradorToUsuario(ctx, usuarioColaboradorTO, empresaId);

                String senha = usuarioColaboradorTO.getAppPassword();
                SecureRandom random = new SecureRandom();
                if (senha == null || senha.trim().isEmpty()) {
                    senha = new BigInteger(130, random).toString(32);
                    senha = senha.length() > 8 ? senha.substring(0, 8) : senha;
                }
                usuario.setSenha(Uteis.encriptar(senha.toUpperCase()));
                Usuario userReturn = inserir(ctx, usuario);
                if (!importador) {
                    adicionarUsuarioServicoDescobrir(ctx, userReturn.getUserName());
                    usuarioNovoLogin(ctx, userReturn.getCodigo(), true, true, false, request);
                }
                return new UsuarioColaboradorResponseTO(userReturn, true, null);
            } else {
                throw new ServiceException(UsuarioExcecoes.ERRO_ACAO_PROIBIDA);
            }
        } catch (Exception e) {
            throw new ServiceException(UsuarioExcecoes.ERRO_SALVAR_USUARIO, e);
        }
    }

    public UsuarioColaboradorResponseTO alterarUsuarioColaborador(HttpServletRequest request, Integer id, UsuarioColaboradorTO usuarioColaboradorTO, Integer empresaId) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        try {
            Usuario usuario = obterPorId(ctx, id);

            if (usuario == null || UteisValidacao.emptyNumber(usuario.getCodigo())) {
                throw new ServiceException(UsuarioExcecoes.ERRO_USUARIO_NAO_ENCONTRADO);
            }
            try {
                if (usuario.getUsuarioEmail() != null && !UteisValidacao.emptyNumber(usuario.getUsuarioEmail().getCodigo())) {
                    usuario.setUsuarioEmail(usuarioEmailService.obterPorId(ctx, usuario.getUsuarioEmail().getCodigo(), usuario.getTipo()));
                }
            } catch (Exception e) {
                Uteis.logar(e, UsuarioServiceImpl.class);
            }
            if (SuperControle.independente(ctx)) {
                usuarioColaboradorTO.setId(id);
                validarUsuarioColaborador(ctx, usuarioColaboradorTO, empresaId);
                String senha = usuario.getSenha();
                String usuarioGeral = usuario.getUsuarioGeral();

                usuario = UsuarioBuilder.usuarioColaboradorToUsuario(ctx, usuarioColaboradorTO, empresaId);

                usuario.setUsuarioGeral(usuarioGeral);

                if (!StringUtils.isBlank(usuarioColaboradorTO.getAppPassword())) {
                    senha = Uteis.encriptar(usuarioColaboradorTO.getAppPassword().toUpperCase());
                }
                usuario.setSenha(senha);

                if (!StringUtils.isBlank(usuarioColaboradorTO.getImagemData())) {
                    salvarMidiaNuvem(ctx, usuarioColaboradorTO, usuario);
                }
                usuario = alterar(ctx, usuario);
                usuarioNovoLogin(ctx, usuario.getCodigo(), false, false, false, request);
            } else {
                Perfil perfil = perfilService.obterPorId(ctx, usuarioColaboradorTO.getPerfilUsuarioPermissoes());
                usuario.setPerfil(perfil);
                usuario.setPerfilCodigo(perfil.getCodigo());
                usuario.setTipoCodigo(usuario.getTipo().getId());
                try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
                    try {
                        ConexaoZWServiceImpl.executarConsulta("update usuario set perfilTw = " + perfil.getCodigo() + ", tipoTw = " + usuario.getTipoCodigo() + " where codigo = " + usuario.getCodigo(), conZW);
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    }
                }

            }
            return new UsuarioColaboradorResponseTO(usuario, SuperControle.independente(ctx),
                    SuperControle.independente(ctx) ? null : empresas(ctx, usuario));
        } catch (Exception e) {
            throw new ServiceException(UsuarioExcecoes.ERRO_SALVAR_USUARIO, e);
        }
    }

    public String empresas(String ctx, Usuario usuario) {
        String empresas = "";
        List<EmpresaWS> empresaWSes = UtilContext.getBean(AdmWSConsumer.class).obterEmpresasZW(ctx, usuario.getUsuarioZW());
        for (EmpresaWS e : empresaWSes) {
            empresas += " | " + e.getNome();
        }
        return empresas.replaceFirst("\\|", "");
    }

    public void adicionarUsuarioServicoDescobrir(String ctx, String email) throws IOException {
        try {
            if (email.contains("@")) {
                String url = String.format("%s/prest/empresa/%s/inserirUsuario", new Object[]{
                        Aplicacao.getProp(Aplicacao.urlOAMD),
                        ctx
                });
                Map<String, String> params = new HashMap<String, String>();
                params.put("email", email);
                ExecuteRequestHttpService.executeRequest(url, params);
            }
        } catch (Exception e) {
            Uteis.logar(e, ClienteSinteticoServiceImpl.class);
        }
    }

    @Override
    public Usuario consultarProColaborador(String contexto, Integer codigoColaborador) throws Exception {
        if (!SuperControle.independente(contexto)) {
            String jpql = new StringBuilder("select obj.codigo, obj.nome, obj.username, obj.colaborador, obj.perfilTw, obj.statusTw, obj.tipoTw, c.pessoa from ").append(Usuario.class.getSimpleName()).append(" obj ")
                    .append(" inner join colaborador c on c.codigo = obj.colaborador")
                    .append(" where c.codigo = " + codigoColaborador).toString();

            try (Connection con = conexaoZWService.conexaoZw(contexto)) {
                try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(jpql.toString(), con)) {
                    if (rs.next()) {
                        return usuarioDao.montarDadosUsuario(contexto, rs, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    }
                }
            }

            //busca o usuario pelo codigo de colaborador ZW (coluna codigo colaborador na tabela de professorsintetico), e depois
            //busca usuario do colaborador pelo código de usuario ZW
            Usuario usuarioTreino = getUsuarioByCodigoColaborador(contexto, codigoColaborador);
            if (usuarioTreino != null) {
                String sqlObtemUsuarioPorCodigo = "SELECT obj.codigo, obj.nome, obj.username, obj.colaborador, obj.perfilTw, obj.statusTw, obj.tipoTw, c.pessoa FROM Usuario obj " +
                        "INNER JOIN colaborador c ON c.codigo = obj.colaborador " +
                        "WHERE obj.codigo = " + usuarioTreino.getUsuarioZW();
                try (Connection con = conexaoZWService.conexaoZw(contexto)) {
                    try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sqlObtemUsuarioPorCodigo, con)) {
                        if (rs.next()) {
                            return usuarioDao.montarDadosUsuario(contexto, rs, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        }
                    }
                }
            }

            return null;
        } else {
            return getUsuarioByCodigoColaborador(contexto, codigoColaborador);
        }
    }

    private Usuario getUsuarioByCodigoColaborador(String contexto, Integer codigoColaborador) throws ServiceException {
        String jpql = new StringBuilder("select obj from ").append(Usuario.class.getSimpleName()).append(" obj ")
                .append("where obj.professor.codigoColaborador = :codigocolaborador").toString();
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("codigocolaborador", codigoColaborador);
        return obterObjetoPorParam(contexto, jpql, map);
    }

    @Override
    public List<Usuario> consultarPorNomeColaborador(final String contexto, final String nomeColaborador) throws Exception {
        if (!SuperControle.independente(contexto)) {
            List<Usuario> users = new ArrayList<>();
            String jpql = new StringBuilder("select obj.codigo, obj.nome, obj.username, obj.colaborador, obj.perfilTw, obj.statusTw, obj.tipoTw from ")
                    .append(Usuario.class.getSimpleName()).append(" obj ")
                    .append(" inner join colaborador c on c.codigo = obj.colaborador")
                    .append(" inner join pessoa p on p.codigo = c.pessoa")
                    .append(" where p.nome ilike '%" + nomeColaborador + "%'").toString();

            try (Connection con = conexaoZWService.conexaoZw(contexto)) {
                try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(jpql.toString(), con)) {
                    while (rs.next()) {
                        users.add(usuarioDao.montarDadosUsuario(contexto, rs, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    }
                }
            }
            return users;
        } else {
            String hql = "select obj from " + Usuario.class.getSimpleName() + " obj " +
                    "where obj.professor.nome = :nomeColaborador";
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("nomeColaborador", nomeColaborador);
            return obterPorParam(contexto, hql, map);
        }
    }

    public void validarUsuarioColaborador(String ctx, UsuarioColaboradorTO usuarioColaboradorTO, Integer empresaId) throws Exception {
        if (usuarioColaboradorTO.getAppUserName() == null || usuarioColaboradorTO.getAppUserName().trim().isEmpty()) {
            throw new ServiceException(UsuarioExcecoes.ERRO_USERNAME_NULL);
        }
        if (usuarioColaboradorTO.getEmail() == null || usuarioColaboradorTO.getEmail().isEmpty()) {
            throw new ServiceException(UsuarioExcecoes.ERRO_EMAIL_NULL);
        }
        if (!UteisValidacao.validaEmail(usuarioColaboradorTO.getEmail())) {
            throw new ServiceException(UsuarioExcecoes.ERRO_EMAIL_INVALIDO);
        }
        if (usuarioColaboradorTO.getTipoUsuario() == null) {
            switch (usuarioColaboradorTO.getPerfilUsuarioPermissoes()) {
                case 1:
                case 5:
                    usuarioColaboradorTO.setTipoUsuario(TipoUsuarioColaboradorEnum.PROFESSOR);
                    break;
                case 2:
                    usuarioColaboradorTO.setTipoUsuario(TipoUsuarioColaboradorEnum.COORDENADOR);
                    break;
                case 3:
                    usuarioColaboradorTO.setTipoUsuario(TipoUsuarioColaboradorEnum.CONSULTOR);
                    break;
                default:
                    throw new ServiceException(UsuarioExcecoes.ERRO_TIPO_USUARIO_NULL);
            }
        }
        /**
         * 27/12/2018 - Ainda não foi criado o crud de perfil do usuario, quando estiver criado o crud, é para descomentando essa parte
         */
//        if (usuarioColaboradorTO.getPerfilUsuario() == null) {
//            throw new ServiceException(UsuarioExcecoes.ERRO_PERFIL_USUARIO_NULL);
//        }

        Usuario usuarioValid = validarUsuarioColaboradorDuplicado(ctx, usuarioColaboradorTO.getAppUserName(), usuarioColaboradorTO.getId(), empresaId);
        if (usuarioValid != null) {
            throw new ServiceException(UsuarioExcecoes.USUARIO_DUPLICADO);
        }
        boolean possuiUsuarioComEmailInformado = this.usuarioDao.verificaSeJaExisteUsuarioComEmail(ctx, usuarioColaboradorTO.getId(), usuarioColaboradorTO.getEmail());
        if (possuiUsuarioComEmailInformado) {
            throw new ServiceException(UsuarioExcecoes.ERRO_EMAIL_JA_EXISTENTE);
        }
    }


    public List<Usuario> obterListaPorAtributo(final String ctx, final String atributo, Object valor)
            throws ServiceException {
        try {
            Map<String, Object> params = new HashMap<String, Object>();
            params.put(atributo, valor);
            return getUsuarioDao().findByParam(ctx,
                    "SELECT obj FROM Usuario obj WHERE obj." + atributo + " = :" + atributo, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<Usuario> obterUsuariosProfessores(final String ctx, Integer empresa) throws ServiceException {
        try {

            HashMap<String, Object> p = new HashMap<String, Object>();
            p.put("status", StatusEnum.ATIVO);

            if (UteisValidacao.emptyNumber(empresa)) {
                return getUsuarioDao().findByParam(ctx, "SELECT obj FROM Usuario obj WHERE obj.professor IS NOT NULL and obj.status = :status", p);
            } else {
                p.put("empresaZW", empresa);
                return getUsuarioDao().findByParam(ctx, "SELECT obj FROM Usuario obj WHERE obj.professor IS NOT NULL and obj.status = :status and obj.empresaZW = :empresaZW", p);
            }


        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    public void gravarAlteracaoUsuario(final String ctx, Usuario usuario) throws ValidacaoException, ServiceException {
        try {
            validarUsuario(usuario);
            usuario.setTipo(TipoUsuarioEnum.getFromID(usuario.getTipoCodigo()));
            usuario.setPerfil(perfilService.obterPorId(ctx, usuario.getPerfilCodigo()));
            if (usuario.getSenhaAlterar() != null && !usuario.getSenhaAlterar().isEmpty()) {
                usuario.setSenha(Uteis.encriptar(usuario.getSenhaAlterar().toUpperCase()));
            }
            alterar(ctx, usuario);
            usuario = obterPorId(ctx, usuario.getCodigo());
            usuario.setSenhaAlterar(null);
        } catch (ValidacaoException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public List<UsuarioResponseTO> listaUsuarioColaborador(FiltroColaboradorJSON filtros, PaginadorDTO paginadorDTO, Integer empresaId) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            List<Usuario> lista;
            if (SuperControle.independente(ctx)) {
                lista = usuarioDao.listaUsuarioColaboradorTreino(ctx, filtros, paginadorDTO, empresaId);
            } else {
                usuarioDao.listaUsuarioColaborador(ctx, filtros, paginadorDTO, empresaId, true);
                lista = usuarioDao.listaUsuarioColaborador(ctx, filtros, paginadorDTO, empresaId, false);
            }
            List<UsuarioResponseTO> listaRet = new ArrayList<>();
            if (lista != null) {
                for (Usuario usuario : lista) {
                    listaRet.add(new UsuarioResponseTO(usuario));
                }
            }
            return listaRet;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(ColaboradoresExcecoes.ERRO_BUSCAR_COLABORADORES, e);
        }
    }

    public void validarUsuario(Usuario usuario) throws ValidacaoException {
        if (usuario.getPerfilCodigo() == null
                || usuario.getPerfilCodigo().intValue() == 0) {
            throw new ValidacaoException("validacao.perfilusuario");
        }
    }

    public PerfilService getPerfilService() {
        return perfilService;
    }

    public void setPerfilService(PerfilService perfilService) {
        this.perfilService = perfilService;
    }

    public void montarUrlsImagem(final String key, final Usuario u) {
        try {
            u.setAvatarEmpresaApp(Aplicacao.getProp(key, Aplicacao.urlOAMD) + "/imagens/" + key + "-" + u.getEmpresaZW() + ".jpg");
            u.setUrlHomeBackground320x276(Aplicacao.getProp(key, Aplicacao.urlOAMD) + "/imagens/homeBackground320x276-" + key + "-" + u.getEmpresaZW() + ".jpg");
            u.setUrlHomeBackground640x551(Aplicacao.getProp(key, Aplicacao.urlOAMD) + "/imagens/homeBackground640x551-" + key + "-" + u.getEmpresaZW() + ".jpg");
        } catch (Exception e) {
            Uteis.logar(e, UsuarioServiceImpl.class);
        }

    }

    public Usuario gerarUsuario(final String ctx, final String userName,
                                final String senha, final ProfessorSintetico professor,
                                final TipoUsuarioEnum tipo,
                                final Perfil perfil) throws ServiceException {
        try {
            Usuario newUser = new Usuario();
            newUser.setProfessor(professor);
            newUser.setEmpresaZW(professor.getEmpresa().getCodZW());
            newUser.setNome(professor.getNome());
            newUser.setSenha(Uteis.encriptar(senha));
            newUser.setUserName(userName.toUpperCase());
            newUser.setStatus(StatusEnum.ATIVO);
            newUser.setTipo(tipo);
            newUser.setEmpresaZW(professor.getEmpresa().getCodigo());
            newUser.setPerfil(perfil);
            return getUsuarioDao().insert(ctx, newUser);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    public Usuario gerarUsuarioCliente(final String ctx, final String userName,
                                       final String senha, final ClienteSintetico cliente,
                                       final TipoUsuarioEnum tipo) throws ServiceException {
        try {
            Usuario newUser = new Usuario();
            newUser.setCliente(cliente);
            newUser.setNome(cliente.getNome());
            newUser.setSenha(Uteis.encriptar(senha));
            newUser.setUserName(userName.toUpperCase());
            newUser.setStatus(StatusEnum.ATIVO);
            newUser.setTipo(tipo);
            return getUsuarioDao().insert(ctx, newUser);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public Usuario consultarPorUserName(final String chave, final String userName) throws Exception {
        return consultarPorUserName(chave, userName, false, false);
    }

    @Override
    public Usuario consultarPorUserName(final String chave, final String userName, final Boolean verificarUsuarioZW, final Boolean aluno) throws Exception {
        if (!SuperControle.independente(chave) && (aluno != null && !aluno)) {
            String s = "SELECT *, col.empresa, col.situacao, ue.codigo as codigoUsuEmail, col.pessoa FROM Usuario u LEFT JOIN usuarioemail ue on ue.usuario = u.codigo\n" +
                    " INNER JOIN colaborador col on col.codigo = u.colaborador";
            s += " WHERE (UPPER(u.username) = '" + userName.toUpperCase() + "' or UPPER(ue.email) = '" + userName.toUpperCase() + "')";

            try (Connection con = conexaoZWService.conexaoZw(chave)) {
                try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(s, con)) {
                    if (rs.next()) {
                        return usuarioDao.montarDadosUsuario(chave, rs, Uteis.NIVELMONTARDADOS_TODOS);
                    }
                }
            }
            return null;
        }

        String s = "SELECT obj FROM Usuario obj\n" +
                "WHERE status = 0\n" +
                "AND UPPER(username) = :username\n" +
                "UNION ALL\n" +
                "SELECT obj FROM Usuario obj\n" +
                "LEFT OUTER JOIN obj.usuarioEmail as usuarioEmail\n" +
                "WHERE status = 0\n" +
                "AND UPPER(usuarioEmail.email) = :username\n";
        Map<String, Object> params = new HashMap();
        params.put("username", userName.toUpperCase());
        if (verificarUsuarioZW && !SuperControle.independente(chave)) {
            Usuario usuario = null;
            List<Usuario> usuarios = getUsuarioDao().findByParam(chave, s, params);
            for (Usuario u : usuarios) {
                if (!UteisValidacao.emptyNumber(u.getUsuarioZW())) {
                    usuario = u;
                    break;
                }
            }
            if (usuario == null && !usuarios.isEmpty()) {
                usuario = usuarios.get(0);
            }
            return usuario;
        }

        return getUsuarioDao().findObjectByParam(chave, s, params);
    }

    public Usuario consultarPorUserNameSimples(final String chave, final String userName) throws Exception {
        if (!SuperControle.independente(chave)) {
            String s = "SELECT *, col.empresa, col.situacao FROM Usuario u\n" +
                    " INNER JOIN colaborador col on col.codigo = u.colaborador";
            s += " WHERE (UPPER(username) = '" + userName + "')";

            try (Connection con = conexaoZWService.conexaoZw(chave)) {
                try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(s, con)) {
                    if (rs.next()) {
                        return usuarioDao.montarDadosUsuario(chave, rs, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    }
                }
            }
            return null;
        }
        String s = "SELECT obj FROM Usuario obj\n";
        s += " WHERE (UPPER(username) = :username)";
        Map<String, Object> params = new HashMap();
        params.put("username", userName.toUpperCase());
        return getUsuarioDao().findObjectByParam(chave, s, params);
    }

    @Override
    public Usuario criarOuConsultarSeExisteRecorrencia(final String chave) throws Exception {
        Usuario userRecorrencia = new Usuario();
        userRecorrencia.setNome("RECORRENCIA");
        userRecorrencia.setUserName("RECOR");
        userRecorrencia.setSenha("6J+3;-[xd@I.:]Z");
        Usuario userReco = consultarPorUserNameSimples(chave, userRecorrencia.getUserName());
        if (userReco == null) {
            inserir(chave, userRecorrencia);
            return userRecorrencia;
        } else {
            return userReco;
        }
    }

    @Override
    public Usuario consultarPorProfessor(final String chave, final Integer professor) throws Exception {
        String s = "SELECT obj FROM Usuario obj where obj.professor.codigo = :professor";
        Map<String, Object> params = new HashMap();
        params.put("professor", professor);
        return getUsuarioDao().findObjectByParam(chave, s, params);
    }

    @Override
    public Usuario consultarPorCliente(final String chave, final Integer cliente) throws Exception {
        String s = "SELECT obj FROM Usuario obj where obj.cliente.codigo = :cliente";
        Map<String, Object> params = new HashMap();
        params.put("cliente", cliente);
        return getUsuarioDao().findObjectByParam(chave, s, params);
    }

    @Override
    public List<String> consultarEmails(String ctx) throws ServiceException {
        try {
            List<String> emails = new ArrayList<String>();
            List<Usuario> findAll = usuarioDao.findAll(ctx);
            for (Usuario u : findAll) {
                try {
                    Uteis.validarEmail(u.getUserName());
                    emails.add(u.getUserName());
                } catch (Exception e) {
                }
            }
            return emails;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public Usuario consultarPorMatricula(final String chave, final Integer matricula) throws Exception {
        String identificador = "usuario-mat-".concat(matricula.toString());
        Usuario usuario = memcached.ler(chave, identificador);
        if (usuario != null) {
            return usuario;
        }
        String s = "SELECT obj FROM Usuario obj where obj.cliente.matricula = :matricula";
        Map<String, Object> params = new HashMap();
        params.put("matricula", matricula);
        usuario = getUsuarioDao().findObjectByParam(chave, s, params);
        if (usuario != null) {
            cachearUsuario(chave, identificador, usuario);
        }
        return usuario;
    }

    public void removerUsuarioDaCache(final String chave, final Integer matricula) {
        String identificador = "usuario-mat-".concat(matricula.toString());
        memcached.remover(chave, identificador);
    }

    @Override
    public Usuario consultarPorCpf(final String chave, final String cpf) throws Exception {
        String s = "SELECT obj FROM Usuario obj where obj.cliente.CPF = :cpf or obj.cliente.CPF = :cpfNR";
        Map<String, Object> params = new HashMap();
        params.put("cpf", Uteis.removerMascara(cpf));
        params.put("cpfNR", Uteis.aplicarMascara(cpf, "999.999.999-99"));
        return getUsuarioDao().findObjectByParam(chave, s, params);
    }

    @Override
    public List<Usuario> consultarPorNome(final String chave, final String nome, final Integer limit) throws Exception {
        String s = " SELECT u.codigo,p.codigoPessoa as ppessoa, c.codigoPessoa as cpessoa, p.nome as professor, c.nome as cliente FROM Usuario u" +
                " left join professorsintetico p on p.codigo = u.professor_codigo " +
                " left join clientesintetico c on c.codigo = u.cliente_codigo " +
                " where (UPPER(c.nome) like '" + nome.toUpperCase() + "%') " +
                " or (UPPER(p.nome) like '" + nome.toUpperCase() + "%') ";
        List<Usuario> list;
        try (ResultSet rs = getUsuarioDao().createStatement(chave, s)) {
            list = new ArrayList<Usuario>();
            while (rs.next()) {
                Usuario u = new Usuario();
                u.setCodigo(rs.getInt("codigo"));
                if (UteisValidacao.emptyString(rs.getString("cliente"))) {
                    u.setProfessor(new ProfessorSintetico());
                    u.getProfessor().setNome(rs.getString("professor"));
                    u.getProfessor().setCodigoPessoa(rs.getInt("ppessoa"));
                } else {
                    u.setCliente(new ClienteSintetico());
                    u.getCliente().setNome(rs.getString("cliente"));
                    u.getCliente().setCodigoPessoa(rs.getInt("cpessoa"));
                }
                list.add(u);
            }
        }
        return list;
    }

    @Override
    public Usuario validarUsuarioEmail(final String chave, final String email, final Boolean verificarUsuarioZW)
            throws ServiceException {
        try {
            StringBuilder query = new StringBuilder();
            Map<String, Object> params = new HashMap();

            Usuario u = consultarPorUserName(chave, email, verificarUsuarioZW, false);

            return prepararUsuario(u, chave, "");
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new ServiceException(ex.getMessage());
        }
    }

    public void salvarMidiaNuvem(String chave, UsuarioColaboradorTO usuarioColaboradorTO, Usuario usuario) throws Exception {
        String key = UtilS3Base64Img.getUrlImage(usuarioColaboradorTO.getImagemData(), usuarioColaboradorTO.getExtensaoImagem(), chave);
        Aplicacao.saveImageLocal(context, chave, key, true, null, false);
        usuario.getProfessor().getPessoa().setFotoKey(key);
        usuarioDao.update(chave, usuario);
    }

    public Usuario recuperarSenhaPorEmail(String key, String email) throws Exception {
        try {
            System.out.println(ConfiguracoesEnum.MAIL_SERVER.ordinal());
            Uteis.validarEmail(email);
            StringBuilder hql = new StringBuilder();
            Usuario u = new Usuario();
            hql.append("SELECT u from Usuario  u WHERE UPPER(u.userName) = :email ");
            Map<String, Object> params = new HashMap<>();
            params.put("email", email.toUpperCase());
            u = getUsuarioDao().findObjectByParam(key, hql.toString(), params);
            if (u == null) {
                hql = new StringBuilder();
                hql.append("SELECT u from Usuario  u WHERE UPPER(u.professor.email) = :email)");
                u = getUsuarioDao().findObjectByParam(key, hql.toString(), params);
                if (u == null) {
                    throw new ServiceException(UsuarioExcecoes.ERRO_OBTER_EMAIL);
                }
            }
            return u;
        } catch (Exception e) {
            throw new ServiceException(UsuarioExcecoes.ERRO_EMAIL_INVALIDO);
        }
    }

    public ProfessorSinteticoDao getProfessorSinteticoDao() {
        return professorSinteticoDao;
    }

    public void setProfessorSinteticoDao(ProfessorSinteticoDao professorSinteticoDao) {
        this.professorSinteticoDao = professorSinteticoDao;
    }

    @Override
    public String urlZW(String usuarioOamd, String urlLogin, Integer empresaId, String sessionId, String tokenOamd) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            Usuario usuario = obterPorId(ctx, sessaoService.getUsuarioAtual().getId());
            Integer codZW = usuario.getUsuarioZW();
            if (UteisValidacao.emptyNumber(codZW)) {
                List<ProfessorSintetico> professorSinteticos = professorSinteticoService.obterProfessoresPorCodigoPessoaZw(ctx, usuario.getProfessor().getCodigoPessoa());
                for (ProfessorSintetico p : professorSinteticos) {
                    if (!p.getCodigo().equals(usuario.getProfessor().getCodigo()) && UteisValidacao.emptyNumber(codZW)) {
                        Usuario porProfessor = consultarPorProfessor(ctx, p.getCodigo());
                        codZW = porProfessor == null ? null : porProfessor.getUsuarioZW();
                    }
                }
            }
            return Aplicacao.getProp(ctx, Aplicacao.urlZillyonWeb)
                    + Uteis.formatUrlRedirect(ctx,
                    usuarioOamd,
                    UteisValidacao.emptyString(urlLogin) ? Aplicacao.getProp("urlLogin") : urlLogin,
                    codZW,
                    empresaId,
                    Uteis.URI_REDIRECT_INICIO,
                    sessaoService.getUsuarioAtual().getToken(), sessionId, tokenOamd);
        } catch (Exception ex) {
            throw new ServiceException(EmpresaExcecoes.ERRO_BUSCAR_EMPRESAS, ex);
        }
    }

    @Override
    public String urlSolicitarAtendimento(Integer empresaId, HttpServletRequest request) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            boolean treinoIndependente = SuperControle.independente(ctx);
            Usuario usuario = obterPorId(ctx, sessaoService.getUsuarioAtual().getId());
            ExecuteRequestHttpService requisicao = new ExecuteRequestHttpService();
            Map<String, String> params = new HashMap<>();
            params.put("key", ctx);
            JSONObject empresaFinanceiro = new JSONObject();
            if (!treinoIndependente) {
                JSONObject response = new JSONObject(requisicao.executeRequest(
                        String.format("%s%s", Aplicacao.getProp(ctx, Aplicacao.urlOAMD), "/prest/empresa/consultarEmpresaFinanceiro"),
                        params
                ));
                empresaFinanceiro = new JSONObject(response.opt("empresaFinanceiro").toString());
            }
            Empresa empresa;
            JSONObject json = new JSONObject();
            if (treinoIndependente) {
                empresa = empresaService.obterPorId(ctx, empresaId);
            } else {
                empresa = empresaService.obterPorIdZW(ctx, empresaId);
                json.put("codigoUsuarioZW", usuario.getUsuarioZW());
            }
            json.put("key", ctx);
            json.put("nomeEmpresa", empresa.getNome());
            json.put("userName", usuario.getUserName());
            json.put("nomeCompleto", usuario.getProfessor().getNome());
            json.put("telefones", usuario.getProfessor().getPessoa().getTelefones(false));
            json.put("origemSistema", 1); //ENUM DA UCP PARA IDENTIFICAR QUE SUA ORIGEM É TREINO
            json.put("codigoUsuario", usuario.getCodigo());
            json.put("timevld", Calendario.getInstance(Calendario.hoje().getTime() + ((60 * 30) * 1000)).getTimeInMillis());
            json.put("urlFoto", fotoService.defineURLFotoPessoa(
                    request,
                    usuario.getProfessor().getPessoa().getFotoKey(),
                    usuario.getProfessor().getPessoa().getCodigo(),
                    false,
                    ctx,
                    !SuperControle.independente(ctx))
            );
            json.put("solicitacao", "true");
            json.put("SolicitacaoSuporte", "movidesk");
            json.put("codigoEmpresa", treinoIndependente ? empresa.getCodigo() : empresaFinanceiro.opt("codigo"));
            json.put("nomeFantasia", treinoIndependente ? empresa.getNome() : empresaFinanceiro.opt("nomeFantasia"));
            json.put("razaoSocial", treinoIndependente ? null : empresaFinanceiro.opt("razaoSocial"));
            json.put("cpfCnpj", treinoIndependente ? null : empresaFinanceiro.opt("cnpj"));
            json.put("cidade", treinoIndependente ? null : empresaFinanceiro.opt("cidade"));
            json.put("estado", treinoIndependente ? null : empresaFinanceiro.opt("estado"));
            json.put("codigoFinanceiro", treinoIndependente ? null : empresaFinanceiro.opt("codigoFinanceiro"));
            String email = "";
            if (usuario.getProfessor().getPessoa().getEmails().size() > 0
                    && isNotBlank(usuario.getProfessor().getPessoa().getEmails().get(0).getEmail())) {
                email = usuario.getProfessor().getPessoa().getEmails().get(0).getEmail().toUpperCase().trim();
            } else if (isNotBlank(empresa.getEmail())) {
                email = empresa.getEmail();
            } else {

                email = empresaFinanceiro.opt("email") != null ? empresaFinanceiro.opt("email").toString().replace(";", "").toUpperCase().trim() : "";
            }
            json.put("email", email);
            json.put("telefoneEmpresa", treinoIndependente ? null : empresaFinanceiro.opt("telefone"));


            final String lgn = Uteis.encriptar(json.toString(), "chave_login_unificado");
            String urlUCP = "";
            JSONObject caminhos = new BetaTestersService().caminhos();
            if (UteisValidacao.emptyString(caminhos.optString("ucp"))) {
                throw new Exception(" o discovery não retornou a url da UCP, verifique se está preenchida no banco corretamente");
            }
            urlUCP = caminhos.optString("ucp");
            return String.format("%s/oid?lgn=%s",
                    !UteisValidacao.emptyString(urlUCP) ? urlUCP : Aplicacao.getProp(ctx, Aplicacao.myUpUrlBase),
                    lgn
            );
        } catch (Exception ex) {
            throw new ServiceException(UsuarioExcecoes.ERRO_SOLICITAR_ATENDIMENTO, ex);
        }
    }

    public String obterUsernameAlunoPorFicha(String ctx, Integer fichaId) throws ServiceException {
        return usuarioDao.obterUsernameAlunoPorFicha(ctx, fichaId);
    }

    public String registrarZillyonWebLoginTreinoWeb(String ctx, Usuario usuario) {
        try {
            if (UteisValidacao.emptyString(ctx)) {
                return "Chave não informada";
            }

            if (usuario == null || usuario.getUsuarioZW() == null || UteisValidacao.emptyNumber(usuario.getUsuarioZW())) {
                return "Usuário inválido";
            }

            String mod = Aplicacao.getProp(ctx, Aplicacao.modulos);
            boolean temZW = mod != null && mod.contains("ZW");
            if (!temZW) {
                return "Não tem módulo ZW";
            }

            Map<String, String> params = new HashMap<String, String>();
            params.put("k", ctx);
            params.put("op", "registrarAcesso");
            params.put("usu", usuario.getUsuarioZW().toString());

            final String urlZW = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg);
            return ExecuteRequestHttpService.executeRequest(urlZW + "/prest/acessoColaborador", params);
        } catch (Exception ex) {
            Uteis.logar(ex, UsuarioServiceImpl.class);
            return ex.getMessage();
        }
    }

    @Override
    public Usuario consultarColaboradorPorUsername(final String contexto, final String username) throws ServiceException {
        String identificador = "usuario-username-".concat(username.replaceAll(" ", "0_"));
        Usuario usuario = memcached.ler(contexto, identificador);
        if (usuario != null) {
            return usuario;
        }
        String hql = "select obj from " + Usuario.class.getSimpleName() + " obj " +
                "where obj.userName = :username and obj.professor is not null";
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("username", username);

        usuario = obterObjetoPorParam(contexto, hql, map);
        if (usuario != null) {
            cachearUsuario(contexto, identificador, usuario);
        }
        return usuario;
    }

    public Map<Integer, String> usernamesClientes(final String ctx, List<ClienteSintetico> clientes) throws ServiceException {
        Map<Integer, String> usernames = new HashMap<>();
        if (clientes.isEmpty()) {
            return usernames;
        }
        try {
            String cods = "";
            for (ClienteSintetico cod : clientes) {
                cods += "," + cod.getCodigo();
            }
            try (ResultSet rs = usuarioDao.createStatement(ctx, "select username, cliente_codigo  from usuario u where cliente_codigo is not null and \n" +
                    "cliente_codigo in (" + cods.replaceFirst(",", "") + ")")) {

                while (rs.next()) {
                    usernames.put(rs.getInt("cliente_codigo"), rs.getString("username"));
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return usernames;
    }

    @Override
    public Usuario gerarUsuarioApp(final String ctx, final String userName,
                                   final String senha, final ProfessorSintetico professor,
                                   final ClienteSintetico cliente,
                                   final TipoUsuarioEnum tipo) throws ServiceException {

        Usuario usuario = null;

        Perfil perfil = perfilService.obterPorId(ctx, 1);
        if (professor != null) {
            usuario = gerarUsuario(ctx, userName, senha, professor, tipo, perfil);
        } else {
            if (cliente != null)
                usuario = gerarUsuarioCliente(ctx, userName, senha, cliente, tipo);
            else {
                throw new ServiceException("Informe professor ou cliente");
            }
        }
        return usuario;
    }

    @Override
    public void setServletContext(ServletContext servletContext) {
        this.context = servletContext;
    }

    @Override
    public Usuario validarUsuarioMovelApp(final String chave, final String userName, boolean aluno)
            throws ServiceException {
        List<Usuario> listaUsuario = new ArrayList<>();
        try {
            if (SuperControle.independente(chave) || aluno) {
                Map<String, Object> params = new HashMap();
                String s = "SELECT obj FROM Usuario obj\n";
                s += " WHERE status = 0 AND (UPPER(TRIM(username)) = :username)";
                s += aluno ? " and cliente_codigo is not null " : " ";
                if(aluno) {
                    s += " or obj.cliente.email = :email";
                    params.put("email", userName);
                }
                params.put("username", userName.toUpperCase().trim());

                listaUsuario = usuarioDao.findByParam(chave, s, params);
            } else {
                String s = "SELECT u.*, c.*, p.* FROM usuario u" +
                        " INNER JOIN colaborador c on c.codigo = u.colaborador" +
                        " INNER JOIN pessoa p ON p.codigo = c.pessoa " +
                        " WHERE u.statustw = 0 AND c.situacao = 'AT' AND (UPPER(TRIM(u.username)) = '" + userName.trim().toUpperCase() + "')" +
                        " UNION ALL" +
                        " SELECT u.*, c.*, p.* FROM usuario u " +
                        " INNER JOIN colaborador c on c.codigo = u.colaborador" +
                        " LEFT JOIN usuarioemail uE on uE.usuario = u.codigo" +
                        " INNER JOIN pessoa p ON p.codigo = c.pessoa " +
                        " WHERE u.statustw = 0 AND c.situacao = 'AT' AND (UPPER(TRIM(uE.email)) = '" + userName.trim().toUpperCase() + "')";
                try (Connection con = conexaoZWService.conexaoZw(chave)) {
                    try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(s, con)) {
                        if (rs.next()) {
                            listaUsuario.add(usuarioDao.montarDadosUsuario(chave, rs, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                        }
                    }
                }
            }
            Usuario u = getUsuarioColaboradorAtivo(listaUsuario, chave);

            return prepararUsuario(u, chave, "");
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new ServiceException(ex.getMessage());
        }
    }

    private Usuario getUsuarioColaboradorAtivo(List<Usuario> listaUsuario, String chave) throws Exception {
        if (listaUsuario == null || listaUsuario.isEmpty()) {
            return null;
        }
        Usuario u = listaUsuario.get(0);
        if (u.getProfessor() == null) {
            return u;
        }
        try (Connection conZW = conexaoZWService.conexaoZw(chave)) {
            for (Usuario user : listaUsuario) {
                try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta("SELECT situacao FROM colaborador WHERE codigo = " + user.getProfessor().getCodigoColaborador(), conZW)) {
                    if (rs.next()) {
                        String colaboradorAtivo = rs.getString("situacao");
                        if (colaboradorAtivo.equals("AT")) {
                            u = user;
                        }
                    }
                }
            }
        }
        return u;
    }

    @Override
    public Integer consultarCodZWPorCodCliente(String ctx, Integer codUsuario) throws ServiceException {
        ResultSet rs;
        int codUsuarioZW = 0;
        try {
            rs = getUsuarioDao().createStatement(ctx, "select usuariozw from usuario where codigo = " + codUsuario);
            while (rs.next()) {
                codUsuarioZW = rs.getInt("usuariozw");
            }
        } catch (Exception ex) {
            throw new ServiceException(UsuarioExcecoes.ERRO_BUSCAR_USUARIO);
        }
        return codUsuarioZW;

    }

    public Integer consultarCodProfessorPorCodigoExterno(String ctx, String codigoExterno, Integer codigoEmpresaZw) throws ServiceException {
        ResultSet rs;
        Integer codigoProfessor = 0;
        try {
            rs = getUsuarioDao().createStatement(ctx, "select professor_codigo from usuario where codigoexterno = '" + codigoExterno + "' and empresazw = " + codigoEmpresaZw);
            while (rs.next()) {
                codigoProfessor = rs.getInt("professor_codigo");
            }
        } catch (Exception ex) {
            throw new ServiceException(UsuarioExcecoes.ERRO_BUSCAR_USUARIO);
        }
        return codigoProfessor;
    }

    public void naoApresentarHoje(String ctx, Integer usuario) throws Exception {
        NotificacaoFimTreino notificacao = new NotificacaoFimTreino();
        notificacao.setUsuario(usuario);
        notificacao.setUltimaNotificacao(Calendario.getDataComHoraZerada(Calendario.hoje()));
        notificacaoFimTreinoDao.insert(ctx, notificacao);
    }

    public Boolean podeApresentarNotificacaoFimTreino(String ctx, Integer usuario) throws ServiceException {
        try {
            String s = "SELECT obj FROM NotificacaoFimTreino obj WHERE usuario = :usuario and ultimaNotificacao = :ultimaNotificacao";
            Map<String, Object> params = new HashMap();
            params.put("usuario", usuario);
            params.put("ultimaNotificacao", Calendario.getDataComHoraZerada(Calendario.hoje()));
            List<NotificacaoFimTreino> naoNotificar = notificacaoFimTreinoDao.findByParam(ctx, s, params);
            return UteisValidacao.emptyList(naoNotificar);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    @Override
    public List<UsuarioAppBasicoJSON> obterIdentidadeDadosBasicosCelularApp(String ctx, String ddi, String ddd, String telefone) throws ServiceException {
        ResultSet rs = null;
        String formatedNumber = '(' + ddd + ')' + telefone;
        List<UsuarioAppBasicoJSON> usuarioJSONs = new ArrayList<>();
        try {
            StringBuilder sql = new StringBuilder("select distinct " +
                    "pro.codigo as codigoColaborador, " +
                    "cli.codigo as codigocliente," +
                    "pes.nome," +
                    "ema.email," +
                    "regexp_replace(tel.telefone, '[^0-9]','','g') as celular," +
                    "usu.username as nomeUsuarioMovel, " +
                    "cli.matricula," +
                    "pes.fotokey as urlFoto, " +
                    "usu.codigo as codigoUsuario " +
                    "from pessoa pes " +
                    "left join telefone tel on tel.pessoa_codigo = pes.codigo " +
                    "left join clientesintetico cli on cli.pessoa_codigo = pes.codigo  " +
                    "left join professorsintetico pro on pro.pessoa_codigo = pes.codigo " +
                    "left join (select pessoa_codigo, min(email) as email   from email  as em group by em.pessoa_codigo ) ema on ema.pessoa_codigo = pes.codigo " +
                    "left join usuario usu on usu.cliente_codigo = cli.codigo or usu.professor_codigo = pro.codigo ");
            sql.append("where tel.telefone = '");
            sql.append(formatedNumber);
            sql.append("' order by pes.nome ");
            rs = getUsuarioDao().createStatement(ctx, sql.toString());
            while (rs.next()) {
                UsuarioAppBasicoJSON usuarioJson = new UsuarioAppBasicoJSON();
                usuarioJson.setCodigocolaborador(rs.getInt("codigoColaborador"));
                usuarioJson.setCodigocliente(rs.getInt("codigocliente"));
                usuarioJson.setNome(rs.getString("nome"));
                usuarioJson.setEmail(rs.getString("email"));
                usuarioJson.setCelular(rs.getString("celular"));
                usuarioJson.setNomeusuariomovel(rs.getString("nomeUsuarioMovel"));
                usuarioJson.setMatricula(rs.getString("matricula"));
                usuarioJson.setUrlfoto(rs.getString("urlFoto"));
                usuarioJson.setCodigousuariotreino(rs.getInt("codigoUsuario"));
                usuarioJSONs.add(usuarioJson);
            }
        } catch (Exception ex) {
            throw new ServiceException(UsuarioExcecoes.ERRO_BUSCAR_USUARIO);
        }
        if (usuarioJSONs.isEmpty()) {
            throw new ServiceException(UsuarioExcecoes.ERRO_USUARIO_NAO_ENCONTRADO);
        }
        return usuarioJSONs;

    }

    public Usuario consultarPorCodigoExternoAndEmpresaZW(final String ctx, final String codigoExterno, final Integer empresaZW) throws ServiceException {
        try {
            StringBuilder hql = new StringBuilder();
            HashMap<String, Object> params = new HashMap<>();

            hql.append("SELECT obj FROM Usuario obj ");
            hql.append("WHERE obj.codigoExterno = :codigoExterno ");
            params.put("codigoExterno", codigoExterno);

            if (!UteisValidacao.emptyNumber(empresaZW)) {
                hql.append("AND obj.empresaZW = :empresaZW ");
                params.put("empresaZW", empresaZW);
            }
            return getUsuarioDao().findObjectByParam(ctx, hql.toString(), params);
        } catch (Exception ex) {
            Logger.getLogger(this.getClass().getName()).log(Level.SEVERE, null, ex);
            throw new ServiceException(ex.getMessage());
        }
    }

    public Usuario validarUsuarioLoginV2(final UsuarioLoginV2DTO dto) throws ServiceException {
        try {
            Usuario u = null;
            if (!UteisValidacao.emptyNumber(dto.getUsuario_tw())
                    && SuperControle.independente(dto.getChave())) {
                u = usuarioDao.obterPorId(dto.getChave(), dto.getUsuario_tw());
            }
            if (!UteisValidacao.emptyNumber(dto.getUsuario_zw())) {
                u = obterPorId(dto.getChave(), dto.getUsuario_zw());
//                u = consultarPorUsuarioZWAndEmpresaZW(dto.getChave(), dto.getUsuario_zw(), null);
            }
            if (u == null && !UteisValidacao.emptyString(dto.getUsuario_username())) {
                u = consultarPorUserNameSimples(dto.getChave(), dto.getUsuario_username());
            }
            return prepararUsuario(u, dto.getChave(), null);
        } catch (Exception ex) {
            Logger.getLogger(this.getClass().getName()).log(Level.SEVERE, null, ex);
            throw new ServiceException("Usuário não encontrado.");
        }
    }

    public Usuario consultarPorUsuarioZWAndEmpresaZW(final String ctx, final Integer usuarioZW, final Integer empresaZW) throws ServiceException {
        try {
            StringBuilder hql = new StringBuilder();
            HashMap<String, Object> params = new HashMap<>();

            hql.append("SELECT obj FROM Usuario obj ");
            hql.append("WHERE obj.usuarioZW = :usuarioZW ");
            params.put("usuarioZW", usuarioZW);

            if (!UteisValidacao.emptyNumber(empresaZW)) {
                hql.append("AND obj.empresaZW = :empresaZW ");
                params.put("empresaZW", empresaZW);
            }
            return getUsuarioDao().findObjectByParam(ctx, hql.toString(), params);
        } catch (Exception ex) {
            Logger.getLogger(this.getClass().getName()).log(Level.SEVERE, null, ex);
            throw new ServiceException(ex.getMessage());
        }
    }

    @Override
    public TokenDTO solicitarCodigoVerficacaoEmail(String chave, Integer idUsuario, String email, HttpServletRequest request) throws ServiceException {
        try {
            if (SuperControle.independente(chave)) {
                boolean possuiUsuarioComEmailInformado = this.usuarioDao.verificaSeJaExisteUsuarioComEmail(chave, idUsuario, email);
                if (possuiUsuarioComEmailInformado) {
                    throw new ServiceException(UsuarioExcecoes.ERRO_EMAIL_JA_EXISTENTE);
                }
                Usuario usuario = obterPorId(chave, idUsuario);
                try {
                    if (usuario.getUsuarioEmail() != null && !UteisValidacao.emptyNumber(usuario.getUsuarioEmail().getCodigo())) {
                        usuario.setUsuarioEmail(usuarioEmailService.obterPorId(chave, usuario.getUsuarioEmail().getCodigo(), usuario.getTipo()));
                    }
                } catch (Exception e) {
                    Uteis.logar(e, UsuarioServiceImpl.class);
                }
                Empresa emp = empresaService.obterEmpresaTreinoIndependente(chave);
                if (StringUtils.isEmpty(usuario.getUsuarioGeral())) {
                    TokenDTO tokenDTO = new TokenDTO();
                    UsuarioEmail usuarioEmail = new UsuarioEmail();
                    UsuarioEmail usuarioEmailByEmail = usuarioEmailService.obterUsuarioEmailParam(chave, "email", email);
                    UsuarioEmail usuarioEmailByUsuario = usuarioEmailService.obterUsuarioEmailParam(chave, "usuario", usuario);

                    if (usuarioEmailByEmail == null) {
                        if (usuarioEmailByUsuario == null) {
                            usuarioEmail.setEmail(email);
                            usuarioEmail.setUsuario(usuario);
                            usuarioEmailService.inserir(chave, usuarioEmail);
                        } else {
                            usuarioEmailByUsuario.setEmail(email);
                            usuarioEmailService.alterar(chave, usuarioEmailByUsuario);
                        }
                    }

                    tokenDTO.setToken(usuarioGeralService.enviarEmailNovoUsuario(chave, idUsuario, false, true, emp, sessaoService.getUsuarioAtual(), Uteis.getIp(request)));
                    return tokenDTO;
                } else {
                    usuario.getUsuarioEmail().setEmail(email);
                    return usuarioGeralService.solicitarTrocaEmail(chave, idUsuario, true, false, usuario.getUsuarioEmail(), emp.getCodigo(), Uteis.getIp(request));
                }
            } else {
                throw new ServiceException("Ação não permitida");
            }
        } catch (Exception ex) {
            Logger.getLogger(this.getClass().getName()).log(Level.SEVERE, null, ex);
            throw new ServiceException(ex.getMessage());
        }
    }

    @Override
    public void processarToken(String chave, Integer idUsuario, TokenDTO tokenDTO, HttpServletRequest request) throws ServiceException {
        try {
            Empresa emp = empresaService.obterEmpresaTreinoIndependente(chave);
            usuarioGeralService.validarToken(chave, idUsuario, emp.getCodigo(), tokenDTO, sessaoService.getUsuarioAtual(), Uteis.getIp(request), "TRI_VALIDAR_CODIGO_VERIFICACAO");
        } catch (Exception ex) {
            Logger.getLogger(this.getClass().getName()).log(Level.SEVERE, null, ex);
            throw new ServiceException(ex.getMessage());
        }
    }

    @Override
    public void recuperarSenhaNovoLogin(String chave, Integer idUsuario, HttpServletRequest request) throws ServiceException {
        try {
            if (SuperControle.independente(chave)) {
                Empresa empresa = empresaService.obterEmpresaTreinoIndependente(chave);
                usuarioGeralService.solicitarTrocaSenha(chave, idUsuario, empresa.getCodigo(), sessaoService.getUsuarioAtual(), Uteis.getIp(request));
            } else {
                throw new ServiceException("Ação não permitida");
            }
        } catch (Exception ex) {
            Logger.getLogger(this.getClass().getName()).log(Level.SEVERE, null, ex);
            throw new ServiceException(ex.getMessage());
        }
    }

    @Override
    public void desvincularUsuarioNovoLogin(String chave, Integer idUsuario, HttpServletRequest request) throws ServiceException {
        try {
            if (SuperControle.independente(chave)) {
                Usuario usuario = usuarioDao.obterPorId(chave, sessaoService.getUsuarioAtual().getId());
                if (usuario.getPerfil().getPermissoes().stream()
                        .noneMatch(
                                permissao ->
                                        permissao.getRecurso().equals(RecursoEnum.DESVINCULAR_USUARIO)
                                                && permissao.getTipoPermissoes() != null && permissao.getTipoPermissoes().size() > 0
                        )) {
                    throw new ServiceException("usuario_sem_permissao", "Usuário sem permissão para a funcionalidade \"Desvincular Usuário\"");
                }
                Empresa empresa = empresaService.obterEmpresaTreinoIndependente(chave);
                usuarioGeralService.desvincularUsuarioNovoLogin(chave, idUsuario, empresa.getCodigo(), sessaoService.getUsuarioAtual(), Uteis.getIp(request));
            } else {
                throw new ServiceException("Ação não permitida");
            }
        } catch (ServiceException se) {
            throw se;
        } catch (Exception ex) {
            Logger.getLogger(this.getClass().getName()).log(Level.SEVERE, null, ex);
            throw new ServiceException("Não foi possível desvincular o usuário da chave " + chave, ex.getMessage());
        }
    }

    private void usuarioNovoLogin(final String ctx, final Integer codigo, boolean novoUsuario, boolean definirSenha,
                                  boolean exception, HttpServletRequest request) throws Exception {
        usuarioGeralService.processarUsuarioGeral(ctx, codigo, definirSenha, novoUsuario, exception, request);
    }

    @Override
    public Usuario criarOuConsultarSeExisteImportacao(final String chave, ColaboradorTO colaboradorTO, Integer empresaId,
                                                      HttpServletRequest request) throws Exception {
        Usuario usuario = null;
        Integer codigoUsuario = criarOuConsultarUsuarioImportadorTreino(chave, empresaId);
        if (!UteisValidacao.emptyNumber(codigoUsuario)) {
            usuario = consultarPorUsuarioZWAndEmpresaZW(chave, codigoUsuario, null);
        }
        if (usuario != null && !UteisValidacao.emptyNumber(usuario.getCodigo())) {
            return usuario;
        } else {
            throw new Exception("Falha ao consultar usuario padrão importador treino no zw");
        }
    }

    private Integer criarOuConsultarUsuarioImportadorTreino(String chave, Integer empresa) throws Exception {
        String urlZW = Aplicacao.getPropOAMD(chave, Aplicacao.urlZillyonWeb);
        urlZW += "/prest/treino/usuario-importador-treino";
        urlZW += "?chave=" + chave;
        urlZW += "&empresa=" + empresa;
        ;
        HttpGet httpGet = new HttpGet(urlZW);
        httpGet.setHeader("Content-Type", "application/json");
        HttpClient client = ExecuteRequestHttpService.createConnectorCloseable();
        HttpResponse response = client.execute(httpGet);

        if (response.getStatusLine().getStatusCode() != 200) {
            throw new Exception("Falha ao consultar usuario padrão importador treino no zw: http code: " + response.getStatusLine().getStatusCode());
        }
        String responseBody = EntityUtils.toString(response.getEntity());

        JSONObject jsonRetorno = new JSONObject(responseBody);
        return jsonRetorno.optInt("usuariozw");
    }

    @Override
    public void registrarUsoApp(String chave, UsuarioAppDTO usuarioAppDTO) throws Exception {
        if(!UteisValidacao.emptyNumber(usuarioAppDTO.getCodUsuario())) {
            Usuario usuario = usuarioDao.findById(chave, usuarioAppDTO.getCodUsuario());
            if (usuario == null) {
                throw new Exception("Usuário não encontrado!");
            }
            try {
                StringBuilder sqlUpdate = new StringBuilder();
                sqlUpdate.append("UPDATE usuario SET idclienteapp = ?, versaodoapp = ?, dataregistrousoapp = ?, apputilizado = ? ");
                sqlUpdate.append(" WHERE codigo = ? ");
                try (PreparedStatement pstm = usuarioDao.getConnection(chave).prepareStatement(sqlUpdate.toString())){
                    pstm.setString(1, usuarioAppDTO.getIdClienteApp());
                    pstm.setString(2, usuarioAppDTO.getVersaoDoApp());
                    pstm.setTimestamp(3, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
                    pstm.setString(4, usuarioAppDTO.getAppUtilizado());
                    pstm.setInt(5, usuario.getCodigo());
                    pstm.execute();
                }
            } catch (Exception e) {
                Uteis.logar(e, UsuarioServiceImpl.class);
                throw e;
            }
        } else {
            validacoesRegistrarUsoApp(chave, usuarioAppDTO);
            try {
                StringBuilder sqlUpdate = new StringBuilder();
                sqlUpdate.append("UPDATE usuario SET idclienteapp = ?, versaodoapp = ?, dataregistrousoapp = ?, apputilizado = ? ");
                sqlUpdate.append(" WHERE username = ? ");
                try (PreparedStatement pstm = usuarioDao.getConnection(chave).prepareStatement(sqlUpdate.toString())){
                    pstm.setString(1, usuarioAppDTO.getIdClienteApp());
                    pstm.setString(2, usuarioAppDTO.getVersaoDoApp());
                    pstm.setTimestamp(3, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
                    pstm.setString(4, usuarioAppDTO.getAppUtilizado());
                    pstm.setString(5, usuarioAppDTO.getUserName().trim());
                    pstm.execute();
                }
            } catch (Exception e) {
                Uteis.logar(e, UsuarioServiceImpl.class);
                throw e;
            }
        }

    }

    private void validacoesRegistrarUsoApp(String chave, UsuarioAppDTO usuarioAppDTO) throws Exception {
        if (UteisValidacao.emptyString(usuarioAppDTO.getUserName())) {
            throw new Exception("Username vazio!");
        }
        String sql = "select codigo from usuario where username = '" + usuarioAppDTO.getUserName() + "'";
        if (!usuarioDao.createStatement(chave, sql).next()) {
            throw new Exception("Usuario não encontrado!");
        }
    }

    @Override
    public Usuario userIA(String chave,
                          Integer empresaId,
                          JSONObject tokenDecode) throws Exception {
        if (tokenDecode.has("p") && tokenDecode.getString("p").contains("TREINOIA")) {
            Usuario userIA = new Usuario();
            userIA.setUserName("PACTOBR");
            Usuario userReco = consultarPorUserNameSimples(chave, "PACTOBR");
            if (userReco == null) {
                ColaboradorTO colaboradorTO = new ColaboradorTO();
                colaboradorTO.setUsarApp(true);
                colaboradorTO.setNome("Pacto IA");
                colaboradorTO.setAppUserName("Pacto IA");
                SecureRandom random = new SecureRandom();
                colaboradorTO.setTipoUsuario(TipoUsuarioColaboradorEnum.COORDENADOR);
                Perfil perfilCoordenador = perfilDao.insertOrGetObjectForName(chave, Perfil.NOME_PERFIL_COORDENADOR);
                colaboradorTO.setPerfilUsuarioID(perfilCoordenador.getCodigo());
                colaboradorTO.setSituacao(SituacaoColaboradorEnum.ATIVO);
                colaboradorTO.setSexo(SexoEnum.N);
                colaboradorTO.setEmails(Arrays.asList(new EmailTO[]{new EmailTO("<EMAIL>")}));
                colaboradorTO.setFones(Arrays.asList(new TelefoneTO[]{new TelefoneTO("(62)999990000")}));
                colaboradorTO.setDataNascimento(new Date(631159200000L));
                professorSinteticoService.cadastrarColaborador(chave, colaboradorTO, empresaId, null);
                return consultarPorUserNameSimples(chave, userIA.getUserName());
            } else {
                return userReco;
            }
        }
        throw new Exception("your token is not welcomed here");
    }

    @Override
    public Boolean temAcessoAcademia(UsuarioJSON uJSON, String ctx) throws Exception {
        Integer codigoPessoa = uJSON.getCodigoPessoa() == null ? uJSON.getCodigoPessoaCliente() : uJSON.getCodigoPessoa();

        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
            try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta("select *\n" +
                    "from situacaoclientesinteticodw\n" +
                    "where situacao = 'AT' and codigocliente = " + uJSON.getCodigoCliente(), conZW)) {
                if (rs.next()) {
                    return true;
                } else if (codigoPessoa != null && codigoPessoa != 0) {
                    try (ResultSet rs2 = ConexaoZWServiceImpl.criarConsulta("SELECT *\n" +
                            "FROM PeriodoAcessoCliente\n" +
                            "WHERE dataInicioAcesso <= '" + Uteis.getDataJDBC(Calendario.hoje()) + "'\n" +
                            "  and dataFinalAcesso >= '" + Uteis.getDataJDBC(Calendario.hoje()) + "'\n" +
                            "  and pessoa = " + codigoPessoa + "\n" +
                            "  and tipoAcesso = 'PL'\n" +
                            "ORDER BY codigo", conZW)) {
                        if (rs2.next()) {
                            return true;
                        }
                    }
                }
            }
        }
        return false;
    }

    @Override
    public void alterarFotoUsuarioColaborador(String key, String url, int valorVersao, Integer codUsuarioZW, Boolean atualizaFotoZW) throws Exception {
        getUsuarioDao().getCurrentSession(key).clear();
        getUsuarioDao().executeNativeSQL(key, "UPDATE usuario \n" +
                "set fotokeyapp = '" + url + "', versaofotoapp = " + valorVersao + " WHERE usuariozw = " + codUsuarioZW);

        if (atualizaFotoZW) {
            alterarFotoPessoaZW(key, url, codUsuarioZW);
        }
    }

    private void alterarFotoPessoaZW(String key, String url, Integer codUsuarioZW) throws Exception {
        try (Connection conZW = conexaoZWService.conexaoZw(key)) {
            try {
                ConexaoZWServiceImpl.executarConsulta("UPDATE pessoa " +
                        "SET fotokey = '" + url + "' " +
                        " WHERE codigo = (SELECT p.codigo\n" +
                        "                  FROM pessoa p\n" +
                        "                           join colaborador c on p.codigo = c.pessoa\n" +
                        "                           join usuario u on c.codigo = u.colaborador\n" +
                        "                  WHERE u.codigo = " + codUsuarioZW + ")", conZW);
            } catch (Exception ex) {
                throw new Exception("Não foi possível alterar a foto do usuario colaborador");
            }
        }
    }

    public boolean temPerfilUnificado(String ctx) {
        String sql = "SELECT * FROM perfilacesso where unificado = true";

        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
            PreparedStatement sqlConsultar = conZW.prepareStatement(sql);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    return false;
                }
                return true;
            }
        } catch (Exception e) {
            return false;
        }
    }

    public String nomeEmpresaClienteSintetico(String chave, Usuario u) throws ServiceException {
        EmpresaService es = UtilContext.getBean(EmpresaService.class);
        Empresa e = es.obterPorIdZW(chave, u.getCliente().getEmpresa());
        if (e != null) {
            return e.getNome();
        }
        return null;
    }

    @Override
    public void preencheFotoEVersaoFotoApp(String ctx, Usuario u, Boolean atualizaSessao) throws Exception {
        try {
            if(atualizaSessao) {
                getUsuarioDao().getCurrentSession(ctx).clear();
            }
            Map<String, String> fotoAppVersao = getUsuarioDao().consultaVersaoFotoApp(ctx, u.getCodigo());
            if (fotoAppVersao != null) {
                u.setFotoKeyApp(fotoAppVersao.get("fotokeyapp"));
                u.setVersaoFotoApp(Integer.parseInt(fotoAppVersao.get("versaofotoapp")));
            }
        } catch (Exception ignore) {
        }
    }


    @Override
    public void preencheFotoEVersaoFotoAppColaborador(String ctx, Usuario u) throws Exception {
        try {
            getUsuarioDao().getCurrentSession(ctx).clear();
            Map<String, String> fotoAppVersao = getUsuarioDao().consultaVersaoFotoAppColaborador(ctx, u.getCodigo());
            if (fotoAppVersao != null) {
                u.setFotoKeyApp(fotoAppVersao.get("fotokeyapp"));
                u.setVersaoFotoApp(Integer.parseInt(fotoAppVersao.get("versaofotoapp")));
            }
        } catch (Exception ignore) {
        }
    }

    @Override
    public void alterarPerfilUsuario(String ctx, Integer usuarioZW, Integer perfilCodigo) throws Exception {
        try {
            getUsuarioDao().executeNativeSQL(ctx, "UPDATE usuario set perfil_codigo = "
                    + perfilCodigo + " WHERE usuariozw =  " + usuarioZW);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public List<UsuarioAppBasicoJSON> obterIdentidadeDadosBasicosEmailApp(String ctx, String email) throws ServiceException {
        List<UsuarioAppBasicoJSON> usuarioJSONs = new ArrayList<>();
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT DISTINCT cli.codigo AS codigocliente,\n ");
            sql.append(" col.codigo AS codigoColaborador,\n ");
            sql.append(" pes.nome,\n ");
            sql.append(" ema.email,\n ");
            sql.append(" STRING_AGG(DISTINCT REGEXP_REPLACE(tel.telefone, '[^0-9]', '', 'g'), ',') AS celular,\n ");
            sql.append(" umo.codigo AS codigousuariomovel,\n ");
            sql.append(" umo.nome AS nomeUsuario,\n ");
            sql.append(" umo.username as nomeUsuarioMovel, \n ");
            sql.append(" string_agg(pes.fotokey, cli.fotokeyapp) AS urlFoto,\n ");
            sql.append(" cli.matricula,\n ");
            sql.append(" umo.username AS userNameUsuario,\n ");
            sql.append(" cli.fotokeyapp\n ");
            sql.append(" FROM usuario umo\n ");
            sql.append(" full join professorsintetico col on umo.professor_codigo = col.codigo\n ");
            sql.append(" full join clientesintetico cli on umo.cliente_codigo = cli.codigo\n ");
            sql.append(" left join pessoa pes on pes.codigo = cli.pessoa_codigo or pes.codigo = col.pessoa_codigo\n ");
            sql.append(" left join telefone tel on tel.pessoa_codigo = pes.codigo\n ");
            sql.append(" left join email ema ON pes.codigo = ema.pessoa_codigo\n ");
            sql.append(" WHERE (UPPER(TRIM(ema.email)) = UPPER(TRIM('").append(email).append("'))\n ");
            sql.append(" OR UPPER(TRIM(umo.email)) = UPPER(TRIM('").append(email).append("'))\n ");
            sql.append(" OR UPPER(TRIM(umo.username)) = UPPER(TRIM('").append(email).append("'))\n ");
            sql.append(" OR UPPER(TRIM(umo.nome)) = UPPER(TRIM('").append(email).append("')))\n ");
            sql.append(" AND (cli.codigo IS NOT NULL OR col.ativo = true) ");
            sql.append(" GROUP BY cli.codigo, col.codigo, pes.nome, ema.email, umo.codigo, umo.nome, pes.fotokey, cli.matricula,\n ");
            sql.append(" umo.nome, umo.username, umo.codigo");

            try (ResultSet rs = usuarioDao.createStatement(ctx, sql.toString())) {
                while (rs.next()) {
                    UsuarioAppBasicoJSON usuarioJson = new UsuarioAppBasicoJSON();
                    usuarioJson.setCodigocolaborador(rs.getInt("codigoColaborador"));
                    usuarioJson.setCodigocliente(rs.getInt("codigocliente"));
                    usuarioJson.setCodigousuariomovel(rs.getInt("codigousuariomovel"));
                    usuarioJson.setNome(rs.getString("nome"));
                    usuarioJson.setEmail(rs.getString("email"));
                    usuarioJson.setCelular(rs.getString("celular"));
                    usuarioJson.setNomeusuariomovel(rs.getString("nomeUsuarioMovel"));
                    usuarioJson.setNomeusuario(rs.getString("nomeUsuario"));
                    usuarioJson.setMatricula(rs.getString("matricula"));
                    usuarioJson.setUrlfoto(rs.getString("urlFoto"));
                    usuarioJson.setCodigousuariotreino(rs.getInt("codigousuariomovel"));
                    usuarioJSONs.add(usuarioJson);
                }
            }
        } catch (Exception ex) {
            throw new ServiceException(UsuarioExcecoes.ERRO_BUSCAR_USUARIO);
        }
        if (usuarioJSONs.isEmpty()) {
            throw new ServiceException(UsuarioExcecoes.ERRO_USUARIO_NAO_ENCONTRADO);
        }
        return usuarioJSONs;
    }

    @Override
    public String carregaFotoPessoaZW(Integer matricula, String ctx) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT p.fotokey FROM pessoa p ");
        sql.append("JOIN cliente c ON c.pessoa = p.codigo ");
        sql.append("WHERE c.codigomatricula = ").append(matricula);

        try (Connection con = conexaoZWService.conexaoZw(ctx)) {
            try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql.toString(), con)) {
                if (rs.next()) {
                    return rs.getString("fotokey");
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return null;
    }

    @Override
    public Usuario consultaPorId(String chave, Integer codUsuario) throws Exception {
        return getUsuarioDao().findById(chave, codUsuario);
    }

    @Override
    public void consultarUsernameEEmailZW(String ctx, UsuarioJSON uJSON) throws Exception {
        String sql = "SELECT u.username, ue.email FROM usuario U JOIN usuarioemail ue ON u.codigo = ue.usuario where U.codigo = " + uJSON.getCodUsuarioZW();
        try (Connection con = conexaoZWService.conexaoZw(ctx)) {
            try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql, con)) {
                if (rs.next()) {
                    uJSON.setUsername(rs.getString("username"));
                    uJSON.setEmail(rs.getString("email"));
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public List<UsuarioDependenteDTO> obterDependentes(String ctx, Usuario u) {
        String sql = "select c.codigo from cliente c join pessoa p on p.codigo = c.pessoa " +
                "where pessoaresponsavel = (SELECT pessoa FROM cliente WHERE codigo = " + u.getCliente().getCodigoCliente() + ")";
        if(!UteisValidacao.emptyString(u.getEmail())) {
            sql += " or p.emailpai = '" + u.getEmail() + "' or p.emailmae = '" + u.getEmail() + "'";
        }
        if(!UteisValidacao.emptyString(u.getCliente().getCPF())) {
            sql += " or p.cpfpai = '" + u.getCliente().getCPF() + "' or p.cpfmae = '" + u.getCliente().getCPF() + "'";
        }
        if(!UteisValidacao.emptyString(u.getUserName())) {
            sql += " or p.emailpai = '" + u.getUserName() + "' or p.emailmae = '" + u.getUserName() + "'";
        }
        List<UsuarioDependenteDTO> dependentes = new ArrayList<>();
        try (Connection con = conexaoZWService.conexaoZw(ctx)) {
            try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql, con)) {
                while (rs.next()) {
                    ClienteSintetico clienteSintetico = clienteSinteticoService.obterPorCodigoCliente(ctx, rs.getInt("codigo"));
                    if (clienteSintetico != null && !UteisValidacao.emptyList(clienteSintetico.getUsuarios())) {
                        UsuarioDependenteDTO dependente = new UsuarioDependenteDTO();

                        for (Usuario usuario : clienteSintetico.getUsuarios()) {
                            dependente.setCodigoUsuarioTreino(usuario.getCodigo());
                            dependente.setFoto(usuario.getFotoKeyApp());
                            dependente.setNome(clienteSintetico.getNome());
                            dependente.setUsername(usuario.getUserName());
                            dependentes.add(dependente);
                        }
                    }
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        return dependentes;
    }

    @Override
    public Usuario consultarPorCodigoColaborador(String ctx, Integer codigoColaborador) throws ServiceException {
        return getUsuarioByCodigoColaborador(ctx, codigoColaborador);
    }

    @Override
    public String consultaCPFClienteZW(Integer matricula, String ctx) {
        String sql = "SELECT p.cfp FROM pessoa p " +
                "JOIN cliente c ON c.pessoa = p.codigo " +
                "WHERE c.codigomatricula = " + matricula;

        try (Connection con = conexaoZWService.conexaoZw(ctx)) {
            try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql, con)) {
                if (rs.next()) {
                    return rs.getString("cfp");
                }
            }
        } catch (Exception e) {
            Uteis.logar(e, UsuarioServiceImpl.class);
        }
        return null;
    }

    @Override
    public String consultaCPFColaboradorZW(String ctx, Integer codigoColaborador) {
        String sql = "SELECT p.cfp FROM pessoa p " +
                "JOIN colaborador c ON c.pessoa = p.codigo " +
                "WHERE c.codigo = " + codigoColaborador;

        try (Connection con = conexaoZWService.conexaoZw(ctx)) {
            try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql, con)) {
                if (rs.next()) {
                    return rs.getString("cfp");
                }
            }
        } catch (Exception e) {
            Uteis.logar(e, UsuarioServiceImpl.class);
        }
        return null;
    }

    @Override
    public String obterUsernameUsuarioZW(Integer usuarioZW, String chave) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT username FROM usuario WHERE codigo = ").append(usuarioZW);

        try (Connection con = conexaoZWService.conexaoZw(chave)) {
            try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql.toString(), con)) {
                if (rs.next()) {
                    return rs.getString("username");
                }
            }
        }
        return null;
    }

    @Override
    public Integer consultarUsuarioMovelPorEmail(String ctx, String email, Boolean cliente) throws Exception {
        Integer codigo = getCodigoAPartirEmail(ctx, email, cliente);
        if (codigo != null) return codigo;
        codigo = getCodigoAPartirUsuarioMovel(ctx, email, cliente);
        if (codigo != null) return codigo;
        codigo = getCodigoAPartirUsuario(ctx, email, cliente);
        if (codigo != null) return codigo;
        codigo = getCodigoAPartirUsuarioEmail(ctx, email, cliente) ;

        return codigo;
    }

    private Integer getCodigoAPartirEmail(String ctx, String email, Boolean cliente) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT c.codigo FROM email e");
        sql.append(" JOIN pessoa p ON e.pessoa = p.codigo");
        if (cliente) {
            sql.append(" JOIN cliente c ON c.pessoa = p.codigo");
        } else {
            sql.append(" JOIN colaborador c ON p.codigo = c.pessoa");
        }
        sql.append(" WHERE e.email = '").append(email).append("'");

        try (Connection con = conexaoZWService.conexaoZw(ctx)) {
            try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql.toString(), con)) {
                if (rs.next()) {
                    return rs.getInt("codigo");
                }
            }
        }
        return null;
    }

    private Integer getCodigoAPartirUsuarioMovel(String ctx, String email, Boolean cliente) throws Exception {
        StringBuilder sql;
        sql = new StringBuilder();
        sql.append("SELECT c.codigo FROM usuariomovel u");
        if (cliente) {
            sql.append(" JOIN cliente c ON u.cliente = c.codigo");
        } else {
            sql.append(" JOIN colaborador c ON u.colaborador = c.codigo");
        }
        sql.append(" WHERE u.nome = '").append(email).append("'");

        try (Connection con = conexaoZWService.conexaoZw(ctx)) {
            try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql.toString(), con)) {
                if (rs.next()) {
                    return rs.getInt("codigo");
                }
            }
        }
        return null;
    }

    private Integer getCodigoAPartirUsuario(String ctx, String email, Boolean cliente) throws Exception {
        StringBuilder sql;
        sql = new StringBuilder();
        sql.append("SELECT c.codigo FROM usuario u");
        if (cliente) {
            sql.append(" JOIN cliente c ON u.cliente = c.codigo");
        } else {
            sql.append(" JOIN colaborador c ON u.colaborador = c.codigo");
        }
        sql.append(" WHERE u.username = '").append(email).append("'");

        try (Connection con = conexaoZWService.conexaoZw(ctx)) {
            try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql.toString(), con)) {
                if (rs.next()) {
                    return rs.getInt("codigo");
                }
            }
        }
        return null;
    }

    private Integer getCodigoAPartirUsuarioEmail(String ctx, String email, Boolean cliente) throws Exception {
        StringBuilder sql;
        sql = new StringBuilder();
        sql.append("SELECT c.codigo FROM usuarioemail ue");
        sql.append(" JOIN usuario u ON ue.usuario = u.codigo");
        if (cliente) {
            sql.append(" JOIN cliente c ON u.cliente = c.codigo");
        } else {
            sql.append(" JOIN colaborador c ON u.colaborador = c.codigo");
        }
        sql.append(" WHERE upper(ue.email) = upper('").append(email).append("')");
        sql.append(" AND ue.verificado = true");

        try (Connection con = conexaoZWService.conexaoZw(ctx)) {
            try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql.toString(), con)) {
                if (rs.next()) {
                    return rs.getInt("codigo");
                }
            }
        }
        return null;
    }


    @Override
    public void alterarFotoCliente(String key, String urlFoto, Integer versaoFotoApp, Integer codUsuario, String matricula, Boolean atualizaFotoZW) throws Exception {
        try {
            getUsuarioDao().getCurrentSession(key).clear();
            getUsuarioDao().executeNativeSQL(key, "UPDATE usuario \n" +
                    "set fotokeyapp = '" + urlFoto + "', versaofotoapp = " + versaoFotoApp + " WHERE codigo = " + codUsuario);

            getUsuarioDao().executeNativeSQL(key, "UPDATE clientesintetico \n" +
                    "set fotokeyapp = '" + urlFoto + "' WHERE codigo = (SELECT cliente_codigo FROM usuario WHERE codigo = " + codUsuario + ")");

            if (atualizaFotoZW) {
                atualizaPessoaClienteZW(key, urlFoto, matricula);
            }


        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }

    @Override
    public String urlFotoAppTreino(String ctx, Integer codUsuarioZW) throws Exception {
        getUsuarioDao().getCurrentSession(ctx).clear();
        Map<String, String> fotoAppVersao = getUsuarioDao().consultaVersaoFotoAppColaborador(ctx, codUsuarioZW);
        if (fotoAppVersao != null) {
            return fotoAppVersao.get("fotokeyapp");
        }
        return null;
    }

    private void atualizaPessoaClienteZW(String key, String urlFoto, String matricula) throws SQLException {
        try (Connection conZW = conexaoZWService.conexaoZw(key)) {
            StringBuilder sql = new StringBuilder();
            sql.append(" UPDATE pessoa ");
            sql.append(" SET fotokey = '").append(urlFoto).append("'");
            sql.append(" WHERE codigo = (SELECT pessoa FROM cliente c WHERE c.codigomatricula = ").append(matricula).append(")");

            ConexaoZWServiceImpl.executarConsulta(sql.toString(), conZW);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void alterarTipoTw(String ctx, Integer usuarioZW, Integer tipoTw) throws Exception{
        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
            StringBuilder sql = new StringBuilder();
            sql.append("UPDATE usuario set tipotw = ").append(tipoTw);
            sql.append(" WHERE codigo = ").append(usuarioZW);

            ConexaoZWServiceImpl.executarConsulta(sql.toString(), conZW);

        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public void criarUsuarioTreinoIA(String ctx) throws Exception {
        String treinoIaName = "Treino por IA";
        String treinoIaUsername = "TreinoIA";
        String treinoIaEmail = "<EMAIL>";

        List<Empresa> empresasTW = empresaService.obterTodos(ctx);
        Optional<Empresa> empresaOpt = empresasTW.stream()
                .filter(e -> e.getCodZW() != null)
                .min(Comparator.comparing(Empresa::getCodZW));
        if (!empresaOpt.isPresent()) {
            return;
        }

        Empresa empresaComMenorCodigo = empresaOpt.get();
        Integer codZWMenor = empresaComMenorCodigo.getCodZW();
        Integer codigoUsuario = 0;

        List<Usuario> usuariosZWExistentes = consultarUsuarioZillyon(ctx, treinoIaUsername);
        if (!usuariosZWExistentes.isEmpty()) {
            System.out.println("Usuário ZW já existe no banco.");
            List<PessoaVO> pessoasZWExistentes = consultarPessoaZillyon(ctx, treinoIaName);
            if (!pessoasZWExistentes.isEmpty()) {
                Integer pessoaCodigoZW = pessoasZWExistentes.get(0).getCodigo();
                for (Empresa empresaTW : empresasTW) {
                    if (empresaTW.getCodZW() == null) {
                        continue;
                    }
                    Integer codZW = empresaTW.getCodZW();
                    List<ColaboradorVO> colaboradoresZWExistentes = consultarColaboradorZillyon(ctx, pessoaCodigoZW, codZW);
                    if (!colaboradoresZWExistentes.isEmpty()) {
                        ColaboradorVO colaboradorExistente = colaboradoresZWExistentes.get(0);
                        if (colaboradorExistente.getCodacesso() == null || colaboradorExistente.getCodacesso().isEmpty()) {
                            atualizarCodigoAcessoColaborador(ctx, colaboradorExistente.getCodigo(), pessoaCodigoZW, codZW);
                        }
                    }
                }
            }
            return;
        } else {
            Usuario usuarioZW = new Usuario();
            usuarioZW.setNome(treinoIaName);
            usuarioZW.setUserName(treinoIaUsername);
            usuarioZW.setEmpresaZW(codZWMenor);
            inserirUsuarioZW(ctx, usuarioZW);
            usuariosZWExistentes = consultarUsuarioZillyon(ctx, treinoIaUsername);
            codigoUsuario = usuariosZWExistentes.get(0).getCodigo();
        }

        List<PessoaVO> pessoasZWExistentes = consultarPessoaZillyon(ctx, treinoIaName);
        Integer pessoaCodigoZW;
        if (!pessoasZWExistentes.isEmpty()) {
            pessoaCodigoZW = pessoasZWExistentes.get(0).getCodigo();
        } else {
            PessoaVO pessoaZW = new PessoaVO();
            pessoaZW.setNome(treinoIaName);
            inserirPessoaZW(ctx, pessoaZW);
            pessoaCodigoZW = consultarPessoaZillyon(ctx, treinoIaName).get(0).getCodigo();
        }

        Pessoa pessoaTW = pessoaService.obterPessoaPorNome(ctx, treinoIaName);
        if (pessoaTW == null) {
            pessoaTW = new Pessoa();
            pessoaTW.setNome(treinoIaName);
            pessoaTW = pessoaService.inserirPessoa(ctx, pessoaTW);
        }

        Perfil perfilCoordenador = perfilDao.insertOrGetObjectForName(ctx, Perfil.NOME_PERFIL_COORDENADOR);

        for (Empresa empresaTW : empresasTW) {
            if (empresaTW.getCodZW() == null) {
                continue;
            }
            Integer codZW = empresaTW.getCodZW();
            inserirUsuarioPerfilAcessoZW(ctx, codZW, codigoUsuario, 1);

            List<ColaboradorVO> colaboradoresZWExistentes = consultarColaboradorZillyon(ctx, pessoaCodigoZW, codZW);
            if (colaboradoresZWExistentes.isEmpty()) {
                inserirColaboradorZW(ctx, pessoaCodigoZW, codZW);
                colaboradoresZWExistentes = consultarColaboradorZillyon(ctx, pessoaCodigoZW, codZW);
                atualizarCodigoAcessoColaborador(ctx, colaboradoresZWExistentes.get(0).getCodigo(), pessoaCodigoZW, codZW);
                inserirTipoColaboradorZW(ctx, colaboradoresZWExistentes.get(0).getCodigo());
            } else {
                ColaboradorVO colaboradorExistente = colaboradoresZWExistentes.get(0);
                if (colaboradorExistente.getCodacesso() == null || colaboradorExistente.getCodacesso().isEmpty()) {
                    atualizarCodigoAcessoColaborador(ctx, colaboradorExistente.getCodigo(), pessoaCodigoZW, codZW);
                }
            }

            Integer codigoColaboradorZW = consultarCodigoColaboradorZillyon(ctx, pessoaCodigoZW, codZW);
            ProfessorSintetico professorTW = professorSinteticoService.consultarPorCodigoColaborador(ctx, codigoColaboradorZW);
            if (professorTW == null) {
                professorTW = new ProfessorSintetico();
                professorTW.setProfessorTW(true);
                professorTW.setAtivo(true);
                professorTW.setNome(treinoIaName);
                professorTW.setEmail(treinoIaEmail);
                professorTW.setCodigoColaborador(codigoColaboradorZW);
                professorTW.setCodigoPessoa(pessoaCodigoZW);
                professorTW.setPessoa(pessoaTW);
                professorTW.setEmpresa(empresaTW);
                professorTW = professorSinteticoService.inserir(ctx, professorTW);
            }

            Usuario usuarioTWExistente = consultarUsuarioPorUserNameEEmpresa(ctx, treinoIaUsername, codZW);
            if (usuarioTWExistente == null) {
                Usuario usuarioTW = new Usuario();
                usuarioTW.setNome(treinoIaName);
                usuarioTW.setUserName(treinoIaUsername);
                usuarioTW.setEmpresaZW(codZW);
                usuarioTW.setProfessor(professorTW);
                usuarioTW.setTipo(TipoUsuarioEnum.COORDENADOR);
                usuarioTW.setUsuarioZW(consultarUsuarioZillyon(ctx, treinoIaUsername).get(0).getCodigo());
                usuarioTW.setPerfil(perfilCoordenador);
                inserir(ctx, usuarioTW);
            }
        }

        Integer colaboradorCodigoMenorEmpresa = consultarCodigoColaboradorZillyon(ctx, pessoaCodigoZW, codZWMenor);
        inserirDadosUsuarioZW(ctx, codigoUsuario, colaboradorCodigoMenorEmpresa, perfilCoordenador.getCodigo());

        UsuarioEmail usuarioEmailExistente = consultarUsuarioEmailZW(ctx, treinoIaEmail);
        if (usuarioEmailExistente != null && treinoIaEmail.equals(usuarioEmailExistente.getEmail())) {
            System.out.println("E-mail já está associado ao colaborador.");
        } else {
            inserirUsuarioEmailZW(ctx, codigoUsuario, treinoIaEmail, true);
        }

        incluirLog(ctx,
                consultarPorUserName(ctx, treinoIaUsername).getCodigo().toString(),
                "",
                "",
                "",
                "INCLUSÃO",
                "INCLUSÃO DE USUÁRIO - TREINO IA",
                EntidadeLogEnum.PERFIL, treinoIaUsername,
                sessaoService, logDao, null, null);
    }


    public List<Usuario> consultarUsuarioZillyon(String ctx, String username) throws Exception {
        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
            List<Usuario> usuarios;
            try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(String.format("SELECT * FROM USUARIO WHERE username LIKE '%s'", username), conZW)) {
                usuarios = new ArrayList<>();
                while (rs.next()) {
                    Usuario usuario = new Usuario();
                    usuario.setCodigo(rs.getInt("codigo"));
                    usuario.setNome(rs.getString("nome"));
                    usuario.setUserName(rs.getString("username"));
                    usuarios.add(usuario);
                }
            }
            return usuarios;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e.getMessage());
        }
    }

    public List<PessoaVO> consultarPessoaZillyon(String ctx, String nome) throws Exception {
        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
            List<PessoaVO> pessoas;
            try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(String.format("SELECT * FROM pessoa WHERE nome ILIKE '%s'", nome), conZW)) {
                pessoas = new ArrayList<>();
                while (rs.next()) {
                    PessoaVO pessoa = new PessoaVO();
                    pessoa.setCodigo(rs.getInt("codigo"));
                    pessoa.setNome(rs.getString("nome"));
                    pessoas.add(pessoa);
                }
            }
            return pessoas;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e.getMessage());
        }
    }

    public List<ColaboradorVO> consultarColaboradorZillyon(String ctx, Integer pessoaCodigo, Integer empresa) throws Exception {
        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
            List<ColaboradorVO> colaboradores;
            try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(String.format("SELECT * FROM colaborador WHERE pessoa = '%s' AND empresa = '%s'", pessoaCodigo, empresa), conZW)) {
                colaboradores = new ArrayList<>();
                while (rs.next()) {
                    ColaboradorVO colaborador = new ColaboradorVO();
                    colaborador.setCodigo(rs.getInt("codigo"));
                    colaborador.setCodacesso(rs.getString("codacesso"));
                    colaboradores.add(colaborador);
                }
            }
            return colaboradores;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e.getMessage());
        }
    }

    public Integer consultarCodigoColaboradorZillyon(String ctx, Integer pessoaCodigo, Integer empresa) throws Exception {
        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
            try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(
                    String.format("SELECT codigo FROM colaborador WHERE pessoa = '%s' AND empresa = '%s'", pessoaCodigo, empresa), conZW)) {
                if (rs.next()) {
                    return rs.getInt("codigo");
                }
                return null;
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException("Erro ao consultar o código do colaborador no Zillyon.", e);
        }
    }

    @Override
    public Usuario consultarUsuarioPorUserNameEEmpresa(final String ctx, final String userName, final Integer codZW) throws ServiceException {
        try {
            String query = "SELECT obj FROM Usuario obj WHERE obj.userName = :userName AND obj.empresaZW = :codZW";
            HashMap<String, Object> params = new HashMap<>();
            params.put("userName", userName);
            params.put("codZW", codZW);

            List<Usuario> usuarios = usuarioDao.findByParam(ctx, query, params);
            return usuarios.isEmpty() ? null : usuarios.get(0);
        } catch (Exception ex) {
            throw new ServiceException("Erro ao buscar usuário por nome de usuário e empresa ZW: " + userName + ", codZW: " + codZW, ex);
        }
    }

    public UsuarioEmail consultarUsuarioEmailZW(String ctx, String email) throws Exception {
        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
            try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(
                    String.format("SELECT * FROM USUARIOEMAIL WHERE email = '%s'", email), conZW)) {
                if (rs.next()) {
                    UsuarioEmail usuarioEmail = new UsuarioEmail();
                    usuarioEmail.setCodigo(rs.getInt("codigo"));
                    usuarioEmail.setEmail(rs.getString("email"));
                    return usuarioEmail;
                }
                return null;
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException("Erro ao consultar o usuário por email no Zillyon.", e);
        }
    }


    public void inserirPessoaZW(String ctx, PessoaVO pessoa) throws Exception {
        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
            ConexaoZWServiceImpl.executarConsulta(
                    String.format(
                            "INSERT INTO pessoa (nome) VALUES ('%s')", pessoa.getNome()), conZW);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e.getMessage());
        }
    }

    public void inserirColaboradorZW(String ctx, Integer pessoaCodigo, Integer empresaId) throws Exception {
        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
            ConexaoZWServiceImpl.executarConsulta(
                    String.format(
                            "INSERT INTO colaborador (pessoa, empresa, situacao, diaVencimento, funcionario) VALUES ('%s', '%s', '%s', '%s', '%s')", pessoaCodigo, empresaId, "AT", 0, true), conZW);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e.getMessage());
        }
    }

    public void inserirTipoColaboradorZW(String ctx, Integer colaboradorCodigo) throws Exception {
        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
            ConexaoZWServiceImpl.executarConsulta(
                    String.format(
                            "INSERT INTO tipocolaborador (colaborador, descricao) VALUES ('%s', '%s')", colaboradorCodigo, "TW"), conZW);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e.getMessage());
        }
    }

    public void inserirUsuarioZW(String ctx, Usuario usuario) throws Exception {
        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
            ConexaoZWServiceImpl.executarConsulta(
                    String.format(
                            "INSERT INTO usuario (nome, username, senha) VALUES ('%s', '%s', '%s')", usuario.getNome(), usuario.getUserName(), "JSP)34X&*"), conZW);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e.getMessage());
        }
    }

    public void inserirUsuarioPerfilAcessoZW(String ctx, Integer empresa, Integer usuario, Integer perfilacesso) throws Exception {
        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
            ConexaoZWServiceImpl.executarConsulta(
                    String.format(
                            "INSERT INTO usuarioperfilacesso (empresa, usuario, perfilacesso) VALUES ('%s', '%s', '%s')", empresa, usuario, perfilacesso), conZW);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e.getMessage());
        }
    }

    public void inserirDadosUsuarioZW(String ctx, Integer usuarioCodigo, Integer colaboradorCodigo, Integer perfilCoordenador) throws Exception {
        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
            ConexaoZWServiceImpl.executarConsulta(
                    String.format(
                            "UPDATE usuario SET colaborador = '%s', perfiltw = '%s' WHERE codigo = '%s'", colaboradorCodigo, perfilCoordenador, usuarioCodigo), conZW);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e.getMessage());
        }
    }

    public void inserirUsuarioEmailZW(String ctx, Integer usuarioCodigo, String email, Boolean verificado) throws Exception {
        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
            ConexaoZWServiceImpl.executarConsulta(
                    String.format(
                            "INSERT INTO usuarioemail (usuario, email, verificado) VALUES ('%s', '%s', '%s')", usuarioCodigo, email, verificado), conZW);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e.getMessage());
        }
    }

    public void atualizarCodigoAcessoColaborador(String ctx, Integer codigoColaborador, Integer pessoaCodigo, Integer codZW) throws Exception {
        DecimalFormat idFormat = new DecimalFormat("00000");
        int attempt = 0;
        String codigoAcessoCandidate;
        boolean existe;

        do {
            int candidateValue = codigoColaborador + attempt;
            String numero = "5" + idFormat.format(candidateValue) + "00" + "0";
            Integer dv = Uteis.gerarDV(numero, 0);
            codigoAcessoCandidate = numero + dv;

            existe = codigoAcessoExiste(ctx, pessoaCodigo, codZW, codigoAcessoCandidate);
            attempt++;
        } while (existe);

        String query = String.format("UPDATE colaborador SET codacesso = '%s' WHERE codigo = '%s'",
                codigoAcessoCandidate, codigoColaborador);
        try (Connection con = conexaoZWService.conexaoZw(ctx)) {
            ConexaoZWServiceImpl.executarConsulta(query, con);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e.getMessage());
        }
    }

    public boolean codigoAcessoExiste(String ctx, Integer pessoaCodigo, Integer codZW, String codigoAcessoCandidate) throws Exception {
        List<ColaboradorVO> colaboradores = consultarColaboradorZillyon(ctx, pessoaCodigo, codZW);
        for (ColaboradorVO colaborador : colaboradores) {
            if(codigoAcessoCandidate.equals(colaborador.getCodacesso())) {
                return true;
            }
        }
        return false;
    }

}


