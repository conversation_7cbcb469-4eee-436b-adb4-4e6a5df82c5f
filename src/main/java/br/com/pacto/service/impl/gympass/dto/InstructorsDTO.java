package br.com.pacto.service.impl.gympass.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.json.JSONObject;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 22/03/2020
 */
@ApiModel(description = "Informações do instrutor responsável pela aula")
public class InstructorsDTO {

    @ApiModelProperty(value = "Nome do instrutor", example = "Maria Silva")
    private String name;

    @ApiModelProperty(value = "Indica se é um instrutor substituto", example = "false")
    private boolean substitute;

    public InstructorsDTO() {
    }

    public InstructorsDTO(JSONObject json) {
        this.name = json.optString("name");
        this.substitute = json.optBoolean("substitute");
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public boolean isSubstitute() {
        return substitute;
    }

    public void setSubstitute(boolean substitute) {
        this.substitute = substitute;
    }
}
