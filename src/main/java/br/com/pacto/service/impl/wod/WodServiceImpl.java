package br.com.pacto.service.impl.wod;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ExecuteRequestHttpService;
import br.com.pacto.bean.aparelho.Aparelho;
import br.com.pacto.bean.aparelho.AparelhoWod;
import br.com.pacto.bean.atividade.Atividade;
import br.com.pacto.bean.atividade.AtividadeWod;
import br.com.pacto.bean.benchmark.Benchmark;
import br.com.pacto.bean.configuracoes.ConfiguracaoSistema;
import br.com.pacto.bean.configuracoes.ConfiguracoesEnum;
import br.com.pacto.bean.crossfit.EventoCrossfit;
import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.nivelwod.NivelWod;
import br.com.pacto.bean.tipowod.TipoWod;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.bean.wod.ComentarioWod;
import br.com.pacto.bean.wod.FiltroWodJSON;
import br.com.pacto.bean.wod.GerarScore;
import br.com.pacto.bean.wod.NivelCrossfitEnum;
import br.com.pacto.bean.wod.ScoreTreino;
import br.com.pacto.bean.wod.Wod;
import br.com.pacto.bean.wod.WodAvaliacao;
import br.com.pacto.bean.wod.WodResponseTO;
import br.com.pacto.bean.wod.WodTO;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.controller.json.crossfit.AparelhoWodJSON;
import br.com.pacto.controller.json.crossfit.AtividadeWodJSON;
import br.com.pacto.controller.json.crossfit.AvaliacaoWodDTO;
import br.com.pacto.controller.json.crossfit.RankingJSON;
import br.com.pacto.controller.json.log.EntidadeLogEnum;
import br.com.pacto.controller.json.notificacao.PushMobileRunnable;
import br.com.pacto.dao.intf.aparelho.AparelhoDao;
import br.com.pacto.dao.intf.aparelho.AparelhoWodDao;
import br.com.pacto.dao.intf.atividade.AtividadeDao;
import br.com.pacto.dao.intf.atividade.AtividadeWodDao;
import br.com.pacto.dao.intf.benchmark.BenchmarkDao;
import br.com.pacto.dao.intf.crossfit.EquipeEventoDao;
import br.com.pacto.dao.intf.log.LogDao;
import br.com.pacto.dao.intf.tipowod.TipoWodDao;
import br.com.pacto.dao.intf.wod.ComentarioWodDao;
import br.com.pacto.dao.intf.wod.ScoreTreinoDao;
import br.com.pacto.dao.intf.wod.WodAvaliacaoDao;
import br.com.pacto.dao.intf.wod.WodDao;
import br.com.pacto.enumerador.crossfit.TipoWodEnum;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.JSONMapper;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.exception.ValidacaoException;
import br.com.pacto.service.impl.conexao.ConexaoZWServiceImpl;
import br.com.pacto.service.intf.configuracoes.ConfiguracaoSistemaService;
import br.com.pacto.service.impl.empresa.EmpresaServiceImpl;
import br.com.pacto.service.impl.notificacao.excecao.WodExcecoes;
import br.com.pacto.service.intf.Validacao;
import br.com.pacto.service.intf.cliente.ClienteSinteticoService;
import br.com.pacto.service.intf.conexao.ConexaoZWService;
import br.com.pacto.service.intf.empresa.EmpresaService;
import br.com.pacto.service.intf.usuario.FotoService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.service.intf.wod.WodService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.UtilS3Base64Img;
import br.com.pacto.util.ViewUtils;
import br.com.pacto.util.impl.UtilReflection;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.HttpClient;
import org.apache.http.client.ResponseHandler;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.BasicResponseHandler;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.ui.ModelMap;
import org.springframework.web.context.ServletContextAware;
import servicos.integracao.midias.MidiaService;
import servicos.integracao.midias.commons.MidiaEntidadeEnum;

import javax.persistence.PersistenceException;
import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.InputStreamReader;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import static br.com.pacto.objeto.Uteis.incluirLog;

/**
 * Created by Rafael on 20/07/2016.
 */
@Service
public class WodServiceImpl implements WodService, ServletContextAware {

    @Autowired
    private ViewUtils viewUtils;
    @Autowired
    private Validacao validacao;
    @Autowired
    private WodDao wodDao;
    @Autowired
    private ClienteSinteticoService css;
    @Autowired
    private BenchmarkDao benchDao;
    @Autowired
    private ScoreTreinoDao scoreDao;
    @Autowired
    private EquipeEventoDao equipeEventoDao;
    @Autowired
    private AtividadeWodDao atividadeWodDao;
    @Autowired
    private AparelhoWodDao aparelhoWodDao;
    @Autowired
    private ComentarioWodDao comentarioWodDao;
    @Autowired
    private UsuarioService usuarioService;
    @Autowired
    private FotoService fotoService;
    @Autowired
    private SessaoService sessaoService;
    @Autowired
    private AtividadeDao atividadeDao;
    @Autowired
    private AparelhoDao aparelhoDao;
    @Autowired
    private TipoWodDao tipoWodDao;
    @Autowired
    private ConfiguracaoSistemaService configService;
    @Autowired
    private ConexaoZWServiceImpl conexaoZWService;
    @Autowired
    private EmpresaServiceImpl empresaService;
    @Autowired
    LogDao logDao;
    @Autowired
    WodAvaliacaoDao wodAvaliacaoDao;
    private ServletContext context;

    public ViewUtils getViewUtils() {
        return viewUtils;
    }

    public void setViewUtils(ViewUtils viewUtils) {
        this.viewUtils = viewUtils;
    }

    public Validacao getValidacao() {
        return validacao;
    }

    public void setValidacao(Validacao validacao) {
        this.validacao = validacao;
    }

    public WodDao getWodDao() {
        return wodDao;
    }

    public void setWodDao(WodDao wodDao) {
        this.wodDao = wodDao;
    }

    public Wod alterar(final String ctx, Wod object) throws ValidacaoException, ServiceException {
        try {
            validarDados(ctx, object);
            tratarLazilyExceptionWod(ctx, object);

            Wod wod = getWodDao().update(ctx, object);
            wod = persistirAtividadesWod(ctx, wod);
            wod = persistirAparelhosWod(ctx, wod);
            return wod;
        } catch (ValidacaoException ex) {
            throw ex;
        }catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public void excluir(final String ctx, Wod object) throws ServiceException {
        try {
            getWodDao().delete(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    private void validarDados(String ctx, Wod wod) throws Exception {
        validarWod(ctx, wod);

        if(UteisValidacao.emptyString(wod.getNome())){
            throw new Exception(getViewUtils().getMensagem("validacao.nome"));
        }
        if(wod.getDia() == null){
            throw new Exception(getViewUtils().getMensagem("validacao.diawod"));
    }
        if(wod.getTipoWodTabela() == null){
            throw new Exception(getViewUtils().getMensagem("validacao.tipowod"));
        }
        /*if (verificaExisteWodMesmoDia(ctx, wod)) {
            throw new Exception("Já existe um Wod cadastrado para o dia: " + Uteis.getData(wod.getDia()));
        }*/
    }

    public Wod inserir(final String ctx, Wod object) throws ServiceException {
        try {
            validarDados(ctx, object);
            tratarLazilyExceptionWod(ctx, object);
            Wod wod = getWodDao().insert(ctx, object);
            wod = persistirAtividadesWod(ctx, wod);
            wod = persistirAparelhosWod(ctx, wod);
            return wod;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    private boolean verificaExisteWodMesmoDia(String ctx, Wod wod) {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append(" dia = '").append(Uteis.getDataJDBC(wod.getDia())).append("'\n");
            if (!UteisValidacao.emptyNumber(wod.getCodigo())) {
                sql.append(" AND codigo <> ").append(wod.getCodigo());
            }
            return getWodDao().existsWithParam(ctx, sql);
        } catch (Exception e) {
            return false;
        }
    }

    private Wod persistirAtividadesWod(final String ctx, Wod object) throws Exception {
        for (AtividadeWod atividadeWod : object.getAtividades()) {
            if (atividadeWod.getCodigo() != null) {
                atividadeWod = getAtividadeWodDao().update(ctx, atividadeWod);
            } else {
                atividadeWod = getAtividadeWodDao().insert(ctx, atividadeWod);
            }
        }
        return object;
    }

    private Wod persistirAparelhosWod(final String ctx, Wod object) throws Exception {
        for (AparelhoWod aparelhoWod : object.getAparelhos()) {
            if (aparelhoWod.getCodigo() != null) {
                aparelhoWod = getAparelhoWodDao().update(ctx, aparelhoWod);
            } else {
                aparelhoWod = getAparelhoWodDao().insert(ctx, aparelhoWod);
            }
        }
        return object;
    }

    public Wod obterObjetoPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException {
        try {
            return getWodDao().findObjectByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public Wod obterPorId(final String ctx, Integer id) throws ServiceException {
        try {
            return getWodDao().obterPorId(ctx, id);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<Wod> obterPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException {
        try {
            return getWodDao().findByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<Wod> wodsDia(final String ctx, final Date dia) throws ServiceException{
        Map<String, Object> params =  new HashMap<String, Object>();
        params.put("dia", Calendario.getDataComHoraZerada(dia));
        return obterPorParam(ctx, " SELECT obj FROM Wod obj WHERE dia = :dia and evento is null", params);
    }

    @Override
    public List<Benchmark> benchmarks(String ctx) throws Exception {
        return benchDao.findAll(ctx);
    }

    @Override
    public List<Wod> obterPorEvento(final String ctx, final EventoCrossfit game) throws ServiceException{
        Map<String, Object> params =  new HashMap<String, Object>();
        String sql = "SELECT obj FROM Wod obj where evento.codigo = :game";
        params.put("game", game.getCodigo());
        return obterPorParam(ctx, sql, params);
    }

    @Override
    public List<Wod> wods(String ctx, Date inicio, Date fim, Integer usuario, Integer wod, Integer empresaCodZw) throws ServiceException {
        Map<String, Object> params =  new HashMap<String, Object>();
        String sql = "SELECT obj FROM Wod obj ";
        if (!UteisValidacao.emptyNumber(wod)) {
            params.put("wod", wod);
            sql += " WHERE obj.codigo = :wod";
        } else {
            params.put("inicio", Calendario.getDataComHoraZerada(inicio));
            params.put("fim", Calendario.getDataComUltimaHora(fim));
            sql += " WHERE dia BETWEEN :inicio AND :fim and evento is null ";
        }
        if (!UteisValidacao.emptyNumber(empresaCodZw)) {
            params.put("empresaCodZw", empresaCodZw);
            sql += " AND obj.empresa = :empresaCodZw";
        }
        List<Wod> wods = obterPorParam(ctx, sql, params);
        if(!UteisValidacao.emptyNumber(usuario)){
            Map<Integer, RankingJSON> wodresultados = wodresultados(ctx, inicio, fim, usuario, wod);
            for(Wod w : wods){
                w.setTemResultado(wodresultados.get(w.getCodigo()) != null);
                w.setRanking(wodresultados.get(w.getCodigo()));
                if (w.getTipoWod() == null) {
                    String nomeTipoWod = w.getTipoWodTabela().getNome();
                    w.setTipoWod(TipoWodEnum.obterPorString(nomeTipoWod.toUpperCase()));
                }
            }
        }
        return wods;
    }

    @Override
    public List<Wod> consultaWodsApp(String ctx, Integer empresaCodigoZw, String inicio, String fim, Integer usuario, String matricula, Integer wodCodigo) throws Exception {
        List<Wod> listWods = new ArrayList<>();
        Empresa empresa = new Empresa();
        try {
            if (!UteisValidacao.emptyNumber(empresaCodigoZw)) {
                empresa = empresaService.obterPorIdZW(ctx, empresaCodigoZw);
            }

            Date dataInicio = null;
            Date dataFim = null;
            if (inicio != null && fim != null) {
                if (!UteisValidacao.emptyString(empresa.getTimeZoneDefault())) {
                    dataInicio = Uteis.dataHoraZeradaUTC(inicio, empresa.getTimeZoneDefault());
                    dataFim = Uteis.dataHoraZeradaUTC(fim, empresa.getTimeZoneDefault());
                } else {
                    dataInicio = Uteis.dataHoraZeradaUTC(inicio);
                    dataFim = Uteis.dataHoraZeradaUTC(fim);
                }
            }

            ConfiguracaoSistema confgPermissaoWod = configService.consultarPorTipo(ctx, ConfiguracoesEnum.PERMITIR_VISUALIZAR_WOD);
            ConfiguracaoSistema confgVerWodTodasEmpresas = configService.consultarPorTipo(ctx, ConfiguracoesEnum.HABILITAR_VER_WOD_TODAS_EMPRESAS_APP);
            boolean permissaoVisualizarWod = Boolean.parseBoolean(confgPermissaoWod.getValor());
            boolean permissaoVerWodTodasEmpresas = Boolean.parseBoolean(confgVerWodTodasEmpresas.getValor());
            boolean podeUsarCross = podeUsarCross(ctx, usuario, matricula, permissaoVisualizarWod);

            listWods = permissaoVerWodTodasEmpresas
                    ? wods(ctx, dataInicio, dataFim, null, wodCodigo, null)
                    : wods(ctx, dataInicio, dataFim, null, wodCodigo, empresaCodigoZw);
            for (Wod wod : listWods) {
                wod.setContratoCrossfit(podeUsarCross);
                wod.setTemResultadoGeral(existeResultado(ctx, wod.getCodigo()));
                for (AtividadeWod atividadeWod : wod.getAtividades()) {
                    wod.getAtividadeWodJSON().add(new AtividadeWodJSON(atividadeWod));
                }
                for (AparelhoWod aparelhoWod : wod.getAparelhos()) {
                    wod.getAparelhoWodJSON().add(new AparelhoWodJSON(aparelhoWod));
                }
            }
        } catch (ServiceException e) {
            throw new ServiceException(WodExcecoes.ERRO_BUSCAR_WODS, e);
        }

        return listWods;
    }

    @Override
    public List<ScoreTreino> resultados(String ctx, Date inicio, Date fim, Integer usuario, Integer wod) throws Exception {
        Map<String, Object> params =  new HashMap<String, Object>();
        params.put("usuario", usuario);
        if(wod == null){
            params.put("inicio", Calendario.getDataComHoraZerada(inicio));
            params.put("fim", Calendario.getDataComHoraZerada(fim));
            return scoreDao.findByParam(ctx, " SELECT obj FROM ScoreTreino obj inner join obj.nivelcrossfit n" +
                    " WHERE obj.wod.dia BETWEEN :inicio AND :fim" +
                    " AND obj.usuario.codigo = :usuario" +
                    " ORDER BY obj.lancamento", params);
        }else{
            params.put("wod", wod);
            return scoreDao.findByParam(ctx, " SELECT obj FROM ScoreTreino obj inner join obj.nivelcrossfit n" +
                    " WHERE obj.usuario.codigo = :usuario " +
                    " AND obj.wod.codigo = :wod" +
                    " ORDER BY obj.lancamento", params);
        }

    }


    private Map<Integer,RankingJSON> wodresultados(String ctx, Date inicio, Date fim, Integer usuario, Integer wod) throws ServiceException {
        try {
            Map<Integer,RankingJSON> scores = new HashMap<Integer, RankingJSON>();
            List<ScoreTreino> scoreTreinos = resultados(ctx, inicio, fim, usuario, wod);
            for(ScoreTreino sc : scoreTreinos){
                RankingJSON ranking = new RankingJSON(sc);
                if(sc.getUsuario() == null){
                    sc.setUsuario(new Usuario());
                }
                ranking.setFoto(fotoService.carregarFotoClienteNuvem(ctx, sc.getUsuario()));
                scores.put(sc.getWod().getCodigo(), ranking);
            }
            return scores;
        }catch (Exception e){
            throw new ServiceException(e);
        }

    }

    public List<RankingJSON> ranking(String ctx,Integer codigoWod, HttpServletRequest request) throws Exception {
        return ranking(ctx, codigoWod, request, null, true);
    }

    public List<RankingJSON> ranking(String ctx,Integer codigoWod, HttpServletRequest request, Integer limit) throws Exception {
        return ranking(ctx,codigoWod, request, limit, false);
    }

    public List<RankingJSON> ranking(String ctx, Integer codigoWod, HttpServletRequest request, Integer limit, boolean forcarFoto) throws Exception {
        Map<String, Object> params = new HashMap<>();
        params.put("wod", codigoWod);

        Wod wod = wodDao.obterPorId(ctx, codigoWod);
        String ordenamento = definirOrdenamento(wod);

        List<RankingJSON> rankings = new ArrayList<>();

        String hql = "SELECT obj FROM ScoreTreino obj " +
                " inner join obj.nivelcrossfit n " +
                "WHERE obj.wod.codigo = :wod " + ordenamento;

        List<ScoreTreino> scoreTreinos = UteisValidacao.emptyNumber(limit) ?
                scoreDao.findByParam(ctx, hql, params) :
                scoreDao.findByParam(ctx, hql, params, limit, 0);

        scoreDao.refresh(ctx, scoreTreinos);

        int posicao = 1;
        for (ScoreTreino sc : scoreTreinos) {
            RankingJSON ranking = new RankingJSON(sc);
            if (sc.getUsuario() == null) {
                sc.setUsuario(new Usuario());
            }
            if (forcarFoto) {
                ranking.setFoto(fotoService.carregarFotoClienteNuvem(ctx, sc.getUsuario()));
            }
            ranking.setPosicao(posicao++);
            rankings.add(ranking);
        }
        return rankings;
    }

    private String definirOrdenamento(Wod wod) {
        String ordenamento = "";
        if (wod.getTipoWodTabela() != null && wod.getTipoWodTabela().getCamposResultado() != null && !wod.getTipoWodTabela().getCamposResultado().isEmpty()) {
            String[] camposOrdenacao = wod.getTipoWodTabela().getCamposResultado().split(",");
            StringBuilder orderByBuilder = new StringBuilder(" ORDER BY");

            Set<String> criteriosAdicionados = new HashSet<>();

            for (String campo : camposOrdenacao) {
                switch (campo.trim()) {
                    case "rx":
                        orderByBuilder.append(" rx DESC,");
                        criteriosAdicionados.add("rx");
                        break;
                    case "nivelCrossfit":
                        orderByBuilder.append(" nivelCrossfit DESC,");
                        criteriosAdicionados.add("nivelCrossfit");
                        break;
                    case "peso":
                        orderByBuilder.append(" peso DESC,");
                        criteriosAdicionados.add("peso");
                        break;
                    case "tempo":
                        orderByBuilder.append(" tempo ASC,");
                        criteriosAdicionados.add("tempo");
                        break;
                    case "repeticoes":
                        orderByBuilder.append(" repeticoes DESC,");
                        criteriosAdicionados.add("repeticoes");
                        break;
                    case "rounds":
                        orderByBuilder.append(" rounds DESC,");
                        criteriosAdicionados.add("rounds");
                        break;
                    default:
                        // Ignorar campos desconhecidos
                        break;
                }
            }

            if (!criteriosAdicionados.contains("rx")) {
                orderByBuilder.append(" rx DESC,");
            }
            if (!criteriosAdicionados.contains("nivelCrossfit")) {
                orderByBuilder.append(" nivelCrossfit DESC,");
            }
            if (!criteriosAdicionados.contains("peso")) {
                orderByBuilder.append(" peso DESC,");
            }
            if (!criteriosAdicionados.contains("tempo")) {
                orderByBuilder.append(" tempo ASC,");
            }
            if (!criteriosAdicionados.contains("repeticoes")) {
                orderByBuilder.append(" repeticoes DESC,");
            }
            if (!criteriosAdicionados.contains("rounds")) {
                orderByBuilder.append(" rounds DESC,");
            }

            orderByBuilder.append(" comentario ASC");
            ordenamento = orderByBuilder.toString();

            if (ordenamento.endsWith(",")) {
                ordenamento = ordenamento.substring(0, ordenamento.length() - 1);
            }
        }
        return ordenamento;
    }



    @Override
    public ScoreTreino gravarScore(final String ctx, final Integer wod, final Integer usuario,
           final Integer benchmark, final Integer tempo, final Double peso, final Integer repeticoes,
           final Integer rounds, final Boolean rx,
           final String comentario, final NivelWod nivelWod, final Integer equipe) throws Exception {
        ScoreTreino score = new ScoreTreino();
        List<ScoreTreino> resultados = resultados(ctx, null, null, usuario, wod);
        if(resultados != null && !resultados.isEmpty()){
            score = scoreDao.findById(ctx, resultados.get(0).getCodigo());
        }
        score.setRx(rx);
        score.setNivelcrossfit(nivelWod);
        TipoWod tipoWod = null;
        if(wod != null){
            score.setWod(wodDao.findById(ctx, wod));
            tipoWod = score.getWod().getTipoWodTabela();
        }
        Usuario us = usuarioService.obterPorId(ctx, usuario);
        if (us == null) {
            us = usuarioService.obterPorId(ctx, usuario, true);
            if (us != null && us.getCliente() == null) {
                us = null;
            }
        }
        score.setUsuario(us);
        try {
            score.setEquipe(equipeEventoDao.findById(ctx, equipe));
        }catch (Exception e){
            //ignore
        }

        if(benchmark != null){
            score.setBenchmark(benchDao.findById(ctx, wod));
            tipoWod = score.getWod().getTipoWodTabela();
        }
        score.setTempo(tempo);
        score.setPeso(peso);
        score.setRepeticoes(repeticoes);
        score.setRounds(rounds);
        score.setComentario(comentario);
        score.setRx(rx);
        score.setLancamento(Calendario.hoje());
        if(score.getCodigo() == null
                || score.getCodigo() == 0){
            score = scoreDao.insert(ctx, score);
        }else{
            score = scoreDao.update(ctx, score);
        }
        if (tipoWod != null) {
            atualizarRanking(ctx, score.getWod().getCodigo(), tipoWod.getOrderBy());
        }else{
            atualizarRanking(ctx, score.getWod().getCodigo(), "");
        }
        return scoreDao.findById(ctx, score.getCodigo());

    }
    @Override
    public ScoreTreino scoreTreinoPorCodigo(final String ctx, Integer codigo){
        try {
            return scoreDao.findById(ctx, codigo);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public void gravarResultados(String ctx, ScoreTreino score) throws Exception{
        if(score.getCodigo() == null
                || score.getCodigo() == 0){
            score = scoreDao.insert(ctx, score);
        }else{
            score = scoreDao.update(ctx, score);
        }
    }

    public void atualizarRanking(final String ctx, final Integer codigoWod, String orderBY) throws Exception{
        scoreDao.executeNative(ctx, "UPDATE scoretreino s SET posicao = r.posicao FROM ( "+
                " select ROW_NUMBER() OVER("+orderBY+") as posicao, codigo from scoretreino "+
                " where wod_codigo = "+codigoWod+") as r WHERE s.codigo = r.codigo AND wod_codigo = "+codigoWod);

    }


    public List<Wod> obterPorParam(final String ctx, String query,
                                         Map<String, Object> params, int max, int index)
            throws ServiceException {
        try {
            return getWodDao().findByParam(ctx, query, params, max, index);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<Wod> obterTodos(final String ctx) throws ServiceException {
        try {
            return obterPorParam(ctx, "SELECT obj FROM Wod obj where evento is null order by nome",  new HashMap<String, Object>());
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public void preencherListaComentarios(final String ctx, Wod wod, Integer usuario, Integer limit, Integer maxResults, HttpServletRequest request) throws ServiceException {
        try {
            List<ComentarioWod> lista = new ArrayList<ComentarioWod>();

            StringBuilder sql = new StringBuilder();
            sql.append("select \n");
            sql.append("(select count(*) from comentariowodlike where comentariowod_codigo = c.codigo) as qtdLike, \n");
            sql.append("exists(select codigo from comentariowodlike where comentariowod_codigo = c.codigo and usuario_codigo = ").append(usuario).append(") as clienteDeuLike, \n");
            sql.append("c.* \n");
            sql.append("from comentariowod c \n");
            sql.append("where 1 = 1 \n");
            sql.append("and c.wod_codigo = ").append(wod.getCodigo()).append(" \n");
            sql.append("order by c.dataRegistro desc \n");
            if (!UteisValidacao.emptyNumber(limit)) {
                sql.append("limit ").append(limit).append("\n");
            }
            if (!UteisValidacao.emptyNumber(maxResults)) {
                sql.append(" offset ").append(maxResults).append("\n");
            }
            try (ResultSet rs = comentarioWodDao.createStatement(ctx, sql.toString())) {
                while (rs.next()) {
                    ComentarioWod comentarioWod = new ComentarioWod();
                    comentarioWod.setCodigo(rs.getInt("codigo"));
                    comentarioWod.setComentario(rs.getString("comentario"));
                    comentarioWod.setDataRegistro(rs.getTimestamp("dataRegistro"));
                    comentarioWod.setClienteDeuLike(rs.getBoolean("clienteDeuLike"));
                    comentarioWod.setQtdLike(rs.getInt("qtdLike"));
                    Object nivelCross = rs.getObject("nivelCrossfit");
                    if (nivelCross != null) {
                        comentarioWod.setNivelCrossfit(NivelCrossfitEnum.valueOf(Integer.valueOf(nivelCross.toString())));
                    } else {
                        comentarioWod.setNivelCrossfit(null);
                    }
                    comentarioWod.setWod(new Wod());
                    comentarioWod.getWod().setCodigo(rs.getInt("wod_codigo"));
                    Usuario usuarioObj = usuarioService.obterPorId(ctx, rs.getInt("usuario_codigo"));
                    comentarioWod.setUsuario(usuarioObj);
                    comentarioWod.setNome(usuarioObj.getSuperNome());
                    if (!UteisValidacao.emptyString(usuarioObj.getFotoKeyApp())) {
                        comentarioWod.getUsuario().setFotoKeyApp(usuarioObj.getFotoKeyApp());
                    } else {
                        if (request != null) {
                            comentarioWod.getUsuario().setFotoKeyApp(SuperControle.defineUrlFotoJSON(ctx, comentarioWod.getUsuario().getIdPessoa(), request, context));
                        }
                    }
                    if (usuarioObj.getCodigo().equals(usuario)) {
                        comentarioWod.setPodeExcluir(true);
                    } else {
                        comentarioWod.setPodeExcluir(false);
                    }
                    lista.add(comentarioWod);
                }
            }
            wod.setComentarios(lista);
        } catch (Exception ex) {
            Logger.getLogger(WodServiceImpl.class.getName()).log(Level.SEVERE, null, ex);
            throw new ServiceException(ex);
        }
    }

    public AtividadeWodDao getAtividadeWodDao() {
        return atividadeWodDao;
    }

    public void setAtividadeWodDao(AtividadeWodDao atividadeWodDao) {
        this.atividadeWodDao = atividadeWodDao;
    }

    public AparelhoWodDao getAparelhoWodDao() {
        return aparelhoWodDao;
    }

    public void setAparelhoWodDao(AparelhoWodDao aparelhoWodDao) {
        this.aparelhoWodDao = aparelhoWodDao;
    }

    @Override
    public List<AtividadeWod> obterListaAtividadesWod(String ctx, Integer codigoWod) throws ServiceException {
        try {
            return atividadeWodDao.findListByAttributes(ctx, new String[]{"wod.codigo"}, new Object[]{codigoWod}, null, 0);
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }

    }

    @Override
    public List<AparelhoWod> obterListaAparelhosWod(String ctx, Integer codigoWod) throws ServiceException {
        try {
            return aparelhoWodDao.findListByAttributes(ctx, new String[]{"wod.codigo"}, new Object[]{codigoWod}, null, 0);
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }

    }

    @Override
    public boolean existeResultado(String ctx, Integer wod) throws Exception {
        StringBuilder where = new StringBuilder();
        where.append(" wod_codigo = ").append(wod);
        return scoreDao.existsWithParam(ctx, where);
    }
    @Override
    public Set<Date> obterDiasComWodPeriodo(String contexto, Integer empresa, Long dataInicial, Long dataFinal) throws ServiceException {
        try {
            return new HashSet<Date>(getWodDao().consultarDiasWodPeriodo(contexto, empresa, dataInicial, dataFinal));
        } catch (Exception e) {
            Uteis.logar(e, WodServiceImpl.class);
            throw new ServiceException(e);
        }
    }

    @Override
    public String obterExerciciosSite(Date data) throws ServiceException {

        if (Calendario.maior(data, new Date())) {
            throw new ServiceException(WodExcecoes.ERRO_DATA_SUPERIOR_ATUAL);
        }
        Map<String, String> paramsHeader = new HashMap<>();
        paramsHeader.put("accept", "application/json");
        ExecuteRequestHttpService executeRequestHttpService = new ExecuteRequestHttpService();
        String url = "https://www.crossfit.com/workout/" + Uteis.getDataAplicandoFormatacao(data, "yyyy/MM/dd");
        try {
            String retorno = executeRequestHttpService.executeRequestGETEncode(url, paramsHeader, "ISO-8859-1", "ISO-8859-1");
            JSONObject response = new JSONObject(retorno).getJSONObject("wods");
            return response.getString("wodRaw");
        } catch (Exception ex) {
            Uteis.logar(ex, WodServiceImpl.class);
            throw new ServiceException(WodExcecoes.ERRO_NAO_ENCONTRADO_WOD_DO_DIA);
        }
    }

    @Override
    public List<WodResponseTO> obterListaWods(FiltroWodJSON filtros, PaginadorDTO paginadorDTO, Integer empresaId, boolean restringirEmpresas) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        String empresaUsuario = sessaoService.getUsuarioAtual().getEmpresaAtual().toString();

        try {
           if (StringUtils.isBlank(paginadorDTO.getSort())) {
                paginadorDTO.setSort("dia,DESC");
            }
            List<Empresa> empresas = empresaService.obterTodos(ctx);
            Map<Integer, String> empresaMap = empresas.stream()
                    .filter(empresa -> empresa.getCodigo() != null && empresa.getNome() != null)
                    .collect(Collectors.toMap(Empresa::getCodigo, Empresa::getNome));

            if (filtros.getUnidade().isEmpty() && empresaUsuario != null){
                filtros.setUnidade(Collections.singletonList(Integer.parseInt(empresaUsuario)));
            }
            List<Wod> wods = wodDao.obterListaWods(ctx, filtros, paginadorDTO, empresaId, restringirEmpresas);

            List<WodResponseTO> ret = new ArrayList<>();
            for (Wod wod : wods) {
                WodResponseTO wodResponseTO = new WodResponseTO(wod, false, false);
                String nomeEmpresa = empresaMap.getOrDefault(wod.getEmpresa(), "N/A");
                wodResponseTO.setNomeEmpresa(nomeEmpresa);
                ret.add(wodResponseTO);
            }
            return ret;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(WodExcecoes.ERRO_BUSCAR_WODS, e);
        }
    }

    @Override
    public List<WodResponseTO> listarTodosWods(Date data, Integer empresaId) throws ServiceException {
        return listarTodosWods(data, empresaId, false);
    }

    @Override
    public List<WodResponseTO> listarTodosWods(Date data, Integer empresaId, Boolean todasUnidades) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();

        if (todasUnidades != null && todasUnidades) {
            List<Wod> wodsLocais = wodDao.listarTodosOsWods(ctx, data, empresaId, todasUnidades);

            List<Wod> wodsRede = buscarWodsDaRede(data);

            Set<String> idsRedeExistentes = new HashSet<>();
            for (Wod wod : wodsLocais) {
                if (wod.getIdRede() != null && !wod.getIdRede().isEmpty()) {
                    idsRedeExistentes.add(wod.getIdRede());
                }
            }
            for (Wod wodRede : wodsRede) {
                if (wodRede.getIdRede() == null || !idsRedeExistentes.contains(wodRede.getIdRede())) {
                    wodsLocais.add(wodRede);
                }
            }

            List<WodResponseTO> ret = new ArrayList<>();
            for (Wod wod : wodsLocais) {
                ret.add(new WodResponseTO(wod, false, false));
            }
            return ret;
        } else {
            List<Wod> wods = wodDao.listarTodosOsWods(ctx, data, empresaId, todasUnidades);
            List<WodResponseTO> ret = new ArrayList<>();
            for (Wod wod : wods) {
                ret.add(new WodResponseTO(wod, false, false));
            }
            return ret;
        }
    }

    @Override
    public WodResponseTO buscarWod(Integer id, HttpServletRequest request) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            Wod wod = obterPorId(ctx, id);
            if (wod == null) {
                throw new ServiceException(WodExcecoes.WOD_NAO_ENCONTRADO);
            }
            List<RankingJSON> ranking = ranking(ctx, wod.getCodigo(), request);
            boolean temResultado = !UteisValidacao.emptyList(ranking);
            tratarLazilyExceptionWod(ctx, wod);
            return new WodResponseTO(wod, true, temResultado);
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(WodExcecoes.ERRO_BUSCAR_WOD, e);
        }
    }

    private void tratarLazilyExceptionWod(final String ctx, Wod wod) throws Exception {
        // Verificar e tratar LazilyException da atividadesWod
        try {
            for (AtividadeWod atividade : wod.getAtividades()) {
                // forçar erro ocorrer para tratar
                break;
            }
        } catch (Exception e) {
            String hql = "SELECT obj FROM AtividadeWod obj WHERE obj.wod.codigo = :wod ";
            Map<String, Object> params = new HashMap<>();
            params.put("wod", wod.getCodigo());
            List<AtividadeWod> atividadeWodList = getAtividadeWodDao().findByParam(ctx, hql, params);
            wod.setAtividades(atividadeWodList);
        }
        // Verificar e tratar LazilyException do AparelhoWod
        try {
            for (AparelhoWod aparelho : wod.getAparelhos()) {
                // forçar erro ocorrer para tratar
                break;
            }
        } catch (Exception e) {
            String hql = "SELECT obj FROM AparelhoWod obj WHERE obj.wod.codigo = :wod ";
            Map<String, Object> params = new HashMap<>();
            params.put("wod", wod.getCodigo());
            List<AparelhoWod> aparelhoWodList = getAparelhoWodDao().findByParam(ctx, hql, params);
            wod.setAparelhos(aparelhoWodList);
        }
    }

    @Override
    public String importarWodCrossfit(Date filtroDia) throws ServiceException {
        return obterExerciciosSite(filtroDia);
    }

    public String salvarMidiaNuvem(String ctx, String base64) throws Exception {
        if (base64 == null) {
            return null;
        }
        String extensao =
                base64.startsWith("data:image/jpeg;base64,") ? ".jpg"
                : base64.startsWith("data:image/png;base64,") ? ".png"
                        : null;
        if (extensao == null) {
            throw new ServiceException(WodExcecoes.IMAGEM_WOD_INVALIDA);
        }
        String key = "";
        String identificador = Calendario.getData(Calendario.hoje(), "ddMMyyyyhhMMss") + "-Wod-";
        MidiaEntidadeEnum tmidia = MidiaEntidadeEnum.obterPorExtensao(extensao);
        key = MidiaService.getInstanceWood().uploadObjectFromByteArray(ctx, tmidia, identificador, base64.getBytes());
        return Aplicacao.obterUrlFotoDaNuvem(key);
    }

    private Wod cadastrarAlterarWod(String ctx, WodTO wodTO, Integer id, Integer empresaId, HttpServletRequest request) throws ServiceException {
        Empresa empresaDeep = new Empresa();
        Wod wodAntesAlteracao;
        try {
            empresaDeep = empresaService.obterPorId(ctx, empresaId);
        } catch (ServiceException e) {
            e.printStackTrace();
        }

        try {
            Wod wod = null;
            if (id == null) {
                wod = new Wod();
            } else {
                wod = obterPorId(ctx, id);
                if (wod == null) {
                    throw new ServiceException(WodExcecoes.WOD_NAO_ENCONTRADO);
                }
            }

            List<Atividade> atividadesReceb = new ArrayList<>();
            StringBuilder sbAtividadesReceb = new StringBuilder();
            if (wodTO.getAtividade() != null) {
                for (Integer atvid : wodTO.getAtividade()) {
                    Atividade atividade = atividadeDao.findById(ctx, atvid);
                    if (atividade == null) {
                        throw new ServiceException(WodExcecoes.ATIVIDADE_NAO_ENCONTRADA);
                    }
                    atividadesReceb.add(atividade);
                    sbAtividadesReceb.append(";").append(atividade.getCodigo().toString()).append(";");
                }
            }
            List<Aparelho> aparelhosReceb = new ArrayList<>();
            StringBuilder sbAparelhosReceb = new StringBuilder();
            if((wodTO.getAparelho() != null)) {
                for (Integer apid : wodTO.getAparelho()) {
                Aparelho aparelho = aparelhoDao.findById(ctx, apid);
                if (aparelho == null) {
                    throw new ServiceException(WodExcecoes.APARELHO_NAO_ENCONTRADO);
                }
                aparelhosReceb.add(aparelho);
                sbAparelhosReceb.append(";").append(aparelho.getCodigo().toString()).append(";");
                }
            }
            TipoWod tipo = tipoWodDao.findById(ctx, Integer.parseInt(wodTO.getTipoWod()));
            if (tipo == null) {
                throw new ServiceException(WodExcecoes.TIPO_WOD_NAO_ENCONTRADO);
            }

            /**
             * Se acaso o wod tiver resultado, não poder esta alterando o tipo wod e o dia do wod
             */
            if (!UteisValidacao.emptyNumber(wod.getCodigo())) {
                List<RankingJSON> ranking = ranking(ctx, wod.getCodigo(), request);
                if (UteisValidacao.emptyList(ranking)) {
                    wod.setDia(Uteis.dataHoraZeradaUTC(wodTO.getDia()));
                    wod.setTipoWodTabela(tipo);
                }
            } else {
                wod.setDia(Uteis.dataHoraZeradaUTC(wodTO.getDia()));
                wod.setTipoWodTabela(tipo);
            }

            tratarLazilyExceptionWod(ctx,wod);
            wodAntesAlteracao = UtilReflection.copy(wod);

            wod.setCompartilharRede(wodTO.getCompartilharRede());
            wod.setNome(wodTO.getNome());
            wod.setEmpresa(empresaId);
            wod.setAlongamento(wodTO.getAlongamentoMobilidade());
            wod.setAquecimento(wodTO.getAquecimento());
            wod.setParteTecnicaSkill(wodTO.getParteTecnicaSkill());
            wod.setComplexEmom(wodTO.getComplexEmom());
            wod.setDescricaoExercicios(wodTO.getWod());
            wod.setNivelWod(wodTO.getNivelWod());

            if (UteisValidacao.emptyString(wodTO.getImagemData()) && !UteisValidacao.emptyString(wod.getKeyImagem())) {
                deleteFoto(wod.getKeyImagem());
                wod.setUrlImagem(null);
            } else if (!UteisValidacao.emptyString(wodTO.getImagemData()) && !wodTO.getImagemData().startsWith("http")) {
                Date agora = new Date();
                String fotoKey = UtilS3Base64Img.getUrlImage(wodTO.getImagemData(), wodTO.getExtensaoImagem(), ctx) + "?time=" + agora.getTime();
                wod.setUrlImagem(fotoKey);
            }else if(!UteisValidacao.emptyString(wodTO.getImagemData()) && wodTO.getImagemData().startsWith("http")){
                wod.setUrlImagem(wod.getKeyImagem());
            }


            if (id == null) {
                for (Atividade atividade : atividadesReceb) {
                    wod.getAtividades().add(new AtividadeWod(wod, atividade));
                }
                for (Aparelho aparelho : aparelhosReceb) {
                    wod.getAparelhos().add(new AparelhoWod(wod, aparelho));
                }
                wod = inserir(ctx, wod);

            } else {
                List<AtividadeWod> atividadesWodAtuais = obterListaAtividadesWod(ctx, wod.getCodigo());
                List<AtividadeWod> atividadesWodNovas = new ArrayList<>();
                if (atividadesWodAtuais != null) {
                    StringBuilder sbAtividadesWodAtuais = new StringBuilder();
                    // Atividades removidas
                    for (AtividadeWod atividadeWod : atividadesWodAtuais) {
                        sbAtividadesWodAtuais.append(";").append(atividadeWod.getAtividade().getCodigo().toString()).append(";");
                        String val = new StringBuilder(";").append(atividadeWod.getAtividade().getCodigo().toString()).append(";").toString();
                        if (!sbAtividadesReceb.toString().contains(val)) {
                            atividadeWodDao.delete(ctx, atividadeWod);
                        } else {
                            atividadesWodNovas.add(atividadeWod);
                        }
                    }
                    // Atividades novas
                    for (Atividade atividadeReceb : atividadesReceb) {
                        String val = new StringBuilder(";").append(atividadeReceb.getCodigo().toString()).append(";").toString();
                        if (!sbAtividadesWodAtuais.toString().contains(val)) {
                            atividadesWodNovas.add(new AtividadeWod(wod, atividadeReceb));
                        }
                    }
                }

                List<AparelhoWod> aparelhosWodAtuais = obterListaAparelhosWod(ctx, wod.getCodigo());
                List<AparelhoWod> aparelhosWodNovos = new ArrayList<>();
                if (aparelhosWodAtuais != null) {
                    StringBuilder sbAparelhosWodAtuais = new StringBuilder();
                    // Aparelhos removidos
                    for (AparelhoWod aparelhoWod : aparelhosWodAtuais) {
                        sbAparelhosWodAtuais.append(";").append(aparelhoWod.getAparelho().getCodigo().toString()).append(";");
                        String val = new StringBuilder(";").append(aparelhoWod.getAparelho().getCodigo().toString()).append(";").toString();
                        if (!sbAparelhosReceb.toString().contains(val)) {
                            aparelhoWodDao.delete(ctx, aparelhoWod);
                        } else {
                            aparelhosWodNovos.add(aparelhoWod);
                        }
                    }
                    // Aparelhos novos
                    for (Aparelho aparelhoReceb : aparelhosReceb) {
                        String val = new StringBuilder(";").append(aparelhoReceb.getCodigo().toString()).append(";").toString();
                        if (!sbAparelhosWodAtuais.toString().contains(val)) {
                            aparelhosWodNovos.add(new AparelhoWod(wod, aparelhoReceb));
                        }
                    }
                }

                wod.setAtividades(atividadesWodNovas);
                wod.setAparelhos(aparelhosWodNovos);

                wod = alterar(ctx, wod);

                incluirLog(ctx, wod.getCodigo().toString(), "", wodAntesAlteracao.getDescricaoParaLog(wod), wod.getDescricaoParaLog(wodAntesAlteracao),
                        "ALTERAÇÃO", "ALTERAÇÃO WOD", EntidadeLogEnum.WOD, "Wod", sessaoService, logDao, null, null);
            }

            if(wodTO.getCompartilharRede()){
                EmpresaService empresaService = UtilContext.getBean(EmpresaService.class);
                Empresa empresa = empresaService.obterPorIdZW(ctx, empresaId);
                wod.setUnidadeLancou(empresa.getNome());
                if(UteisValidacao.emptyString(wod.getIdRede())){
                    wod.setIdRede("WOD" + wod.getCodigo() + "_" + Calendario.hoje().getTime());
                }
                wod.setUsuarioLancouRede(sessaoService.getUsuarioAtual().getUsername());
                wod.setChaveLancouRede(sessaoService.getUsuarioAtual().getChave());
                wod = alterar(ctx, wod);
                compartilharWodRede(ctx, wod, wodTO);
            }
            return wod;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            if (id == null) {
                throw new ServiceException(WodExcecoes.ERRO_INCLUIR_WOD, e);
            } else {
                throw new ServiceException(WodExcecoes.ERRO_ALTERAR_WOD, e);
            }
        }
    }

    private void compartilharWodRede(final String ctx, Wod wod,
                                     WodTO wodTO){
        try {
            JSONObject jsonBody = new JSONObject();
            jsonBody.put("idRede", wod.getIdRede());
            jsonBody.put("empresaId", wod.getEmpresa());
            jsonBody.put("lancou", wod.getUsuarioLancouRede());
            jsonBody.put("chaveLancou", wod.getChaveLancouRede());
            jsonBody.put("nome", wodTO.getNome());
            jsonBody.put("dia", wodTO.getDia());
            jsonBody.put("tipo", wodTO.getTipoWod());
            jsonBody.put("aquecimento", wodTO.getAquecimento());
            jsonBody.put("alongamento", wodTO.getAlongamentoMobilidade());
            jsonBody.put("parteTecnicaSkill", wodTO.getParteTecnicaSkill());
            jsonBody.put("complex", wodTO.getComplexEmom());
            jsonBody.put("wod", wodTO.getWod());
            jsonBody.put("atividade", wodTO.getAtividade());
            jsonBody.put("aparelho", wodTO.getAparelho());
            jsonBody.put("imagemData", wodTO.getImagemData());
            jsonBody.put("extensaoImagem", wodTO.getExtensaoImagem());
            jsonBody.put("unidadeLancou", wod.getUnidadeLancou());
            jsonBody.put("nivelWod", wod.getNivelWod());

            Set<String> chavesRede = getOAMDChavesRede(ctx);
            for(String chave : chavesRede){
                try {
                    JSONObject urls = urls(chave);
                    StringEntity entity = new StringEntity(jsonBody.toString(), "UTF-8");
                    HttpPost httpPost = new HttpPost(urls.getString("treinoApiUrl") + "/crossfit/" + chave + "/compartilhado");
                    httpPost.setEntity(entity);
                    httpPost.setHeader("Content-Type", "application/json");
                    HttpClient client = ExecuteRequestHttpService.createConnectorCloseable();
                    HttpResponse response = client.execute(httpPost);
                }catch (Exception e){
                    Uteis.logar(e, WodServiceImpl.class);
                }
            }
        }catch (Exception e){
            Uteis.logar(e, WodServiceImpl.class);
        }
    }

    public JSONObject urls(String chave) throws Exception {
        JSONObject result = Uteis.getJSON(Aplicacao.getProp(Aplicacao.discoveryUrls) + "/find/"+chave);
        return result.getJSONObject("content").getJSONObject("serviceUrls");
    }

    private Set<String> getOAMDChavesRede(final String chave) throws Exception {
        final String url = Aplicacao.getProp(Aplicacao.urlOAMD) + "/prest/empresaFinanceiro/chavesRede?chaveZW=" + chave;
        String s = ExecuteRequestHttpService.executeRequestGET(url);
        JSONArray array = new JSONObject(s).getJSONArray("chaves");
        return new HashSet(){{
            for(int i = 0; i < array.length(); i++){
                try {
                    add(array.getString(i));
                } catch (Exception e){

                }

            }
        }};
    }

    private void validarWod(String ctx, Wod wod) throws Exception {
        StringBuilder where = new StringBuilder();
        String nomeTratado = wod.getNome().replace("'", "''");
        where.append(" wod.codigo <> ").append(wod.getCodigo() != null ? wod.getCodigo() : 0);
        where.append(" and trim(lower(wod.nome)) = '").append(nomeTratado.toLowerCase()+"'");
        where.append(" and wod.dia = '").append(Uteis.getDataFormatoBD(wod.getDia())+"'");

        if(getWodDao().existsWithParam(ctx, where)){
            throw new ServiceException(WodExcecoes.VALIDACAO_NOME_EXISTE);
        }
    }

    private Wod consultarWodRede(String ctx, String idRede, Integer empresaId) throws Exception {
        StringBuilder hql = new StringBuilder();
        hql.append("SELECT obj FROM Wod obj where obj.idRede = :idRede");
        hql.append(" AND obj.empresa = :empresaId ");
        HashMap<String, Object> param = new HashMap<String, Object>();
        param.put("empresaId", empresaId);
        param.put("idRede", idRede);
        List<Wod> wods = getWodDao().findByParam(ctx, hql.toString(), param);
        return wods == null || wods.isEmpty() ? null : wods.get(0);
    }

    @Override
    public WodResponseTO cadastrarWod(WodTO wodTO, Integer empresaId, HttpServletRequest request) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            Wod wod = cadastrarAlterarWod(ctx, wodTO, null, empresaId, request);
            incluirLog(ctx, wod.getCodigo().toString(), "", "",
                    wod.getDescricaoParaLog(null), "INCLUSÃO",
                    "INCLUSÃO DE WOD", EntidadeLogEnum.WOD,  "Wod", sessaoService, logDao, null, null);
            return new WodResponseTO(wod, true, false);
        } catch (Exception e) {
              throw new ServiceException(WodExcecoes.ERRO_INCLUIR_WOD, e);
        }
    }

    @Override
    public WodResponseTO alterarWod(WodTO wodTO, Integer id, Integer empresaId, HttpServletRequest request) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            Wod wod = cadastrarAlterarWod(ctx, wodTO, id, empresaId, request);
            return new WodResponseTO(wod, true, false);
        } catch (Exception e) {
            throw new ServiceException(WodExcecoes.ERRO_ALTERAR_WOD, e);
        }
    }

    @Override
    public void removerWod(Integer id) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            Wod wod = obterPorId(ctx, id);
            if (wod != null) {
                excluir(ctx, wod);
                incluirLog(ctx, wod.getCodigo().toString(), "",
                        wod.getDescricaoParaLog(null), "",
                        "EXCLUSÃO", "EXCLUSÃO DE WOD",
                        EntidadeLogEnum.WOD, "Wod", sessaoService, logDao, null, null);
            } else {
                throw new ServiceException(WodExcecoes.WOD_NAO_ENCONTRADO);
            }
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(WodExcecoes.ERRO_EXCLUIR_WOD, e);
        }
    }

    public void simularScore(String ctx, Date dia) throws ServiceException{
        try {
            String diastr = Uteis.getDataAplicandoFormatacao(dia == null ? Calendario.hoje() : dia, "yyyy-MM-dd");
            java.sql.Connection con = wodDao.getConnection(ctx);
            try (ResultSet rs = con.prepareStatement("SELECT * FROM wod where dia = '" + diastr + "'").executeQuery()) {
                while (rs.next()) {
                    TipoWodEnum tipo = TipoWodEnum.obterPorOrdinal(rs.getInt("tipoWod"));
                    GerarScore.gerarProWod(diastr, rs.getInt("codigo"), con, tipo == null ? TipoWodEnum.FOR_TIME : tipo);
                }
            }
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    public void deleteFoto(final String key) throws ServiceException{
        try {
            MidiaService.getInstanceWood().deleteObject(key);
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    @Override
    public void setServletContext(ServletContext servletContext) {
        this.context = servletContext;
    }

    public void cadastrarAlterarWodCompartilhado(String ctx, JSONObject compartilhado) throws Exception {
        EmpresaService empresaService = UtilContext.getBean(EmpresaService.class);
        List<Empresa> empresas = empresaService.obterTodos(ctx);
        for(Empresa e : empresas){
            try {
                cadastrarAlterarWodCompartilhado(ctx, compartilhado, e.getCodZW() == null ? 0 : e.getCodZW());
            }catch (Exception ex){
                Uteis.logar(ex, WodServiceImpl.class);
            }
        }
    }

    public void cadastrarAlterarWodCompartilhado(String ctx, JSONObject compartilhado, Integer empresa) throws Exception {
        String chaveLancou = compartilhado.optString("chaveLancou") == null ? "" : compartilhado.optString("chaveLancou");
        Integer empresaId = compartilhado.optInt("empresaId");

        //se a chave q lancou é igual a da requisicao e a empresa tbm, não fazer nada
        if(chaveLancou.equals(ctx) && empresa.equals(empresaId) && empresa > 0){
            return;
        }

        String idrede = compartilhado.optString("idRede");
        String lancou = compartilhado.optString("lancou");
        WodTO wodTO = new WodTO();
        wodTO.setNome(compartilhado.optString("nome"));
        wodTO.setDia(compartilhado.optLong("dia"));
        wodTO.setTipoWod(compartilhado.optString("tipo"));
        wodTO.setAquecimento(compartilhado.optString("aquecimento"));
        wodTO.setAlongamentoMobilidade(compartilhado.optString("alongamento"));
        wodTO.setParteTecnicaSkill(compartilhado.optString("parteTecnicaSkill"));
        wodTO.setComplexEmom(compartilhado.optString("complex"));
        wodTO.setWod(compartilhado.optString("wod"));
        wodTO.setAtividade(jsonArrayToArrayInt(compartilhado.optJSONArray("atividade")));
        wodTO.setAparelhoIds(jsonArrayToArrayInt(compartilhado.optJSONArray("aparelho")));
        wodTO.setImagemData(compartilhado.optString("imagemData"));
        wodTO.setExtensaoImagem(compartilhado.optString("extensaoImagem"));

        Integer id = null;
        Wod wod = consultarWodRede(ctx, idrede, empresa);
        if (wod == null) {
            wod = new Wod();
        } else {
            id = wod.getCodigo();
        }

        wod.setIdRede(idrede);
        wod.setChaveLancouRede(chaveLancou);
        wod.setUsuarioLancouRede(lancou);

        List<Atividade> atividadesReceb = new ArrayList<>();
        StringBuilder sbAtividadesReceb = new StringBuilder();
        for (Integer atvid : wodTO.getAtividade()) {
            Atividade atividade = atividadeDao.findById(ctx, atvid);
            if (atividade == null) {
                throw new ServiceException(WodExcecoes.ATIVIDADE_NAO_ENCONTRADA);
            }
            atividadesReceb.add(atividade);
            sbAtividadesReceb.append(";").append(atividade.getCodigo().toString()).append(";");
        }
        List<Aparelho> aparelhosReceb = new ArrayList<>();
        StringBuilder sbAparelhosReceb = new StringBuilder();
        for (Integer apid : wodTO.getAparelho()) {
            Aparelho aparelho = aparelhoDao.findById(ctx, apid);
            if (aparelho == null) {
                continue;
            }
            aparelhosReceb.add(aparelho);
            sbAparelhosReceb.append(";").append(aparelho.getCodigo().toString()).append(";");
        }
        TipoWod tipo = tipoWodDao.findById(ctx, Integer.parseInt(wodTO.getTipoWod()));
        if (tipo != null) {
            wod.setTipoWodTabela(tipo);
        }
        wod.setDia(Uteis.dataHoraZeradaUTC(wodTO.getDia()));

        wod.setCompartilharRede(wodTO.getCompartilharRede());
        wod.setNome(wodTO.getNome());
        wod.setEmpresa(empresa);
        wod.setUnidadeLancou(compartilhado.optString("unidadeLancou"));
        wod.setAquecimento(wodTO.getAquecimento());
        wod.setAlongamento(wodTO.getAlongamentoMobilidade());
        wod.setParteTecnicaSkill(wodTO.getParteTecnicaSkill());
        wod.setComplexEmom(wodTO.getComplexEmom());
        wod.setDescricaoExercicios(wodTO.getWod());
        if (wodTO.getImagemData() != null && !wodTO.getImagemData().equals("")) {
            try {
                if (!wodTO.getImagemData().startsWith("http")) {
                    String extensaoImg = UteisValidacao.emptyString(wodTO.getExtensaoImagem()) ? obterExtensaoImagem(wodTO.getImagemData()) : wodTO.getExtensaoImagem();
                    wod.setUrlImagem(UtilS3Base64Img.getUrlImage(wodTO.getImagemData(), extensaoImg, ctx));
                } else if (UteisValidacao.emptyString(wod.getKeyImagem())) {
                    wod.setUrlImagem(wodTO.getImagemData());
                }
            } catch (Exception ex) {}
        } else if (wod.getKeyImagem() != null && !wod.getKeyImagem().equalsIgnoreCase("")) {
            deleteFoto(wod.getKeyImagem());
            wod.setUrlImagem(null);
        }

        if (id == null) {
            for (Atividade atividade : atividadesReceb) {
                wod.getAtividades().add(new AtividadeWod(wod, atividade));
            }
            for (Aparelho aparelho : aparelhosReceb) {
                wod.getAparelhos().add(new AparelhoWod(wod, aparelho));
            }
            wod = inserir(ctx, wod);
        } else {
            List<AtividadeWod> atividadesWodAtuais = obterListaAtividadesWod(ctx, wod.getCodigo());
            List<AtividadeWod> atividadesWodNovas = new ArrayList<>();
            if (atividadesWodAtuais != null) {
                StringBuilder sbAtividadesWodAtuais = new StringBuilder();
                // Atividades removidas
                for (AtividadeWod atividadeWod : atividadesWodAtuais) {
                    sbAtividadesWodAtuais.append(";").append(atividadeWod.getAtividade().getCodigo().toString()).append(";");
                    String val = new StringBuilder(";").append(atividadeWod.getAtividade().getCodigo().toString()).append(";").toString();
                    if (!sbAtividadesReceb.toString().contains(val)) {
                        atividadeWodDao.delete(ctx, atividadeWod);
                    } else {
                        atividadesWodNovas.add(atividadeWod);
                    }
                }
                // Atividades novas
                for (Atividade atividadeReceb : atividadesReceb) {
                    String val = new StringBuilder(";").append(atividadeReceb.getCodigo().toString()).append(";").toString();
                    if (!sbAtividadesWodAtuais.toString().contains(val)) {
                        atividadesWodNovas.add(new AtividadeWod(wod, atividadeReceb));
                    }
                }
            }

            List<AparelhoWod> aparelhosWodAtuais = obterListaAparelhosWod(ctx, wod.getCodigo());
            List<AparelhoWod> aparelhosWodNovos = new ArrayList<>();
            if (aparelhosWodAtuais != null) {
                StringBuilder sbAparelhosWodAtuais = new StringBuilder();
                // Aparelhos removidos
                for (AparelhoWod aparelhoWod : aparelhosWodAtuais) {
                    sbAparelhosWodAtuais.append(";").append(aparelhoWod.getAparelho().getCodigo().toString()).append(";");
                    String val = new StringBuilder(";").append(aparelhoWod.getAparelho().getCodigo().toString()).append(";").toString();
                    if (!sbAparelhosReceb.toString().contains(val)) {
                        aparelhoWodDao.delete(ctx, aparelhoWod);
                    } else {
                        aparelhosWodNovos.add(aparelhoWod);
                    }
                }
                // Aparelhos novos
                for (Aparelho aparelhoReceb : aparelhosReceb) {
                    String val = new StringBuilder(";").append(aparelhoReceb.getCodigo().toString()).append(";").toString();
                    if (!sbAparelhosWodAtuais.toString().contains(val)) {
                        aparelhosWodNovos.add(new AparelhoWod(wod, aparelhoReceb));
                    }
                }
            }
            wod.setAtividades(atividadesWodNovas);
            wod.setAparelhos(aparelhosWodNovos);
            wod = alterar(ctx, wod);
        }
    }

    private String obterExtensaoImagem(String wodImagemData) {
        if (wodImagemData.toLowerCase().contains(".png")) {
            return ".png";
        }
        if (wodImagemData.toLowerCase().contains(".jpg")) {
            return ".jpg";
        }
        if (wodImagemData.toLowerCase().contains(".jpeg")) {
            return ".jpeg";
        }
        if (wodImagemData.toLowerCase().contains(".webp")) {
            return ".webp";
        }
        if (wodImagemData.toLowerCase().contains(".gif")) {
            return ".gif";
        }
        return null;
    }

    private Integer[] jsonArrayToArrayInt(JSONArray jsonArray) throws JSONException {
        if(jsonArray == null){
            return new Integer[]{};
        }
        List<Integer> lista = new ArrayList(){{
            for(int i = 0; i < jsonArray.length(); i++){
                add(jsonArray.getInt(i));
            }
        }};
        Integer[] array = new Integer[lista.size()];
        return lista.toArray(array);
    }

    public boolean podeUsarCross(final String ctx,
                                 final Integer codUsuario,
                                 final String matricula,
                                 Boolean permissaoVisualizarWod) throws ServiceException {
        Boolean podeUsarCross = true;
        try {
            String sql;
            if(UteisValidacao.emptyString(matricula)){
                sql = "select codigocliente, empresa, crossfit from clientesintetico c " +
                        " inner join usuario u on c.codigo = u.cliente_codigo " +
                        " where u.codigo = " + codUsuario;
            } else {
                sql = "select codigocliente, empresa, crossfit from clientesintetico where matricula =" + matricula;
            }
            try (ResultSet rs = wodDao.createStatement(ctx, sql)) {
                if (rs.next()) {
                    podeUsarCross = rs.getBoolean("crossfit");
                    if (!podeUsarCross) {
                        return checarAlunoIntegracao(ctx, rs.getInt("codigocliente"), rs.getInt("empresa"), permissaoVisualizarWod);
                    }
                }
            }
        }catch (Exception e){
            Uteis.logar(e, WodServiceImpl.class);
        }
        return podeUsarCross;
    }

    private boolean checarAlunoIntegracao(String ctx, Integer codigo, Integer empresa, Boolean permissaoVisualizarWod){
        return checarAlunoGympass(ctx, codigo, empresa, permissaoVisualizarWod) || checarAlunoTotalpass(ctx, codigo, empresa, permissaoVisualizarWod);
    }

    private boolean checarAlunoTotalpass(String ctx, Integer codigo, Integer empresa, Boolean permissaoVisualizarWod){
        try {
            String sql = "select c.permitirWod from configtotalpass c where c.empresa_codigo = " + empresa;
            ConexaoZWService conexaoZWService = UtilContext.getBean(ConexaoZWService.class);
            try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
                try (ResultSet rsClienteTotalPass = ConexaoZWServiceImpl.criarConsulta(sql, conZW)) {
                    boolean permitirWod = rsClienteTotalPass.next() ? rsClienteTotalPass.getBoolean("permitirWod") : false;
                    if (permitirWod) {
                        java.sql.Date dia = Uteis.getDataJDBC(Calendario.hoje());
                        StringBuilder sqlTp = new StringBuilder();
                            sqlTp.append(" SELECT p.codigo FROM PeriodoAcessoCliente p \n")
                                .append(" INNER JOIN cliente c ON c.pessoa = p.pessoa \n")
                                .append(" WHERE c.codigo = ").append(codigo).append(" \n")
                                .append(" AND p.tipoAcesso = 'PL' \n")
                                .append(" AND p.tipototalpass = true ");
                        if (!permissaoVisualizarWod) {
                            // Adicionar a condição de data somente se permissaoVisualizarWod for falsa
                            sqlTp.append("\n AND p.dataInicioAcesso <= '").append(dia).append("' \n")
                                    .append(" AND p.dataFinalAcesso >= '").append(dia).append("'");
                        }
                        try (ResultSet rsPeriodoAcesso = ConexaoZWServiceImpl.criarConsulta(sqlTp.toString(), conZW)) {
                            return rsPeriodoAcesso.next();
                        }
                    }
                }
            }
        }catch (Exception e) {
            Uteis.logar(e, WodServiceImpl.class);
        }
        return false;
    }


    private boolean checarAlunoGympass(String ctx, Integer codigo, Integer empresa, Boolean permissaoVisualizarWod){
        try {
            String sql = "select c.permitirWod from configgympass c \n" +
                    "inner join empresa e on e.codigo = c.empresa_codigo \n" +
                    "where e.codzw = " + empresa;
            boolean permitirWod;
            try (ResultSet rs = wodDao.createStatement(ctx, sql)) {
                permitirWod = rs.next() ? rs.getBoolean("permitirWod") : false;
            }
            try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
                if (permitirWod) {
                    java.sql.Date dia = Uteis.getDataJDBC(Calendario.hoje());
                    String sqlPAGympass = "SELECT p.codigo FROM PeriodoAcessoCliente p " +
                            " inner join cliente c on c.pessoa = p.pessoa "
                            + " WHERE c.codigo = " + codigo
                            + " AND p.tipoAcesso = 'PL' "
                            + " AND p.tokengympass IS NOT NULL AND p.tokengympass <> '' ";
                    if (!permissaoVisualizarWod) {
                        // Adicionar a condição de data somente se permissaoVisualizarWod for falsa
                        sqlPAGympass += " AND p.dataInicioAcesso <= '" + dia + "' " +
                                "AND p.dataFinalAcesso >= '" + dia + "'";
                    }
                    try (ResultSet rsClienteGympass = ConexaoZWServiceImpl.criarConsulta(sqlPAGympass, conZW)) {
                        return rsClienteGympass.next();
                    }
                }
            }
        }catch (Exception e){
            Uteis.logar(e, WodServiceImpl.class);
        }
        return false;
    }

    public String importarWodsDaFranqueadora(Integer codEmpresaZW) throws ServiceException {
        try {
            String chaveFranqueado = sessaoService.getUsuarioAtual().getChave();
            final String url = Aplicacao.getProp(Aplicacao.urlOAMD) + "/prest/empresaFinanceiro/configsTreinoRede?chaveZW=" + chaveFranqueado;
            String s = ExecuteRequestHttpService.executeRequestGET(url);
            JSONObject json = new JSONObject(s).getJSONObject("configsTreinoRede");

            if (!json.has("chaveFranqueadora") || UteisValidacao.emptyString(json.getString("chaveFranqueadora"))) {
                throw new ServiceException("Não foi encontrada a chave da franqueadora!");
            }
            if (chaveFranqueado.equals(json.getString("chaveFranqueadora"))) {
                throw new ServiceException("Este processo é somente para a franqueada, e você está logado na franqueadora!");
            }
            String chaveFranqueadora = json.getString("chaveFranqueadora");
            JSONObject result = Uteis.getJSON(Aplicacao.getProp(Aplicacao.discoveryUrls) + "/find/" + chaveFranqueadora);
            String treinoUrl = result.getJSONObject("content").getJSONObject("serviceUrls").getString("treinoUrl");
            List<Wod> wodsFranquia = wodsRecentesProcessoImportacao(treinoUrl, chaveFranqueadora);

                if (wodsFranquia.isEmpty()) {
                    return "Não foram encontrados WODs na franqueadora para serem importados.";
                }

         int count = 0;
            if (!chaveFranqueado.equals(chaveFranqueadora)) {
                result = Uteis.getJSON(Aplicacao.getProp(Aplicacao.discoveryUrls) + "/find/" + chaveFranqueado);
                treinoUrl = result.getJSONObject("content").getJSONObject("serviceUrls").getString("treinoUrl");
                try {
                    List<Wod> wodsFranqueados = wodsRecentesProcessoImportacao(treinoUrl, chaveFranqueado);
                    List<Wod> wodsNomeConflitante = new ArrayList<>();

                        for (Wod wod : wodsFranquia) {
                        if (wod.getCompartilharRede()) {
                            boolean compartilhar = true;
                            for (Wod wfranqueado : wodsFranqueados) {
                               if (wfranqueado.getNome().equals(wod.getNome())) {
                                    if (!wfranqueado.getIdRede().equals(wod.getIdRede())) {
                                        // Se entrar aqui é porque na franqueada existe um wod desatualizado com o mesmo nome mas idRede diferente
                                        // Deverá ser compartilhado quando for completo compartilhamento dos wods que não estão neste caso;
                                        wodsNomeConflitante.add(wod);
                                    }
                                    compartilhar = false;
                                    break;
                                }
                            }
                            if (compartilhar) {
                                count++;
                                processoCompartilharWodRede(chaveFranqueado, wod);
                            }
                        }
                    }
                    for (Wod wod : wodsNomeConflitante) {
                        count++;
                        processoCompartilharWodRede(chaveFranqueado, wod);
                    }
                } catch (Exception e) {
                    Uteis.logar(e, WodServiceImpl.class);
                        throw new ServiceException("Erro durante o processo de importação de WODs.");
                    }
                }
                if (count == 0) {
                    return "Todos os WODs dos próximos 150 dias da franqueadora já foram importados ou não há WODs para importar.";
                } else {
                    return "Realizada com sucesso a importação de " + count + " WOD(s) da franqueadora dos próximos 150 dias!";
           }

        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    @Override
    public void avaliarWod(String ctx, AvaliacaoWodDTO avaliacaoWodDTO) throws Exception {
        try {
            if(avaliacaoWodDTO.getUsuario() == null || avaliacaoWodDTO.getUsuario() == 0) {
                throw new Exception("Usuário não informado!");
            } else if(avaliacaoWodDTO.getWod() == null || avaliacaoWodDTO.getWod() == 0) {
                throw new Exception("Wod não informado!");
            } else if(avaliacaoWodDTO.getEmpresa() == null || avaliacaoWodDTO.getEmpresa() == 0) {
                throw new Exception("Empresa não informada!");
            }

            WodAvaliacao wodAvaliacao = new WodAvaliacao();
            wodAvaliacao.setComentario(avaliacaoWodDTO.getComentario());
            wodAvaliacao.setEmpresa(avaliacaoWodDTO.getEmpresa());
            wodAvaliacao.setNota(avaliacaoWodDTO.getNota());
            wodAvaliacao.setPercepcaoEsforco(avaliacaoWodDTO.getPercepcaoEsforco());
            wodAvaliacao.setWod(avaliacaoWodDTO.getWod());
            wodAvaliacao.setUsuario(avaliacaoWodDTO.getUsuario());
            wodAvaliacaoDao.insert(ctx, wodAvaliacao);
        } catch (PersistenceException e) {
            String mensagem = e.getCause().getCause().getMessage();
            if(mensagem.contains("unique_wod_usuario")) {
                throw new PersistenceException("Pode existir somente 1 avaliação de usuario por wod!", e.getCause());
            } else if (mensagem.contains("wodavaliacao_nota_check")) {
                throw new PersistenceException("Nota deve ser entre 1 e 5!", e.getCause());
            } else if (mensagem.contains("wodavaliacao_percepcao_esforco_check")) {
                throw new PersistenceException("Percepção de esforço deve ser entre 1 e 10!", e.getCause());
            }
            throw e;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public List<WodAvaliacao> avaliacoesWod(String ctx) throws Exception {
        List<WodAvaliacao> avaliacaoWods = wodAvaliacaoDao.findAll(ctx);
        return avaliacaoWods;
    }

    @Override
    public List<ScoreTreino> obterPrimeirasTresPosicoesWodRanking(String ctx, Integer codigo) throws Exception {
        Map<String, Object> params =  new HashMap<String, Object>();

        StringBuilder sql = new StringBuilder();
        sql.append("SELECT obj FROM ScoreTreino obj inner join obj.nivelcrossfit n WHERE obj.wod.codigo = :wod order by obj.posicao asc");
        params.put("wod", codigo);
        List<ScoreTreino> scoreTreinos = scoreDao.findByParam(ctx, sql.toString(), params, 3);

        return scoreTreinos;
    }

    @Override
    public void enviaNotificacaoTopTres(String ctx, List<ScoreTreino> scoreTreinoAntesAtualizacao, List<ScoreTreino> scoreTreinoDepoisAtualizacao) {
        List<ScoreTreino> scoreTreinoPerdeuTopTres = new ArrayList<>();
        List<ScoreTreino> scoreTreinoGanhouTopTres = new ArrayList<>();
        Boolean jaEstavaNoTopTres;

        for(ScoreTreino scoreTreino : scoreTreinoAntesAtualizacao){
            jaEstavaNoTopTres = false;
            for (ScoreTreino scoreTreinoDepois : scoreTreinoDepoisAtualizacao) {
                if(scoreTreino.getUsuario().getCodigo().equals(scoreTreinoDepois.getUsuario().getCodigo())){
                    jaEstavaNoTopTres = true;
                    break;
                }
            }
            if(!jaEstavaNoTopTres){
                scoreTreinoPerdeuTopTres.add(scoreTreino);
            }
        }

        for(ScoreTreino scoreTreino : scoreTreinoDepoisAtualizacao){
            jaEstavaNoTopTres = false;
            for (ScoreTreino scoreTreinoAntes : scoreTreinoAntesAtualizacao) {
                if(scoreTreino.getUsuario().getCodigo().equals(scoreTreinoAntes.getUsuario().getCodigo())) {
                    jaEstavaNoTopTres = true;
                    break;
                }
            }
            if(!jaEstavaNoTopTres){
                scoreTreinoGanhouTopTres.add(scoreTreino);
            }
        }

        for(ScoreTreino scoreTreino : scoreTreinoGanhouTopTres){
            PushMobileRunnable.executarNoticacaoAppDoAluno(ctx, "No topo do Ranking!", " Parabéns! Você está entre os três primeiros no ranking do WOD!", scoreTreino.getUsuario().getUserName(), "");
        }

        for(ScoreTreino scoreTreino : scoreTreinoPerdeuTopTres){
            PushMobileRunnable.executarNoticacaoAppDoAluno(ctx, "Alguém pegou a sua \uD83D\uDC51.", "Você saiu do top 3 no ranking, não desanime! Volte com tudo no próximo WOD \uD83D\uDCAA", scoreTreino.getUsuario().getUserName(), "");
        }
    }

    @Override
    public List<Wod> obterPorTipoWod(String ctx, Integer codigo) throws Exception {
        Map<String, Object> params =  new HashMap<String, Object>();

        StringBuilder sql = new StringBuilder();
        sql.append("SELECT obj FROM Wod obj WHERE obj.tipoWodTabela.codigo = :tipoWod");
        params.put("tipoWod", codigo);
        List<Wod> wods = wodDao.findByParam(ctx, sql.toString(), params);

        return wods;
    }


    private static List<Wod> wodsRecentesProcessoImportacao(String treinoUrl, String chave) throws Exception {
        List<NameValuePair> params = new ArrayList<>();

        String inicio = Uteis.getDataAplicandoFormatacao(Uteis.obterDataAnterior(new Date(), 5), "dd/MM/yyyy");
        String fim = Uteis.getDataAplicandoFormatacao(Uteis.obterDataFutura(new Date(), 150), "dd/MM/yyyy");

        params.add(new BasicNameValuePair("inicio", inicio));
        params.add(new BasicNameValuePair("fim", fim));
        params.add(new BasicNameValuePair("empresaCodigo", "1"));
        params.add(new BasicNameValuePair("usuario", "1"));

        CloseableHttpClient client = ExecuteRequestHttpService.createConnector();
        HttpPost httpPost = new HttpPost(treinoUrl + "/prest/crossfit/" + chave + "/wods");
        httpPost.setEntity(new UrlEncodedFormEntity(params));
        CloseableHttpResponse response = client.execute(httpPost);
        ResponseHandler<String> handler = new BasicResponseHandler();
        JSONObject retornoWODS = new JSONObject(handler.handleResponse(response));
        JSONArray aReturn = retornoWODS.getJSONArray("return");
        JSONArray wods = new JSONArray();
        for (int i = 0; i < aReturn.length(); i++) {
            JSONObject jsonObject = aReturn.getJSONObject(i);
            jsonObject.remove("diaApresentar");
            jsonObject.remove("urlImagemRetorno");
            jsonObject.remove("keyImagem");
            wods.put(jsonObject);
        }
        return JSONMapper.getList(wods, Wod.class);
    }

    private void processoCompartilharWodRede(final String chaveFranqueado, Wod wod) {
        try {
            JSONObject jsonBody = new JSONObject();
            jsonBody.put("idRede", wod.getIdRede());
            jsonBody.put("empresaId", wod.getEmpresa());
            jsonBody.put("lancou", wod.getUsuarioLancouRede());
            jsonBody.put("chaveLancou", wod.getChaveLancouRede());
            jsonBody.put("nome", wod.getNome());
            jsonBody.put("dia", wod.getDia().getTime());
            jsonBody.put("tipo", wod.getTipoWodTabela().getCodigo());
            jsonBody.put("aquecimento", wod.getAquecimento());
            jsonBody.put("alongamento", wod.getAlongamento());
            jsonBody.put("parteTecnicaSkill", wod.getParteTecnicaSkill());
            jsonBody.put("complex", wod.getComplexEmom());
            jsonBody.put("wod", wod.getDescricaoExercicios());
            jsonBody.put("unidadeLancou", wod.getUnidadeLancou());

            try {
                cadastrarAlterarWodCompartilhado(chaveFranqueado, jsonBody);
            } catch (Exception e) {
                Uteis.logar(e, WodServiceImpl.class);
            }
        } catch (Exception e) {
            Uteis.logar(e, WodServiceImpl.class);
        }
    }

    public void runExcluirWodsPorChaves(String csvBase64Data, Date dataInicio, Date dataFim, ModelMap mm) throws ServiceException {
        byte[] data = Base64.getDecoder().decode(csvBase64Data);
        try (BufferedReader br = new BufferedReader(new InputStreamReader(new ByteArrayInputStream(data), StandardCharsets.UTF_8))) {
            List<String> logs = new ArrayList<>();
            String line;
            while ((line = br.readLine()) != null) {
                String ctx = line.trim();
                if (!ctx.isEmpty()) {
                    try {
                        JSONObject result = Uteis.getJSON(Aplicacao.getProp(Aplicacao.discoveryUrls) + "/find/" + ctx);
                        String treinoUrl = result.getJSONObject("content").getJSONObject("serviceUrls").getString("treinoUrl");
                        HttpPost httpPost = new HttpPost(treinoUrl + "/prest/crossfit/" + ctx + "/excluir-wods-por-chave");
                        URI uri = new URIBuilder(httpPost.getURI())
                                .addParameter("dataInicio", Uteis.getDataAplicandoFormatacao(dataInicio, "yyyy-MM-dd"))
                                .addParameter("dataFim", Uteis.getDataAplicandoFormatacao(dataFim, "yyyy-MM-dd"))
                                .build();
                        httpPost.setURI(uri);
                        httpPost.setHeader("Content-Type", "application/json");
                        HttpClient client = ExecuteRequestHttpService.createConnectorCloseable();
                        HttpResponse response = client.execute(httpPost);
                        String responseBody = EntityUtils.toString(response.getEntity());
                        JSONObject json = new JSONObject(responseBody);
                        if (json.has("content")) {
                            logs.add("chave: " + ctx + " - " + json.getString("content"));
                        }
                    } catch (Exception ex) {
                        Uteis.logar(ex, WodServiceImpl.class);
                    }
                }
            }
            mm.addAttribute("SUCESSO", "wods excluídos com sucesso!");
            mm.addAttribute("logs", logs);
        } catch (Exception ex) {
            mm.addAttribute("ERRO ao excluir wods runExcluirWodsPorChaves", ex.getMessage());
            Logger.getLogger(WodServiceImpl.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public String excluirWodsPorChave(String ctx, Date dataInicio, Date dataFim) throws ServiceException {
        int qtSucesso = 0;
        int qtError = 0;
        int qtTotal = 0;

        try {
            StringBuilder sql = new StringBuilder("SELECT obj FROM Wod obj WHERE obj.dia BETWEEN :dataInicio AND :dataFim");
            Map<String, Object> params = new HashMap<>();
            params.put("dataInicio", dataInicio);
            params.put("dataFim", dataFim);

            List<Wod> wodsParaExcluir = obterPorParam(ctx, sql.toString(), params);
            if (wodsParaExcluir != null && !wodsParaExcluir.isEmpty()) {
                qtTotal = wodsParaExcluir.size();
                for (Wod wod : wodsParaExcluir) {
                    try {
                        String sqlScoreTreino = "DELETE FROM scoretreino WHERE wod_codigo = " + wod.getCodigo();
                        try {
                            wodDao.createStatement(ctx, sqlScoreTreino);
                        } catch (Exception e) {
                            Logger.getLogger(WodServiceImpl.class.getName()).log(Level.WARNING, "Nenhum resultado foi retornado pela consulta scoretreino: " + e.getMessage());
                        }

                        String sqlWodComentario = "DELETE FROM comentariowod WHERE wod_codigo = " + wod.getCodigo();
                        try {
                            wodDao.createStatement(ctx, sqlWodComentario);
                        } catch (Exception e) {
                            Logger.getLogger(WodServiceImpl.class.getName()).log(Level.WARNING, "Nenhum resultado foi retornado pela consulta comentariowod: " + e.getMessage());
                        }

                        excluir(ctx, wod);

                        qtSucesso++;
                    } catch (Exception e) {
                        qtError++;
                        Uteis.logar(e, WodServiceImpl.class);
                    }
                }
            } else {
                Logger.getLogger(WodServiceImpl.class.getName()).log(Level.INFO, "Nenhum WOD encontrado para o contexto: " + ctx);
            }
        } catch (Exception ex) {
            qtError++;
            Uteis.logar(ex, WodServiceImpl.class);
        }

        return "SUCESSO: Total WODs " + qtTotal + " e " + qtSucesso + " WODs excluídos com sucesso e " + qtError + " exceções ocorreram durante o processo.";
    }


    public Wod recarregarWod(String ctx, Wod wod) throws Exception {
        Wod wodNovo = new Wod();

        wodNovo.setCodigo(wod.getCodigo());
        wodNovo.setNome(wod.getNome());
        wodNovo.setEmpresa(wod.getEmpresa());
        wodNovo.setCompartilharRede(wod.getCompartilharRede());
        wodNovo.setIdRede(wod.getIdRede());
        wodNovo.setUsuarioLancouRede(wod.getUsuarioLancouRede());
        wodNovo.setChaveLancouRede(wod.getChaveLancouRede());
        wodNovo.setUnidadeLancou(wod.getUnidadeLancou());
        wodNovo.setDescricaoExercicios(wod.getDescricaoExercicios());
        wodNovo.setTipoWod(wod.getTipoWod());
        wodNovo.setUrlImagem(wod.getKeyImagem());
        wodNovo.setOrigemExercicio(wod.getOrigemExercicio());
        wodNovo.setBenchmark(wod.getBenchmark());
        wodNovo.setObservacao(wod.getObservacao());
        wodNovo.setDia(wod.getDia());
        wodNovo.setTipoWodTabela(wod.getTipoWodTabela());
        wodNovo.setEvento(wod.getEvento());
        wodNovo.setAlongamento(wod.getAlongamento());
        wodNovo.setAquecimento(wod.getAquecimento());
        wodNovo.setComplexEmom(wod.getComplexEmom());
        wodNovo.setNivelWod(wod.getNivelWod());
        wodNovo.setContratoCrossfit(wod.isContratoCrossfit());
        wodNovo.setTemResultado(wod.isTemResultado());
        wodNovo.setTemResultadoGeral(wod.isTemResultadoGeral());

        if (wod.getAtividades() != null) {
            wodNovo.setAtividades(new ArrayList<>(wod.getAtividades()));
        }
        if (wod.getAparelhos() != null) {
            wodNovo.setAparelhos(new ArrayList<>(wod.getAparelhos()));
        }
        if (wod.getComentarios() != null) {
            wodNovo.setComentarios(new ArrayList<>(wod.getComentarios()));
        }
        if (wod.getRanking() != null) {
            wodNovo.setRanking(wod.getRanking());
        }

        return wodNovo;
    }

    private List<Wod> buscarWodsDaRede(Date dia) {
        List<Wod> wodsRede = new ArrayList<>();
        try {
            String chaveFranqueado = sessaoService.getUsuarioAtual().getChave();

            final String url = Aplicacao.getProp(Aplicacao.urlOAMD) + "/prest/empresaFinanceiro/configsTreinoRede?chaveZW=" + chaveFranqueado;
            String s = ExecuteRequestHttpService.executeRequestGET(url);
            JSONObject json = new JSONObject(s).getJSONObject("configsTreinoRede");

            if (json.has("chaveFranqueadora") && !UteisValidacao.emptyString(json.getString("chaveFranqueadora"))) {
                String chaveFranqueadora = json.getString("chaveFranqueadora");

                if (!chaveFranqueado.equals(chaveFranqueadora)) {
                    JSONObject result = Uteis.getJSON(Aplicacao.getProp(Aplicacao.discoveryUrls) + "/find/" + chaveFranqueadora);
                    String treinoUrl = result.getJSONObject("content").getJSONObject("serviceUrls").getString("treinoUrl");

                    wodsRede = buscarWodsRecentesDia(treinoUrl, chaveFranqueadora, dia);
                }
            }
        } catch (Exception e) {
            Uteis.logar(e, WodServiceImpl.class);
        }
        return wodsRede;
    }

    private List<Wod> buscarWodsRecentesDia(String treinoUrl, String chave, Date dia) throws Exception {
        List<NameValuePair> params = new ArrayList<>();

        String diaFormatado = Uteis.getDataAplicandoFormatacao(dia, "dd/MM/yyyy");

        params.add(new BasicNameValuePair("inicio", diaFormatado));
        params.add(new BasicNameValuePair("fim", diaFormatado));
        params.add(new BasicNameValuePair("empresaCodigo", "1"));
        params.add(new BasicNameValuePair("usuario", "1"));

        CloseableHttpClient client = ExecuteRequestHttpService.createConnector();
        HttpPost httpPost = new HttpPost(treinoUrl + "/prest/crossfit/" + chave + "/wods");
        httpPost.setEntity(new UrlEncodedFormEntity(params));
        CloseableHttpResponse response = client.execute(httpPost);
        ResponseHandler<String> handler = new BasicResponseHandler();
        JSONObject retornoWODS = new JSONObject(handler.handleResponse(response));
        JSONArray aReturn = retornoWODS.getJSONArray("return");
        JSONArray wods = new JSONArray();

        for (int i = 0; i < aReturn.length(); i++) {
            JSONObject jsonObject = aReturn.getJSONObject(i);
            jsonObject.remove("diaApresentar");
            jsonObject.remove("urlImagemRetorno");
            jsonObject.remove("keyImagem");
            wods.put(jsonObject);
        }

        return JSONMapper.getList(wods, Wod.class);
    }

}
