package br.com.pacto.service.impl.cliente.perfil;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Informações consolidadas da avaliação física do aluno, incluindo dados biométricos, histórico de evolução e grupos musculares trabalhados.")
public class PerfilAlunoAvaliacaoDTO {

    @ApiModelProperty(value = "Número total de avaliações físicas realizadas pelo aluno.", example = "3")
    private Integer totalAvaliacoes;

    @ApiModelProperty(value = "Período em dias entre avaliações físicas.", example = "90")
    private Integer periodoDias;

    @ApiModelProperty(value = "Número de dias restantes para a próxima avaliação física.", example = "20")
    private Integer diasProximaAvaliacao;

    @ApiModelProperty(value = "Data da próxima avaliação física em formato timestamp.", example = "1649703163")
    private Long dataProxima;

    @ApiModelProperty(value = "Percentual atual de massa gorda do aluno.", example = "31.34")
    private Double percentualMassaGorda;

    @ApiModelProperty(value = "Percentual atual de massa magra do aluno.", example = "68.66")
    private Double percentualMassaMagra;

    @ApiModelProperty(value = "Massa gorda inicial do aluno em kg (primeira avaliação).", example = "38.95")
    private Double massaGordaInicial;

    @ApiModelProperty(value = "Massa gorda atual do aluno em kg (última avaliação).", example = "37.99")
    private Double massaGordaAtual;

    @ApiModelProperty(value = "Percentual de evolução geral do aluno baseado nas avaliações.", example = "25.9")
    private Double evolucaoGeral;

    @ApiModelProperty(value = "Massa magra inicial do aluno em kg (primeira avaliação).", example = "40.99")
    private Double massaMagraInicial;

    @ApiModelProperty(value = "Massa magra atual do aluno em kg (última avaliação).", example = "45.87")
    private Double massaMagraAtual;

    @ApiModelProperty(value = "Nível atual de gordura corporal do aluno.", example = "23.22")
    private Double nivelGorduraCorporal;

    @ApiModelProperty(value = "Nível inicial de gordura corporal do aluno (primeira avaliação).", example = "31.66")
    private Double nivelGorduraCorporalInicial;

    @ApiModelProperty(value = "Quantidade de gordura corporal que ainda falta perder para atingir o objetivo.", example = "11.66")
    private Double nivelGorduraCorporalFaltando;

    @ApiModelProperty(value = "Lista de grupos musculares trabalhados pelo aluno com estatísticas de treino.")
    private List<TreinoGruposMuscularesDTO> grupos;

    @ApiModelProperty(value = "Histórico de peso e massa gorda do aluno ao longo das avaliações.")
    private List<PesoMassaGordaHistoricoDTO> pesos;

    @ApiModelProperty(value = "Histórico das medidas de dobras cutâneas do aluno.")
    private List<DobrasAlunoHistoricoDTO> dobras;

    public PerfilAlunoAvaliacaoDTO() {
    }

    public PerfilAlunoAvaliacaoDTO(Integer totalAvaliacoes, Integer periodoDias,
                                   Integer diasProximaAvaliacao, Long dataProxima, Double percentualMassaGorda, Double percentualMassaMagra, Double massaGordaInicial, Double massaGordaAtual, Double evolucaoGeral, Double massaMagraInicial, Double massaMagraAtual, Double nivelGorduraCorporal, Double nivelGorduraCorporalInicial,
                                   Double nivelGorduraCorporalFaltando, List<TreinoGruposMuscularesDTO> grupos,
            List<PesoMassaGordaHistoricoDTO> pesos,
            List<DobrasAlunoHistoricoDTO> dobras) {
        this.totalAvaliacoes = totalAvaliacoes;
        this.periodoDias = periodoDias;
        this.diasProximaAvaliacao = diasProximaAvaliacao;
        this.dataProxima = dataProxima;
        this.percentualMassaGorda = percentualMassaGorda;
        this.percentualMassaMagra = percentualMassaMagra;
        this.massaGordaInicial = massaGordaInicial;
        this.massaGordaAtual = massaGordaAtual;
        this.evolucaoGeral = evolucaoGeral;
        this.massaMagraInicial = massaMagraInicial;
        this.massaMagraAtual = massaMagraAtual;
        this.nivelGorduraCorporal = nivelGorduraCorporal;
        this.nivelGorduraCorporalInicial = nivelGorduraCorporalInicial;
        this.nivelGorduraCorporalFaltando = nivelGorduraCorporalFaltando;
        this.grupos = grupos;
        this.pesos = pesos;
        this.dobras = dobras;
    }

    public Integer getTotalAvaliacoes() {
        return totalAvaliacoes;
    }

    public void setTotalAvaliacoes(Integer totalAvaliacoes) {
        this.totalAvaliacoes = totalAvaliacoes;
    }

    public Integer getPeriodoDias() {
        return periodoDias;
    }

    public void setPeriodoDias(Integer periodoDias) {
        this.periodoDias = periodoDias;
    }

    public Integer getDiasProximaAvaliacao() {
        return diasProximaAvaliacao;
    }

    public void setDiasProximaAvaliacao(Integer diasProximaAvaliacao) {
        this.diasProximaAvaliacao = diasProximaAvaliacao;
    }

    public Long getDataProxima() {
        return dataProxima;
    }

    public void setDataProxima(Long dataProxima) {
        this.dataProxima = dataProxima;
    }

    public Double getPercentualMassaGorda() {
        return percentualMassaGorda;
    }

    public void setPercentualMassaGorda(Double percentualMassaGorda) {
        this.percentualMassaGorda = percentualMassaGorda;
    }

    public Double getPercentualMassaMagra() {
        return percentualMassaMagra;
    }

    public void setPercentualMassaMagra(Double percentualMassaMagra) {
        this.percentualMassaMagra = percentualMassaMagra;
    }

    public Double getMassaGordaInicial() {
        return massaGordaInicial;
    }

    public void setMassaGordaInicial(Double massaGordaInicial) {
        this.massaGordaInicial = massaGordaInicial;
    }

    public Double getMassaGordaAtual() {
        return massaGordaAtual;
    }

    public void setMassaGordaAtual(Double massaGordaAtual) {
        this.massaGordaAtual = massaGordaAtual;
    }

    public Double getEvolucaoGeral() {
        return evolucaoGeral;
    }

    public void setEvolucaoGeral(Double evolucaoGeral) {
        this.evolucaoGeral = evolucaoGeral;
    }

    public Double getMassaMagraInicial() {
        return massaMagraInicial;
    }

    public void setMassaMagraInicial(Double massaMagraInicial) {
        this.massaMagraInicial = massaMagraInicial;
    }

    public Double getMassaMagraAtual() {
        return massaMagraAtual;
    }

    public void setMassaMagraAtual(Double massaMagraAtual) {
        this.massaMagraAtual = massaMagraAtual;
    }

    public Double getNivelGorduraCorporal() {
        return nivelGorduraCorporal;
    }

    public void setNivelGorduraCorporal(Double nivelGorduraCorporal) {
        this.nivelGorduraCorporal = nivelGorduraCorporal;
    }

    public Double getNivelGorduraCorporalInicial() {
        return nivelGorduraCorporalInicial;
    }

    public void setNivelGorduraCorporalInicial(Double nivelGorduraCorporalInicial) {
        this.nivelGorduraCorporalInicial = nivelGorduraCorporalInicial;
    }

    public Double getNivelGorduraCorporalFaltando() {
        return nivelGorduraCorporalFaltando;
    }

    public void setNivelGorduraCorporalFaltando(Double nivelGorduraCorporalFaltando) {
        this.nivelGorduraCorporalFaltando = nivelGorduraCorporalFaltando;
    }

    public List<TreinoGruposMuscularesDTO> getGrupos() {
        return grupos;
    }

    public void setGrupos(List<TreinoGruposMuscularesDTO> grupos) {
        this.grupos = grupos;
    }

    public List<PesoMassaGordaHistoricoDTO> getPesos() {
        return pesos;
    }

    public void setPesos(List<PesoMassaGordaHistoricoDTO> pesos) {
        this.pesos = pesos;
    }

    public List<DobrasAlunoHistoricoDTO> getDobras() {
        return dobras;
    }

    public void setDobras(List<DobrasAlunoHistoricoDTO> dobras) {
        this.dobras = dobras;
    }
}
