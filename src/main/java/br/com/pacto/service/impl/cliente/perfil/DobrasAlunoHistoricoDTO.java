package br.com.pacto.service.impl.cliente.perfil;

import br.com.pacto.bean.avaliacao.DobrasEnum;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Histórico de medidas de dobras cutâneas do aluno em uma data específica de avaliação física.")
public class DobrasAlunoHistoricoDTO {

    @ApiModelProperty(
            value = "Tipo de dobra cutânea medida.\n\n" +
                    "Valores possíveis:\n" +
                    "- ABDOMINAL: Dobra abdominal\n" +
                    "- SUPRA_ILIACA: Dobra supra-ilíaca\n" +
                    "- PEITORAL: Dobra peitoral\n" +
                    "- TRICEPS: Dobra do tríceps\n" +
                    "- COXA_MEDIAL: Dobra da coxa medial\n" +
                    "- BICEPS: Dobra do bíceps\n" +
                    "- SUBESCAPULAR: Dobra subescapular\n" +
                    "- AXILARMEDIA: Dobra axilar média\n" +
                    "- SUPRA_ESPINHAL: Dobra supra-espinhal\n" +
                    "- PANTURRILHA: Dobra da panturrilha",
            example = "ABDOMINAL"
    )
    private DobrasEnum dobra;

    @ApiModelProperty(value = "Data da medição em formato timestamp.", example = "1649703163")
    private Long dia;

    @ApiModelProperty(value = "Valor da medida da dobra cutânea em milímetros.", example = "12")
    private Integer valor;

    public DobrasEnum getDobra() {
        return dobra;
    }

    public void setDobra(DobrasEnum dobra) {
        this.dobra = dobra;
    }

    public Long getDia() {
        return dia;
    }

    public void setDia(Long dia) {
        this.dia = dia;
    }

    public Integer getValor() {
        return valor;
    }

    public void setValor(Integer valor) {
        this.valor = valor;
    }
}
