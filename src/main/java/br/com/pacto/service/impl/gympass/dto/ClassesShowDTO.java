package br.com.pacto.service.impl.gympass.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Informações detalhadas de uma aula/classe disponível no GymPass")
public class ClassesShowDTO extends ClassesDTO {

    @ApiModelProperty(value = "Código identificador único da classe", example = "123")
    public Integer classeId;

    public ClassesShowDTO(ClassesDTO dto) {
        this.classeId = dto.getId();
        setName(dto.getName());
        setSlug(dto.getSlug());
        setDescription(dto.getDescription());
        setNotes(dto.getNotes());
        setBookable(dto.isBookable());
        setVisible(dto.isVisible());
        setProduct_id(dto.getProduct_id());
        setGym_id(dto.getGym_id());
        setReference(dto.getReference());
    }

    public Integer getClasseId() {
        return classeId;
    }

    public void setClasseId(Integer classeId) {
        this.classeId = classeId;
    }
}
