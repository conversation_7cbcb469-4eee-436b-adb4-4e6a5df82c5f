package br.com.pacto.service.impl.cliente.perfil;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Estatísticas de horários de treino do aluno no programa atual, mostrando a distribuição dos treinos por período do dia.")
public class HorariosQueTreinouProgramaAtual {

    @ApiModelProperty(value = "Número de treinos realizados no período da manhã (06:00 às 11:59).", example = "8")
    private Integer manha = 0;

    @ApiModelProperty(value = "Número de treinos realizados no período da tarde (12:00 às 17:59).", example = "12")
    private Integer tarde = 0;

    @ApiModelProperty(value = "Número de treinos realizados no período da noite (18:00 às 23:59).", example = "15")
    private Integer noite = 0;

    @ApiModelProperty(value = "Quantidade de dias em que o programa de treino esteve vigente.", example = "45")
    private Integer qtdDiasQueOProgTaVigente;

    public HorariosQueTreinouProgramaAtual() { }

    public HorariosQueTreinouProgramaAtual(Integer manha, Integer tarde, Integer noite, Integer qtdDiasQueOProgTaVigente) {
        this.manha = manha;
        this.tarde = tarde;
        this.noite = noite;
        this.qtdDiasQueOProgTaVigente = qtdDiasQueOProgTaVigente;
    }

    public Integer getManha() {
        return manha;
    }

    public void setManha(Integer manha) {
        this.manha = manha;
    }

    public Integer getTarde() {
        return tarde;
    }

    public void setTarde(Integer tarde) {
        this.tarde = tarde;
    }

    public Integer getNoite() {
        return noite;
    }

    public void setNoite(Integer noite) {
        this.noite = noite;
    }

    public Integer getQtdDiasQueOProgTaVigente() {
        return qtdDiasQueOProgTaVigente;
    }

    public void setQtdDiasQueOProgTaVigente(Integer qtdDiasQueOProgTaVigente) {
        this.qtdDiasQueOProgTaVigente = qtdDiasQueOProgTaVigente;
    }

}
