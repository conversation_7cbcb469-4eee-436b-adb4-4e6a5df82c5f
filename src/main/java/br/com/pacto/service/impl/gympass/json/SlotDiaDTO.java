package br.com.pacto.service.impl.gympass.json;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Informações de um slot de horário específico por dia")
public class SlotDiaDTO {

    @ApiModelProperty(value = "Código identificador da turma", example = "123")
    private Integer turma;

    @ApiModelProperty(value = "Código identificador do horário", example = "456")
    private Integer horario;

    @ApiModelProperty(value = "Código identificador da classe", example = "789")
    private Integer classe;

    @ApiModelProperty(value = "Código identificador do slot", example = "101112")
    private Integer slot;

    @ApiModelProperty(value = "Indica se o slot está ativo", example = "true")
    private Boolean ativo = true;

    @ApiModelProperty(value = "Referência externa do slot", example = "ZW-B-123")
    private String referencia;

    @ApiModelProperty(value = "Data do slot no formato string", example = "2025-06-20")
    private String dia;

    @ApiModelProperty(value = "Horário de início da aula", example = "10:00")
    private String inicio;

    public Integer getTurma() {
        return turma;
    }

    public void setTurma(Integer turma) {
        this.turma = turma;
    }

    public Integer getHorario() {
        return horario;
    }

    public void setHorario(Integer horario) {
        this.horario = horario;
    }

    public Integer getClasse() {
        return classe;
    }

    public void setClasse(Integer classe) {
        this.classe = classe;
    }

    public Integer getSlot() {
        return slot;
    }

    public void setSlot(Integer slot) {
        this.slot = slot;
    }

    public Boolean getAtivo() {
        return ativo;
    }

    public void setAtivo(Boolean ativo) {
        this.ativo = ativo;
    }

    public String getReferencia() {
        return referencia;
    }

    public void setReferencia(String referencia) {
        this.referencia = referencia;
    }

    public String getDia() {
        return dia;
    }

    public void setDia(String dia) {
        this.dia = dia;
    }

    public String getInicio() {
        return inicio;
    }

    public void setInicio(String inicio) {
        this.inicio = inicio;
    }
}
