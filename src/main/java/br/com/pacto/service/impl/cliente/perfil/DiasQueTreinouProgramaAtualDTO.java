package br.com.pacto.service.impl.cliente.perfil;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Estatísticas de frequência de treino do aluno por dia da semana no programa atual, mostrando a distribuição dos treinos realizados.")
public class DiasQueTreinouProgramaAtualDTO {

    @ApiModelProperty(value = "Quantidade de treinos realizados aos domingos no programa atual.", example = "2.5")
    private Double domingo = 0.0;

    @ApiModelProperty(value = "Quantidade de treinos realizados às segundas-feiras no programa atual.", example = "4.2")
    private Double segunda = 0.0;

    @ApiModelProperty(value = "Quantidade de treinos realizados às terças-feiras no programa atual.", example = "3.8")
    private Double terca = 0.0;

    @ApiModelProperty(value = "Quantidade de treinos realizados às quartas-feiras no programa atual.", example = "4.1")
    private Double quarta = 0.0;

    @ApiModelProperty(value = "Quantidade de treinos realizados às quintas-feiras no programa atual.", example = "3.9")
    private Double quinta = 0.0;

    @ApiModelProperty(value = "Quantidade de treinos realizados às sextas-feiras no programa atual.", example = "3.7")
    private Double sexta = 0.0;

    @ApiModelProperty(value = "Quantidade de treinos realizados aos sábados no programa atual.", example = "2.1")
    private Double sabado = 0.0;

    @ApiModelProperty(value = "Número total de treinos executados pelo aluno no período do programa atual.", example = "24")
    private Integer treinosExecutadosPeriodo;

    public DiasQueTreinouProgramaAtualDTO() { }

    public DiasQueTreinouProgramaAtualDTO(Double domingo, Double segunda, Double terca, Double quarta, Double quinta, Double sexta, Double sabado, Integer treinosExecutadosPeriodo) {
        this.domingo = domingo;
        this.segunda = segunda;
        this.terca = terca;
        this.quarta = quarta;
        this.quinta = quinta;
        this.sexta = sexta;
        this.sabado = sabado;
        this.treinosExecutadosPeriodo = treinosExecutadosPeriodo;
    }

    public Double getDomingo() {
        return domingo;
    }

    public void setDomingo(Double domingo) {
        this.domingo = domingo;
    }

    public Double getSegunda() {
        return segunda;
    }

    public void setSegunda(Double segunda) {
        this.segunda = segunda;
    }

    public Double getTerca() {
        return terca;
    }

    public void setTerca(Double terca) {
        this.terca = terca;
    }

    public Double getQuarta() {
        return quarta;
    }

    public void setQuarta(Double quarta) {
        this.quarta = quarta;
    }

    public Double getQuinta() {
        return quinta;
    }

    public void setQuinta(Double quinta) {
        this.quinta = quinta;
    }

    public Double getSexta() {
        return sexta;
    }

    public void setSexta(Double sexta) {
        this.sexta = sexta;
    }

    public Double getSabado() {
        return sabado;
    }

    public void setSabado(Double sabado) {
        this.sabado = sabado;
    }

    public Integer getTreinosExecutadosPeriodo() {
        return treinosExecutadosPeriodo;
    }

    public void setTreinosExecutadosPeriodo(Integer treinosExecutadosPeriodo) {
        this.treinosExecutadosPeriodo = treinosExecutadosPeriodo;
    }

}
