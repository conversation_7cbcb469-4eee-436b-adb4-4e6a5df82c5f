package br.com.pacto.service.impl.cliente.perfil;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Histórico de peso e massa gorda do aluno em uma data específica de avaliação física.")
public class PesoMassaGordaHistoricoDTO {

    @ApiModelProperty(value = "Data da medição em formato timestamp.", example = "1649703163")
    private Long dia;

    @ApiModelProperty(value = "Peso do aluno na data da medição, em quilogramas.", example = "68.9")
    private Double peso;

    @ApiModelProperty(value = "Massa gorda do aluno na data da medição, em quilogramas.", example = "30.8")
    private Double massaGorda;

    public PesoMassaGordaHistoricoDTO() {

    }
    public PesoMassaGordaHistoricoDTO(Long dia, Double peso, Double massaGorda) {
        this.dia = dia;
        this.peso = peso;
        this.massaGorda = massaGorda;
    }

    public Long getDia() {
        return dia;
    }

    public void setDia(Long dia) {
        this.dia = dia;
    }

    public Double getPeso() {
        return peso;
    }

    public void setPeso(Double peso) {
        this.peso = peso;
    }

    public Double getMassaGorda() {
        return massaGorda;
    }

    public void setMassaGorda(Double massaGorda) {
        this.massaGorda = massaGorda;
    }
}
