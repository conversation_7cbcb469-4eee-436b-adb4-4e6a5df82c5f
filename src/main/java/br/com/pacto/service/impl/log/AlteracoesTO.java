package br.com.pacto.service.impl.log;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Transfer Object para representação de uma alteração específica em um campo, contendo o nome do campo e os valores anterior e atual")
public class AlteracoesTO {

    @ApiModelProperty(value = "Nome do campo que foi alterado", example = "nome")
    private String campo;

    @ApiModelProperty(value = "Valor anterior do campo antes da alteração. Exibe 'vazio' quando o valor era nulo", example = "João Silva")
    private String valorAnterior;

    @ApiModelProperty(value = "Novo valor do campo após a alteração. Exibe 'vazio' quando o valor é nulo", example = "João <PERSON>")
    private String valorAlterado;

    public AlteracoesTO() {
    }

    public AlteracoesTO(String campo, String valorAnterior, String valorAlterado) {
        this.campo = campo;
        this.valorAnterior = valorAnterior == null || valorAnterior.equals("null") ? "vazio" : valorAnterior;
        this.valorAlterado = valorAlterado == null || valorAlterado.equals("null") ? "vazio" : valorAlterado;
    }

    public String getCampo() {
        return campo;
    }

    public void setCampo(String campo) {
        this.campo = campo;
    }

    public String getValorAnterior() {
        return valorAnterior;
    }

    public void setValorAnterior(String valorAnterior) {
        this.valorAnterior = valorAnterior;
    }

    public String getValorAlterado() {
        return valorAlterado;
    }

    public void setValorAlterado(String valorAlterado) {
        this.valorAlterado = valorAlterado;
    }
}
