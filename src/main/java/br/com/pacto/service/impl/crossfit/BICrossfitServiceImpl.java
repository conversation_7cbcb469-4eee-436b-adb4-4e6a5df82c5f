package br.com.pacto.service.impl.crossfit;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ExecuteRequestHttpService;
import br.com.pacto.bean.aula.AulaAluno;
import br.com.pacto.bean.aula.AulaDia;
import br.com.pacto.bean.aula.AulaHorario;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.professor.Professor<PERSON>int<PERSON>;
import br.com.pacto.bean.wod.Wod;
import br.com.pacto.bean.wod.WodResponseTO;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.controller.json.crossfit.RankingJSON;
import br.com.pacto.controller.json.gestao.*;
import br.com.pacto.dao.intf.agenda.AgendamentoDao;
import br.com.pacto.dao.intf.aula.AulaAlunoDao;
import br.com.pacto.dao.intf.wod.ScoreTreinoDao;
import br.com.pacto.dao.intf.wod.WodDao;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.crossfit.BICrossfitService;
import br.com.pacto.service.intf.professor.ProfessorSinteticoService;
import br.com.pacto.service.intf.wod.WodService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.enumeradores.DiasSemana;
import br.com.pacto.util.impl.Ordenacao;
import org.json.JSONArray;
import org.json.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.NameValuePair;
import org.apache.http.client.ResponseHandler;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.BasicResponseHandler;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.message.BasicNameValuePair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.sql.ResultSet;
import java.util.*;

@Service
public class BICrossfitServiceImpl implements BICrossfitService {

    @Autowired
    private SessaoService sessaoService;
    @Autowired
    private WodDao wodDao;
    @Autowired
    private WodService ws;
    @Autowired
    private ScoreTreinoDao scoreTreinoDao;
    @Autowired
    private AulaAlunoDao aulaAlunoDao;
    @Autowired
    private AgendamentoDao agendamentoDao;
    @Autowired
    private ProfessorSinteticoService professorSinteticoService;

    private static final int MAXIMO_CLIENTE_SINTETICO_CONSULTAR = 50;

    @Override
    public BICrossfitDTO gerarBI(Integer mes, Integer ano, Integer idProfessor, Integer empresa) throws ServiceException {
        try {
            Date primeirodia = Calendario.getInstance(ano, mes, 1).getTime();
            Date ultimoDia = Uteis.obterUltimoDiaMesUltimaHora(primeirodia);
            BICrossfitDTO bi = new BICrossfitDTO();
            String chave = sessaoService.getUsuarioAtual().getChave();

            List<AulaAluno> alunos = alunosBi(chave, idProfessor, mes, ano, empresa, primeirodia, ultimoDia, null, null, false);

            bi.setNumeroDealunos(nrAlunos(chave, idProfessor, empresa));

            String diaString = Uteis.getDataAplicandoFormatacao(primeirodia, "yyyy-MM-dd");
            String ultimodiaString = Uteis.getDataAplicandoFormatacao(ultimoDia, "yyyy-MM-dd");

            bi.setNumeroAgendamentos(alunos.size());
            bi.setResultadosLancados(nrResultadosLancados(chave, diaString, ultimodiaString, idProfessor, null, empresa));

            OcupacaoDTO ocupacao = new OcupacaoDTO();
            ocupacao.setSegunda(new DiaOcupacaoDTO(ocupacaoDiaSemana(alunos, DiasSemana.SEGUNDA_FEIRA)));
            ocupacao.setTerca(new DiaOcupacaoDTO(ocupacaoDiaSemana(alunos, DiasSemana.TERCA_FEIRA)));
            ocupacao.setQuarta(new DiaOcupacaoDTO(ocupacaoDiaSemana(alunos, DiasSemana.QUARTA_FEIRA)));
            ocupacao.setQuinta(new DiaOcupacaoDTO(ocupacaoDiaSemana(alunos, DiasSemana.QUINTA_FEIRA)));
            ocupacao.setSexta(new DiaOcupacaoDTO(ocupacaoDiaSemana(alunos, DiasSemana.SEXTA_FEIRA)));
            ocupacao.setSabado(new DiaOcupacaoDTO(ocupacaoDiaSemana(alunos, DiasSemana.SABADO)));
            bi.setOcupacao(ocupacao);

            bi.setFrequenciaPorProfessor(ocupacaoProfessor(alunos));

            return bi;
        } catch (Exception e) {
            throw new ServiceException(e);
        }

    }

    private List<AulaAluno> alunosBi(String ctx, Integer idProfessor, Integer mes, Integer ano,
                                     Integer empresa,
                                     Date primeirodia, Date ultimoDia, Integer max, Integer index, Boolean count) throws Exception{
        if(SuperControle.independente(ctx)){
            String hql = "select obj from AulaAluno obj where obj.dia between :inicio and :fim ";
            if (!UteisValidacao.emptyNumber(idProfessor)) {
                hql += "and professor.codigo = :idProfessor";
            }
            Map<String, Object> params = new HashMap<String, Object>();
            params.put("inicio", Calendario.getDataComHoraZerada(primeirodia));
            params.put("fim", Calendario.getDataComHora(ultimoDia, "23:59"));
            if (!UteisValidacao.emptyNumber(idProfessor)) {
                params.put("idProfessor", idProfessor);
            }
            return aulaAlunoDao.findByParam(ctx, hql, params);
        }else{
            JSONArray array = agendadosCrossfitAulaCheia(ctx, mes, ano, empresa, idProfessor,null, null, null, max, index, count);
            return convertArrayToAluno(array, ctx);
        }
    }

    private List<AulaAluno> convertArrayToAluno(JSONArray array, String ctx) throws Exception{
        Map<Integer, ProfessorSintetico> professores = new HashMap<>();
        List<AulaAluno> alunosBi = new ArrayList<>();
        for(int i = 0; i < array.length(); i++){
            JSONObject jsonObject = array.getJSONObject(i);
            ProfessorSintetico professorSintetico = obterProfessor(ctx, jsonObject.getInt("professor"), professores);
            if(professorSintetico != null){
                AulaAluno aluno = new AulaAluno();
                aluno.setCliente(new ClienteSintetico());
                aluno.getCliente().setNome(jsonObject.getString("nome"));
                aluno.getCliente().setMatricula(jsonObject.getInt("matricula"));
                aluno.getCliente().setCodigoCliente(jsonObject.getInt("cliente"));
                aluno.getCliente().setSituacao(jsonObject.getString("situacao"));
                aluno.setModalidade(jsonObject.getString("modalidade"));
                aluno.setProfessor(professorSintetico);
                Date inicio = Uteis.getDate(jsonObject.getString("dia") + " " + jsonObject.getString("inicio"), "dd/MM/yyyy HH:mm");
                Date fim = Uteis.getDate(jsonObject.getString("dia") + " " + jsonObject.getString("fim"), "dd/MM/yyyy HH:mm");

                aluno.setDia(Uteis.getDate(jsonObject.getString("dia"), "dd/MM/yyyy"));

                AulaDia aula = new AulaDia();
                aula.setInicio(inicio);
                aula.setFim(fim);
                aula.setProfessor(professorSintetico);
                aluno.setAula(aula);
                AulaHorario aulaHorario = new AulaHorario();
                aulaHorario.setInicio(jsonObject.getString("inicio"));
                aulaHorario.setFim(jsonObject.getString("fim"));
                aluno.setHorario(aulaHorario);
                alunosBi.add(aluno);
            }
        }
        return alunosBi;
    }

    private ProfessorSintetico obterProfessor(String ctx, Integer codigo, Map<Integer, ProfessorSintetico> professores) throws Exception{
        ProfessorSintetico professorSintetico = professores.get(codigo);
        if(professorSintetico == null){
            professorSintetico = professorSinteticoService.consultarPorCodigoColaborador(ctx, codigo);
            professores.put(codigo, professorSintetico);
        }
        return professorSintetico;
    }

    private JSONArray agendadosCrossfitAulaCheia(String ctx, Integer mes, Integer ano, Integer empresa, Integer professor, Integer diaDaSemana, String horaInicio, String horaFim, Integer max, Integer index, Boolean count) throws Exception{
        final String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg);
        CloseableHttpClient client = ExecuteRequestHttpService.createConnectorCloseable();
        HttpPost httpPost = new HttpPost(url + "/prest/aulacheia/professor-alunos-crossfit");

        List<NameValuePair> params = new ArrayList<>();
        params.add(new BasicNameValuePair("chave", ctx));
        params.add(new BasicNameValuePair("mes", mes.toString()));
        params.add(new BasicNameValuePair("ano", ano.toString()));
        params.add(new BasicNameValuePair("empresa", empresa.toString()));
        if(!UteisValidacao.emptyNumber(professor)){
            params.add(new BasicNameValuePair("professor", professor.toString()));
        }
        if(!UteisValidacao.emptyNumber(diaDaSemana)){
            params.add(new BasicNameValuePair("diaDaSemana", diaDaSemana.toString()));
        }
        if(!UteisValidacao.emptyString(horaInicio)){
            params.add(new BasicNameValuePair("horaInicio", horaInicio));
        }
        if(!UteisValidacao.emptyString(horaFim)){
            params.add(new BasicNameValuePair("horaFim", horaFim));
        }
        if(!UteisValidacao.emptyNumber(max)){
            params.add(new BasicNameValuePair("max", max.toString()));
        }
        if(index != null){
            params.add(new BasicNameValuePair("index", index.toString()));
        }
        if(count != null) {
            params.add(new BasicNameValuePair("count", count.toString()));
        }
        httpPost.setEntity(new UrlEncodedFormEntity(params));
        CloseableHttpResponse response = client.execute(httpPost);
        ResponseHandler<String> handler = new BasicResponseHandler();
        String body = handler.handleResponse(response);
        client.close();
        return new JSONArray(body);
    }

    private List<FrequenciaProfessorDTO> ocupacaoProfessor(List<AulaAluno> alunos){
        Map<Integer, Integer> count = new HashMap<Integer, Integer>();
        Map<Integer, ProfessorSintetico> professores = new HashMap<Integer, ProfessorSintetico>();

        for (AulaAluno a : alunos) {
            Integer alunosProf = count.get(a.getProfessor().getCodigoColaborador());
            if(alunosProf == null){
                count.put(a.getProfessor().getCodigoColaborador(), 1);
                professores.put(a.getProfessor().getCodigoColaborador(), a.getProfessor());
            }else{
                count.put(a.getProfessor().getCodigoColaborador(), alunosProf + 1);
            }
        }
        List<FrequenciaProfessorDTO> frequencias = new ArrayList<FrequenciaProfessorDTO>();
        for(Integer k : count.keySet()){
            ProfessorSintetico professorSintetico = professores.get(k);
            frequencias.add(new FrequenciaProfessorDTO(professorSintetico.getCodigoColaborador(),
                    Uteis.getPrimeiroNome(professorSintetico.getNome()),
                    "",
                    count.get(k)));
        }
        return frequencias;
    }

    private Map<String, Integer> ocupacaoDiaSemana(List<AulaAluno> alunos, DiasSemana diasSemana) throws Exception {
        Map<String, Integer> periodoDia = new HashMap<String, Integer>();
        periodoDia.put("manha", 0);
        periodoDia.put("tarde", 0);
        periodoDia.put("noite", 0);
        for (AulaAluno a : alunos) {
            if (diasSemana.equals_ISO8601(a.getDia())) {
                String inicio = a.getHorario().getInicio();
                Integer inicioInt = Integer.parseInt(inicio.replace(":", ""));
                if (inicioInt < 1201) {
                    periodoDia.put("manha", periodoDia.get("manha") + 1);
                } else if (inicioInt < 1801) {
                    periodoDia.put("tarde", periodoDia.get("tarde") + 1);
                } else {
                    periodoDia.put("noite", periodoDia.get("noite") + 1);
                }
            }
        }

        return periodoDia;

    }

    public List<RankingGeralDTO> rankingGeral(Date inicio, Date fim, HttpServletRequest request, Integer empresaId) throws Exception {
        String chave = sessaoService.getUsuarioAtual().getChave();
        List<Date> diasEntreDatas = Uteis.getDiasEntreDatas(inicio, fim);
        Map<Integer, RankingGeralDTO> rankingMap = new HashMap<Integer, RankingGeralDTO>();
        Map<Integer, Integer> nrVezes = new HashMap<Integer, Integer>();

        for (Date d : diasEntreDatas) {
            List<WodResponseTO> wodResponseTOS = ws.listarTodosWods(d, empresaId);
            for (WodResponseTO w : wodResponseTOS) {
                Wod wod = ws.obterPorId(chave, w.getId());

                if (wod.getTipoWodTabela() != null) {
                    ws.atualizarRanking(chave, wod.getCodigo(), wod.getTipoWodTabela().getOrderBy());
                } else {
                    ws.atualizarRanking(chave, wod.getCodigo(), "");
                }

                List<RankingJSON> ranking = ws.ranking(chave, w.getId(), request);
                for (RankingJSON r : ranking) {

                    Integer score = 100 - (r.getPosicao() - 1);
                    score = score < 0 ? 0 : score;

                    RankingGeralDTO ra = rankingMap.get(r.getCodUsuario());
                    if (ra == null) {
                        nrVezes.put(r.getCodUsuario(), 1);
                        ra = new RankingGeralDTO();
                        ra.setAlunoId(r.getCodUsuario());
                        ra.setNome(r.getNome());
                        ra.setAproveitamento(score.doubleValue());
                        rankingMap.put(r.getCodUsuario(), ra);
                    } else {
                        nrVezes.put(r.getCodUsuario(), nrVezes.get(r.getCodUsuario()) + 1);
                        ra.setAproveitamento(score.doubleValue());
                    }
                }
            }

        }
        List<RankingGeralDTO> rankingGeral = new ArrayList<RankingGeralDTO>();
        for (Integer cod : nrVezes.keySet()) {
            RankingGeralDTO ra = rankingMap.get(cod);
            ra.setAproveitamento(Uteis.arredondarForcando2CasasDecimais(ra.getAproveitamento() / (nrVezes.get(cod) * 1.0)));
            rankingGeral.add(ra);
        }
        rankingGeral = Ordenacao.ordenarLista(rankingGeral, "aproveitamento");
        Collections.reverse(rankingGeral);
        return rankingGeral;
    }

    private Integer nrAlunos(String ctx, Integer idProfessor, Integer empresa) throws Exception {
        Integer professorCodigo = idProfessor;
        if(!SuperControle.independente(ctx) && !UteisValidacao.emptyNumber(idProfessor)){
            ProfessorSintetico professorSintetico = professorSinteticoService.consultarPorCodigoColaborador(ctx, idProfessor);
            professorCodigo = professorSintetico.getCodigo();
        }
        String sql = "select count(codigo) as nr from ClienteSintetico where situacao = 'AT' ";
        sql += " and empresa = " + empresa;
        if (!UteisValidacao.emptyNumber(idProfessor)) {
            sql += " and professorsintetico_codigo = " + professorCodigo;
        }
        try (ResultSet rs = wodDao.createStatement(ctx, sql)) {
            return rs.next() ? rs.getInt("nr") : 0;
        }
    }

    private Integer nrResultadosLancados(String ctx, String dia, String ultimodia, Integer professorId, String nomeAluno, Integer empresaId) throws Exception {

        Integer professorCodigo = professorId;
        if(!SuperControle.independente(ctx) && !UteisValidacao.emptyNumber(professorId)){
            ProfessorSintetico professorSintetico = professorSinteticoService.consultarPorCodigoColaborador(ctx, professorId);
            professorCodigo = professorSintetico.getCodigo();
        }

        String sql = "select count(score.codigo) as nr from scoretreino score" + " inner join usuario us on us.codigo = score.usuario_codigo inner join clientesintetico cs on cs.codigo = us.cliente_codigo" +
                " where score.lancamento between '" + dia + "' and '" + ultimodia + "'";
        if (!UteisValidacao.emptyNumber(empresaId)) {
            sql += " and cs.empresa = " + empresaId;
        }
        if (!UteisValidacao.emptyNumber(professorId)) {
            sql += " and cs.professorsintetico_codigo = " + professorCodigo;
        }
        if (!StringUtils.isBlank(nomeAluno)) {
            sql += " and upper(cs.nome) like '%" + nomeAluno.toUpperCase() + "%' ";
        }

        try (ResultSet rs = scoreTreinoDao.createStatement(ctx, sql)) {
            return rs.next() ? rs.getInt("nr") : 0;
        }
    }

    @Override
    public List<DetalhesIndicadorBICrossfitDTO> alunosComResultadosLancados(FiltrosBiCrossfitJSON filtrosBiCrossfitJSON, Integer empresaId, PaginadorDTO paginadorDTO) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            int maxResults = MAXIMO_CLIENTE_SINTETICO_CONSULTAR;
            int indiceInicial = 0;

            if (paginadorDTO.getSort() == null || paginadorDTO.getSort().contains("dataLancamento")) {
                paginadorDTO.setSort(paginadorDTO.getSort() == null ? "lancamento,ASC" : paginadorDTO.getSort().replace("dataLancamento", "lancamento"));
            }

            Date dia = Calendario.getInstance(filtrosBiCrossfitJSON.getAno(), filtrosBiCrossfitJSON.getMes(), 1).getTime();
            Date ultimaHora = Uteis.obterUltimoDiaMesUltimaHora(dia);

            String diaString = Uteis.getDataAplicandoFormatacao(Uteis.getDataComHoraZerada(dia), "yyyy-MM-dd");
            String ultimodiaString = Uteis.getDataAplicandoFormatacao(ultimaHora, "yyyy-MM-dd");

            if (paginadorDTO != null) {
                maxResults = paginadorDTO.getSize() == null ? 50 : paginadorDTO.getSize().intValue();
                indiceInicial = paginadorDTO.getPage() == null || paginadorDTO.getSize() == null ? indiceInicial : paginadorDTO.getPage().intValue() * paginadorDTO.getSize().intValue();
            }
            StringBuilder sql = new StringBuilder();
            sql.append("select (cs.matricula) as matricula, (cs.nome) as nome, (cs.situacao) as situacao, (obj.lancamento) as lancamento from ScoreTreino obj ");
            sql.append(" inner join Usuario us ON us.codigo = obj.usuario_codigo ");
            sql.append(" inner join ClienteSintetico cs ON cs.codigo = us.cliente_codigo ");
            sql.append(" where obj.lancamento between '").append(diaString).append("' and '").append(ultimodiaString).append("'");
            if (!UteisValidacao.emptyNumber(empresaId)) {
                sql.append(" and cs.empresa = ").append(empresaId);
            }
            if (!UteisValidacao.emptyNumber(filtrosBiCrossfitJSON.getIdProfessor())) {
                sql.append(" and cs.professorsintetico_codigo = ").append(filtrosBiCrossfitJSON.getIdProfessor());
            }
            if (!StringUtils.isBlank(filtrosBiCrossfitJSON.getFiltro()) && filtrosBiCrossfitJSON.isFiltroNome()) {
                sql.append(" and upper(cs.nome) like '%").append(filtrosBiCrossfitJSON.getFiltro().toUpperCase()).append("%' ");
            }
            if (paginadorDTO != null) {
                paginadorDTO.setQuantidadeTotalElementos(Long.valueOf(nrResultadosLancados(ctx, diaString, ultimodiaString, filtrosBiCrossfitJSON.getIdProfessor(), filtrosBiCrossfitJSON.getFiltro(), empresaId)));
                sql.append(paginadorDTO.getSQLOrderByUse());
            }

            sql.append(" LIMIT ").append(maxResults);
            sql.append(" OFFSET ").append(indiceInicial);
            List<DetalhesIndicadorBICrossfitDTO> ret;
            try (ResultSet rs = scoreTreinoDao.createStatement(ctx, sql.toString())) {

                paginadorDTO.setSize((long) maxResults);
                paginadorDTO.setPage((long) indiceInicial);

                ret = new ArrayList<>();
                while (rs.next()) {
                    DetalhesIndicadorBICrossfitDTO resultado = new DetalhesIndicadorBICrossfitDTO();
                    resultado.setMatricula(rs.getInt("matricula"));
                    resultado.setNome(rs.getString("nome"));
                    resultado.setSituacaoAluno(rs.getString("situacao"));
                    resultado.setdia(rs.getDate("lancamento"));

                    ret.add(resultado);
                }
            }
            return ret;
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    private Integer nrAgendamentos(String ctx, String dia, String ultimodia, Integer idProfessor, String nomeAluno) throws Exception {


        String sql = "select count(aulaaluno.codigo) as nr from aulaaluno inner join clientesintetico cs on cs.codigo = aulaaluno.cliente_codigo " +
                "where aulaaluno.dia between '" + dia + "' and '" + ultimodia + "' ";
        String and;
        String sqlQuery;

        if (idProfessor > 0) {
            and = " and aulaaluno.professor_codigo = " + idProfessor;
            sqlQuery = sql + and;
        } else {
            sqlQuery = sql;
        }
        if (!StringUtils.isBlank(nomeAluno)) {
            sqlQuery += " and upper(cs.nome) like '%" + nomeAluno.toUpperCase() + "%'";
        }

        try (ResultSet rs = agendamentoDao.createStatement(ctx, sqlQuery)) {
            return rs.next() ? rs.getInt("nr") : 0;
        }
    }

    @Override
    public List<AgendamentosAlunosDTO> alunosPorAgendamentos(FiltrosBiCrossfitJSON filtrosBiCrossfitJSON, Integer empresaId, PaginadorDTO paginadorDTO) throws ServiceException {
        try {
            int maxResults = MAXIMO_CLIENTE_SINTETICO_CONSULTAR;
            int indiceInicial = 0;
            if (paginadorDTO != null) {
                maxResults = paginadorDTO.getSize() == null ? MAXIMO_CLIENTE_SINTETICO_CONSULTAR : paginadorDTO.getSize().intValue();
                indiceInicial = paginadorDTO.getPage() == null || paginadorDTO.getSize() == null ? indiceInicial : paginadorDTO.getPage().intValue() * paginadorDTO.getSize().intValue();
            }

            Date primeirodia = Calendario.getInstance(filtrosBiCrossfitJSON.getAno(), filtrosBiCrossfitJSON.getMes(), 1).getTime();
            Date ultimoDia = Uteis.obterUltimoDiaMesUltimaHora(primeirodia);
            String chave = sessaoService.getUsuarioAtual().getChave();

            List<AulaAluno> alunos = alunosBi(chave, filtrosBiCrossfitJSON.getIdProfessor(), filtrosBiCrossfitJSON.getMes(), filtrosBiCrossfitJSON.getAno(), empresaId, primeirodia, ultimoDia, maxResults, indiceInicial, false);

            List<AgendamentosAlunosDTO> ret = new ArrayList<>();
            for (AulaAluno aluno : alunos) {
                AgendamentosAlunosDTO agendamentosAlunosDTO = new AgendamentosAlunosDTO();

                agendamentosAlunosDTO.setMatricula(aluno.getCliente().getMatricula());
                agendamentosAlunosDTO.setNome(aluno.getCliente().getNome());
                agendamentosAlunosDTO.setModalidade(aluno.getModalidade());
                agendamentosAlunosDTO.setDia(aluno.getDia());

                ret.add(agendamentosAlunosDTO);
            }
            JSONArray arrayQuantidade = agendadosCrossfitAulaCheia(chave, filtrosBiCrossfitJSON.getMes(), filtrosBiCrossfitJSON.getAno(), empresaId, filtrosBiCrossfitJSON.getIdProfessor(), null, null, null, maxResults, indiceInicial, true);
            JSONObject jsonObject = arrayQuantidade.getJSONObject(0);
            paginadorDTO.setQuantidadeTotalElementos((long) jsonObject.getInt("total"));
            paginadorDTO.setSize((long) maxResults);
            paginadorDTO.setPage((long) indiceInicial);

            return ret;
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    @Override
    public List<DetalhesIndicadorBICrossfitDTO> alunosFrequentaAulaProfessor(Integer empresa, FiltrosBiCrossfitJSON filtrosBiCrossfitJSON, PaginadorDTO paginadorDTO) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();

            int maxResults = MAXIMO_CLIENTE_SINTETICO_CONSULTAR;
            int indiceInicial = 0;
            Integer diaDaSemana = null;
            if (paginadorDTO != null) {
                maxResults = paginadorDTO.getSize() == null ? MAXIMO_CLIENTE_SINTETICO_CONSULTAR : paginadorDTO.getSize().intValue();
                indiceInicial = paginadorDTO.getPage() == null || paginadorDTO.getSize() == null ? indiceInicial : paginadorDTO.getPage().intValue() * paginadorDTO.getSize().intValue();
            }

            Date dia = Calendario.getInstance(filtrosBiCrossfitJSON.getAno(), filtrosBiCrossfitJSON.getMes(), 1).getTime();
            Date ultimaHora = Uteis.obterUltimoDiaMesUltimaHora(dia);
            String horaInicio = "";
            String horaFim = "";

            if (!StringUtils.isBlank(filtrosBiCrossfitJSON.getTurno())) {
                if (filtrosBiCrossfitJSON.getTurno().equals("manha")) {
                    horaInicio = "00:00";
                    horaFim = "12:00";
                } else if (filtrosBiCrossfitJSON.getTurno().equals("tarde")) {
                    horaInicio = "12:01";
                    horaFim = "18:00";
                } else if (filtrosBiCrossfitJSON.getTurno().equals("noite")) {
                    horaInicio = "18:01";
                    horaFim = "23:59";
                }
            }
            List<AulaAluno> aulaAlunos = new ArrayList<>();
            if(SuperControle.independente(ctx)){
                StringBuilder hql = new StringBuilder();
                StringBuilder where = new StringBuilder();

                hql.append("SELECT obj FROM AulaAluno obj ");
                where.append("WHERE obj.dia between :inicio AND :fim ");
                if (!UteisValidacao.emptyNumber(filtrosBiCrossfitJSON.getIdProfessor())) {
                    where.append("AND professor.codigo = :idProfessor ");
                }
                if (filtrosBiCrossfitJSON.isFiltroNome() && !StringUtils.isBlank(filtrosBiCrossfitJSON.getFiltro())) {
                    where.append("AND upper(cliente.nome) like :nome ");
                }
                if (!StringUtils.isBlank(filtrosBiCrossfitJSON.getTurno())) {
                    where.append("AND extract(dow from obj.dia) = :diaSemana ");
                    where.append("AND cast(obj.horario.inicio as time) between ").append("cast('").append(horaInicio).append("' as time)").append(" AND ").append("cast('").append(horaFim).append("' as time)");
                }
                hql.append(where.toString());
                Map<String, Object> params = new HashMap<>();
                params.put("inicio", Calendario.getDataComHoraZerada(dia));
                params.put("fim", Calendario.getDataComHora(ultimaHora, "23:59"));
                if (!UteisValidacao.emptyNumber(filtrosBiCrossfitJSON.getIdProfessor())) {
                    params.put("idProfessor", filtrosBiCrossfitJSON.getIdProfessor());
                }
                if (!StringUtils.isBlank(filtrosBiCrossfitJSON.getFiltro()) && filtrosBiCrossfitJSON.isFiltroNome()) {
                    params.put("nome", "%" + filtrosBiCrossfitJSON.getFiltro().toUpperCase() + "%");
                }
                if (!StringUtils.isBlank(filtrosBiCrossfitJSON.getTurno())) {
                    params.put("diaSemana", filtrosBiCrossfitJSON.getDiasSemana().ordinal());
                }
                aulaAlunos = aulaAlunoDao.findByParam(ctx, hql.toString(), params, maxResults, indiceInicial);
            }else{
                if(filtrosBiCrossfitJSON.getDiasSemana() != null && !UteisValidacao.emptyNumber(filtrosBiCrossfitJSON.getDiasSemana().getNumeral())) {
                    diaDaSemana = filtrosBiCrossfitJSON.getDiasSemana().getNumeral()-1;
                }
                JSONArray array = agendadosCrossfitAulaCheia(ctx, filtrosBiCrossfitJSON.getMes(),
                        filtrosBiCrossfitJSON.getAno(), empresa, filtrosBiCrossfitJSON.getIdProfessor(), diaDaSemana, horaInicio, horaFim, maxResults, indiceInicial, false);
                aulaAlunos = convertArrayToAluno(array, ctx);
            }
            List<DetalhesIndicadorBICrossfitDTO> alunosFrequentados = new ArrayList<>();
            for (AulaAluno aulaAluno : aulaAlunos) {
                DetalhesIndicadorBICrossfitDTO alunoFrequentado = new DetalhesIndicadorBICrossfitDTO();
                alunoFrequentado.setMatricula(aulaAluno.getCliente().getMatricula());
                alunoFrequentado.setNome(aulaAluno.getCliente().getNome());
                alunoFrequentado.setSituacaoAluno(aulaAluno.getCliente().getSituacao());
                alunoFrequentado.setdia(aulaAluno.getDia());
                alunosFrequentados.add(alunoFrequentado);
            }
            if(!SuperControle.independente(ctx)){
                JSONArray arrayQuantidade = agendadosCrossfitAulaCheia(ctx, filtrosBiCrossfitJSON.getMes(),
                        filtrosBiCrossfitJSON.getAno(), empresa, filtrosBiCrossfitJSON.getIdProfessor(), diaDaSemana, horaInicio, horaFim, null, null, true);
                JSONObject jsonObject = arrayQuantidade.getJSONObject(0);
                paginadorDTO.setQuantidadeTotalElementos((long) jsonObject.getInt("total"));
                paginadorDTO.setSize((long) maxResults);
                paginadorDTO.setPage((long) indiceInicial);
            }


            return alunosFrequentados;
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

}
