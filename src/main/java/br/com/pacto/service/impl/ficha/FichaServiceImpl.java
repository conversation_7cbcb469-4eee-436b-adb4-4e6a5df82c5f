/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.impl.ficha;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.impl.EntityManagerService;
import br.com.pacto.base.util.ExecuteRequestHttpService;
import br.com.pacto.bean.atividade.*;
import br.com.pacto.bean.configuracoes.ConfiguracaoSistema;
import br.com.pacto.bean.configuracoes.ConfiguracoesEnum;
import br.com.pacto.bean.ficha.*;
import br.com.pacto.bean.nivel.Nivel;
import br.com.pacto.bean.programa.ProgramaTreino;
import br.com.pacto.bean.programa.ProgramaTreinoFicha;
import br.com.pacto.bean.programa.TipoExecucaoEnum;
import br.com.pacto.bean.serie.Serie;
import br.com.pacto.bean.serie.SerieEndpointTO;
import br.com.pacto.bean.sincronizacao.TipoClassSincronizarEnum;
import br.com.pacto.bean.sincronizacao.TipoRevisaoEnum;
import br.com.pacto.bean.usuario.TipoUsuarioEnum;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.atividade.FiltroFichaPredefinidaJSON;
import br.com.pacto.controller.json.atividade.write.AtividadeFichaAjusteWriteJSON;
import br.com.pacto.controller.json.atividade.write.AtividadeFichaWriteJSON;
import br.com.pacto.controller.json.atividade.write.SerieWriteJSON;
import br.com.pacto.controller.json.ficha.write.FichaWriteJSON;
import br.com.pacto.controller.json.log.EntidadeLogEnum;
import br.com.pacto.controller.json.programa.read.ProgramaTreinoFichaJSON;
import br.com.pacto.dao.intf.atividade.*;
import br.com.pacto.dao.intf.atividadeEmpresa.AtividadeEmpresaDao;
import br.com.pacto.dao.intf.ficha.CategoriaFichaDao;
import br.com.pacto.dao.intf.ficha.FichaDao;
import br.com.pacto.dao.intf.log.LogDao;
import br.com.pacto.dao.intf.programa.ProgramaTreinoDao;
import br.com.pacto.dao.intf.programa.ProgramaTreinoFichaDao;
import br.com.pacto.dao.intf.serie.SerieDao;
import br.com.pacto.dao.intf.serie.SerieRealizadaDao;
import br.com.pacto.dao.intf.serie.TreinoRealizadoDao;
import br.com.pacto.dao.intf.sincronizacao.HistoricoRevisaoDao;
import br.com.pacto.objeto.*;
import br.com.pacto.objeto.to.AtividadeFichaDTO;
import br.com.pacto.objeto.to.AtividadeFichaTO;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.exception.ValidacaoException;
import br.com.pacto.service.impl.notificacao.excecao.AtividadeFichaExcecoes;
import br.com.pacto.service.impl.notificacao.excecao.FichaExcecoes;
import br.com.pacto.service.impl.notificacao.excecao.ProgramaTreinoExcecoes;
import br.com.pacto.service.impl.programa.ProgramaTreinoServiceImpl;
import br.com.pacto.service.intf.configuracoes.ConfiguracaoSistemaService;
import br.com.pacto.service.intf.ficha.CategoriaFichaService;
import br.com.pacto.service.intf.ficha.FichaService;
import br.com.pacto.service.intf.ficha.FilaImpressaoService;
import br.com.pacto.service.intf.ficha.SerieService;
import br.com.pacto.service.intf.programa.ProgramaTreinoService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.ViewUtils;
import br.com.pacto.util.bean.GenericoTO;
import br.com.pacto.util.impl.JSFUtilities;
import br.com.pacto.util.impl.Ordenacao;
import br.com.pacto.util.impl.UtilReflection;
import br.com.pacto.validacao.intf.ficha.FichaValidacaoService;
import com.google.common.collect.ImmutableList;
import org.apache.commons.collections.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.util.EntityUtils;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.persistence.EntityExistsException;
import javax.servlet.http.HttpServletRequest;
import java.util.*;

import static br.com.pacto.objeto.Uteis.incluirLog;
import static br.com.pacto.objeto.Uteis.obtemNomeEVersaoApp;

/**
 *
 * <AUTHOR>
 */
@Service
public class FichaServiceImpl implements FichaService {

    @Autowired
    private ViewUtils viewUtils;
    @Autowired
    private FichaValidacaoService validacao;
    @Autowired
    private FichaDao fichaDao;
    @Autowired
    private CategoriaFichaDao categoriaFichaDao;
    @Autowired
    private AtividadeDao atividadeDao;
    @Autowired
    private AtividadeFichaDao atividadeFichaDao;
    @Autowired
    private SerieDao serieDao;
    @Autowired
    private ProgramaTreinoFichaDao programaTreinoFichaDao;
    @Autowired
    private AtividadeFichaAjusteDao atividadeFichaAjusteDao;
    @Autowired
    private ProgramaTreinoService programaTreinoService;
    @Autowired
    private CategoriaFichaService categoriaFichaService;
    @Autowired
    private SerieService serieService;
    @Autowired
    private SerieRealizadaDao serieRealizadaDao;
    @Autowired
    private CategoriaFichaService ctfs;
    @Autowired
    private HistoricoRevisaoDao historicoRevisaoDao;
    @Autowired
    private ProgramaTreinoDao programaTreinoDao;
    @Autowired
    private TreinoRealizadoDao treinoRealizadoDao;
    @Autowired
    private SessaoService sessaoService;
    @Autowired
    private UsuarioService usuarioService;
    @Autowired
    private FilaImpressaoService filaImpressaoService;
    @Autowired
    private ConfiguracaoSistemaService configuracaoSistemaService;
    @Autowired
    private AtividadeEmpresaDao atividadeEmpresaDao;
    @Autowired
    private AtividadeCategoriaAtividadeDao atividadeCategoriaAtividadeDao;
    @Autowired
    private AtividadeAparelhoDao atividadeAparelhoDao;
    @Autowired
    private AtividadeGrupoMuscularDao atividadeGrupoMuscularDao;
    @Autowired
    private AtividadeMusculoDao atividadeMusculoDao;
    @Autowired
    private AtividadeNivelDao atividadeNivelDao;
    @Autowired
    private AtividadeAnimacaoDao atvAnimacaoDao;
    @Autowired
    private LogDao logDao;
    private static Map<String, String> mapaUrlExterna = new HashMap<>();

    public ViewUtils getViewUtils() {
        return viewUtils;
    }

    public void setViewUtils(ViewUtils viewUtils) {
        this.viewUtils = viewUtils;
    }

    public FichaValidacaoService getValidacao() {
        return validacao;
    }

    public void setValidacao(FichaValidacaoService validacao) {
        this.validacao = validacao;
    }

    public FichaDao getFichaDao() {
        return this.fichaDao;
    }

    public void setFichaDao(FichaDao fichaDao) {
        this.fichaDao = fichaDao;
    }

    public AtividadeFichaDao getAtividadeFichaDao() {
        return atividadeFichaDao;
    }

    public void setAtividadeFichaDao(AtividadeFichaDao atividadeFichaDao) {
        this.atividadeFichaDao = atividadeFichaDao;
    }


    @Override
    public Ficha alterar(final String ctx, Ficha object, boolean persistirAtividades) throws ServiceException, ValidacaoException {
        try {
            getValidacao().validarDadosBasicos(object);
            if (persistirAtividades) {
                salvarAtividadesDaFicha(ctx, object);
            }
            return getFichaDao().update(ctx, object);
        } catch (ValidacaoException ex) {
            throw ex;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public void excluir(final String ctx, Ficha object) throws ServiceException {
        try {
            getFichaDao().delete(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public Ficha inserir(final String ctx, Ficha object) throws ServiceException, ValidacaoException {
        try {
            getValidacao().validarDadosBasicos(object);
            return getFichaDao().insert(ctx, object);
        } catch (EntityExistsException ex) {
            throw new ServiceException(ex);
        } catch (ValidacaoException ex) {
            throw ex;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public Ficha cadastrar(final String ctx, Ficha ficha) throws ServiceException {
        try {
            getValidacao().validarDadosBasicos(ficha);
            ficha = getFichaDao().inserir(ctx, ficha);
            if (!UteisValidacao.emptyList(ficha.getAtividades())) {
                ficha.setAtividades(atualizarAtividadesFicha(ctx, ficha.getAtividades()));
            }
            return ficha;
        } catch (EntityExistsException ex) {
            throw new ServiceException(ex);
        } catch (ValidacaoException ex) {
            throw ex;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public Ficha obterObjetoPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException {
        try {
            return getFichaDao().findObjectByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public Ficha obterPorId(final String ctx, Integer id) throws ServiceException {
        try {
            return getFichaDao().findById(ctx, id);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public FichaWriteJSON obterPorIdApp(final String ctx, Integer id) throws ServiceException {
        try {
            return getFichaDao().consultarFichaPorIdApp(ctx, id);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    public List<Ficha> obterPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException {
        try {
            return getFichaDao().findByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<Ficha> obterPorParam(final String ctx, String query,
            Map<String, Object> params, int max, int index)
            throws ServiceException {
        try {
            return getFichaDao().findByParam(ctx, query, params, max, index);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<Ficha> obterTodos(final String ctx) throws ServiceException {
        try {
            return getFichaDao().findAll(ctx);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<Ficha> obterPorNomePrograma(final String ctx, Integer codigoPrograma, String nome) throws ServiceException {
        String s = "SELECT obj FROM Ficha obj "
                + " INNER JOIN obj.programas atr"
                + " with atr.programa.codigo = :_codigo"
                + " where nome = :_nome";
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("_codigo", codigoPrograma);
        params.put("_nome", nome);
        try {
            return obterPorParam(ctx, s, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<Ficha> obterPorPrograma(final String ctx, Integer codigo) throws ServiceException {
        String s = "SELECT obj FROM Ficha obj "
                + " INNER JOIN obj.programas atr"
                + " with atr.programa.codigo = :_codigo order by obj.codigo";
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("_codigo", codigo);
        try {
            return obterPorParam(ctx, s, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public AtividadeFicha obterAtividadeFicha(String ctx, Integer atividade, Integer ficha) throws ServiceException {
        try {
            String query = "select obj from AtividadeFicha obj "
                    + "where obj.ficha.codigo = :ficha and obj.atividade.codigo = :atividade";
            HashMap<String, Object> p = new HashMap<String, Object>();
            p.put("ficha", ficha);
            p.put("atividade", atividade);
            return getAtividadeFichaDao().findObjectByParam(ctx, query, p);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    public AtividadeFicha alterarAtividadeFicha(String ctx, AtividadeFicha object) throws ServiceException {
        try {
            object = getAtividadeFichaDao().updateNoClear(ctx, object);
            refresh(ctx, object);
            return object;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public void excluirAtividadeFicha(String ctx, AtividadeFicha object) throws ServiceException {
        try {
            getAtividadeFichaDao().delete(ctx, object);
            atualizarVersaoFicha(ctx, object.getFicha().getCodigo());
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public AtividadeFicha inserirAtividadeFicha(String ctx, AtividadeFicha object) throws ServiceException {
        try {
            object.setVersao(1);
            for (AtividadeFichaAjuste atAj : object.getAjustes()) {
                atAj.setAtividadeFicha(object);
            }
            atualizarVersaoFicha(ctx, object.getFicha().getCodigo());
            return getAtividadeFichaDao().insert(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    public void adicionarAtividadeFichaAjuste(String ctx, AtividadeFicha object) throws ServiceException {
        try {
            atividadeFichaAjusteDao.deleteComParam(ctx, new String[]{"atividadeFicha.codigo"}, new Object[]{object.getCodigo()});
            for (AtividadeFichaAjuste atAj : object.getAjustes()) {
                atAj.setAtividadeFicha(object);
                atividadeFichaAjusteDao.insert(ctx, atAj);
            }
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }

    }

    public List<AtividadeFicha> obterAtividadesFicha(String ctx, Integer ficha) throws ServiceException {
        try {
            List<AtividadeFicha> atividadesFicha = getAtividadeFichaDao().findListByAttributes(ctx, new String[]{"ficha.codigo"},
                    new Object[]{ficha}, "ordem", 0);
            getAtividadeFichaDao().refresh(ctx, atividadesFicha);
            return atividadesFicha;
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    public List<Ficha> obterFichasPredefinidas(String ctx, boolean somenteAtivas) throws ServiceException {
        try {
            StringBuilder query = new StringBuilder("select obj from Ficha obj ");
            query.append("where obj.usarComoPredefinida = true ");
            if (somenteAtivas) {
                query.append(" and obj.ativo is true ");
            }
            query.append("order by obj.nome");
            HashMap<String, Object> p = new HashMap<String, Object>();
            return getFichaDao().findByParam(ctx, query.toString(), p);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    public FichaResponseTO obterFichaPredefinidaById(Integer id) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        try {
            StringBuilder query = new StringBuilder("select obj from Ficha obj ");
            query.append("where obj.usarComoPredefinida = true ");
            query.append(" and obj.codigo = "+id);
            query.append(" order by obj.nome ");
            HashMap<String, Object> p = new HashMap<>();
            Ficha ficha = getFichaDao().findObjectByParam(ctx, query.toString(), p);
            try {
                getFichaDao().refresh(ctx, ficha);
            }catch (Exception e){
                Uteis.logar(e, FichaServiceImpl.class);
            }
            return new FichaResponseTO(ficha, null);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    public List<Ficha> obterFichasComCategoria(String ctx, CategoriaFicha categoria) throws ServiceException {
        try {
            StringBuilder query = new StringBuilder("select obj from Ficha obj ");
            query.append("where obj.categoria.codigo = "+categoria.getCodigo());
            query.append(" order by obj.nome");
            HashMap<String, Object> p = new HashMap<String, Object>();
            return getFichaDao().findByParam(ctx, query.toString(), p);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    public List<ProgramaTreinoFicha> obterProgramasFicha(final String ctx, final Integer ficha) throws ServiceException {
        try {
            return programaTreinoFichaDao.findListByAttributes(ctx, new String[]{"ficha.codigo"},
                    new Object[]{ficha}, "ficha.nome", 0);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    public List<Ficha> obterPorNome(final String ctx, boolean preDefinidas, String nome, Integer codigo) throws ServiceException {
        StringBuilder s = new StringBuilder("SELECT obj FROM Ficha obj where obj.nome = :nome");
        Map<String, Object> params = new HashMap<String, Object>();
        s.append((preDefinidas ? " and obj.usarComoPredefinida is true " : ""));
        if (codigo != null && codigo > 0) {
            s.append(" and obj.codigo <> :codigo");
            params.put("codigo", codigo);
        }
        params.put("nome", nome);
        try {
            return obterPorParam(ctx, s.toString(), params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public Boolean reordenar(final String ctx, final AtividadeFicha atividade, final Ficha ficha, boolean up) throws ServiceException {
        try {
            int index = ficha.getAtividades().indexOf(atividade);
            int indexOutro = up ? index - 1 : index + 1;
            if (indexOutro < 0 || indexOutro >= ficha.getAtividades().size()) {
                return false;
            }
            AtividadeFicha atv = ficha.getAtividades().get(index);
            atv.setOrdem(up ? atv.getOrdem() - 1 : atv.getOrdem() + 1);

            AtividadeFicha atvOutro = ficha.getAtividades().get(indexOutro);
            atvOutro.setOrdem(up ? atvOutro.getOrdem() + 1 : atvOutro.getOrdem() - 1);

            for (AtividadeFicha atvFic : ficha.getAtividades()) {
                if (atvFic.getAtividade().getCodigo().equals(atv.getCodigo())) {
                    atvFic.setOrdem(atv.getOrdem());
                    getAtividadeFichaDao().updateAlgunsCampos(ctx, new String[]{"ordem"}, new Object[]{atvFic.getOrdem()},
                            new String[]{"codigo"}, new Object[]{atvFic.getCodigo()});
                } else if (atvFic.getAtividade().getCodigo().equals(atvOutro.getCodigo())) {
                    atvFic.setOrdem(atvOutro.getOrdem());
                    getAtividadeFichaDao().updateAlgunsCampos(ctx, new String[]{"ordem"}, new Object[]{atvFic.getOrdem()},
                            new String[]{"codigo"}, new Object[]{atvFic.getCodigo()});
                }
            }
            Ordenacao.ordenarLista(ficha.getAtividades(), "ordem");
            atualizarVersaoFicha(ctx, ficha.getCodigo());
            return true;
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    public void atualizarVersaoFicha(String ctx, Integer ficha) throws ServiceException {
        try {
            ProgramaTreinoFicha ptFicha = programaTreinoService.obterProgramaTreinoFichaPorFicha(ctx, ficha);
            if (ptFicha == null) {
                return;
            }
            ptFicha.getPrograma().setVersao(ptFicha.getPrograma().getVersao() + 1);
            programaTreinoDao.updateAlgunsCampos(ctx, new String[]{"versao"},
                    new Object[]{ptFicha.getPrograma().getVersao()},
                    new String[]{"codigo"}, new Object[]{ptFicha.getPrograma().getCodigo()});
            ptFicha.setVersao(ptFicha.getVersao() + 1);
            programaTreinoFichaDao.updateAlgunsCampos(ctx, new String[]{"versao"},
                    new Object[]{ptFicha.getVersao()}, new String[]{"codigo"}, new Object[]{ptFicha.getCodigo()});
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }

    }

    @Override
    public void tornarPreDefinida(String key, Ficha ficha) throws ValidacaoException, ServiceException {
        try {
            List<AtividadeFicha> atividadesFicha = obterAtividadesFicha(key, ficha.getCodigo());

            Set<String> vincBiTriSet = getVinculos(atividadesFicha);

            Ficha fichaPredefinida = new Ficha(ficha, atividadesFicha, true);

            for ( AtividadeFicha atividade : fichaPredefinida.getAtividades()) {
                for (Serie serie : atividade.getSeries() ) {
                    serie.setCargaApp(serie.getCargaComp());
                    serie.setRepeticaoApp(serie.getRepeticaoComp());
                    serie.setAtualizadoApp(true);
                }
            }

            validacao.validarNomeFichaPreDefinida(key, null, fichaPredefinida.getNome());
            fichaPredefinida = inserir(key, fichaPredefinida);
            //gravar atividades
            for (AtividadeFicha atvFic : fichaPredefinida.getAtividades()) {
                atvFic.setFicha(fichaPredefinida);
                atvFic = inserirAtividadeFicha(key, atvFic);
                //gravar series
                for (Serie serie : atvFic.getSeries()) {
                    serie.setAtividadeFicha(atvFic);
                    serieService.inserir(key, serie);
                }
            }

            if(!vincBiTriSet.isEmpty()){
                doAjustarAtividades(key, vincBiTriSet, fichaPredefinida.getAtividades());

            }

        } catch (ValidacaoException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    private void doAjustarAtividades(final String ctx, Set<String> vincBiTriSet, List<AtividadeFicha> atividades) throws ServiceException {

        for (String b : vincBiTriSet){
            Collection<AtividadeFicha> atividadeFichas = new ArrayList<>();
            String[] setsId = b.split("\\|");
            for (String setId : setsId) {
                for (AtividadeFicha atividadeFicha : atividades) {
                    if (atividadeFicha.getCodigoAntigo().equals(Integer.valueOf(setId))) {
                        atividadeFichas.add(atividadeFicha);
                    }
                }
            }

            if(null != atividadeFichas && !atividadeFichas.isEmpty()) {
                String nSetId = "";
                for (AtividadeFicha af : atividadeFichas) {
                    nSetId += af.getCodigo() + "|";
                }

                for (AtividadeFicha af : atividadeFichas) {
                    af.setSetId(nSetId);
                   alterarAtividadeFicha(ctx, af);
                }
            }

        }


    }

    private Set<String> getVinculos(List<AtividadeFicha> atividades) {
        Set<String> strings = new HashSet<String>();
        for (AtividadeFicha af : atividades){
            if(null != af.getSetId() && !af.getSetId().isEmpty()){
                strings.add(af.getSetId());
            }
        }
        return strings;
    }

    @Override
    public AtividadeFicha salvarAtividadeFicha(final String ctx, AtividadeFicha atividadeFicha) throws ServiceException {
        try {
            if (atividadeFicha.getCodigo() == null || atividadeFicha.getCodigo() == 0) {
                List<AtividadeFicha> atividadesFicha = new ArrayList<>();
                atividadesFicha.add(atividadeFicha);
                getAtividadeFichaDao().atualizarLista(ctx, atividadesFicha);
                for (Serie serie : atividadeFicha.getSeries()) {
                    serie.setAtividadeFicha(atividadeFicha);
                    serie = serieService.inserir(ctx, serie);
                    serieService.refresh(ctx, serie);
                }
            } else {
                atividadeFicha.setSetId(null);
                if(atividadeFicha.getBiSet() || atividadeFicha.getTriSet()){
                    atividadeFicha.setSetId(atividadeFicha.getCodigo()+"|"
                            + (atividadeFicha.getAtividadeAssociada1() == null ? "" : (atividadeFicha.getAtividadeAssociada1().getCodigo()+"|"))
                            + (atividadeFicha.getAtividadeAssociada2() == null ? "" : (atividadeFicha.getAtividadeAssociada2().getCodigo()+"|")));
                    if(atividadeFicha.getAtividadeAssociada1() != null){
                        getAtividadeFichaDao().updateAlgunsCampos(ctx, new String[]{"setId", "metodoExecucao"},
                                new Object[]{atividadeFicha.getSetId(), atividadeFicha.getMetodoExecucao()}, new String[]{"codigo"},
                                new Object[]{atividadeFicha.getAtividadeAssociada1().getCodigo()});
                    }
                    if(atividadeFicha.getAtividadeAssociada2() != null){
                        getAtividadeFichaDao().updateAlgunsCampos(ctx, new String[]{"setId", "metodoExecucao"},
                                new Object[]{atividadeFicha.getSetId(), atividadeFicha.getMetodoExecucao()}, new String[]{"codigo"},
                                new Object[]{atividadeFicha.getAtividadeAssociada2().getCodigo()});
                    }
                }
                for (Serie serie : atividadeFicha.getSeries()) {
                    serie.setarSegundos(false);
                    if (serie.getAtividadeFicha() != null && serie.getAtividadeFicha().getCodigo() != null) {
                        if(UteisValidacao.emptyNumber(serie.getCodigo())){
                            serie = serieService.inserir(ctx, serie);
                        }else{
                            serie = serieService.alterar(ctx, serie);
                        }
                        serieService.refresh(ctx, serie);
                    }
                }
                List<AtividadeFicha> atividadesFicha = new ArrayList<>();
                atividadesFicha.add(atividadeFicha);
                atividadeFicha = getAtividadeFichaDao().atualizarLista(ctx, atividadesFicha).get(0);
            }

            if (JSFUtilities.isJSFContext()) {
                Usuario u = (Usuario) JSFUtilities.getFromSession(JSFUtilities.LOGGED);
                if (atividadeFicha.getFicha().getProgramas() != null && atividadeFicha.getFicha().getProgramas().size() == 1) {
                    ProgramaTreino programaTreino = obterProgramaPorFicha(ctx, atividadeFicha.getFicha().getCodigo());
                    historicoRevisaoDao.saveHistoricoRevisao(TipoRevisaoEnum.UPDATE, TipoClassSincronizarEnum.ProgramaTreino, ctx, u, programaTreino.getCodigo());
                }
            }
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }

        Ordenacao.ordenarLista(atividadeFicha.getSeries(), "codigo");
        return atividadeFicha;
    }

    public void ordenarAtividadesAssociadas(final String ctx, final AtividadeFicha itemDeslocado,
                                            final Integer posFinal,
                                            final Ficha ficha) throws Exception{
        if(UteisValidacao.emptyString(itemDeslocado.getSetId())){
            return;
        }
        Integer pos1 = null;
        Integer pos2 = null;
        for(AtividadeFicha af : ficha.getAtividades()){
            if(itemDeslocado.getAtividadeAssociada1() != null && af.getCodigo().equals(itemDeslocado.getAtividadeAssociada1().getCodigo())){
                pos1 = ficha.getAtividades().indexOf(af);
            }
            if(itemDeslocado.getAtividadeAssociada2() != null && af.getCodigo().equals(itemDeslocado.getAtividadeAssociada2().getCodigo())){
                pos2 = ficha.getAtividades().indexOf(af);
            }
        }
        if(pos1 != null && pos2 == null){
            alterarOrdemForm(ctx, ficha, (pos1+1), pos1 > posFinal ? (posFinal+1) : (posFinal-1), false);
        }else if(pos2 != null && pos1 == null){
            alterarOrdemForm(ctx, ficha, pos2, pos2 > posFinal ? (posFinal+1) : (posFinal-1), false);
        }else {
            alterarOrdemForm(ctx, ficha, pos1,
                    (pos1 > pos2 && pos2 > posFinal) ? posFinal + 2 :
                    (pos1 < pos2 && pos2 < posFinal) ? posFinal - 2:
                    (pos1 < pos2 && pos2 > posFinal) ? posFinal - 1 :
                    posFinal + 1, false);
            alterarOrdemForm(ctx, ficha, pos2,
                    (pos1 > pos2 && pos2 > posFinal) ? posFinal + 1 :
                            (pos1 < pos2 && pos2 < posFinal) ? posFinal - 1:
                                    (pos1 < pos2 && pos2 > posFinal) ? posFinal - 2 :
                                            posFinal + 2, false);
        }
    }

    public void montarOrdenacaoIndice(final String ctx, List<AtividadeFicha> atividades) throws Exception {
        for(int i = 0 ; i < atividades.size() ; i++){
            atividades.get(i).setOrdem(i);
            getAtividadeFichaDao().updateAlgunsCampos(ctx, new String[]{"setId", "ordem"},
                    new Object[]{atividades.get(i).getSetId(),atividades.get(i).getOrdem()}, new String[]{"codigo"},
                    new Object[]{atividades.get(i).getCodigo()});
        }
    }

    public void alterarOrdemForm(final String ctx, Ficha ficha, int itemInicio, int itemFinal) throws Exception {
        alterarOrdemForm(ctx, ficha, itemInicio, itemFinal, true);
    }

    public void alterarOrdemForm(final String ctx, Ficha ficha, int itemInicio, int itemFinal, boolean alterarAssociado) throws Exception {
//        ficha.setAtividades(alterarOrdemForm(ctx, ficha.getAtividades(), itemInicio, itemFinal, alterarAssociado));
        alterarOrdemForm(ctx, ficha.getAtividades(), itemInicio, itemFinal, alterarAssociado);
    }

    public List<AtividadeFicha> alterarOrdemForm(final String ctx, List<AtividadeFicha> atividades, int itemInicio, int itemFinal, boolean alterarAssociado) throws Exception {
        atividades = Ordenacao.ordenarLista(atividades, "ordem");
        AtividadeFicha itemDeslocado = atividades.get(itemInicio);
        atividades.remove(itemInicio);
        atividades.add(itemFinal, itemDeslocado);
        if(!UteisValidacao.emptyString(itemDeslocado.getSetId())){
            atividades = ordenarSet(atividades, itemDeslocado);
        }
        montarOrdenacaoIndice(ctx,atividades);
        return atividades;
    }

    public List<AtividadeFicha> ordenarSet(List<AtividadeFicha> atividades, AtividadeFicha itemDeslocado){
        for(int i = 0 ; i < atividades.size() ; i++){
            atividades.get(i).setOrdem(i);
        }
        for (AtividadeFicha at : atividades) {
            if (at.getSetId() == null || !at.getSetId().equals(itemDeslocado.getSetId())) {
                at.setOrdem(at.getOrdem() + (at.getOrdem() > itemDeslocado.getOrdem() ? 999 : -999));
            }
        }
        return Ordenacao.ordenarLista(atividades, "ordem");

    }

    public void limparAssociadas(final String ctx, final AtividadeFicha atv ) throws Exception {
        if(atv == null){
            return;
        }
        getAtividadeFichaDao().updateAlgunsCampos(ctx, new String[]{"setId"},
                new Object[]{null}, new String[]{"codigo"},
                new Object[]{atv.getCodigo()});
    }

    @Override
    public Ficha salvarAtividadesDaFicha(final String ctx, Ficha ficha) throws ServiceException {
        try {
            List<AtividadeFicha> listaFicha = ImmutableList.copyOf(ficha.getAtividades());
            for (AtividadeFicha atividadeFicha : listaFicha) {
                salvarAtividadeFicha(ctx, atividadeFicha);
            }
            atualizarVersaoFicha(ctx, ficha.getCodigo());
            return ficha;
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public void removerAtividade(final String ctx, final AtividadeFicha atividade, final Ficha ficha) throws ValidacaoException, ServiceException {
        excluirAtividadeFicha(ctx, atividade);
        ficha.getAtividades().remove(atividade);
    }

    @Override
    public Ficha gerarSeries(Ficha ficha) {
        if (ficha != null && !ficha.getAtividades().isEmpty()) {
            final Integer nSeries = new Integer(UteisValidacao.emptyNumber(ficha.getQtdSeriesInserir()) ? 0 : ficha.getQtdSeriesInserir());
            final Integer nRepeticoes = new Integer(UteisValidacao.emptyNumber(ficha.getQtdRepeticoesSeriesInserir()) ? 0 : ficha.getQtdRepeticoesSeriesInserir());
            final String descansoStr = UteisValidacao.emptyString(ficha.getDescansoRepetir()) ? "00:00" :ficha.getDescansoRepetir();
            for (AtividadeFicha atividadeFicha : ficha.getAtividades()) {
                if (atividadeFicha.getAtividade().getTipo() == TipoAtividadeEnum.AEROBICO) {
                    continue;
                }
                if (atividadeFicha.getSeries() == null) {
                    atividadeFicha.setSeries(new ArrayList<Serie>());
                }
                //existentes, alterar numero de repeticoes e descanso
                //se o descanso for <> de 00:00
                //se a quantidade de repetições for <> de 0
                for (Serie s : atividadeFicha.getSeries()) {
                    if (nRepeticoes != 0) {
                        s.setRepeticao(new Integer(nRepeticoes));
                    }
                    if (!ficha.getDescansoRepetir().equals("00:00") && !UteisValidacao.emptyString(ficha.getDescansoRepetir())) {
                        s.setDescansoStr(descansoStr);
                        s.setarSegundos(false);
                    }
                }
                boolean remover = false;
                int diferenca = 0;
                final int seriesSize = atividadeFicha.getSeries().size();
                if(atividadeFicha.getSeries().size() > nSeries){
                    remover = true;
                    diferenca = atividadeFicha.getSeries().size() - nSeries;
                } else {
                    diferenca = nSeries - atividadeFicha.getSeries().size();
                }

                for (int i = 1; i <= diferenca; i++) {
                    if(remover){
                        Serie serie = atividadeFicha.getSeries().remove(seriesSize - i) ;
                        atividadeFicha.getSeriesRemover().add(serie);
                    }else{
                        Serie s = new Serie(seriesSize + i, atividadeFicha);
                        s.setRepeticao(nRepeticoes);
                        s.setDescansoStr(descansoStr);
                        s.setarSegundos(true);
                        atividadeFicha.getSeries().add(s);
                    }
                }
            }
        }
        return ficha;
    }

    @Override
    public Ficha prepararPersistenciaAppJSON(final String ctx, final FichaWriteJSON fw)
            throws ServiceException {
        try {
            Ficha f = new Ficha();
            if (fw.getCodigo() != null && !fw.getCodigo().equals(0)) {
                f = obterPorId(ctx, fw.getCodigo());
                if (f == null) {
                    throw new ValidacaoException("validacao.ficha.naoencontrada");
                }
                boolean continuar = true;
                if (f.isUsarComoPredefinida()){
                    f = new Ficha();
                    fw.setCodigo(null);
                    continuar = false;
                }
                if (continuar){
                    f.setAtividades(obterAtividadesFicha(ctx, fw.getCodigo()));
                    for (AtividadeFicha atv : f.getAtividades()) {
                        atv.setSeries(serieService.obterSeriePorAtividadeFicha(ctx, atv.getCodigo()));
                        atv.setAjustes(obterAtividadeFichaAjustePorAtividadeFicha(ctx, atv.getCodigo()));
                    }
                    f.setProgramas(obterProgramasFicha(ctx,fw.getCodigo()));
                    setFichaWriteNovo(ctx,fw,f);
                } else {
                    setFichaWriteExistente(ctx, fw, f);
                }
            } else {
                setFichaWriteExistente(ctx, fw, f);
            }
            return f;
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    private void setFichaWriteNovo(String ctx, FichaWriteJSON fw, Ficha f) throws Exception {
        if(fw.getNivel() != null) {
            Nivel nivel = new Nivel();
            nivel.setCodigo(fw.getNivel().getCodigo());
            nivel.setNome(fw.getNivel().getNome());
            nivel.setOrdem(fw.getNivel().getOrdem());
            f.setNivel(nivel);
        }
        if (fw.getCategoria() != null) {
            CategoriaFicha cf = ctfs.obterPorId(ctx, fw.getCategoria());
            f.setCategoria(cf);
        }
        f.setMensagemAluno(fw.getMensagemAluno());
        f.setNome(fw.getNome());
        f.setAtivo(true);
        f.setSexo(fw.getSexo());
        List<ProgramaTreinoFichaJSON> progsJSON = fw.getProgramasFicha();
        if (progsJSON != null) {
            List<ProgramaTreinoFicha> progTrFichaLista = new ArrayList<>();
            for (ProgramaTreinoFichaJSON json : progsJSON) {
                if(json.getPrograma() != null ){
                    ProgramaTreino prog = programaTreinoService.obterPorId(ctx, json.getPrograma());
                    if (TipoExecucaoEnum.valueOf(json.getTipoExecucao()) == null || TipoExecucaoEnum.valueOf(json.getTipoExecucao()).equals(TipoExecucaoEnum.ALTERNADO)) {
                        json.setDiaSemana(null);
                    }
                    if (f.getProgramas() != null && f.getProgramas().size() > 0) {
                        f.getProgramas().get(0).setDiaSemana(json.getDiaSemana());
                        f.getProgramas().get(0).setTipoExecucao(json.getTipoExecucao() == 0 ? TipoExecucaoEnum.ALTERNADO : TipoExecucaoEnum.DIAS_SEMANA);
                    }
                }
            }
        }
        List<AtividadeFichaWriteJSON> atvsJSON = fw.getAtividadesFicha();
        if (atvsJSON != null) {
            for (AtividadeFichaWriteJSON atvFichaJSON : atvsJSON) {
                AtividadeFicha atividadeFicha = new AtividadeFicha();
                if (atvFichaJSON.getCodigo() != null) {
                    atividadeFicha = atividadeFichaDao.findById(ctx, atvFichaJSON.getCodigo());
                }
                if (atividadeFicha != null) {
                    if (atividadeFicha.getSeries() != null) {
                        atividadeFicha.setSeries(atualizarListaSeries(atividadeFicha, atvFichaJSON));
                    }
                    Atividade atividade = atividadeDao.findById(ctx, atvFichaJSON.getAtividade());
                    if (atividade == null) {
                        throw new ValidacaoException("cadastros.atividade.naoexiste");
                    }
                    atividadeFicha.setAtividade(atividade);
                    atividadeFicha.setFicha(f);
                    atividadeFicha.setMetodoExecucao(MetodoExecucaoEnum.getFromId(atvFichaJSON.getMetodoExecucao()));
                    if(atividadeFicha.getBiSet() || atividadeFicha.getTriSet())
                    {
                        String[] associadas = atvFichaJSON.getSetid().split("\\|");
                        int indexAssociada;
                        Integer associada1 = null;

                        for(indexAssociada = 0; indexAssociada < associadas.length; ++indexAssociada)
                        {
                            associada1 = Integer.parseInt(associadas[indexAssociada]);
                            if(!associada1.equals(atvFichaJSON.getCodigo()))
                            {
                                ++indexAssociada;
                                break;
                            }
                            associada1 = null;
                        }
                        if(associada1 != null && associada1 > 0)
                        {
                            atividadeFicha.setAtividadeAssociada1(atividadeFichaDao.findById(ctx, associada1));
                        }

                        Integer associada2 = null;
                        for(; indexAssociada < associadas.length; ++indexAssociada)
                        {
                            associada2 = Integer.parseInt(associadas[indexAssociada]);
                            if(!associada2.equals(atvFichaJSON.getCodigo()))
                            {
                                ++indexAssociada;
                                break;
                            }
                            associada2 = null;
                        }
                        if(associada2 != null && associada2 > 0)
                        {
                            atividadeFicha.setAtividadeAssociada2(atividadeFichaDao.findById(ctx, associada2));
                        }
                    }
                    atividadeFicha.setOrdem(atvFichaJSON.getOrdem());
                    List<AtividadeFichaAjusteWriteJSON> listaAjustesJSON = atvFichaJSON.getAjustes();

                    if (listaAjustesJSON != null) {
                        for (AtividadeFichaAjusteWriteJSON ajusteJSON : listaAjustesJSON) {
                            if(fw.getCodigo() == null || fw.getCodigo() == 0)
                            {
                                ajusteJSON.setCodigo(null);
                            }
                            AtividadeFichaAjuste ajuste = new AtividadeFichaAjuste();

                            if (ajusteJSON.getCodigo() != null) {
                                ajuste = atividadeFichaAjusteDao.findById(ctx, ajusteJSON.getCodigo());
                            }
                            if (ajuste != null) {
                                ajuste.setAtividadeFicha(atividadeFicha);
                                ajuste.setNome(ajusteJSON.getNome());
                                ajuste.setValor(ajusteJSON.getValor());
                                AtividadeFichaAjuste encontrado = null;
                                final Integer codAjuste = ajuste.getCodigo();
                                if (codAjuste != null) {
                                    encontrado = (AtividadeFichaAjuste) ColecaoUtils.find(atividadeFicha.getAjustes(), o -> ((AtividadeFichaAjuste) o).getCodigo() != null
                                            && ((AtividadeFichaAjuste) o).getCodigo().equals(codAjuste));
                                }
                                if (encontrado != null) {
                                    int i = getIndexOfState(atividade.getAjustes(),encontrado);
                                    atividadeFicha.getAjustes().set(i, ajuste);
                                } else {
                                    atividadeFicha.getAjustes().add(ajuste);
                                }
                            }
                        }
                    }
                    List<SerieWriteJSON> listaSeriesJSON = atvFichaJSON.getSeries();
                    if (listaSeriesJSON != null) {
                        for (SerieWriteJSON serieJSON : listaSeriesJSON) {
                            Serie s = new Serie();
                            if (serieJSON.getCodigo() != null) {
                                s = serieService.obterPorId(ctx, serieJSON.getCodigo());
                            }
                            if (s != null) {
                                s.setCadencia(serieJSON.getCadencia());
                                s.setAtividadeFicha(atividadeFicha);
                                s.setCarga(serieJSON.getCarga());
                                s.setVelocidade(serieJSON.getVelocidade());
                                s.setComplemento(serieJSON.getComplemento());
                                s.setDescanso(serieJSON.getDescanso());
                                s.setDistancia(serieJSON.getDistancia());
                                s.setDuracao(serieJSON.getDuracao());
                                s.setOrdem(serieJSON.getOrdem());
                                s.setRepeticao(serieJSON.getRepeticao());
                                if (atividadeFicha.getAtividade().getTipo().equals(TipoAtividadeEnum.ANAEROBICO)) {
                                    s.setRepeticaoComp(serieJSON.getRepeticaoComp());
                                    s.setCargaComp(serieJSON.getCargaComp());
                                }
                                s.setCargaApp(serieJSON.getCargaApp() == null ? String.valueOf(serieJSON.getCarga().intValue()) : serieJSON.getCargaApp());
                                s.setRepeticaoApp(serieJSON.getRepeticaoApp() == null ? String.valueOf(serieJSON.getRepeticao().intValue()) : serieJSON.getRepeticaoApp());
                                s.setAtualizadoApp(true);
                                final Integer codserie = s.getCodigo();
                                Serie encontrada = null;
                                if (codserie != null) {
                                    encontrada = (Serie) ColecaoUtils.find(atividadeFicha.getSeries(), o -> ((Serie) o).getCodigo().equals(codserie));
                                }
                                if (encontrada != null) {
                                    int i = atividadeFicha.getSeries().indexOf(encontrada);
                                    atividadeFicha.getSeries().set(i, s);
                                } else {
                                    atividadeFicha.getSeries().add(s);
                                }
                            }
                        }
                    }
                    AtividadeFicha encontrado = null;
                    final Integer codAtividadeFicha = atividadeFicha.getCodigo();
                    if (codAtividadeFicha != null) {
                        encontrado = (AtividadeFicha) ColecaoUtils.find(f.getAtividades(), o -> ((AtividadeFicha) o).getCodigo().equals(codAtividadeFicha));
                    }
                    if (encontrado != null) {
                        int i = f.getAtividades().indexOf(encontrado);
                        f.getAtividades().set(i, atividadeFicha);
                    } else {
                        f.getAtividades().add(atividadeFicha);
                    }
                }
            }
        }
    }

    private List<Serie> atualizarListaSeries(AtividadeFicha atividadeFicha, AtividadeFichaWriteJSON atvFichaJSON) {
        List<Serie> serieExistenteAtualizada = new ArrayList();
        for (Serie serieExistente : atividadeFicha.getSeries()) {
            Boolean existe = false;
            for (SerieWriteJSON serieAlteracao : atvFichaJSON.getSeries()) {
                if (serieAlteracao.getCodigo() != null && serieExistente.getCodigo().equals(serieAlteracao.getCodigo())) {
                    existe = true;
                    break;
                }
            }
            if (existe) {
                serieExistenteAtualizada.add(serieExistente);
            }
        }
        return serieExistenteAtualizada;
    }

    private int getIndexOfState(List<AtividadeFichaAjuste> ajustes, AtividadeFichaAjuste encontrado) {
        for(AtividadeFichaAjuste ajuste : ajustes)  {
            if(ajuste.getCodigo().equals(encontrado.getCodigo()))
                return ajustes.indexOf(ajuste);
        }
        return 0;
    }

    private void setFichaWriteExistente(String ctx, FichaWriteJSON fw, Ficha f) throws Exception {
        boolean nullNosCodigos = fw.getCodigo() == null || fw.getCodigo() == 0;
        if(fw.getNivel() != null) {
            Nivel nivel = new Nivel();
            nivel.setCodigo(fw.getNivel().getCodigo());
            nivel.setNome(fw.getNivel().getNome());
            nivel.setOrdem(fw.getNivel().getOrdem());
            f.setNivel(nivel);
        }
        if (fw.getCategoria() != null) {
            CategoriaFicha cf = ctfs.obterPorId(ctx, fw.getCategoria());
            f.setCategoria(cf);
        }
        f.setMensagemAluno(fw.getMensagemAluno());
        f.setNome(fw.getNome());
        f.setAtivo(true);
        f.setSexo(fw.getSexo());
        List<ProgramaTreinoFichaJSON> progsJSON = fw.getProgramasFicha();
        if (progsJSON != null) {
            List<ProgramaTreinoFicha> progTrFichaLista = new ArrayList<>();
            for (ProgramaTreinoFichaJSON json : progsJSON) {
                if(json.getPrograma() != null ){
                    ProgramaTreino prog = programaTreinoService.obterPorId(ctx, json.getPrograma());
                    if (TipoExecucaoEnum.valueOf(json.getTipoExecucao()) == null || TipoExecucaoEnum.valueOf(json.getTipoExecucao()).equals(TipoExecucaoEnum.ALTERNADO)) {
                        json.setDiaSemana(null);
                    }
                    ProgramaTreinoFicha progTrFicha = new ProgramaTreinoFicha(prog, f, TipoExecucaoEnum.valueOf(json.getTipoExecucao()),json.getDiaSemana());
                    progTrFichaLista.add(progTrFicha);
                }
            }
            if (progTrFichaLista.size() > 0){
                f.setProgramas(progTrFichaLista);
            }
        }
        List<AtividadeFichaWriteJSON> atvsJSON = fw.getAtividadesFicha();
        if (atvsJSON != null) {
            for (AtividadeFichaWriteJSON atvFichaJSON : atvsJSON) {
                if (nullNosCodigos){
                    atvFichaJSON.setCodigo(null);
                }
                atvFichaJSON.setCodigo(null);
                AtividadeFicha atividadeFicha = new AtividadeFicha();
                if (atvFichaJSON.getCodigo() != null) {
                    atividadeFicha = atividadeFichaDao.findById(ctx, atvFichaJSON.getCodigo());
                }
                if (atividadeFicha != null) {
                    Atividade atividade = atividadeDao.findById(ctx, atvFichaJSON.getAtividade());
                    if (atividade == null) {
                        throw new ValidacaoException("cadastros.atividade.naoexiste");
                    }
                    atividadeFicha.setAtividade(atividade);
                    atividadeFicha.setFicha(f);
                    atividadeFicha.setMetodoExecucao(MetodoExecucaoEnum.getFromId(atvFichaJSON.getMetodoExecucao()));
                    if(atividadeFicha.getBiSet() || atividadeFicha.getTriSet())
                    {
                        String[] associadas = atvFichaJSON.getSetid().split("\\|");
                        int indexAssociada;
                        Integer associada1 = null;

                        for(indexAssociada = 0; indexAssociada < associadas.length; ++indexAssociada)
                        {
                            associada1 = Integer.parseInt(associadas[indexAssociada]);
                            if(!associada1.equals(atvFichaJSON.getCodigo()))
                            {
                                ++indexAssociada;
                                break;
                            }
                            associada1 = null;
                        }
                        if(associada1 != null && associada1 > 0)
                        {
                            atividadeFicha.setAtividadeAssociada1(atividadeFichaDao.findById(ctx, associada1));
                        }

                        Integer associada2 = null;
                        for(; indexAssociada < associadas.length; ++indexAssociada)
                        {
                            associada2 = Integer.parseInt(associadas[indexAssociada]);
                            if(!associada2.equals(atvFichaJSON.getCodigo()))
                            {
                                ++indexAssociada;
                                break;
                            }
                            associada2 = null;
                        }
                        if(associada2 != null && associada2 > 0)
                        {
                            atividadeFicha.setAtividadeAssociada2(atividadeFichaDao.findById(ctx, associada2));
                        }
                    }
                    atividadeFicha.setOrdem(atvFichaJSON.getOrdem());
                    List<AtividadeFichaAjusteWriteJSON> listaAjustesJSON = atvFichaJSON.getAjustes();

                    if (listaAjustesJSON != null) {
                        for (AtividadeFichaAjusteWriteJSON ajusteJSON : listaAjustesJSON) {
                            if (nullNosCodigos){
                                ajusteJSON.setCodigo(null);
                            }
                            AtividadeFichaAjuste ajuste = new AtividadeFichaAjuste();

                            if (ajusteJSON.getCodigo() != null) {
                                ajuste = atividadeFichaAjusteDao.findById(ctx, ajusteJSON.getCodigo());
                            }
                            if (ajuste != null) {
                                ajuste.setAtividadeFicha(atividadeFicha);
                                ajuste.setNome(ajusteJSON.getNome());
                                ajuste.setValor(ajusteJSON.getValor());
                                AtividadeFichaAjuste encontrado = null;
                                final Integer codAjuste = ajuste.getCodigo();
                                if (codAjuste != null) {
                                    encontrado = (AtividadeFichaAjuste) ColecaoUtils.find(atividadeFicha.getAjustes(), o -> ((AtividadeFichaAjuste) o).getCodigo() != null
                                            && ((AtividadeFichaAjuste) o).getCodigo().equals(codAjuste));
                                }
                                if (encontrado != null) {
                                    int i = getIndexOfState(atividade.getAjustes(),encontrado);
                                    atividadeFicha.getAjustes().set(i, ajuste);
                                } else {
                                    atividadeFicha.getAjustes().add(ajuste);
                                }
                            }
                        }
                    }
                    List<SerieWriteJSON> listaSeriesJSON = atvFichaJSON.getSeries();
                    if (listaSeriesJSON != null) {
                        for (SerieWriteJSON serieJSON : listaSeriesJSON) {
                            if (nullNosCodigos){
                                serieJSON.setCodigo(null);
                            }
                            Serie s = new Serie();
                            if (serieJSON.getCodigo() != null) {
                                s = serieService.obterPorId(ctx, serieJSON.getCodigo());
                            }
                            if (s != null) {
                                s.setCadencia(serieJSON.getCadencia());
                                s.setAtividadeFicha(atividadeFicha);
                                s.setCarga(serieJSON.getCarga());
                                s.setVelocidade(serieJSON.getVelocidade());
                                s.setComplemento(serieJSON.getComplemento());
                                s.setDescanso(serieJSON.getDescanso());
                                s.setDistancia(serieJSON.getDistancia());
                                s.setDuracao(serieJSON.getDuracao());
                                s.setOrdem(serieJSON.getOrdem());
                                s.setRepeticao(serieJSON.getRepeticao());
                                if (atividadeFicha.getAtividade().getTipo().equals(TipoAtividadeEnum.ANAEROBICO)) {
                                    s.setRepeticaoComp(serieJSON.getRepeticaoComp());
                                    s.setCargaComp(serieJSON.getCargaComp());
                                }
                                s.setCargaApp(serieJSON.getCargaApp());
                                s.setRepeticaoApp(serieJSON.getRepeticaoApp());
                                s.setAtualizadoApp(true);
                                final Integer codserie = s.getCodigo();
                                Serie encontrada = null;
                                if (codserie != null) {
                                    encontrada = (Serie) ColecaoUtils.find(atividadeFicha.getSeries(), o -> ((Serie) o).getCodigo().equals(codserie));
                                }
                                if (encontrada != null) {
                                    int i = atividadeFicha.getSeries().indexOf(encontrada);
                                    atividadeFicha.getSeries().set(i, s);
                                } else {
                                    atividadeFicha.getSeries().add(s);
                                }
                            }
                        }
                    }
                    AtividadeFicha encontrado = null;
                    final Integer codAtividadeFicha = atividadeFicha.getCodigo();
                    if (codAtividadeFicha != null) {
                        encontrado = (AtividadeFicha) ColecaoUtils.find(f.getAtividades(), o -> ((AtividadeFicha) o).getCodigo().equals(codAtividadeFicha));
                    }
                    if (encontrado != null) {
                        int i = f.getAtividades().indexOf(encontrado);
                        f.getAtividades().set(i, atividadeFicha);
                    } else {
                        f.getAtividades().add(atividadeFicha);
                    }
                }
            }
        }
    }


    @Override
    public Ficha prepararPersistenciaJSON(final String ctx, final FichaWriteJSON fw)
            throws ServiceException {
        try {
            Ficha f = new Ficha();
            if (fw.getCodigo() != null && !fw.getCodigo().equals(0)) {
                f = obterPorId(ctx, fw.getCodigo());
                if (f == null) {
                    throw new ValidacaoException("validacao.ficha.naoencontrada");
                }
            }
            if (fw.getCategoria() != null) {
                CategoriaFicha cf = ctfs.obterPorId(ctx, fw.getCategoria());
                f.setCategoria(cf);
            }
            if(f.getCodigo() != null) {
                List<ProgramaTreinoFicha> programaTreinoFicha = buscarProgramaTreinoFicha(ctx, f);
                if (!UteisValidacao.emptyList(programaTreinoFicha)) {
                    f.setProgramas(programaTreinoFicha);
                }
            }
            f.setMensagemAluno(fw.getMensagemAluno());
            f.setNome(fw.getNome());
            f.setAtivo(true);
            List<ProgramaTreinoFichaJSON> progsJSON = fw.getProgramasFicha();
            if (progsJSON != null) {
                for (ProgramaTreinoFichaJSON json : progsJSON) {
                    ProgramaTreinoFicha progTrFicha = new ProgramaTreinoFicha();
                    progTrFicha.setCodigo(json.getCodigo());
                    progTrFicha.setDiaSemana(json.getDiaSemana());
                    progTrFicha.setFicha(f);
                    if (json.getPrograma() == null) {
                        throw new ValidacaoException("validacao.ficha.programa");
                    }
                    ProgramaTreino prog = programaTreinoService.obterPorId(ctx, json.getPrograma());
                    if (prog == null) {
                        throw new ValidacaoException("validacao.ficha.programa");
                    }
                    progTrFicha.setPrograma(prog);
                    progTrFicha.setTipoExecucao(TipoExecucaoEnum.valueOf(json.getTipoExecucao()));
                    if (progTrFicha.getTipoExecucao() == null && progTrFicha.getTipoExecucao().equals(TipoExecucaoEnum.ALTERNADO)) {
                        progTrFicha.setDiaSemana(null);
                    }
                    //
                    final Integer codProgTrFicha = progTrFicha.getCodigo();
                    ProgramaTreinoFicha encontrado = null;
                    if (codProgTrFicha != null && codProgTrFicha > 0) {
                        encontrado = (ProgramaTreinoFicha) ColecaoUtils.find(f.getProgramas(), new Predicate() {
                            @Override
                            public boolean evaluate(Object o) {
                                ProgramaTreinoFicha progTF = ((ProgramaTreinoFicha) o);
                                return progTF != null && progTF.getCodigo().equals(codProgTrFicha);
                            }
                        });
                    }
                    if (encontrado != null) {
                        int i = f.getProgramas().indexOf(encontrado);
                        f.getProgramas().set(i, progTrFicha);
                    } else {
                        f.getProgramas().add(progTrFicha);
                    }
                }
            }
            List<AtividadeFichaWriteJSON> atvsJSON = fw.getAtividadesFicha();
            if (atvsJSON != null) {
                for (AtividadeFichaWriteJSON atvFichaJSON : atvsJSON) {
                    AtividadeFicha atividadeFicha = new AtividadeFicha();
                    if (atvFichaJSON.getCodigo() != null) {
                        atividadeFicha = atividadeFichaDao.findById(ctx, atvFichaJSON.getCodigo());
                    }
                    if (atividadeFicha != null) {
                        Atividade atividade = atividadeDao.findById(ctx, atvFichaJSON.getAtividade());
                        if (atividade == null) {
                            throw new ValidacaoException("cadastros.atividade.naoexiste");
                        }
                        atividadeFicha.setAtividade(atividade);
                        atividadeFicha.setFicha(f);
                        atividadeFicha.setMetodoExecucao(MetodoExecucaoEnum.getFromId(atvFichaJSON.getMetodoExecucao()));
                        atividadeFicha.setOrdem(atvFichaJSON.getOrdem());
                        List<AtividadeFichaAjusteWriteJSON> listaAjustesJSON = atvFichaJSON.getAjustes();
                        if (listaAjustesJSON != null) {
                            for (AtividadeFichaAjusteWriteJSON ajusteJSON : listaAjustesJSON) {
                                AtividadeFichaAjuste ajuste = new AtividadeFichaAjuste();
                                if (ajusteJSON.getCodigo() != null) {
                                    ajuste = atividadeFichaAjusteDao.findById(ctx, ajusteJSON.getCodigo());
                                }
                                if (ajuste != null) {
                                    ajuste.setAtividadeFicha(atividadeFicha);
                                    ajuste.setNome(ajusteJSON.getNome());
                                    ajuste.setValor(ajusteJSON.getValor());
                                    AtividadeFichaAjuste encontrado = null;
                                    final Integer codAjuste = ajuste.getCodigo();
                                    if (codAjuste != null) {
                                        encontrado = (AtividadeFichaAjuste) ColecaoUtils.find(atividadeFicha.getAjustes(), new Predicate() {
                                            @Override
                                            public boolean evaluate(Object o) {
                                                return ((AtividadeFichaAjuste) o).getCodigo() != null
                                                        && ((AtividadeFichaAjuste) o).getCodigo().equals(codAjuste);
                                            }
                                        });
                                    }
                                    if (encontrado != null) {
                                        int i = atividadeFicha.getAjustes().indexOf(encontrado);
                                        atividadeFicha.getAjustes().set(i, ajuste);
                                    } else {
                                        atividadeFicha.getAjustes().add(ajuste);
                                    }
                                }
                            }
                        }
                        List<SerieWriteJSON> listaSeriesJSON = atvFichaJSON.getSeries();
                        if (listaSeriesJSON != null) {
                            for (SerieWriteJSON serieJSON : listaSeriesJSON) {
                                Serie s = new Serie();
                                if (serieJSON.getCodigo() != null) {
                                    s = serieService.obterPorId(ctx, serieJSON.getCodigo());
                                }
                                if (s != null) {
                                    s.setCadencia(serieJSON.getCadencia());
                                    s.setAtividadeFicha(atividadeFicha);
                                    s.setCarga(serieJSON.getCarga());
                                    s.setVelocidade(serieJSON.getVelocidade());
                                    s.setComplemento(serieJSON.getComplemento());
                                    s.setDescanso(serieJSON.getDescanso());
                                    s.setDistancia(serieJSON.getDistancia());
                                    s.setDuracao(serieJSON.getDuracao());
                                    s.setOrdem(serieJSON.getOrdem());
                                    s.setRepeticao(serieJSON.getRepeticao());
                                    if (atividadeFicha.getAtividade().getTipo().equals(TipoAtividadeEnum.ANAEROBICO)) {
                                        s.setRepeticaoComp(serieJSON.getRepeticaoComp());
                                        s.setCargaComp(serieJSON.getCargaComp());
                                    }
                                    final Integer codserie = s.getCodigo();
                                    Serie encontrada = null;
                                    if (codserie != null) {
                                        encontrada = (Serie) ColecaoUtils.find(atividadeFicha.getSeries(), new Predicate() {
                                            @Override
                                            public boolean evaluate(Object o) {
                                                return ((Serie) o).getCodigo() == codserie;
                                            }
                                        });
                                    }
                                    if (encontrada != null) {
                                        int i = atividadeFicha.getSeries().indexOf(encontrada);
                                        atividadeFicha.getSeries().set(i, s);
                                    } else {
                                        atividadeFicha.getSeries().add(s);
                                    }
                                }
                            }
                        }
                        //
                        AtividadeFicha encontrado = null;
                        if(f.getCodigo() != null) {
                            List<AtividadeFicha> atividadeFichas = buscarAtividadesFicha(ctx, f);
                            if (!UteisValidacao.emptyList(atividadeFichas)) {
                                f.setAtividades(atividadeFichas);
                            }
                        }
                        final Integer codAtividadeFicha = atividadeFicha.getCodigo();
                        if (codAtividadeFicha != null) {
                            encontrado = (AtividadeFicha) ColecaoUtils.find(f.getAtividades(), new Predicate() {
                                @Override
                                public boolean evaluate(Object o) {
                                    return ((AtividadeFicha) o).getCodigo() == codAtividadeFicha;
                                }
                            });
                        }
                        if (encontrado != null) {
                            int i = f.getAtividades().indexOf(encontrado);
                            f.getAtividades().set(i, atividadeFicha);
                        } else {
                            f.getAtividades().add(atividadeFicha);
                        }
                        atividadeFicha.getSeries().size();
                    }
                }
            }
            if(f.getCodigo() != null) {
                List<AtividadeFicha> atividadeFichas = buscarAtividadesFicha(ctx, f);
                if (!UteisValidacao.emptyList(atividadeFichas)) {
                    f.getAtividades().addAll(atividadeFichas);
                }
            }
            return f;
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

   public List<ProgramaTreinoFicha> buscarProgramaTreinoFicha(String ctx, Ficha ficha) throws Exception {

            List<ProgramaTreinoFicha> programaTreinoFichaList = new ArrayList<>();
            StringBuilder queryP = new StringBuilder();
            HashMap<String, Object> p = new HashMap<>();
            queryP.append(" where obj.ficha = :codFicha ");
            p.put("codFicha", ficha);
            programaTreinoFichaList = programaTreinoFichaDao.findByParam(ctx, queryP, p);
            return programaTreinoFichaList;

   }
   public List<AtividadeFicha> buscarAtividadesFicha(String ctx, Ficha ficha) throws Exception {

            List<AtividadeFicha> atividadeFichaList= new ArrayList<>();
            StringBuilder queryP = new StringBuilder();
            HashMap<String, Object> p = new HashMap<>();
            queryP.append(" where obj.ficha = :codFicha ");
            p.put("codFicha", ficha);
            atividadeFichaList = atividadeFichaDao.findByParam(ctx, queryP, p);
            return atividadeFichaList;

   }

    @Override
    public Ficha efetuarExclusoesJSON(final String ctx, final FichaWriteJSON fw, Ficha fichaPreparada)
            throws ServiceException {
        try {
            if (fichaPreparada == null) {
                throw new ValidacaoException("validacao.ficha.naoencontrada");
            }
            List<ProgramaTreinoFichaJSON> progsJSON = fw.getProgramasFicha();
            if (progsJSON != null) {
                List<ProgramaTreinoFicha> tmp = ImmutableList.copyOf(fichaPreparada.getProgramas());
                for (ProgramaTreinoFicha ptf : tmp) {
                    final Integer codPtf = ptf.getCodigo();
                    if (ColecaoUtils.exists(progsJSON, o -> {
                        ProgramaTreinoFichaJSON ptfJ = (ProgramaTreinoFichaJSON) o;
                        return ptfJ.getCodigo() != null && ptfJ.getCodigo().equals(codPtf);
                    })) {
                        fichaPreparada.getProgramas().remove(ptf);
                        programaTreinoFichaDao.deleteNoClear(ctx, ptf);
                    }
                }
            }
            List<AtividadeFichaWriteJSON> atvsJSON = fw.getAtividadesFicha();
            if (atvsJSON != null) {
                List<AtividadeFicha> tmpAtividades = ImmutableList.copyOf(fichaPreparada.getAtividades());
                for (AtividadeFicha atvf : tmpAtividades) {
                    final Integer codAtvf = atvf.getCodigo();
                    if (codAtvf != null) {
                        AtividadeFichaWriteJSON atvfJSON = (AtividadeFichaWriteJSON) ColecaoUtils.find(atvsJSON, o -> {
                            AtividadeFichaWriteJSON atvfJ = (AtividadeFichaWriteJSON) o;
                            return atvfJ.getCodigo() != null && atvfJ.getCodigo().equals(codAtvf);
                        });
                        if (atvfJSON == null) {
                            fichaPreparada.getAtividades().remove(atvf);
                            try {
                                atividadeFichaDao.deleteNoClear(ctx, atvf);
                            } catch (Exception e) {
                                Uteis.logar(null, "IGNORADO: " + e.getMessage());
                            }
                            //se já excluiu atividadeFicha não tem porque continuar fazer algo com os filhos dela
                            //evitando erros também
                            continue;
                        } else {
                            List<SerieWriteJSON> seriesJ = atvfJSON.getSeries();
                            if (seriesJ != null) {
                                List<Serie> tmpSeries = ImmutableList.copyOf(atvf.getSeries());
                                for (Serie s : tmpSeries) {
                                    final Integer codSerie = s.getCodigo();

                                    Integer iAtividadeFicha;
                                    if (s.getAtividadeFicha() != null) {
                                        iAtividadeFicha = s.getAtividadeFicha().getCodigo();
                                    } else {
                                        iAtividadeFicha = null;
                                    }
                                    final Integer codAtividadeFicha = iAtividadeFicha;
                                    final double carga = s.getCarga();
                                    final String complemento = s.getComplemento();
                                    final Integer duracao = s.getDuracao();
                                    final Integer repeticao = s.getRepeticao();
                                    final double velocidade = s.getVelocidade();


                                    if (codSerie != null) {
                                        SerieWriteJSON sJ = (SerieWriteJSON) ColecaoUtils.find(seriesJ, o -> {
                                            SerieWriteJSON serieJ = (SerieWriteJSON) o;
                                            return (serieJ.getCodigo() != null && serieJ.getCodigo().equals(codSerie)) ||
                                                    (serieJ.getCodigo() == null && serieJ.getCarga().equals(carga) && serieJ.getComplemento().equals(complemento)
                                                            && serieJ.getDuracao().equals(duracao) && serieJ.getRepeticao().equals(repeticao) &&
                                                            serieJ.getVelocidade().equals(velocidade) && serieJ.getAtividadeFicha().equals(codAtividadeFicha));
                                        });
                                        if (sJ == null) {
                                            atvf.getSeries().remove(s);
                                            serieDao.deleteNoClear(ctx, s);
                                        }
                                    }
                                }
                            }
                            List<AtividadeFichaAjusteWriteJSON> ajustesJ = atvfJSON.getAjustes();
                            if (ajustesJ.size() == 0){
                                List<AtividadeFichaAjuste> tmpAjustes = ImmutableList.copyOf(atvf.getAjustes());
                                for (AtividadeFichaAjuste ajuste : tmpAjustes) {
                                    atividadeFichaAjusteDao.deleteNoClear(ctx, ajuste);
                                }
                                atvf.getAjustes().clear();
                            } else {
                                List<AtividadeFichaAjuste> tmpAjustes = ImmutableList.copyOf(atvf.getAjustes());
                                for (AtividadeFichaAjuste ajuste : tmpAjustes) {
                                    final Integer codAjuste = ajuste.getCodigo();
                                    if (codAjuste != null) {
                                        AtividadeFichaAjusteWriteJSON ajusteJ = (AtividadeFichaAjusteWriteJSON) ColecaoUtils.find(ajustesJ, o -> ((AtividadeFichaAjusteWriteJSON) o).getCodigo() != null
                                                && ((AtividadeFichaAjusteWriteJSON) o).getCodigo().equals(codAjuste));
                                        if (ajusteJ == null) {
                                            atvf.getAjustes().remove(ajuste);
                                            atividadeFichaAjusteDao.deleteNoClear(ctx, ajuste);
                                        }
                                    }
                                }
                            }
                        }
                    }
                    serieService.arrumarOrdem(atvf.getSeries());
                    serieService.salvarSeries(ctx, atvf, fichaPreparada);
                }
            }
            return fichaPreparada;
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }



    @Override
    public AtividadeFicha obterAtividadeFichaPorId(final String ctx, final Integer id) throws ServiceException{
        try{
            return atividadeFichaDao.findById(ctx, id);
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    @Override
    public void trocarFichaDaAtividade(String ctx, AtividadeFichaTO atividadeTO, Map<String, Ficha> mapaFichas, Integer ordem) throws ServiceException{
        try{
            Ficha f = mapaFichas.get(atividadeTO.getFicha());
            AtividadeFicha atividadeFicha = obterAtividadeFichaPorId(ctx, atividadeTO.getCodigoAtividadeFicha());
            atividadeFicha.setFicha(f);
            atividadeFicha.setOrdem(ordem);
            getAtividadeFichaDao().update(ctx, atividadeFicha);
            atividadeTO.setFichaOriginal(atividadeTO.getFicha());

        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public ProgramaTreino obterProgramaPorFicha(final String ctx, final Integer ficha) throws ServiceException {
        try {
            String query = "SELECT obj.programa from ProgramaTreinoFicha obj "
                    + "WHERE obj.ficha.codigo = :ficha";
            HashMap<String, Object> p = new HashMap<String, Object>();
            p.put("ficha", ficha);

            ProgramaTreino programaTreino = programaTreinoDao.findObjectByParam(ctx, query, p);
            programaTreinoService.refresh(ctx, programaTreino);
            return programaTreino;
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    public Ficha atualizarUltimaExecucao(final String ctx, Date dataUltimaExecucao, Integer idFicha) throws ServiceException {
        try {
            Ficha ficha = fichaDao.findById(ctx, idFicha);
            if (ficha != null && ficha.getUltimaExecucao().before(dataUltimaExecucao)) {
                ficha.setUltimaExecucao(dataUltimaExecucao);
                fichaDao.update(ctx, ficha);
                return ficha;
            } else {
                throw new Exception("Ficha não encontrada!");
            }
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

     @Override
    public void refresh(String ctx, Ficha object) throws Exception {
        getFichaDao().refresh(ctx, object);
    }

    public void refresh(String ctx, AtividadeFicha object) throws Exception {
        getAtividadeFichaDao().refresh(ctx, object);
    }

    public void refresh(String ctx, ProgramaTreinoFicha object) throws Exception {
        programaTreinoFichaDao.refresh(ctx, object);
    }

    @Override
    public Ficha alterarSimples(final String ctx, Ficha object, ProgramaTreinoFicha ptf) throws ServiceException {
        try {
            getValidacao().validarDadosBasicos(object);

            if(ptf != null){
                ptf = programaTreinoFichaDao.update(ctx, ptf);
            }

            object = fichaDao.update(ctx, object);

            atualizarVersaoFicha(ctx, object.getCodigo());

            refresh(ctx, object);
            if(ptf != null) {
                refresh(ctx, ptf);
            }
        } catch (ValidacaoException ex) {
            throw ex;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
        return object;
    }

    private Ficha alterarFicha(final String ctx, Ficha object) throws ServiceException {
        try {
            getValidacao().validarDadosBasicos(object);

            object = fichaDao.atualizarAlgunsCampos(ctx, object);

            atualizarVersaoFicha(ctx, object.getCodigo());
        } catch (Exception e) {
            throw new ServiceException(e);
        }

        return object;
    }

    public String sugerirNomeFicha(List fichas) {
        String nomeFicha = "";
        char letra = 'a';
        List<String> nomesFichas = new ArrayList<String>();
        for (Object o : fichas) {
            Ficha f;
            if(o instanceof ProgramaTreinoFicha){
                f = ((ProgramaTreinoFicha)o).getFicha();
            }else{
                f = (Ficha) o;
            }

            nomesFichas.add(f.getNome().toUpperCase());
        }
        for (int i = 0; i <= 26; i++) {
            if (!nomesFichas.contains(("FICHA " + letra).toUpperCase())) {
                nomeFicha = ("FICHA " + letra).toUpperCase();
                break;
            }
            letra++;
        }

        if (StringUtils.isBlank(nomeFicha)) {
            int limite = Integer.MAX_VALUE;
            for (int i = 1; i < limite; i++) {
                if (!nomesFichas.contains(("FICHA " + i).toUpperCase())) {
                    nomeFicha = ("FICHA " + i).toUpperCase();
                    break;
                }
            }
        }

        return nomeFicha;
    }


    public void excluirFicha(final String ctx, Ficha ficha, ProgramaTreino programa) throws Exception{
        excluir(ctx, ficha);
        programaTreinoService.atualizarVersaoPrograma(ctx, programa);
        programaTreinoService.atualizarNrTreinosRealizados(ctx, programa.getCodigo());


    }

    public void aplicarPadraoSeries(final String ctx,
                                                    Ficha ficha,
                                                    List<AtividadeFicha> atvs,
                                                    Integer nrSeries,
                                                    Serie serie,
                                                    boolean alternar) throws Exception{
        for(AtividadeFicha aa : atvs){
            if(aa.getEscolhida()){
                Integer nrSeriesDaAtividade = aa.getSeries().size();
                if(nrSeries != null){
                    nrSeriesDaAtividade = nrSeries;
                }

                serieDao.deleteComParam(ctx, new String[]{"atividadeFicha.codigo"}, new Object[]{aa.getCodigo()});
                aa.setSeries(new ArrayList<Serie>());
                if (aa.getAtividade().getAerobica()) {
                    aa.addSeries(aa, 1, Uteis.converterMinutosEmSegundos(serie.getDuracaoStr()),
                            serie.getDistancia(), serie.getVelocidade(),
                            Uteis.converterMinutosEmSegundos(serie.getDescansoStr()));
                } else {
                    aa.addSeries(aa, nrSeriesDaAtividade, serie.getRepeticoesVetor(), serie.getCargaVetor(), Uteis.converterMinutosEmSegundos(serie.getDescansoStr()), false, alternar,"");
                }
                for (Serie s : aa.getSeries()) {
                    s.setAtividadeFicha(aa);
                    s.setCargaApp(s.getCargaComp());
                    s.setRepeticaoApp(s.getRepeticaoComp());
                    s.setAtualizadoApp(true);
                    serieService.inserir(ctx, s);
                }
            }

        }
    }

    public List<String> atividadesSugeridas(final String ctx,
                                            Integer professor,
                                            String nomeFicha,
                                            Integer idade,
                                            String sexo) throws Exception {
        Map<String, Integer> atividadesSugeridas = mapAtividadesSugeridas(ctx,
                professor,
                nomeFicha,
                idade,
                sexo, true);
        List<GenericoTO> is = new ArrayList<GenericoTO>();
        for(String s : atividadesSugeridas.keySet()){
            is.add(new GenericoTO(atividadesSugeridas.get(s), s));
        }
        List<String> sugeridas = new ArrayList<String>();
        List<GenericoTO> ordem = Ordenacao.ordenarLista(is, "codigo");
        for(GenericoTO g : ordem){
            sugeridas.add(g.getLabel());
        }
        return sugeridas;
    }

    public Map<String, Integer> mapAtividadesSugeridas(final String ctx,
                                            Integer professor,
                                            String nomeFicha,
                                            Integer idade,
                                            String sexo,
                                            boolean loop) throws Exception{

        StringBuilder sql = new StringBuilder();
        sql.append(" select a.nome, count(a.codigo) as usos, cast(avg(af.ordem) as integer) as posicao from atividade a\n");
        sql.append(" inner join atividadeficha af on af.atividade_codigo = a.codigo\n" );
        sql.append(" inner join ficha f on f.codigo = af.ficha_codigo\n" );
        sql.append(" inner join programatreinoficha ptf on ptf.ficha_codigo = f.codigo\n" );
        sql.append(" inner join programatreino pt on pt.codigo = ptf.programa_codigo\n" );
        sql.append(" inner join clientesintetico cli on cli.codigo = pt.cliente_codigo\n" );
        sql.append(" where 1 = 1 ");

        if(professor != null){
            sql.append(" and pt.professorMontou_codigo = ").append(professor);
        }

        if(!loop && !UteisValidacao.emptyString(nomeFicha)){
            sql.append(" and f.nome ilike '%").append(nomeFicha).append("%'\n" );
        }
        if(!UteisValidacao.emptyNumber(idade)){
            sql.append(" and cli.idade between ").append(idade-5).append(" and ").append(idade+5);
        }
        if(!UteisValidacao.emptyString(sexo)){
            sql.append(" and cli.sexo = '").append(sexo).append("' ");
        }
        sql.append(" group by a.nome\n" );
        sql.append(" order by 2 desc limit 10");
        Map<String, Integer> mapa = new HashMap<String, Integer>();

        List listOfObjects = atividadeDao.listOfObjects(ctx, sql.toString());
        for (Object o : listOfObjects) {
            Object[] arr = (Object[]) o;
            String nome = (String) arr[0];
            Integer ordem = (Integer) arr[2];
            mapa.put(nome, ordem);
        }

        if(mapa.size() < 10 && !loop){
            Map<String, Integer> map2 = mapAtividadesSugeridas(ctx, null, null, idade, sexo, false);
            for(String s : map2.keySet()){
                if(!mapa.keySet().contains(s)){
                    mapa.put(s, map2.get(s));
                }
            }
        }

        if(mapa.size() < 10 && !loop){
            Map<String, Integer> map2 = mapAtividadesSugeridas(ctx, null, null, null, sexo, false);
            for(String s : map2.keySet()){
                if(!mapa.keySet().contains(s)){
                    mapa.put(s, map2.get(s));
                }
            }
        }

        if(mapa.size() < 10 && !loop){
            Map<String, Integer> map2 = mapAtividadesSugeridas(ctx, professor, null, null, null, false);
            for(String s : map2.keySet()){
                if(!mapa.keySet().contains(s)){
                    mapa.put(s, map2.get(s));
                }
            }
        }

        if(mapa.size() < 10 && !loop){
            Map<String, Integer> map2 = mapAtividadesSugeridas(ctx, null, nomeFicha, null, null, false);
            for(String s : map2.keySet()){
                if(!mapa.keySet().contains(s)){
                    mapa.put(s, map2.get(s));
                }
            }
        }



        return mapa;

    }

    @Override
    public List<FichaResponseTO> consultarFichasPreDefinidas(FiltroFichaPredefinidaJSON filtroFichaPredefinidaJSON, Integer categoriaId, String nome, PaginadorDTO paginadorDTO) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            if (!StringUtils.isBlank(paginadorDTO.getSort())) {
                paginadorDTO.setSort(paginadorDTO.getSort());
            } else {
                paginadorDTO.setSort("nome,ASC");
            }
            return fichaDao.consultarFichasPreDefinidas(ctx, categoriaId, nome, filtroFichaPredefinidaJSON, paginadorDTO);
        } catch (Exception ex) {
            throw new ServiceException(FichaExcecoes.ERRO_BUSCAR_FICHAS);
        }
    }

    public void validarDados(String ctx, Ficha object) throws ServiceException {
        if (object.getNome() == null || object.getNome().trim().isEmpty()) {
            if (!JSFUtilities.isJSFContext()){
                throw new ServiceException(FichaExcecoes.VALIDACAO_NOME_FICHA);
            }
            throw new  ValidacaoException("validacao.nome");
        }
        /*
        ...
        */
    }

    private Ficha montarFichaPredefinida(String ctx, Ficha ficha, FichaDTO fichaDTO) throws ServiceException {
        try {
            if (!UteisValidacao.emptyNumber(fichaDTO.getCategoriaId())) {
                CategoriaFicha cf = categoriaFichaDao.findById(ctx, fichaDTO.getCategoriaId());

                if (cf == null || UteisValidacao.emptyNumber(cf.getCodigo())) {
                    throw new ServiceException(FichaExcecoes.CATEGORIA_FICHA_NAO_ENCONTRADA);
                }
                ficha.setCategoria(cf);
            }
            ficha.setNome(fichaDTO.getNome());
            ficha.setAtivo(fichaDTO.getAtivo() != null ? fichaDTO.getAtivo() : true);
            ficha.setMensagemAluno(fichaDTO.getMensagem());
            ficha.setUsarComoPredefinida(fichaDTO.getPredefinida());
            ConfiguracaoSistema config = configuracaoSistemaService.consultarPorTipo(ctx, ConfiguracoesEnum.SUGERIR_DESCANSO);
            ficha.setDescansoRepetir(config.getValor());

            if (UteisValidacao.emptyList(ficha.getAtividades())) {
                ficha.getAtividades().addAll(montarAtividadesFicha(ctx, fichaDTO.getAtividades(), ficha));
            } else {
                atualizarFilhas(ctx, ficha, fichaDTO);
            }
            ficha.setVersao(atualizarVersaoFichaPredefinida(ficha.getVersao()));
            return ficha;
        } catch (Exception ex) {
            throw new ServiceException(FichaExcecoes.ERRO_MONTAR_FICHA, ex);
        }
    }

    private Ficha montarFichaNaoPredefinida(String ctx, Ficha ficha, FichaDTO fichaDTO) throws ServiceException {
        try {
            ProgramaTreino pt = programaTreinoDao.findById(ctx, fichaDTO.getProgramaId());
            if (pt == null || UteisValidacao.emptyNumber(pt.getCodigo())) {
                throw new ServiceException(FichaExcecoes.PROGRAMA_NAO_ENCONTRADO);
            }
            if (!UteisValidacao.emptyNumber(fichaDTO.getCategoriaId())) {
                CategoriaFicha cf = categoriaFichaDao.findById(ctx, fichaDTO.getCategoriaId());

                if (cf == null || UteisValidacao.emptyNumber(cf.getCodigo())) {
                    throw new ServiceException(FichaExcecoes.CATEGORIA_FICHA_NAO_ENCONTRADA);
                }
                ficha.setCategoria(cf);
            }
            ficha.setNome(fichaDTO.getNome());
            ficha.setAtivo(true);
            ficha.setMensagemAluno(fichaDTO.getMensagem());
            ConfiguracaoSistema config = configuracaoSistemaService.consultarPorTipo(ctx, ConfiguracoesEnum.SUGERIR_DESCANSO);
            ficha.setDescansoRepetir(config.getValor());
            if (!UteisValidacao.emptyList(ficha.getProgramas())) {
                for (ProgramaTreinoFicha programaTreinoFicha : ficha.getProgramas()) {
                    programaTreinoFicha.setDiaSemana(fichaDTO.getDias_semana());
                    programaTreinoFicha.setTipoExecucao(fichaDTO.getTipo_execucao());
                }
            } else {
                ProgramaTreinoFicha programaTreinoFicha = new ProgramaTreinoFicha();
                programaTreinoFicha.setTipoExecucao(fichaDTO.getTipo_execucao());
                programaTreinoFicha.setDiaSemana(fichaDTO.getDias_semana());
                programaTreinoFicha.setFicha(ficha);
                programaTreinoFicha.setPrograma(pt);

                ficha.getProgramas().add(programaTreinoFicha);
            }

            if (UteisValidacao.emptyList(ficha.getAtividades())) {
                ficha.getAtividades().addAll(montarAtividadesFicha(ctx, fichaDTO.getAtividades(), ficha));
            } else {
                atualizarFilhas(ctx, ficha, fichaDTO);
            }
            return ficha;
        } catch (Exception ex) {
            throw new ServiceException(FichaExcecoes.ERRO_MONTAR_FICHA, ex);
        }
    }

    private void atualizarFilhas(String ctx, Ficha ficha, FichaDTO fichaDTO) throws Exception {
        List<AtividadeFicha> atividadesRemovida = new ArrayList<>();
        List<Serie> seriesRemovida = new ArrayList<>();

        /**
         * Filtrando atividades e series removidas
         */
        for (AtividadeFicha atividadeFicha : ficha.getAtividades()) {
            boolean atividadeRemovida = true;
            for (AtividadeFichaDTO atividadeFichaDTO : fichaDTO.getAtividades()) {
                if (!UteisValidacao.emptyNumber(atividadeFichaDTO.getId()) && atividadeFichaDTO.getId().equals(atividadeFicha.getCodigo())) {
                    atividadeFicha.setMetodoExecucao(atividadeFichaDTO.getMetodoExecucaoEnum());
                    atividadeFicha.setIntensidade(atividadeFichaDTO.getEsforco());
                    atividadeFicha.setOrdem(atividadeFichaDTO.getSequencia());
                    atividadeRemovida = false;
                    atividadeFicha.setComplementoNomeAtividade(atividadeFichaDTO.getComplementoNomeAtividade());
                    for (Serie serie : atividadeFicha.getSeries()) {
                        boolean serieRemovida = true;
                        for (SerieEndpointTO serieDTO : atividadeFichaDTO.getSeries()) {
                            if (!UteisValidacao.emptyNumber(serieDTO.getId()) && serieDTO.getId().equals(serie.getCodigo())) {
                                serie.setCadencia(serieDTO.getCadencia());
                                serie.setCargaComp(serieDTO.getCarga());
                                serie.setComplemento(serieDTO.getComplemento());
                                serie.setDescanso(serieDTO.getDescanso());
                                serie.setDistancia(serieDTO.getDistancia());
                                serie.setDuracao(serieDTO.getDuracao());
                                serie.setRepeticaoComp(serieDTO.getRepeticoes());
                                serie.setOrdem(serieDTO.getSequencia());
                                serie.setVelocidade(serieDTO.getVelocidade());
                                serieRemovida = false;
                            }
                        }
                        if (serieRemovida) {
                            seriesRemovida.add(serie);
                        }
                    }
                }
            }
            if (atividadeRemovida) {
                atividadesRemovida.add(atividadeFicha);
            }
        }

        /**
         * Filtrando Atividades e Series add
         */
        List<AtividadeFichaDTO> atividadesAdd = new ArrayList<>();
        Map<Integer, List<SerieEndpointTO>> seriesMapAdd = new HashMap<>();
        for (AtividadeFichaDTO atividadeFichaDTO : fichaDTO.getAtividades()) {
            if (UteisValidacao.emptyNumber(atividadeFichaDTO.getId())) {
                atividadesAdd.add(atividadeFichaDTO);
            } else {
                List<SerieEndpointTO> novasSeries = new ArrayList<>();
                for (SerieEndpointTO serieDTO : atividadeFichaDTO.getSeries()) {
                    if (UteisValidacao.emptyNumber(serieDTO.getId())) {
                        novasSeries.add(serieDTO);
                    }
                }
                if (!UteisValidacao.emptyList(novasSeries)) {
                    seriesMapAdd.put(atividadeFichaDTO.getId(), novasSeries);
                }
            }
        }

        /**
         * Add na instância as atividades e series
         */
        if (!UteisValidacao.emptyList(atividadesAdd)) {
            ficha.getAtividades().addAll(montarAtividadesFicha(ctx, atividadesAdd, ficha));
        }
        for (Map.Entry<Integer, List<SerieEndpointTO>> serieNovaMap : seriesMapAdd.entrySet()) {
            for (AtividadeFicha atividadeFicha : ficha.getAtividades()) {
                if (serieNovaMap.getKey().equals(atividadeFicha.getCodigo())) {
                    atividadeFicha.getSeries().addAll(montarSeriesAtividade(atividadeFicha, serieNovaMap.getValue()));
                }
            }
        }

        /**
         * Deletando do banco e da instância as atividades removidas
         */
        if (!UteisValidacao.emptyList(atividadesRemovida)) {
            List<Integer> atividadesRemovidaId = new ArrayList<>();
            for (AtividadeFicha atividadeFicha : atividadesRemovida) {
                atividadesRemovidaId.add(atividadeFicha.getCodigo());
                ficha.getAtividades().remove(atividadeFicha);
            }
            atividadeFichaDao.removeList(ctx, atividadesRemovidaId);
        }

        /**
         * Deletando do banco e da instância as series removidas
         */
        if (!UteisValidacao.emptyList(seriesRemovida)) {
            for (Serie serie : seriesRemovida) {
                for (AtividadeFicha atividadeFicha : ficha.getAtividades()) {
                    if (!UteisValidacao.emptyNumber(atividadeFicha.getCodigo()) && atividadeFicha.getCodigo().equals(serie.getAtividadeFicha().getCodigo())) {
                        atividadeFicha.getSeries().remove(serie);
                    }
                }
            }
            serieDao.removeList(ctx, seriesRemovida);
        }
    }

    private List<AtividadeFicha> montarAtividadesFicha(String ctx, List<AtividadeFichaDTO> atividadesFichaDTO, Ficha ficha) throws ServiceException {
        try {
            List<AtividadeFicha> ret = new ArrayList<>();
            for (AtividadeFichaDTO atividadeFichaDTO : atividadesFichaDTO) {
                AtividadeFicha atividadeFicha = new AtividadeFicha();
                atividadeFicha.setAtividade(atividadeDao.findById(ctx, atividadeFichaDTO.getAtividadeId()));
                atividadeFicha.setNome(atividadeFicha.getAtividade().getNome());
                atividadeFicha.setMetodoExecucao(atividadeFichaDTO.getMetodoExecucaoEnum());
                atividadeFicha.setIntensidade(atividadeFichaDTO.getEsforco());
                atividadeFicha.setOrdem(atividadeFichaDTO.getSequencia());
                atividadeFicha.getSeries().addAll(montarSeriesAtividade(atividadeFicha, atividadeFichaDTO.getSeries()));
                atividadeFicha.setFicha(ficha);
                atividadeFicha.setSetId("");
                atividadeFicha.setComplementoNomeAtividade(atividadeFichaDTO.getComplementoNomeAtividade());

                ret.add(atividadeFicha);
            }

            return ret;
        } catch (Exception ex) {
            throw new ServiceException(FichaExcecoes.ATIVIDADE_FICHA_NAO_ENCONTRADA);
        }
    }

    private List<Serie> montarSeriesAtividade(AtividadeFicha atividadeFicha, List<SerieEndpointTO> serieTOS) {
        List<Serie> ret = new ArrayList<>();
        for (SerieEndpointTO serieTO : serieTOS) {
            Serie serie = new Serie();
            serie.setAtividadeFicha(atividadeFicha);
            serie.setCadencia(serieTO.getCadencia());
            serie.setCargaComp(serieTO.getCarga());
            try {
                serie.setCarga(serieTO.getCarga().equals("") ? 0 : Double.parseDouble(serieTO.getCarga()));
            } catch (Exception e) {}
            serie.setComplemento(serieTO.getComplemento() == null ? "" : serieTO.getComplemento());
            serie.setDescanso(serieTO.getDescanso());
            serie.setDistancia(serieTO.getDistancia());
            serie.setDuracao(serieTO.getDuracao());
            serie.setRepeticaoComp(serieTO.getRepeticoes());
            if (serieTO.getRepeticoes().matches("[0-9]*")) {
                serie.setRepeticao(serieTO.getRepeticoes().equals("") ? 0 : Integer.parseInt(serieTO.getRepeticoes()));
            }
            serie.setOrdem(serieTO.getSequencia());
            serie.setVelocidade(serieTO.getVelocidade());

            ret.add(serie);
        }

        return ret;
    }

    private Ficha montarFicha(String ctx, Boolean nova, Integer preDefinidoId, FichaTO fichaTO) throws ServiceException {
        try {
            Ficha ficha = null;
            if (nova) {
                if (fichaTO == null || fichaTO.getProgramaId() == null || fichaTO.getProgramaId() < 1) {
                    throw new ServiceException(FichaExcecoes.PROGRAMA_NAO_ENCONTRADO);
                }
                if (preDefinidoId != null && preDefinidoId > 0) {
                    Ficha fichaPD = obterPorId(ctx, preDefinidoId);
                    if (fichaPD == null) {
                        throw new ServiceException(FichaExcecoes.FICHA_PREDEFINIDA_NAO_ENCONTRADA);
                    }
                    if (!fichaPD.isUsarComoPredefinida()) {
                        throw new ServiceException(FichaExcecoes.FICHA_PREDEFINIDA_INVALIDA);
                    }
                    ProgramaTreino pt = programaTreinoDao.findById(ctx, fichaTO.getProgramaId());
                    if (pt == null) {
                        throw new ServiceException(FichaExcecoes.PROGRAMA_NAO_ENCONTRADO);
                    }
                    ProgramaTreinoFicha ptf = new ProgramaTreinoFicha();
                    ptf.setPrograma(pt);

                    ficha = new Ficha();
                    ficha.setCategoria(fichaPD.getCategoria());
                    ptf.setFicha(ficha);
                    ptf.setTipoExecucao(TipoExecucaoEnum.ALTERNADO);
                    ficha.getProgramas().add(ptf);
                    ficha.setNome(fichaPD.getNome());
                    ficha.setAtividades(new ArrayList<>());
                    for (AtividadeFicha af : obterAtividadesFicha(ctx, fichaPD.getCodigo())) {
                        AtividadeFicha afClone = af.getClone();
                        afClone.setSeries(new ArrayList<Serie>());
                        for (Serie serie : af.getSeries()) {
                            Serie serieClone = serie.getClone();
                            serieClone.setCodigo(null);
                            serieClone.setAtividadeFicha(afClone);
                            afClone.getSeries().add(serieClone);
                        }
                        afClone.setCodigo(null);
                        afClone.setFicha(ficha);
                        afClone.setAjustes(null);
                        ficha.getAtividades().add(afClone);
                    }
                    ficha.setMensagemAluno(fichaPD.getMensagemAluno());
                } else {
                    CategoriaFicha cf = categoriaFichaDao.findById(ctx, fichaTO.getCategoriaId());
                    if (cf == null) {
                        throw new ServiceException(FichaExcecoes.CATEGORIA_FICHA_NAO_ENCONTRADA);
                    }
                    ProgramaTreino pt = programaTreinoDao.findById(ctx, fichaTO.getProgramaId());
                    if (pt == null) {
                        throw new ServiceException(FichaExcecoes.PROGRAMA_NAO_ENCONTRADO);
                    }
                    ProgramaTreinoFicha ptf = new ProgramaTreinoFicha();
                    ptf.setPrograma(pt);
                    ptf.setDiaSemana(fichaTO.getDias_semana());
                    ptf.setTipoExecucao(fichaTO.getTipo_execucao());

                    ficha = new Ficha();
                    ficha.setNome(fichaTO.getNome());
                    ficha.setCategoria(cf);
                    ficha.setProgramas(new ArrayList<ProgramaTreinoFicha>());
                    ptf.setFicha(ficha);
                    ficha.getProgramas().add(ptf);
                    ficha.setMensagemAluno(fichaTO.getMensagem());
                }
            } else {
                if (fichaTO.getId() == null || fichaTO.getId() < 1) {
                    // Edição, mas não tem o id da ficha que será alterada
                    throw new ServiceException(FichaExcecoes.FICHA_NAO_ENCONTRADA);
                }
                ficha = obterPorId(ctx, fichaTO.getId());
                if (ficha == null) {
                    throw new ServiceException(FichaExcecoes.FICHA_NAO_ENCONTRADA);
                }
                ficha.setNome(fichaTO.getNome());
                if (fichaTO.getCategoriaId() == null || fichaTO.getCategoriaId() < 1) {
                    ficha.setCategoria(null);
                } else if (ficha.getCategoria() == null || fichaTO.getCategoriaId() != ficha.getCategoria().getCodigo()) {
                    CategoriaFicha cf =  categoriaFichaDao.findById(ctx, fichaTO.getCategoriaId());
                    if (cf == null) {
                        throw new ServiceException(FichaExcecoes.CATEGORIA_FICHA_NAO_ENCONTRADA);
                    }
                    ficha.setCategoria(cf);
                }
                if (ficha.getProgramas() != null) {
                    for (ProgramaTreinoFicha ptf : ficha.getProgramas()) {
                        ptf.setTipoExecucao(fichaTO.getTipo_execucao());
                        ptf.setDiaSemana(fichaTO.getDias_semana());
                    }
                }
                ficha.setMensagemAluno(fichaTO.getMensagem());
            }

            validarDados(ctx, ficha);
            return ficha;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            if (nova) {
                throw new ServiceException(FichaExcecoes.ERRO_MONTAR_FICHA);
            } else {
                throw new ServiceException(FichaExcecoes.ERRO_BUSCAR_FICHA);
            }
        }
    }

    @Override
    public FichaResponseTO criarFicha(Integer preDefinidoId,
                                      Integer programaId, HttpServletRequest request,
                                      String chaveOrigem,
                                      String nomeFicha) throws ServiceException {
        Integer fichaId = null;
        String ctx = sessaoService.getUsuarioAtual().getChave();
        ProgramaTreino programa = new ProgramaTreino();
        try {
            Ficha fichaPreDefinida = null;
            if (!UteisValidacao.emptyNumber(programaId)) {
                programa = programaTreinoService.obterPorId(ctx, programaId);
                programaTreinoService.acao(programa, TipoRevisaoEnum.UPDATE);
            } else {
                throw new ServiceException(ProgramaTreinoExcecoes.ERRO_BUSCAR_PROGRAMA_TREINO);
            }
            if (!UteisValidacao.emptyNumber(preDefinidoId)) {
                if(UteisValidacao.emptyString(chaveOrigem)){
                    fichaPreDefinida = fichaDao.findById(ctx, preDefinidoId);
                    if (fichaPreDefinida == null || UteisValidacao.emptyNumber(fichaPreDefinida.getCodigo())) {
                        throw new ServiceException(FichaExcecoes.FICHA_PREDEFINIDA_INVALIDA);
                    }
                } else {
                    fichaPreDefinida = obterFichaDeOutraOrigem(ctx, chaveOrigem, preDefinidoId, nomeFicha);
                }
            }
            List<ProgramaTreinoFicha> listTreinoFicha = programaTreinoService.obterProgramasFichasPorPrograma(ctx, programaId);

            ProgramaTreinoFicha programaTreinoFicha = programaTreinoService.novaFicha(ctx, listTreinoFicha, programa, fichaPreDefinida);
            fichaId = programaTreinoFicha.getFicha().getCodigo();
            programaTreinoService.atualizarVersaoPrograma(ctx, programa);
            String username = sessaoService.getUsuarioAtual().getUsername();
            Usuario usuario = usuarioService.obterPorAtributo(ctx, "username", username);
            if (usuario != null && !usuario.getTipo().equals(TipoUsuarioEnum.ALUNO)
                    && usuario.getProfessor() != null && (programa.getPreDefinido() == null || !programa.getPreDefinido())) {
                try {
                    programa.manterAntesAlteracao();
                    programaTreinoService.gravarHistoricoRevisoes(ctx, programa, "REVISADO", Calendario.proximoDeveSerUtil(programa.getDataInicio(), 7), usuario.getProfessor());
                }catch (Exception e){
                    Uteis.logar(e, FichaServiceImpl.class);
                }
            }
            preencherAtividade(ctx, programaTreinoFicha.getFicha().getAtividades());
            Map<String, String> appInfo = obtemNomeEVersaoApp(request);
            String nomeApp = appInfo.get("nomeApp");
            String versaoApp = appInfo.get("versaoApp");

            incluirLog(ctx, programaTreinoFicha.getFicha().getCodigo().toString(), programaId.toString(), "", programaTreinoFicha.getFicha().getDescricaoParaLog(null),
                    "INCLUSÃO", "INCLUSÃO DE FICHA", EntidadeLogEnum.FICHA, "Ficha", sessaoService, logDao, nomeApp, versaoApp);
            return new FichaResponseTO(programaTreinoFicha.getFicha(), programaTreinoFicha.getDiaSemana());
        }catch (ServiceException e) {
            throw e;
        }catch (Exception e) {
            throw new ServiceException(FichaExcecoes.ERRO_INCLUIR_FICHA, e);
        } finally {
            boolean predefinido = programa.getPreDefinido() != null && programa.getPreDefinido() ? true : false;
            if (!predefinido) {
                programaTreinoService.notificarOuvintes(ctx, programa.getCliente() == null ? null : programa.getCliente().getCodigo(),
                        programa.getCodigoColaborador(), request);
            }
            programaTreinoService.leaveAcao();
        }
    }

    public Ficha obterFichaDeOutraOrigem(String key, String chaveOrigem, Integer idFicha, String nome) throws Exception {
        try {
            FichaWriteJSON fichaIA = fichaIA(idFicha, chaveOrigem);
            List<AtividadeFicha> atividadesFicha = new ArrayList<>();
                for(AtividadeFichaWriteJSON af : fichaIA.getAtividadesFicha()){
                    Atividade atividade = atividadeDao.obterPorIDIA(key, af.getIdIA());
                    if(atividade == null){
                        atividade = atividadeDao.obterPorNomeExato(key, af.getNomeAtividade());
                        if(atividade == null){
                            atividade = new Atividade();
                            atividade.setNome(af.getNomeAtividade());
                            atividade.setIdIA(af.getIdIA());
                            atividade.setNomeOriginalIA(af.getNomeAtividade());
                            atividade = atividadeDao.insert(key, atividade);
                        } else {
                            atividade.setIdIA(af.getIdIA());
                            atividade.setNomeOriginalIA(af.getNomeAtividade());
                            atividade = atividadeDao.update(key, atividade);
                        }

                    }
                    AtividadeFicha atividadeFicha = new AtividadeFicha();
                    atividadeFicha.setAtividade(atividade);
                    atividadeFicha.setNome(af.getNomeAtividade());
                    atividadeFicha.setOrdem(af.getOrdem());
                    atividadeFicha.setMetodoExecucao(MetodoExecucaoEnum.getFromId(af.getMetodoExecucao()));

                    List<Serie> series = new ArrayList<>();
                    for(SerieWriteJSON serieTO : af.getSeries()){
                        Serie serie = new Serie();
                        serie.setOrdem(serieTO.getOrdem() == 0 || serieTO.getOrdem() == null ?
                                af.getSeries().size() : serieTO.getOrdem());
                        serie.setRepeticaoComp(serieTO.getRepeticaoComp());
                        serie.setRepeticao(serieTO.getRepeticao());
                        serie.setCargaComp(serieTO.getCargaComp());
                        serie.setCarga(serieTO.getCarga());
                        serie.setarCompValores();
                        serie.setCadencia(serieTO.getCadencia());
                        serie.setDescanso(serieTO.getDescanso());
                        serie.setComplemento(serieTO.getComplemento());
                        serie.setVelocidade(serieTO.getVelocidade());
                        serie.setDuracao(serieTO.getDuracao());
                        serie.setDistancia(serieTO.getDistancia());
                        serie.setAtividadeFicha(atividadeFicha);
                        series.add(serie);
                    }
                    atividadeFicha.setSeries(series);
                    atividadesFicha.add(atividadeFicha);
                }
                Ficha fichaFranqueadora = new Ficha();
                fichaFranqueadora.setNome(UteisValidacao.emptyString(nome) ? fichaIA.getNome() : nome);
                fichaFranqueadora.setMensagemAluno(fichaIA.getMensagemAluno());
                return new Ficha(fichaFranqueadora, atividadesFicha, false);
        }catch (Exception e){
            Uteis.logar(e, ProgramaTreinoServiceImpl.class);
            throw e;
        }
    }

    private FichaWriteJSON fichaIA(Integer id, String chaveOrigem) throws Exception{
        String treinoUrl = mapaUrlExterna.get(chaveOrigem);
        if(treinoUrl == null){
            JSONObject result = Uteis.getJSON(Aplicacao.getProp(Aplicacao.discoveryUrls) + "/find/"+chaveOrigem);
            treinoUrl = result.getJSONObject("content").getJSONObject("serviceUrls").getString("treinoUrl");
            mapaUrlExterna.put(chaveOrigem, treinoUrl);
        }
        HttpPost httpPost = new HttpPost(treinoUrl + "/prest/ficha/" + chaveOrigem + "/get?username=pactobr&codigoFicha=" + id);
        httpPost.setHeader("Content-Type", "application/json");
        HttpClient client = ExecuteRequestHttpService.createConnectorCloseable();
        HttpResponse response = client.execute(httpPost);
        String responseBody = EntityUtils.toString(response.getEntity());
        JSONObject json = new JSONObject(responseBody);
        if (json.has("sucesso")) {
            return JSONMapper.getObject(json.getJSONObject("sucesso"), FichaWriteJSON.class);
        }
        throw new Exception("nao foi possivel obter a ficha IA");
    }

    private void preencherAtividade(String ctx, List<AtividadeFicha> atividadesFicha) throws Exception {
        if(atividadesFicha != null) {
            for (AtividadeFicha atividadeFicha : atividadesFicha) {
                atividadeFicha.getAtividade().setAnimacoesPadrao(new ArrayList<>());
                atividadeFicha.getAtividade().getAnimacoes().clear();
                atividadeFicha.getAtividade().setAnimacoes(atvAnimacaoDao.obterImagens(ctx, atividadeFicha.getAtividade().getCodigo()));
                StringBuilder where = new StringBuilder("where obj.atividade.codigo = :codigo");
                Map<String, Object> filtro = new HashMap<>();
                filtro.put("codigo", atividadeFicha.getAtividade().getCodigo());
                atividadeFicha.setSeriesPadrao(new ArrayList<>());
                atividadeFicha.getSeries().clear();
                atividadeFicha.setSeries(serieDao.obterPorAtividadeFicha(ctx, atividadeFicha.getCodigo()));
                atividadeFicha.getAtividade().setEmpresasHabilitadasPadrao(new ArrayList<>());
                atividadeFicha.getAtividade().getEmpresasHabilitadas().clear();
                atividadeFicha.getAtividade().setEmpresasHabilitadas(atividadeEmpresaDao.findByParam(ctx, where, filtro));
                atividadeFicha.getAtividade().setCategoriasPadrao(new ArrayList<>());
                atividadeFicha.getAtividade().getCategorias().clear();
                atividadeFicha.getAtividade().setCategorias(atividadeCategoriaAtividadeDao.findByParam(ctx, where, filtro));
                atividadeFicha.getAtividade().setAparelhosPadrao(new ArrayList<>());
                atividadeFicha.getAtividade().getAparelhos().clear();
                atividadeFicha.getAtividade().setAparelhos(atividadeAparelhoDao.findByParam(ctx, where, filtro));
                atividadeFicha.getAtividade().setGruposMuscularesPadrao(new ArrayList<>());
                atividadeFicha.getAtividade().getGruposMusculares().clear();
                atividadeFicha.getAtividade().setGruposMusculares(atividadeGrupoMuscularDao.findByParam(ctx, where, filtro));
                atividadeFicha.getAtividade().setMusculosPadrao(new ArrayList<>());
                atividadeFicha.getAtividade().getMusculos().clear();
                atividadeFicha.getAtividade().setMusculos(atividadeMusculoDao.findByParam(ctx, where, filtro));
                atividadeFicha.getAtividade().setNiveisPadrao(new ArrayList<>());
                atividadeFicha.getAtividade().getNiveis().clear();
                atividadeFicha.getAtividade().setNiveis(atividadeNivelDao.findByParam(ctx, where, filtro));
            }
        }
    }

    @Override
    public void criarFichaPredefinida(Integer fichaId) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();

            Ficha ficha = obterPorId(ctx, fichaId);
            if (ficha == null || UteisValidacao.emptyNumber(ficha.getCodigo())) {
                throw new ServiceException(FichaExcecoes.ERRO_BUSCAR_FICHA);
            }
            List<Ficha> fichas = obterPorNome(ctx, true, ficha.getNome(), null);
            if (!fichas.isEmpty()) {
                throw new ServiceException(FichaExcecoes.ERRO_REGISTRO_DUPLICADO);
            }
            tornarPreDefinida(ctx, ficha);
        } catch (ServiceException e) {
            throw new ServiceException(FichaExcecoes.ERRO_TORNAR_FICHA_PRE_DEFINIDA, e);
        }
    }

    @Override
    public FichaResponseTO editarFicha(FichaDTO fichaDTO, HttpServletRequest request) throws ServiceException {
        Ficha fichaAntesAlteracao;
        String ctx = sessaoService.getUsuarioAtual().getChave();
        if (validarExisteFichaPredefinidaComNome(ctx, fichaDTO.getId(), fichaDTO.getNome(), fichaDTO.getPredefinida())) {
            throw new ServiceException(FichaExcecoes.VALIDACAO_FICHA_PREDEFINIDA_JA_EXISTE);
        }
        Ficha ficha = new Ficha();
        try {
            for (AtividadeFichaDTO atividadeFichaDTO : fichaDTO.getAtividades()) {
                if (atividadeFichaDTO.getMetodoExecucao() == null) {
                    atividadeFichaDTO.setMetodoExecucao("NAO_ATRIBUIDO");
                }
            }
            ficha = fichaDao.obterPorCodigo(ctx, fichaDTO.getId());
            fichaAntesAlteracao = UtilReflection.copy(ficha);
            List<AtividadeFicha> atvs = povoarAtividadesSeriesFichaAntesAlteracao(fichaAntesAlteracao);

            if(!UteisValidacao.emptyList(ficha.getProgramas())){
                programaTreinoService.acao(ficha.getProgramas().get(0).getPrograma(), TipoRevisaoEnum.UPDATE);
            }
            if(UteisValidacao.emptyNumber(fichaDTO.getProgramaId()) && fichaDTO.getPredefinida() != null && fichaDTO.getPredefinida()) {
                montarFichaPredefinida(ctx, ficha, fichaDTO);
            }else{
                montarFichaNaoPredefinida(ctx, ficha, fichaDTO);
            }
            for (AtividadeFicha atividade : ficha.getAtividades()) {
                for (Serie serie : atividade.getSeries()) {
                    if (serie.getCargaComp() == null || serie.getCargaComp().isEmpty() || serie.getCargaComp().equals("null")){
                            serie.setCargaComp("0.0");
                    }
                    serie.setCargaApp(serie.getCargaComp());
                    serie.setRepeticaoApp(serie.getRepeticaoComp());
                    serie.setAtualizadoApp(true);
                }
            }
            ficha.setAtividades(atualizarAtividadesFicha(ctx, ficha.getAtividades()));
            if(UteisValidacao.emptyNumber(fichaDTO.getProgramaId()) && fichaDTO.getPredefinida() != null && !fichaDTO.getPredefinida()) {
                programaTreinoFichaDao.update(ctx, ficha.getProgramas().get(0));
            }
            ficha = alterarFicha(ctx, ficha);
            programaTreinoFichaDao.alterarOrdemProgramaTreinoFicha(ctx, fichaDTO.getId(), fichaDTO.getProgramaId(), fichaDTO.getOrdem());
            if(!UteisValidacao.emptyList(ficha.getProgramas())) {
                String username = sessaoService.getUsuarioAtual().getUsername();
                Usuario usuario = usuarioService.obterPorAtributo(ctx, "username", username);
                ProgramaTreino pt = ficha.getProgramas().get(0).getPrograma();
                if (usuario != null && !usuario.getTipo().equals(TipoUsuarioEnum.ALUNO)
                        && usuario.getProfessor() != null && (pt.getPreDefinido() == null || !pt.getPreDefinido())) {
                    try {
                        pt.manterAntesAlteracao();
                        programaTreinoService.gravarHistoricoRevisoes(ctx, pt, "REVISADO", Calendario.proximoDeveSerUtil(pt.getDataInicio(), 7), usuario.getProfessor());
                    }catch (Exception e){
                        Uteis.logar(e, FichaServiceImpl.class);
                    }

                }
            }
            atualizarRelacionamentosSet(ctx, ficha, fichaDTO);
            List<String> diasSemana = new ArrayList<>();

            String codigoProgramaFichaLog = "";

            if (!UteisValidacao.emptyList(ficha.getProgramas()) && ficha.getProgramas().size() > 0) {
                diasSemana.addAll(ficha.getProgramas().get(0).getDiaSemana());
                codigoProgramaFichaLog = ficha.getProgramas().get(0).getPrograma().getCodigo().toString();
            }

            fichaAntesAlteracao.setAtividades(atvs);
            Map<String, String> appInfo = Uteis.obtemNomeEVersaoApp(request);
            String nomeApp = appInfo.get("nomeApp");
            String versaoApp = appInfo.get("versaoApp");

            incluirLog(ctx, fichaAntesAlteracao.getCodigo().toString(), codigoProgramaFichaLog,  fichaAntesAlteracao.getDescricaoParaLog(ficha), ficha.getDescricaoParaLog(fichaAntesAlteracao),
                    "ALTERAÇÃO", "ALTERAÇÃO DE "+(ficha.isUsarComoPredefinida() ? "PRÉ-DEFINIDA" : "FICHA"),
                    ficha.isUsarComoPredefinida() ? EntidadeLogEnum.FICHAPREDEFINIDA : EntidadeLogEnum.FICHA,  "Ficha", sessaoService, logDao, nomeApp, versaoApp);
            return new FichaResponseTO(ficha, diasSemana);
        }catch (Exception e) {
            throw new ServiceException(FichaExcecoes.ERRO_ALTERAR_FICHA, e);
        }finally {
            boolean predefinido = !ficha.getProgramas().isEmpty()
                    && ficha.getProgramas().get(0).getPrograma().getPreDefinido() != null
                    && ficha.getProgramas().get(0).getPrograma().getPreDefinido() ? true : false;
            if(!predefinido && !UteisValidacao.emptyNumber(fichaDTO.getProgramaId()) &&  fichaDTO.getPredefinida() != null && !fichaDTO.getPredefinida()) {
                programaTreinoService.notificarOuvintes(ctx,
                        ficha.getProgramas().get(0).getPrograma().getCliente() == null ? null : ficha.getProgramas().get(0).getPrograma().getCliente().getCodigo(),
                        ficha.getProgramas().get(0).getPrograma().getCodigoColaborador(), request);
            }
            programaTreinoService.leaveAcao();
        }
    }

    public boolean validarExisteFichaPredefinidaComNome(String ctx, Integer codigo, String nome, boolean isPredefinida) throws ServiceException {
        try {
            if (isPredefinida) {
                StringBuilder where = new StringBuilder();
                where.append(" upper(nome) = '").append(nome.toUpperCase()).append("' ");
                where.append(" and usarcomopredefinida ");
                if (!UteisValidacao.emptyNumber(codigo)) {
                    where.append(" and codigo <> ").append(codigo);
                }
                if (isPredefinida && fichaDao.existsWithParam(ctx, where)) {
                    return true;
                }
            }
        } catch (Exception e) {
            Uteis.logar(e, ProgramaTreinoServiceImpl.class);
        }
        return false;
    }

    private static List<AtividadeFicha> povoarAtividadesSeriesFichaAntesAlteracao(Ficha fichaAntesAlteracao) {
        List<AtividadeFicha> atvs = new ArrayList<>();
        fichaAntesAlteracao.getAtividades().forEach(atv -> {
            List<Serie> seres = new ArrayList<>();
            atv.getSeries().forEach(se -> {
                seres.add(UtilReflection.copy(se));
            });
            AtividadeFicha att = UtilReflection.copy(atv);
            att.setSeriesPadrao(seres);
            atvs.add(att);
        });
        return atvs;
    }

    private Integer atualizarVersaoFichaPredefinida(Integer versaoAtual) {
        if (versaoAtual == null) {
            return 1;
        } else {
            return versaoAtual + 1;
        }
    }

    @Override
    public FichaResponseTO criarPredefinida(FichaDTO fichaDTO, HttpServletRequest request) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        if (validarExisteFichaPredefinidaComNome(ctx, null, fichaDTO.getNome(), fichaDTO.getPredefinida())) {
            throw new ServiceException(FichaExcecoes.VALIDACAO_FICHA_PREDEFINIDA_JA_EXISTE);
        }
        Ficha ficha = new Ficha();
        try {
            montarFichaPredefinida(ctx, ficha, fichaDTO);
            for ( AtividadeFicha atividade : ficha.getAtividades()) {
                for (Serie serie : atividade.getSeries() ) {
                    serie.setCargaApp(serie.getCargaComp());
                    serie.setRepeticaoApp(serie.getRepeticaoComp());
                    serie.setAtualizadoApp(true);
                }
            }
            ficha = inserir(ctx, ficha);
            atualizarRelacionamentosSet(ctx, ficha, fichaDTO);
            List<String> diasSemana = new ArrayList<>();
            if (!UteisValidacao.emptyList(ficha.getProgramas()) && ficha.getProgramas().size() > 0) {
                diasSemana.addAll(ficha.getProgramas().get(0).getDiaSemana());
            }
            incluirLog(ctx, ficha.getCodigo().toString(), null, "", ficha.getDescricaoParaLog(null),
                    "INCLUSÃO", "INCLUSÃO DE FICHA PRÉ-DEFINIDA",
                    EntidadeLogEnum.FICHAPREDEFINIDA, "Ficha", sessaoService, logDao, null, null);
                return new FichaResponseTO(ficha, diasSemana);
        }catch (Exception e) {
            throw new ServiceException(FichaExcecoes.ERRO_ALTERAR_FICHA, e);
        }
    }

    private List<AtividadeFicha> atualizarAtividadesFicha(String ctx, List<AtividadeFicha> atividadeFichas) throws ServiceException {
        try {
            for (AtividadeFicha atividadeFicha : atividadeFichas) {
                if (!UteisValidacao.emptyNumber(atividadeFicha.getCodigo())) {
                    List<Serie> seriesAtualizadas = new ArrayList<>();
                    for (Serie serie : atividadeFicha.getSeries()) {
                        if (serie.getCargaComp() == null || serie.getCargaComp().isEmpty() || serie.getCargaComp().equals("null")){
                            serie.setCargaComp("0.0");
                        }
                            seriesAtualizadas.add(serie);

                    }
                    atividadeFicha.setSeries(seriesAtualizadas);
                    atividadeFicha.setSeries(serieDao.atualizarList(ctx, atividadeFicha.getSeries()));
                }
            }
            return atividadeFichaDao.atualizarLista(ctx, atividadeFichas);
        } catch (Exception ex) {
            throw new ServiceException(AtividadeFichaExcecoes.ERRO_ATUALIZAR_ATIVIDADE_FICHA, ex);
        }
    }

    private void atualizarRelacionamentosSet(String ctx, Ficha ficha, FichaDTO fichaDTO) throws ServiceException {
        try {
            for (AtividadeFichaDTO atividadeFichaDTO : fichaDTO.getAtividades()) {
                if (atividadeFichaDTO.getMetodoExecucaoEnum() != null && (atividadeFichaDTO.getMetodoExecucaoEnum().equals(MetodoExecucaoEnum.TRI_SET)
                        || atividadeFichaDTO.getMetodoExecucaoEnum().equals(MetodoExecucaoEnum.BI_SET)) && !UteisValidacao.emptyList(atividadeFichaDTO.getAtividadesSequenciaSet())) {
                    AtividadeFicha atividadeFichaAtualizada =
                            ficha.getAtividades().stream().filter((atividadeFicha) -> atividadeFicha.getOrdem().equals(atividadeFichaDTO.getSequencia())).findFirst().orElse(null);
                    if (atividadeFichaAtualizada != null) {
                        String setIds = atividadeFichaAtualizada.getCodigo().toString() + '|';
                        for (Integer sequenciaSet : atividadeFichaDTO.getAtividadesSequenciaSet()) {
                            AtividadeFicha atvFicha =
                                    ficha.getAtividades().stream().filter((atividadeFicha) -> atividadeFicha.getCodigo().equals(sequenciaSet)).findFirst().orElse(null);
                            if (atvFicha != null) {
                                setIds += atvFicha.getCodigo().toString() + "|";
                            }
                        }
                        atividadeFichaAtualizada.setSetId(setIds);
                        atividadeFichaDao.update(ctx, atividadeFichaAtualizada);
                    }
                }else{
                    if(atividadeFichaDTO.getId()==null){
                        AtividadeFicha atividade =
                                ficha.getAtividades().stream().filter((atividadeFicha) -> atividadeFicha.getOrdem().equals(atividadeFichaDTO.getSequencia())).findFirst().orElse(null);
                        atividadeFichaDao.update(ctx, atividade);
                    }
                }
            }
        } catch (Exception ex) {
            throw new ServiceException(FichaExcecoes.ERRO_ATUALIZAR_SET_ATIVIDADES);
        }
    }

    @Override
    public List<AtividadeFichaResponseTO> reordenarAtividades(Integer fichaId, List<Integer> atividadesIds) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        Integer cont = 0;
        try {
            List<AtividadeFichaResponseTO> ret = new ArrayList<AtividadeFichaResponseTO>();
            for (Integer atividadeId : atividadesIds) {
                AtividadeFicha af = obterAtividadeFicha(ctx, atividadeId, fichaId);
                if (af == null) {
                    throw new ServiceException(FichaExcecoes.ATIVIDADE_FICHA_NAO_ENCONTRADA);
                }
                for (Serie serie : af.getSeries() ) {
                    serie.setCargaApp(serie.getCargaComp());
                    serie.setRepeticaoApp(serie.getRepeticaoComp());
                    serie.setAtualizadoApp(true);
                }
                af.setOrdem(cont);
                AtividadeFicha afRet = atividadeFichaDao.update(ctx, af);
                ret.add(new AtividadeFichaResponseTO(afRet));
                cont++;
            }
            return ret;
        }catch (ServiceException e) {
            throw e;
        }catch (Exception e) {
            throw new ServiceException(FichaExcecoes.ERRO_REORDENAR_ATIVIDADES, e);
        }
    }

    @Override
    public List<AtividadeFichaResponseTO> aplicarPadraoSeries(Integer fichaId, AtividadeFichaPadraoSeriesTO padraoSeriesTO) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            List<AtividadeFicha> atividades = obterAtividadesFicha(ctx, fichaId);
            if (atividades.size() == 0) {
                throw new ServiceException(FichaExcecoes.ATIVIDADE_FICHA_NAO_ENCONTRADA);
            }
            List<AtividadeFichaResponseTO> ret = new ArrayList<AtividadeFichaResponseTO>();
            for (AtividadeFicha af : atividades) {
                if (padraoSeriesTO.getAtividadesFichaId().contains(af.getAtividade().getCodigo())) {
                    for (Serie serie : af.getSeries()) {
                        serie.setQuantidade(padraoSeriesTO.getNumeroSeries());
                        serie.setRepeticao(padraoSeriesTO.getRepeticoes());
                        serie.setCarga(padraoSeriesTO.getCarga());
                        serie.setDescanso(padraoSeriesTO.getDescanso());
                    }
                    AtividadeFicha afRet = atividadeFichaDao.update(ctx, af);
                    ret.add(new AtividadeFichaResponseTO(afRet));
                }
            }
            return ret;
        }catch (ServiceException e) {
            throw e;
        }catch (Exception e) {
            throw new ServiceException(FichaExcecoes.ERRO_REORDENAR_ATIVIDADES, e);
        }
    }

    @Override
    public List<String> obterMensagensRecomendadas() throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        return fichaDao.obterMensagensRecomendadas(ctx);
    }

    @Override
    public void excluirPorId(Integer id, HttpServletRequest request) throws ServiceException {
        String ctx = null;
        Integer codigoCliente = null;
        Integer codigoPrograma = null;
        try {
            ctx = sessaoService.getUsuarioAtual().getChave();
            Ficha ficha = obterPorId(ctx, id);
            ficha.setAtividadesPadrao(atividadeFichaDao.obterPorFicha(ctx, ficha.getCodigo()));
            if(!UteisValidacao.emptyList(ficha.getProgramas())) {
                String username = sessaoService.getUsuarioAtual().getUsername();
                Usuario usuario = usuarioService.obterPorAtributo(ctx, "username", username);
                ProgramaTreino pt = ficha.getProgramas().get(0).getPrograma();
                if (usuario != null && !usuario.getTipo().equals(TipoUsuarioEnum.ALUNO)
                        && usuario.getProfessor() != null && (pt.getPreDefinido() == null || !pt.getPreDefinido())) {
                    try {
                        pt.manterAntesAlteracao();
                        programaTreinoService.gravarHistoricoRevisoes(ctx, pt, "REVISADO", Calendario.proximoDeveSerUtil(pt.getDataInicio(), 7), usuario.getProfessor());
                    }catch (Exception e){
                        Uteis.logar(e, FichaServiceImpl.class);
                    }
                }
            }
            String codigoProgramaLog = "";
            if(ficha.getProgramas().size() > 0) {
                codigoCliente = ficha.getProgramas().get(0).getPrograma().getCliente() != null
                        ? ficha.getProgramas().get(0).getPrograma().getCliente().getCodigo()
                        : null;
                codigoPrograma = ficha.getProgramas().get(0).getPrograma().getCodigo();
                programaTreinoService.acao(ficha.getProgramas().get(0).getPrograma(), TipoRevisaoEnum.UPDATE);
                programaTreinoService.atualizarVersaoProgramaSemGerarLog(ctx, ficha.getProgramas().get(0).getPrograma());
            }
            codigoProgramaLog = (UteisValidacao.emptyNumber(codigoPrograma) ? "" : codigoPrograma.toString());
            Ficha fichaAntesExclusao = UtilReflection.copy(ficha);
            List<AtividadeFicha> atvs = povoarAtividadesSeriesFichaAntesAlteracao(fichaAntesExclusao);

            excluir(ctx, ficha);

            fichaAntesExclusao.setAtividades(atvs);
            incluirLog(ctx, ficha.getCodigo().toString(), codigoProgramaLog, fichaAntesExclusao.getDescricaoParaLog(null), "",
                    "EXCLUSÃO", "EXCLUSÃO DE "+(ficha.isUsarComoPredefinida() ? "PRÉ-DEFINIDA" : "FICHA"),
                    ficha.isUsarComoPredefinida() ? EntidadeLogEnum.FICHAPREDEFINIDA : EntidadeLogEnum.FICHA, "Ficha", sessaoService, logDao, null, null);
        }catch (ServiceException e) {
            throw e;
        }catch (Exception e) {
            throw new ServiceException(FichaExcecoes.ERRO_EXCLUIR_FICHA, e);
        } finally {
            if (codigoCliente != null) {
                programaTreinoService.notificarOuvintes(ctx, codigoCliente, null, request);
            }
            programaTreinoService.leaveAcao();
        }
    }

    @Override
    public List<CategoriaFichaResponseTO> carregarCategoriaFichas() throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        prepare(ctx);
        try {
            List<CategoriaFicha> listCategoria = categoriaFichaService.obterTodos(ctx);
            List<CategoriaFichaResponseTO> listReturn = new ArrayList<>();
            if (listCategoria != null && listCategoria.size() > 0) {
                for (CategoriaFicha categoria : listCategoria) {
                    listReturn.add(new CategoriaFichaResponseTO(categoria));
                }
            }
            return listReturn;
        } catch (ServiceException e) {
            throw new ServiceException(FichaExcecoes.ERRO_BUSCAR_CATEGORIA_FICHA);
        }
    }

    protected void prepare(final String ctx) throws ServiceException {
        EntityManagerService ems = (EntityManagerService) UtilContext.getBean(EntityManagerService.class);
        try {
            ems.closeQuietly(ctx);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    private class Filter implements Predicate {
        private final String setid;

        public Filter(String setid) {
            this.setid = setid;
        }

        @Override
        public boolean evaluate(Object  object) {
            AtividadeFicha atividadeFicha = (AtividadeFicha) object;
            return null != atividadeFicha.getSetId() && atividadeFicha.getSetId().equalsIgnoreCase(setid);
        }
    }

    public ProgramaTreinoFicha persistirProgramaTreinoFicha(final String ctx, ProgramaTreinoFicha ptf)throws ServiceException
    {
        ProgramaTreinoFicha programaTreinoFicha = null;
        try {
            programaTreinoFicha =  programaTreinoFichaDao.insert(ctx, ptf);
        }catch (Exception e){
            throw new ServiceException("Ficha não vinculada ao programa");
        }
        return programaTreinoFicha;
    }

    @Override
    public Ficha tornarPreDefinidaApp(String key, Ficha ficha) throws ValidacaoException, ServiceException {
        Ficha fichaPredefinida = null;
        try {
            List<AtividadeFicha> atividadesFicha = ficha.getAtividades();

            Set<String> vincBiTriSet = getVinculos(atividadesFicha);

            fichaPredefinida = new Ficha(ficha, atividadesFicha, true);
            fichaPredefinida.setNivel(ficha.getNivel());
            fichaPredefinida.setSexo(ficha.getSexo());
            fichaPredefinida.setCategoria(ficha.getCategoria());

            fichaPredefinida = inserir(key, fichaPredefinida);
            //gravar atividades
            for (AtividadeFicha atvFic : fichaPredefinida.getAtividades()) {
                atvFic.setFicha(fichaPredefinida);
                atvFic = inserirAtividadeFicha(key, atvFic);
                //gravar series
                for (Serie serie : atvFic.getSeries()) {
                    serie.setAtividadeFicha(atvFic);
                    serieService.inserir(key, serie);
                }
            }

            if(!vincBiTriSet.isEmpty()){
                doAjustarAtividades(key, vincBiTriSet, fichaPredefinida.getAtividades());

            }

        } catch (ValidacaoException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
        return fichaPredefinida;
    }

    @Override
    public List<Ficha> obterFichasPredefinidasApp(String ctx, boolean somenteAtivas, Integer index, Integer maxResult, String nomeFicha) throws ServiceException {
        try {
            StringBuilder query = new StringBuilder("select obj from Ficha obj ");
            query.append("where obj.usarComoPredefinida = true ");
            if (somenteAtivas) {
                query.append(" and obj.ativo is true ");
            }
            if (!UteisValidacao.emptyString(nomeFicha)){
                query.append(" and LOWER(obj.nome) like ('%");
                query.append(nomeFicha.toLowerCase());
                query.append("%')");
            }
            query.append(" order by obj.nome");
            HashMap<String, Object> p = new HashMap<String, Object>();
            return getFichaDao().findByParam(ctx, query.toString(), p,
                    maxResult == null ? 0 : maxResult,
                    index == null ? 0 : index);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    public List<AtividadeFichaAjuste> obterAtividadeFichaAjustePorAtividadeFicha(final String ctx, Integer codAtividadeFicha) throws Exception{
        try{
            StringBuilder query = new StringBuilder("select obj from AtividadeFichaAjuste obj ");
            query.append("where obj.atividadeFicha.codigo = :codAtividadeFicha ");
            HashMap<String, Object> p = new HashMap<String, Object>();
            p.put("codAtividadeFicha", codAtividadeFicha);
            return atividadeFichaAjusteDao.findByParam(ctx, query.toString(), p);
        } catch (Exception e){
            throw new Exception(e.getMessage());
        }

    }

    @Override
    public List<String> consultarGruposMuscularesPorFichaTreino(Integer id) throws Exception {
        String ctx = sessaoService.getUsuarioAtual().getChave();

        return fichaDao.findById(ctx, id).getGrupos();
    }

    @Override
    public Ficha cadastrarApp(final String ctx, Ficha ficha) throws ServiceException {
        try {
            getValidacao().validarDadosBasicos(ficha);
            ficha = getFichaDao().inserir(ctx, ficha);
            return ficha;
        } catch (EntityExistsException ex) {
            throw new ServiceException(ex);
        } catch (ValidacaoException ex) {
            throw ex;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public void atualizarSituacaoFichaPredefinida(FichaDTO fichaTO, Integer id) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            Ficha ficha = obterPorId(ctx, id);
            if(ficha != null){
                if(fichaTO.getAtivo() != null && fichaTO.getAtivo()){
                    ficha.setAtivo(false);
                }else{
                    ficha.setAtivo(true);
                }
                alterar(ctx, ficha, false);
            }

        } catch (ServiceException e) {
            throw new ServiceException(FichaExcecoes.ERRO_ALTERAR_FICHA, e);
        }
    }
    
}
