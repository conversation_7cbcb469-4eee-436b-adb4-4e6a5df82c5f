package br.com.pacto.service.impl.cliente.perfil;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Informações da ficha de treino programada para o dia atual do aluno, incluindo identificação e quantidade de execuções.")
public class FichaDoDiaDTO {

    @ApiModelProperty(value = "Identificador único da ficha de treino do dia.", example = "125")
    private Integer id;

    @ApiModelProperty(value = "Nome descritivo da ficha de treino programada para o dia.", example = "Treino Superior A")
    private String nome;

    @ApiModelProperty(value = "Número de vezes que a ficha foi executada pelo aluno.", example = "3")
    private Integer vezes;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getVezes() {
        return vezes;
    }

    public void setVezes(Integer vezes) {
        this.vezes = vezes;
    }
}
