package br.com.pacto.service.impl.cliente.perfil;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Informações das fichas de treino relacionadas ao aluno, incluindo a última ficha executada e a próxima ficha programada.")
public class FichasRelacionadasDTO {

    @ApiModelProperty(value = "Identificador único da última ficha de treino executada pelo aluno.", example = "89")
    private Integer idUltima;

    @ApiModelProperty(value = "Nome descritivo da última ficha de treino executada pelo aluno.", example = "Treino Inferior B")
    private String nomeUltima;

    @ApiModelProperty(value = "Identificador único da próxima ficha de treino programada para o aluno.", example = "92")
    private Integer idProxima;

    @ApiModelProperty(value = "Nome descritivo da próxima ficha de treino programada para o aluno.", example = "Treino Superior A")
    private String nomeProxima;

    public Integer getIdUltima() {
        return idUltima;
    }

    public void setIdUltima(Integer idUltima) {
        this.idUltima = idUltima;
    }

    public String getNomeUltima() {
        return nomeUltima;
    }

    public void setNomeUltima(String nomeUltima) {
        this.nomeUltima = nomeUltima;
    }

    public Integer getIdProxima() {
        return idProxima;
    }

    public void setIdProxima(Integer idProxima) {
        this.idProxima = idProxima;
    }

    public String getNomeProxima() {
        return nomeProxima;
    }

    public void setNomeProxima(String nomeProxima) {
        this.nomeProxima = nomeProxima;
    }
}
