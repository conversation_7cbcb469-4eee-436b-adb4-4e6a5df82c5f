package br.com.pacto.service.impl.gympass.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.json.JSONObject;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 22/03/2020
 */
@ApiModel(description = "Janela de tempo para abertura e fechamento de agendamentos")
public class BookingWindowDTO {

    @ApiModelProperty(value = "Data e hora de abertura dos agendamentos", example = "2025-06-19T06:00:00Z")
    private String opens_at;

    @ApiModelProperty(value = "Data e hora de fechamento dos agendamentos", example = "2025-06-20T09:00:00Z")
    private String closes_at;

    public BookingWindowDTO() {
    }

    public BookingWindowDTO(JSONObject json) {
        this.opens_at = json.optString("opens_at");
        this.closes_at = json.optString("closes_at");
    }

    public String getOpens_at() {
        return opens_at;
    }

    public void setOpens_at(String opens_at) {
        this.opens_at = opens_at;
    }

    public String getCloses_at() {
        return closes_at;
    }

    public void setCloses_at(String closes_at) {
        this.closes_at = closes_at;
    }
}
