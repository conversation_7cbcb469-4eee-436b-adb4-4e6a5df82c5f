package br.com.pacto.service.impl.conexao;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class ConcurrentHttpRequests {
    public static void main(String[] args) {
        String url = "http://localhost:8085/TreinoWeb/prest/alunoTurma/" +
                "pratiqueonline/app/consultarTurmasDisponiveis?matricula=20222&inicio=12%2F09%2F2023&fim=12%2F09%2F2023&contrato=59491";
        int numRequests = 200;
        CountDownLatch latch = new CountDownLatch(numRequests);
        ExecutorService executorService = Executors.newFixedThreadPool(numRequests);

        for (int i = 0; i < numRequests; i++) {
            final int index = i;
            executorService.execute(() -> {
                try {
                    long init = System.currentTimeMillis();
                    HttpURLConnection connection = (HttpURLConnection) new URL(url).openConnection();
                    connection.setRequestMethod("POST");

                    int responseCode = connection.getResponseCode();
                    long tempo = System.currentTimeMillis() - init;

                    BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                    String inputLine;
                    StringBuilder content = new StringBuilder();

                    while ((inputLine = in.readLine()) != null) {
                        content.append(inputLine);
                    }
                    in.close();

                    if(content.toString().equals("{\"aulas\":[]}")){
                        System.err.println(index + " ERRO.");

                    } else if (responseCode == 200) {
                        System.out.println(index + " pronto. " + tempo + " ms. " + (content.length() <= 20 ? content : (content.subSequence(0,20) + "...")));
                    } else {
                        System.err.println(index + " ERRO: " + responseCode + " em "+ tempo + " ms. " + content);
                    }
                    System.out.println(latch.getCount());
                    connection.disconnect();
                } catch (IOException e) {
                    e.printStackTrace();
                }finally {
                    latch.countDown();
                }
            });
        }

        try {
            latch.await();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

        System.out.println("Todas as requisições foram concluídas.");


        executorService.shutdown();
    }
}
