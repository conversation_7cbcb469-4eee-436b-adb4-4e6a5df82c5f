package br.com.pacto.service.impl.log;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;

@ApiModel(description = "Transfer Object para representação de logs de auditoria do sistema, contendo informações sobre operações realizadas pelos usuários e as alterações efetuadas")
public class LogTO {

    @ApiModelProperty(value = "Chave primária do registro que foi alterado. Pode ser um código numérico ou '-' quando não aplicável", example = "123")
    private String chave;

    @ApiModelProperty(value = "Tipo de operação realizada no sistema. \n\n" +
            "<strong>Valores disponíveis</strong>\n" +
            "- Adicionado (INSERT)\n" +
            "- Alterado (UPDATE)\n" +
            "- Removido (DELETE)\n", example = "Alterado")
    private String operacao;

    @ApiModelProperty(value = "Nome do usuário que realizou a operação", example = "joao.silva")
    private String usuario;

    @ApiModelProperty(value = "Data e hora da operação no formato dd/MM/yyyy HH:mm", example = "15/06/2024 14:30")
    private String dia;

    @ApiModelProperty(value = "Hora da operação no formato HH:mm:ss", example = "14:30:25")
    private String hora;

    @ApiModelProperty(value = "Identificador ou nome do item que foi alterado (ex: nome do aluno, nome da atividade)", example = "João da Silva")
    private String identificador;

    @ApiModelProperty(value = "Descrição detalhada das alterações realizadas, formatada em HTML com as mudanças de valores", example = "[nome:'João Silva' para 'João da Silva Santos']<br/>[email:'<EMAIL>' para '<EMAIL>']<br/>")
    private String descricao;

    @ApiModelProperty(value = "Origem ou fonte da operação (sistema, módulo específico, etc.)", example = "Sistema Web")
    private String origem;

    @ApiModelProperty(value = "Lista detalhada das alterações realizadas, contendo campo alterado, valor anterior e novo valor")
    private List<AlteracoesTO> alteracoes;

    public LogTO(Integer chave, String operacao, String identificador, String usuario, String dia, String hora, List<AlteracoesTO> alteracoes) {
        this.identificador = identificador;
        this.chave = chave == null ? "-" : chave.toString();
        this.operacao = operacao;
        this.usuario = usuario;
        this.dia = dia;
        this.hora = hora;
        this.alteracoes = alteracoes;
        this.descricao = "";
        try {
            for(AlteracoesTO a : alteracoes){
                this.descricao  += ("["+a.getCampo()+":"+"'"+a.getValorAnterior()+"' para '"+a.getValorAlterado()+"']<br/>");
            }
        }catch (Exception e){
        }
    }

    public LogTO() {
    }

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public String getOperacao() {
        return operacao;
    }

    public void setOperacao(String operacao) {
        this.operacao = operacao;
    }

    public String getUsuario() {
        return usuario;
    }

    public void setUsuario(String usuario) {
        this.usuario = usuario;
    }

    public List<AlteracoesTO> getAlteracoes() {
        return alteracoes;
    }

    public void setAlteracoes(List<AlteracoesTO> alteracoes) {
        this.alteracoes = alteracoes;
    }

    public String getDia() {
        return dia;
    }

    public void setDia(String dia) {
        this.dia = dia;
    }

    public String getHora() {
        return hora;
    }

    public void setHora(String hora) {
        this.hora = hora;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getIdentificador() {
        return identificador;
    }

    public void setIdentificador(String identificador) {
        this.identificador = identificador;
    }

    public String getOrigem() {return origem; }

    public void setOrigem(String origem) { this.origem = origem;}
}
