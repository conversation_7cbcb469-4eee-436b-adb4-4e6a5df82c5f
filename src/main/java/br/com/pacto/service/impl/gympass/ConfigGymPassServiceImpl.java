/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.impl.gympass;

import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.gympass.ConfigGymPass;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.controller.json.gympass.ConfigGymPassDTO;
import br.com.pacto.dao.intf.gympass.ConfigGymPassDao;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.configuracoes.ConfiguracaoSistemaService;
import br.com.pacto.service.intf.empresa.EmpresaService;
import br.com.pacto.service.intf.gympass.ConfigGymPassService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.util.UteisValidacao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: Luiz Felipe
 * Date: 22/03/2020
 */
@Service
public class ConfigGymPassServiceImpl implements ConfigGymPassService {

    @Autowired
    private ConfigGymPassDao dao;
    @Autowired
    private EmpresaService empresaService;
    @Autowired
    private ConfiguracaoSistemaService configService;
    @Autowired
    private SessaoService sessaoService;
    @Autowired
    private UsuarioService usuarioService;

    public ConfigGymPass obterPorEmpresa(final String ctx, Empresa empresa) throws ServiceException {
        try {
            ConfigGymPass obj = dao.findObjectByAttributes(ctx, new String[]{"empresa.codigo"}, new Object[]{empresa.getCodigo()}, "codigo");
            if (obj == null) {
                obj = new ConfigGymPass();
                obj.setEmpresa(empresa);
                obj.setUsarGymPassBooking(false);
                obj.setCodigoGymPass("");
                obj.setAtivo(false);
                dao.insert(ctx, obj);
            }
            dao.refresh(ctx, obj);
            return obj;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public ConfigGymPass obterPorEmpresaZW(final String ctx, Integer codEmpresaZW) throws ServiceException {
        try {
            Empresa empresa = null;
            if (!UteisValidacao.emptyNumber(codEmpresaZW)) {
                empresa = empresaService.obterPorIdZW(ctx, codEmpresaZW);
            } else {
                empresa = empresaService.obterEmpresaTreinoIndependente(ctx);
            }

            return obterPorEmpresa(ctx, empresa);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public ConfigGymPass obterPorEmpresaTR(final String ctx, Integer codEmpresaTR) throws ServiceException {
        try {
            Empresa empresa = null;
            if (!UteisValidacao.emptyNumber(codEmpresaTR)) {
                if(SuperControle.independente(ctx)){
                    empresa = empresaService.obterPorId(ctx, codEmpresaTR);
                }else{
                    empresa = empresaService.obterPorIdZW(ctx, codEmpresaTR);
                }
                return obterPorEmpresa(ctx, empresa);
            }
            return null;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public ConfigGymPass obterPorId(final String ctx, Integer id) throws ServiceException {
        try {
            return dao.findById(ctx, id);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public ConfigGymPass alterar(final String ctx, ConfigGymPass object) throws ServiceException {
        try {
            return dao.update(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<ConfigGymPass> obterTodos(final String ctx) throws ServiceException {
        try {
            List<ConfigGymPass> lista = new ArrayList<>();
            List<Empresa> empresas = empresaService.obterTodos(ctx);
            for (Empresa emp : empresas) {
                ConfigGymPass configGymPass = obterPorEmpresa(ctx, emp);
                lista.add(configGymPass);
            }
            return lista;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<ConfigGymPassDTO> obterTodosDTO(final String ctx) throws ServiceException {
        List<ConfigGymPassDTO> lista = new ArrayList<>();
        List<ConfigGymPass> listaConfig = obterTodos(ctx);
        for (ConfigGymPass configGymPass : listaConfig) {
            lista.add(new ConfigGymPassDTO(configGymPass));
        }
        return lista;
    }

    public ConfigGymPass alterarDTO(final String ctx, ConfigGymPassDTO dto) throws ServiceException {
        try {
            Empresa empresa = empresaService.obterPorId(ctx, dto.getEmpresa());
            Usuario usuario = usuarioService.obterPorId(ctx, sessaoService.getUsuarioAtual().getId());
            Date agora = new Date();
            ConfigGymPass config = new ConfigGymPass(dto, empresa, usuario, agora);
            if (UteisValidacao.emptyNumber(config.getCodigo())) {
                config.setAtivo(false);
                dao.insert(ctx, config);
            } else {
                dao.update(ctx, config);
            }
            configService.persistirIntegracoesLog(ctx);
            return config;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public ConfigGymPass obterPorCodigoGymPass(final String ctx, String codigoGymPass) throws ServiceException {
        try {
            return dao.findObjectByAttributes(ctx, new String[]{"codigoGymPass"}, new Object[]{codigoGymPass}, "codigo");
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public void validarConfiguracaoGympass(ConfigGymPass configGymPass) throws Exception {
        if (configGymPass == null) {
            throw new Exception("Configuração Gympass não encontrada.");
        }
        if (!configGymPass.isUsarGymPassBooking()) {
            throw new Exception("Configuração GymPass Booking desativada.");
        }
        if (UteisValidacao.emptyString(configGymPass.getCodigoGymPass())) {
            throw new Exception("Código da empresa no Gympass não configurado.");
        }
    }
}
