package br.com.pacto.service.intf.agenda;

import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.exception.ValidacaoException;
import br.com.pacto.service.impl.agenda.TipoFixarAluno;

public interface AlunoFixoService {


    void fixarAlunoTurma(Integer aulaHorario, String matricula,
                         Long dataAula, Long ate, TipoFixarAluno tipo, String origem, Boolean validarAulaCheia) throws ValidacaoException, ServiceException;

    void desafixarAlunoTurma(Integer aulaHorario, String matricula, Long diaDesafixar) throws ServiceException;

    void desafixarTodosHorariosDoAluno(Integer codigoCliente) throws ServiceException;

}
