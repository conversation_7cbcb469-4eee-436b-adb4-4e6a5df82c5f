package br.com.pacto.service.intf.cliente;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Dados consolidados do histórico de presença do cliente")
public class HistoricoPresencasVO {

    @ApiModelProperty(value = "Total de aulas realizadas pelo cliente", example = "150")
    private Integer totalAulasRealizadas = 0;

    @ApiModelProperty(value = "Número de aulas realizadas no mês atual", example = "12")
    private Integer aulasMesAtual = 0;

    @ApiModelProperty(value = "Número de semanas consecutivas frequentando", example = "4")
    private Integer semanasConsecutivas = 0;

    public Integer getTotalAulasRealizadas() {
        return totalAulasRealizadas;
    }

    public void setTotalAulasRealizadas(Integer totalAulasRealizadas) {
        this.totalAulasRealizadas = totalAulasRealizadas;
    }

    public Integer getAulasMesAtual() {
        return aulasMesAtual;
    }

    public void setAulasMesAtual(Integer aulasMesAtual) {
        this.aulasMesAtual = aulasMesAtual;
    }

    public Integer getSemanasConsecutivas() {
        return semanasConsecutivas;
    }

    public void setSemanasConsecutivas(Integer semanasConsecutivas) {
        this.semanasConsecutivas = semanasConsecutivas;
    }
}
