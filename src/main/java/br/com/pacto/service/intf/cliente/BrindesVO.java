package br.com.pacto.service.intf.cliente;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Brinde disponível no programa de fidelidade da academia")
public class BrindesVO {

    @ApiModelProperty(value = "Quantidade de pontos necessários para resgatar o brinde", example = "500")
    private Integer quantidadeDePontos;

    @ApiModelProperty(value = "Nome do brinde disponível", example = "Camiseta da Academia")
    private String nome;

    @ApiModelProperty(value = "Indica se o brinde está ativo e disponível para resgate", example = "true")
    private Boolean ativo;

    public Integer getQuantidadeDePontos() {
        return quantidadeDePontos;
    }

    public void setQuantidadeDePontos(Integer quantidadeDePontos) {
        this.quantidadeDePontos = quantidadeDePontos;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Boolean getAtivo() {
        return ativo;
    }

    public void setAtivo(Boolean ativo) {
        this.ativo = ativo;
    }
}
