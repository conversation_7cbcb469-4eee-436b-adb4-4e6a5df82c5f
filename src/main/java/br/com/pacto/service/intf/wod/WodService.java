package br.com.pacto.service.intf.wod;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.aparelho.AparelhoWod;
import br.com.pacto.bean.atividade.AtividadeWod;
import br.com.pacto.bean.benchmark.Benchmark;
import br.com.pacto.bean.crossfit.EventoCrossfit;
import br.com.pacto.bean.nivelwod.NivelWod;
import br.com.pacto.bean.wod.FiltroWodJSON;
import br.com.pacto.bean.wod.NivelCrossfitEnum;
import br.com.pacto.bean.wod.ScoreTreino;
import br.com.pacto.bean.wod.Wod;
import br.com.pacto.bean.wod.WodAvaliacao;
import br.com.pacto.bean.wod.WodResponseTO;
import br.com.pacto.bean.wod.WodTO;
import br.com.pacto.controller.json.crossfit.AvaliacaoWodDTO;
import br.com.pacto.controller.json.crossfit.RankingJSON;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.exception.ValidacaoException;
import org.json.JSONObject;
import org.springframework.ui.ModelMap;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by Rafael on 20/07/2016.
 */
public interface WodService {

    public List<RankingJSON> ranking(String ctx,Integer codigoWod, HttpServletRequest request) throws Exception;

    public List<RankingJSON> ranking(String ctx,Integer codigoWod, HttpServletRequest request, Integer limit) throws Exception;

    public List<RankingJSON> ranking(String ctx,Integer codigoWod, HttpServletRequest request, Integer limit, boolean forcarImagens) throws Exception;

    ScoreTreino scoreTreinoPorCodigo(String ctx, Integer codigo);

    public void gravarResultados(String ctx, ScoreTreino score) throws Exception;

    public void atualizarRanking(final String ctx, final Integer codigoWod, String orderBY) throws Exception;

    public static final String SERVICE_NAME = "WodService";

    public Wod inserir(final String ctx, Wod object) throws ServiceException;

    public Wod obterPorId(final String ctx, Integer id) throws ServiceException;

    public Wod alterar(final String ctx, Wod object) throws ValidacaoException, ServiceException;

    public void excluir(final String ctx, Wod object) throws ServiceException;

    public List<Wod> obterTodos(final String ctx) throws ServiceException;

    public List<Wod> obterPorEvento(final String ctx, final EventoCrossfit game) throws ServiceException;

    public List<Benchmark> benchmarks(String ctx) throws Exception;

    public List<Wod> wodsDia(final String ctx, Date dia) throws ServiceException;

    public List<Wod> wods(final String ctx, Date inicio, Date fim, Integer usuario, Integer wod, Integer empresaCodZw) throws ServiceException;

    public List<Wod> consultaWodsApp(final String ctx, Integer empresaCodigoZw, String inicio, String fim, Integer usuario, String matricula, Integer wodCodigo) throws Exception;

    public List<ScoreTreino> resultados(String ctx, Date inicio, Date fim, Integer usuario, Integer wod) throws Exception;

    public ScoreTreino gravarScore(final String ctx, final Integer wod, final Integer usuario,
           final Integer benchmark, final Integer tempo, final Double peso, final Integer repeticoes,
           final Integer rounds, final Boolean rx,
           final String comentario, final NivelWod nivelWod, final Integer equipe) throws Exception;

    public List<Wod> obterPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException;

    public List<Wod> obterPorParam(final String ctx, String query,
                                         Map<String, Object> params, int max, int index)
            throws ServiceException;

    public Wod obterObjetoPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException;

    void preencherListaComentarios(final String ctx, Wod wod, Integer usuario, Integer limit, Integer maxResults, HttpServletRequest request) throws ServiceException;

    List<AtividadeWod> obterListaAtividadesWod(String ctx, Integer codigoWod) throws ServiceException;

    List<AparelhoWod> obterListaAparelhosWod(String ctx, Integer codigoWod) throws ServiceException;

    boolean existeResultado(String ctx, Integer wod) throws Exception;

    /**
     * Calcula os dias que possuem WOD e retorna uma lista com os mesmos
     *
     * @param contexto    Contexto no qual a consulta será realizada
     * @param empresa     empresa no qual a consulta será realizada
     * @param dataInicial Data inicial dos WODs em milissegundos (inclusiva)
     * @param dataFinal   Data final dos WODs em milissegundos (exclusiva)
     * @return A lista de datas em que haverá WOD, caso haja mais de um no mesmo dia, ele só é retornado uma vez
     * @throws ServiceException caso ocorra alguma exceção na busca dos dias com Wod
     */

    Set<Date> obterDiasComWodPeriodo(String contexto, Integer empresa, Long dataInicial, Long dataFinal) throws ServiceException;

    String obterExerciciosSite(Date data) throws ServiceException;

    List<WodResponseTO> obterListaWods(FiltroWodJSON filtros, PaginadorDTO paginadorDTO, Integer empresaId, boolean restringirEmpresas) throws ServiceException;

    WodResponseTO buscarWod(Integer id, HttpServletRequest request) throws ServiceException;

    String importarWodCrossfit(Date filtroDia) throws ServiceException;

    WodResponseTO cadastrarWod(WodTO wodTO, Integer empresaId, HttpServletRequest request) throws ServiceException;

    WodResponseTO alterarWod(WodTO wodTO, Integer id, Integer empresaId, HttpServletRequest request) throws ServiceException;

    void removerWod(Integer id) throws ServiceException;

    void simularScore(String ctx, Date dia) throws ServiceException;

    List<WodResponseTO> listarTodosWods(Date data, Integer empresaId) throws ServiceException;

    List<WodResponseTO> listarTodosWods(Date data, Integer empresaId, Boolean todasUnidades) throws ServiceException;

    void cadastrarAlterarWodCompartilhado(String ctx, JSONObject compartilhado) throws Exception;

    boolean podeUsarCross(final String ctx,final Integer codUsuario,final String matricula, Boolean permissaoVisualizarWod) throws ServiceException;

    String importarWodsDaFranqueadora(Integer codEmpresaZW) throws ServiceException;

    void avaliarWod(String ctx, AvaliacaoWodDTO avaliacaoWodDTO) throws Exception;

    List<WodAvaliacao> avaliacoesWod(String ctx) throws Exception;

    void runExcluirWodsPorChaves(String csvBase64Data, Date dataInicio, Date dataFim, ModelMap mm) throws ServiceException;

    String excluirWodsPorChave(String ctx, Date dataInicio, Date dataFim) throws ServiceException;


    List<ScoreTreino> obterPrimeirasTresPosicoesWodRanking(String ctx, Integer codigo) throws Exception;

    void enviaNotificacaoTopTres(String ctx, List<ScoreTreino> scoreTreinoAntesAtualizacao, List<ScoreTreino> scoreTreinoDepoisAtualizacao);

    List<Wod> obterPorTipoWod(String ctx, Integer codigo) throws Exception;
}
