/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.intf.atividade;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.animacao.Animacao;
import br.com.pacto.bean.animacao.TipoAnimacaoEnum;
import br.com.pacto.bean.aparelho.Aparelho;
import br.com.pacto.bean.atividade.*;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.atividade.FiltroAtividadeCrossfitJSON;
import br.com.pacto.controller.json.atividade.FiltroAtividadeJSON;
import br.com.pacto.controller.json.atividade.read.AtividadeJSON;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.exception.ValidacaoException;
import br.com.pacto.controller.to.UploadedFile;
import br.com.pacto.service.impl.atividade.AtividadeAlteradaDTO;
import br.com.pacto.service.impl.atividade.RelatorioExclusaoAtividadeIADTO;
import servicos.integracao.admapp.client.Midia;

import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public interface AtividadeService {

    public static final String SERVICE_NAME = "AtividadeService";

    public Aparelho addAparelho(final String ctx, Atividade atividade, final String nome, TipoAtividadeEnum tipo) throws Exception;

    public Atividade add(final String ctx, Atividade atividade, Midia midia) throws Exception;

    public Atividade add(final String ctx, TipoAtividadeEnum tipo, final String nome,
            String[] categorias, final String[] urls, final String[] aparelhos, final String[] grupos) throws ServiceException;

    public boolean remove(Atividade atividade, AtividadeAparelho aparelho);

    public void addMusculos(final String ctx, Atividade atividade, final String[] nomes) throws Exception;

    public boolean remove(Atividade atividade, AtividadeMusculo musculo);

    public void addGruposMusculares(final String ctx, Atividade atividade, final String[] nomes) throws Exception;

    public boolean remove(Atividade atividade, AtividadeGrupoMuscular grupoMuscular);

    public Animacao addAnimacao(final String ctx, Atividade atividade, final String titulo, final String subtitulo,
            final String url, TipoAnimacaoEnum tipo) throws Exception;

    public boolean remove(Atividade atividade, AtividadeAnimacao atividadeAnimacao);

    public Atividade inserir(final String ctx, Atividade object) throws ServiceException, ValidacaoException;

    public Atividade obterPorId(final String ctx, Integer id) throws ServiceException;

    public Atividade alterar(final String ctx, Atividade object) throws ServiceException, ValidacaoException;

    public void excluir(final String ctx, Atividade object) throws ServiceException;

    public List<Atividade> obterTodos(final String ctx, boolean somenteAtivas, boolean somenteInativas, boolean crossfit) throws ServiceException;

    public List<Atividade> obterPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException;

    public List<Atividade> obterPorParam(final String ctx, String query,
            Map<String, Object> params, int max, int index)
            throws ServiceException;

    public Atividade obterObjetoPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException;

    public List<Atividade> obterPorRelacionamento(final String ctx, Integer codigo, String relacionamento, String atributo) throws ServiceException;

    public void removerRelacoes(final String ctx, Atividade object) throws ServiceException;

    public List<Atividade> obterPorRelacionamento(final String ctx, Map<CadastrosAuxiliaresEnum, List<Integer>> params, String nome, final Usuario usuario) throws ServiceException;

    public void montarListaAtividadesFicha(String ctx, List<AtividadeFicha> atividadesFicha);

    public List<AtividadeFichaAjuste> montarAjustes(String ctx, Atividade atividade, List<AtividadeFichaAjuste> ajustes);

    public List<AtividadeFicha> obterListaAtividadesFicha(String ctx, Integer codigoAtividade) throws ServiceException;

    public Long totalAtividades(String ctx) throws ServiceException;

    public List<AtividadeFichaAjuste> consultarAjustes(String ctx, AtividadeFicha atividadeFicha) throws ServiceException;
    
    public List<AtividadeFicha> obterAtividadesDaFicha(String ctx, Integer codigoFicha) throws ServiceException;

    AtividadeAlteradaDTO atualizarAtividadePorFicha(String ctx, Integer codigoAtividade, Integer codigoAtividadeAntigo, Integer codigoFicha) throws ServiceException;

    public Map<String, Integer> obterMapaAtividades(String ctx) throws ServiceException;

    public void atualizaVersaoProgramas(String ctx, Integer codigoAtividade) throws ServiceException;

    void atualizarAtividadesFicha(String ctx, Integer codigoAtividade, String nomeAtividadeAtual, String nomeAtividadeAntigo) throws ServiceException;

    public void salvarMidiaNuvem(String chave, List<UploadedFile> objs, Atividade atividade) throws Exception;

    public void removerImagem(String chave, AtividadeAnimacao aa) throws Exception;

    public void refresh(String ctx, Atividade object) throws Exception;

    public List<AtividadeAnimacao> consultarImagens(String key, Integer atividade) throws Exception;

    public List<AtividadeAnimacao> obterImagens(String ctx, Integer codigoAtividade) throws Exception;

    List<AtividadeSimplesResponseTO> listarAtividadesMontagemTreino(Integer ficha, FiltroAtividadeJSON filtroAtividadeJSON, Boolean comGrupo, String tipo, PaginadorDTO paginadorDTO, boolean crossfit, Integer empresaId) throws ServiceException;

    List<AtividadeCompletaResponseTO> listarAtividades(FiltroAtividadeJSON filtroAtividadeJSON, String tipo, PaginadorDTO paginadorDTO, boolean crossfit, Integer empresaId) throws ServiceException;

    AtividadeCompletaResponseTO buscarAtividade(Integer id) throws ServiceException;

    String replicarAtividade(String ctxMmatriz, Integer codigoEmpresaZWMatriz, String ctxFilial, Integer codigoEmpresaZWFilial, Boolean status) throws ServiceException;

    List<AtividadeCompletaResponseTO> listarTodasAtividades(FiltroAtividadeCrossfitJSON filtroAtividadeCrossfitJSON) throws ServiceException;

    AtividadeCompletaResponseTO cadastrarAtividade(AtividadeTO atividadeTO) throws ServiceException;

    AtividadeCompletaResponseTO atualizarAtividade(AtividadeTO atividadeTO) throws ServiceException;

    void atualizarSituacaoAtividade(AtividadeTO atividadeTO, Integer id) throws ServiceException;

    void removerAtividade(Integer id) throws ServiceException;

    void inativar(final String ctx, Atividade object) throws ServiceException;

    void montarImagens(String ctx, Atividade object) throws Exception;

    boolean isAtividadesCached(final String ctx, boolean crossfit);

    void purgeAtividadesCache(final String ctx);

    void syncAtividadesIA(final String ctx, final String ctxIA) throws Exception;

    void storeAtividadesCache(final String ctx, final List<AtividadeJSON> atividades, boolean crossfit);

    List<AtividadeJSON> getAtividadesCache(final String ctx, boolean crossfit);

    List<AtividadeEmpresaResponseTO> configsEmpresa() throws ServiceException;

    void salvarMidiaNuvemEndpoint(String chave, List<AtividadeImagemUploadTO> objs, Atividade atividade) throws Exception;

    String processoReduzirTamanhoGif(double tamanhoMaximo, boolean somenteValidacao, Integer idAtividade) throws ServiceException;

    String excluirFotokeyAwsCsv(String csvBase64Data) throws ServiceException;

    void toggleAtividadesIA(Boolean marcar) throws ServiceException;

    Atividade obterPorIdIAVersaoDois(String ctx, int idIA) throws Exception;

    Atividade buscarPorNomeOriginalIA(String ctx, String nomeOriginalIA) throws Exception;

    Atividade buscarPorNomeAtividade(String ctx, String nomeAtividade) throws Exception;

    void apenasValidar(final String ctx, Atividade object) throws ServiceException, ValidacaoException;

    String replicarImagensAtividades(String ctxMatriz, Integer codigoEmpresaZWMatriz, String ctxFilial,
                                            Integer codigoEmpresaZWFilial, Boolean substituirImagens) throws ServiceException;

    String desativarAtividadesComIdiaPreenchido() throws ServiceException;

    String desativarAtividadesComIdiaPreenchidoComContexto(String ctx) throws ServiceException;

    String reativarAtividadesComIdiaPreenchido() throws ServiceException;

    String reativarAtividadesComIdiaPreenchidoComContexto(String ctx) throws ServiceException;

    Atividade importarAtividadeIa(String ctx, String atividadeIdIA);

    RelatorioExclusaoAtividadeIADTO removerAtividadesGeradasPorIA(Boolean forcarExclusaoTotal) throws ServiceException;

    String replicarAtividadesSubstituindo(String ctxOrigem, Integer codigoEmpresaZWOrigem, String ctxDestino, Integer codigoEmpresaZWDestino) throws ServiceException;
}
