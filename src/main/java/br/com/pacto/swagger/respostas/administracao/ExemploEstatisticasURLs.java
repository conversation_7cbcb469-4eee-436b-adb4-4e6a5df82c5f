package br.com.pacto.swagger.respostas.administracao;

import java.util.ArrayList;
import java.util.List;

/**
 * Classe de exemplo para resposta do endpoint de estatísticas de URLs (implementação vazia)
 */
public class ExemploEstatisticasURLs {
    
    private List<Object> estatisticas;
    
    public ExemploEstatisticasURLs() {
        this.estatisticas = new ArrayList<>();
        // Lista vazia pois a implementação está comentada
    }
    
    public List<Object> getEstatisticas() {
        return estatisticas;
    }
    
    public void setEstatisticas(List<Object> estatisticas) {
        this.estatisticas = estatisticas;
    }
}
