package br.com.pacto.swagger.respostas.cliente;

import br.com.pacto.controller.json.aluno.AvaliacaoProfessorDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Exemplo de resposta para avaliação de professor")
public class ExemploRespostaAvaliarProfessor {

    @ApiModelProperty(value = "Dados da avaliação do professor processada")
    private AvaliacaoProfessorDTO content;

    public AvaliacaoProfessorDTO getContent() {
        return content;
    }

    public void setContent(AvaliacaoProfessorDTO content) {
        this.content = (AvaliacaoProfessorDTO) content;
    }
}
