package br.com.pacto.swagger.respostas.treino;

import br.com.pacto.controller.json.gestao.BITreinoCarteiraDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200")
public class ExemploRespostaBITreinoCarteiraDTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as informações solicitadas")
    private BITreinoCarteiraDTO content;

    public BITreinoCarteiraDTO getContent() {
        return content;
    }

    public void setContent(BITreinoCarteiraDTO content) {
        this.content = content;
    }
}
