package br.com.pacto.swagger.respostas.avaliacao;

import br.com.pacto.bean.avaliacao.AvaliacaoFisicaDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para consulta de avaliação física")
public class ExemploRespostaAvaliacaoFisicaDTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo os dados completos da avaliação física")
    private AvaliacaoFisicaDTO content;

    public AvaliacaoFisicaDTO getContent() {
        return content;
    }

    public void setContent(AvaliacaoFisicaDTO content) {
        this.content = content;
    }
}
