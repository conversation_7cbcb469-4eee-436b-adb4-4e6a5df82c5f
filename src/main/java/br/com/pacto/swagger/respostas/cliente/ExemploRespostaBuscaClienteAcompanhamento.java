package br.com.pacto.swagger.respostas.cliente;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.bean.cliente.ClienteAcompanhamentoJSON;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para consulta de acompanhamento de clientes")
public class ExemploRespostaBuscaClienteAcompanhamento extends EnvelopeRespostaDTO {

    @ApiModelProperty(value = "Lista de acompanhamentos de clientes no período especificado")
    private List<ClienteAcompanhamentoJSON> content;

    public List<ClienteAcompanhamentoJSON> getContent() {
        return content;
    }

    public void setContent(List<ClienteAcompanhamentoJSON> content) {
        this.content = content;
    }
}
