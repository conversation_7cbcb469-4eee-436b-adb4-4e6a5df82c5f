package br.com.pacto.swagger.respostas.perfil;

import br.com.pacto.bean.perfil.PerfilResponseTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para consulta de todos os perfis de acesso encapsulada em EnvelopeRespostaDTO")
public class ExemploRespostaListPerfilResponseTO {

    @ApiModelProperty(value = "Lista contendo todos os perfis de acesso cadastrados no sistema")
    private List<PerfilResponseTO> content;

    public List<PerfilResponseTO> getContent() {
        return content;
    }

    public void setContent(List<PerfilResponseTO> content) {
        this.content = content;
    }
}
