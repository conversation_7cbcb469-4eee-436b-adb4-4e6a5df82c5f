package br.com.pacto.swagger.respostas.gympass;

import br.com.pacto.service.impl.gympass.json.SlotDiaDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Arrays;
import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para consulta de slots por período")
public class ExemploRespostaListSlotDiaDTO {

    @ApiModelProperty(value = "Lista de slots disponíveis no período consultado")
    private List<SlotDiaDTO> content;

    public List<SlotDiaDTO> getContent() {
        return content;
    }

    public void setContent(List<SlotDiaDTO> content) {
        this.content = content;
    }
}
