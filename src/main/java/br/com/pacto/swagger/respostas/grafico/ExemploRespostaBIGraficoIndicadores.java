package br.com.pacto.swagger.respostas.grafico;

import br.com.pacto.controller.json.gestao.BIGraficoResponseGeralSemProfessorDTO;
import br.com.pacto.controller.json.gestao.BIGraficoResponseGeralComProfessorDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para consulta de dados de indicadores de gráfico BI")
public class ExemploRespostaBIGraficoIndicadores {

    @ApiModelProperty(value = "Dados dos indicadores de gráfico BI. Pode ser uma resposta sem separação por professor ou com separação por professor, dependendo da configuração da view")
    private Object content;

    public Object getContent() {
        return content;
    }

    public void setContent(Object content) {
        this.content = content;
    }
}
