package br.com.pacto.swagger.respostas.imagem;

import br.com.pacto.bean.imagem.ImagemResponseTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para listagem de imagens do catálogo")
public class ExemploRespostaListImagemResponseTO {

    @ApiModelProperty(value = "Lista de imagens do catálogo")
    private List<ImagemResponseTO> content;

    public List<ImagemResponseTO> getContent() {
        return content;
    }

    public void setContent(List<ImagemResponseTO> content) {
        this.content = content;
    }
}
