package br.com.pacto.swagger.respostas.administracao;

import java.util.ArrayList;
import java.util.List;

/**
 * Classe de exemplo para resposta do endpoint de lembretes de compromisso
 */
public class ExemploLembretesCompromisso {
    
    private String sucesso;
    private List<String> mensagens;
    
    public ExemploLembretesCompromisso() {
        this.sucesso = "sucesso";
        this.mensagens = new ArrayList<>();
        this.mensagens.add("Lembrete: Você deve treinar 3 vezes por semana. Seu último treino foi em 12/12/2024 14:30:00");
        this.mensagens.add("Lembrete: Você deve treinar 4 vezes por semana. Seu último treino foi em 10/12/2024 16:00:00");
        this.mensagens.add("Lembrete: É hora de começar seu programa de treino! Você deve treinar 2 vezes por semana");
        this.mensagens.add("Lembrete: Você deve treinar 5 vezes por semana. Seu último treino foi em 11/12/2024 08:30:00");
    }
    
    public String getSucesso() {
        return sucesso;
    }
    
    public void setSucesso(String sucesso) {
        this.sucesso = sucesso;
    }
    
    public List<String> getMensagens() {
        return mensagens;
    }
    
    public void setMensagens(List<String> mensagens) {
        this.mensagens = mensagens;
    }
}
