package br.com.pacto.swagger.respostas.auladia;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Classe de exemplo para resposta do endpoint desmarcarAula
 * Representa a estrutura de retorno quando um aluno desmarca uma aula agendada
 */
@ApiModel(description = "Resposta do sistema ao desmarcar uma aula agendada de um aluno")
public class ExemploRespostaDesmarcarAula {

    @ApiModelProperty(value = "Mensagem de confirmação quando a desmarcação é realizada com sucesso", 
                      example = "Aula desmarcada com sucesso")
    private String sucesso;

    @ApiModelProperty(value = "Mensagem de erro caso ocorra algum problema durante a desmarcação da aula", 
                      example = "Não foi possível desmarcar a aula. Horário não encontrado para esta data")
    private String erro;

    public String getSucesso() {
        return sucesso;
    }

    public void setSucesso(String sucesso) {
        this.sucesso = sucesso;
    }

    public String getErro() {
        return erro;
    }

    public void setErro(String erro) {
        this.erro = erro;
    }
}
