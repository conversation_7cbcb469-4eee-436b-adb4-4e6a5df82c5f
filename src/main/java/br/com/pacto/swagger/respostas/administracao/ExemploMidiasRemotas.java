package br.com.pacto.swagger.respostas.administracao;

import java.util.ArrayList;
import java.util.List;

/**
 * Classe de exemplo para resposta do endpoint de mídias remotas
 */
public class ExemploMidiasRemotas {
    
    private List<MidiaExemplo> midias;
    
    public ExemploMidiasRemotas() {
        this.midias = new ArrayList<>();
        
        MidiaExemplo midia1 = new MidiaExemplo();
        midia1.setCodigo(1);
        midia1.setNome("supino_reto.gif");
        midia1.setTema("DEFAULT");
        midia1.setTipo("IMAGEM");
        midia1.setTitulo("Supino Reto");
        
        MidiaExemplo midia2 = new MidiaExemplo();
        midia2.setCodigo(2);
        midia2.setNome("agachamento_livre.mp4");
        midia2.setTema("CLUBWELL");
        midia2.setTipo("VIDEO");
        midia2.setTitulo("Agachamento Livre");
        
        MidiaExemplo midia3 = new MidiaExemplo();
        midia3.setCodigo(3);
        midia3.setNome("rosca_direta.gif");
        midia3.setTema("IRONBOX");
        midia3.setTipo("IMAGEM");
        midia3.setTitulo("Rosca Direta");
        
        MidiaExemplo midia4 = new MidiaExemplo();
        midia4.setCodigo(4);
        midia4.setNome("leg_press.mp4");
        midia4.setTema("DEFAULT");
        midia4.setTipo("VIDEO");
        midia4.setTitulo("Leg Press");
        
        this.midias.add(midia1);
        this.midias.add(midia2);
        this.midias.add(midia3);
        this.midias.add(midia4);
    }
    
    public List<MidiaExemplo> getMidias() {
        return midias;
    }
    
    public void setMidias(List<MidiaExemplo> midias) {
        this.midias = midias;
    }
    
    public static class MidiaExemplo {
        private Integer codigo;
        private String nome;
        private String tema;
        private String tipo;
        private String titulo;
        
        // Getters e Setters
        public Integer getCodigo() {
            return codigo;
        }
        
        public void setCodigo(Integer codigo) {
            this.codigo = codigo;
        }
        
        public String getNome() {
            return nome;
        }
        
        public void setNome(String nome) {
            this.nome = nome;
        }
        
        public String getTema() {
            return tema;
        }
        
        public void setTema(String tema) {
            this.tema = tema;
        }
        
        public String getTipo() {
            return tipo;
        }
        
        public void setTipo(String tipo) {
            this.tipo = tipo;
        }
        
        public String getTitulo() {
            return titulo;
        }
        
        public void setTitulo(String titulo) {
            this.titulo = titulo;
        }
    }
}
