package br.com.pacto.swagger.respostas.aparelho;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para listagem de sensores Selfloop encapsulada em EnvelopeRespostaDTO")
public class ExemploRespostaListSensoresSelfloop {

    @ApiModelProperty(value = "Lista contendo informações dos sensores Selfloop disponíveis para integração")
    private List<Object> content;

    public List<Object> getContent() {
        return content;
    }

    public void setContent(List<Object> content) {
        this.content = content;
    }
}
