package br.com.pacto.swagger.respostas.avaliacao;

import br.com.pacto.controller.json.avaliacao.BIAvaliacaoFisicaDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para consulta de Business Intelligence de avaliações físicas")
public class ExemploRespostaBIAvaliacaoFisicaDTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo os indicadores consolidados de avaliações físicas")
    private BIAvaliacaoFisicaDTO content;

    public BIAvaliacaoFisicaDTO getContent() {
        return content;
    }

    public void setContent(BIAvaliacaoFisicaDTO content) {
        this.content = content;
    }
}
