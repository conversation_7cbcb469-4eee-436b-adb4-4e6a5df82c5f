package br.com.pacto.swagger.respostas.configuracao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para consulta de configurações de manutenção")
public class ExemploRespostaConfiguracoesManutencao {

    @ApiModelProperty(value = "String JSON contendo as configurações de manutenção do sistema", 
                     example = "{\"aplicativo_personalizado\":true,\"aplicativo_personalizado_nome\":\"FitApp Academia\",\"aplicativo_personalizado_url\":\"https://app.academia.com\",\"app_url_email\":\"https://app.academia.com/email\"}")
    private String RETURN;

    public String getRETURN() {
        return RETURN;
    }

    public void setRETURN(String RETURN) {
        this.RETURN = RETURN;
    }
}
