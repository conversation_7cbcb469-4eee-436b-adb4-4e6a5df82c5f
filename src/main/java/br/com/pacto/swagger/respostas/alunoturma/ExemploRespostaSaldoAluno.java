package br.com.pacto.swagger.respostas.alunoturma;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Classe de exemplo para resposta do endpoint saldoAluno
 * Representa a estrutura ModelMap retornada com o saldo de créditos do aluno
 */
@ApiModel(description = "Resposta da consulta do saldo de créditos do aluno")
public class ExemploRespostaSaldoAluno {

    @ApiModelProperty(value = "Saldo atual de créditos de treino do aluno", 
                     example = "Você possui 8 créditos disponíveis para agendamento de aulas.")
    private String sucesso;

    @ApiModelProperty(value = "Mensagem de erro caso a consulta não seja possível", 
                     example = "Não foi possível consultar o saldo. Contrato não encontrado.")
    private String erro;
}
