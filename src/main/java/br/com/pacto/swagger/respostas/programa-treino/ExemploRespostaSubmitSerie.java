package br.com.pacto.swagger.respostas.programatreino;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Classe de exemplo para resposta do endpoint submitSerie
 * Representa a estrutura ModelMap retornada após submissão de uma série de treino
 */
@ApiModel(description = "Resposta da submissão de execução de série de treino")
public class ExemploRespostaSubmitSerie {

    @ApiModelProperty(value = "Status de sucesso da operação", example = "sucesso")
    private String status;

    @ApiModelProperty(value = "Mensagem de erro caso a submissão não seja possível", 
                     example = "Erro ao registrar série de treino")
    private String statusErro;

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatusErro() {
        return statusErro;
    }

    public void setStatusErro(String statusErro) {
        this.statusErro = statusErro;
    }
}
