package br.com.pacto.swagger.respostas.alunoturma;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Classe de exemplo para resposta do endpoint saldoAlunoReporEMarcar
 * Representa a estrutura ModelMap retornada com saldo de reposições e créditos do aluno
 */
@ApiModel(description = "Resposta da consulta do saldo de reposições e créditos do aluno para marcação de aulas")
public class ExemploRespostaSaldoAlunoReporEMarcar {

    @ApiModelProperty(value = "Saldo de reposições de turma disponíveis para o aluno",
                     example = "3")
    private String saldoTurmaReposicoes;

    @ApiModelProperty(value = "Quantidade de créditos disponíveis para agendamento de aulas", 
                     example = "8")
    private String creditos;

    @ApiModelProperty(value = "Mensagem de erro caso a consulta não seja possível", 
                     example = "Não foi possível consultar o saldo. Contrato não encontrado.")
    private String erro;

    public String getSaldoTurmaReposicoes() {
        return saldoTurmaReposicoes;
    }

    public void setSaldoTurmaReposicoes(String saldoTurmaReposicoes) {
        this.saldoTurmaReposicoes = saldoTurmaReposicoes;
    }

    public String getCreditos() {
        return creditos;
    }

    public void setCreditos(String creditos) {
        this.creditos = creditos;
    }

    public String getErro() {
        return erro;
    }

    public void setErro(String erro) {
        this.erro = erro;
    }
}
