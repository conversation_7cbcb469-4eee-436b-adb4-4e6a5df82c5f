package br.com.pacto.swagger.respostas.auladia;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Classe de exemplo para resposta do endpoint confirmarAlunoEmAula
 * Representa a estrutura de retorno ao verificar se um aluno está confirmado/presente em uma aula
 */
@ApiModel(description = "Resposta do sistema ao verificar a presença de um aluno em uma aula específica")
public class ExemploRespostaConfirmarAlunoEmAula {

    @ApiModelProperty(value = "Indica se o aluno está confirmado/presente na aula. " +
                              "Retorna true quando o aluno está registrado como presente na aula, " +
                              "false quando não está presente ou não foi encontrado na lista de participantes", 
                      example = "true")
    private Boolean alunoNaAula;

    @ApiModelProperty(value = "Mensagem de erro caso ocorra algum problema durante a verificação da presença. " +
                              "Aparece quando há falhas na consulta, aluno não encontrado, aula inexistente ou problemas de conectividade", 
                      example = "Não foi possível verificar a presença do aluno. Aula não encontrada para a data informada")
    private String erro;

    public Boolean getAlunoNaAula() {
        return alunoNaAula;
    }

    public void setAlunoNaAula(Boolean alunoNaAula) {
        this.alunoNaAula = alunoNaAula;
    }

    public String getErro() {
        return erro;
    }

    public void setErro(String erro) {
        this.erro = erro;
    }
}
