package br.com.pacto.swagger.respostas.usuario;

import br.com.pacto.controller.json.colaborador.UsuarioResponseTO;
import br.com.pacto.swagger.respostas.ExemploPaginadorDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para listagem paginada de usuários colaboradores encapsulada em EnvelopeRespostaDTO")
public class ExemploRespostaListUsuarioResponseTOPaginacao extends ExemploPaginadorDTO {

    @ApiModelProperty(value = "Lista contendo dados simplificados dos usuários colaboradores")
    private List<UsuarioResponseTO> content;

    public List<UsuarioResponseTO> getContent() {
        return content;
    }

    public void setContent(List<UsuarioResponseTO> content) {
        this.content = content;
    }
}
