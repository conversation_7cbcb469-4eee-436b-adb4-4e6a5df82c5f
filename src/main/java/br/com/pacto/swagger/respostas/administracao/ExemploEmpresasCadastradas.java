package br.com.pacto.swagger.respostas.administracao;

import java.util.HashMap;
import java.util.Map;

/**
 * Classe de exemplo para resposta do endpoint de listagem de empresas cadastradas
 */
public class ExemploEmpresasCadastradas {
    
    private Map<String, String> empresas;
    
    public ExemploEmpresasCadastradas() {
        this.empresas = new HashMap<>();
        this.empresas.put("empresa1", "***********************************************");
        this.empresas.put("empresa2", "***********************************************");
        this.empresas.put("demo", "*******************************************");
        this.empresas.put("teste", "********************************************");
    }
    
    public Map<String, String> getEmpresas() {
        return empresas;
    }
    
    public void setEmpresas(Map<String, String> empresas) {
        this.empresas = empresas;
    }
}
