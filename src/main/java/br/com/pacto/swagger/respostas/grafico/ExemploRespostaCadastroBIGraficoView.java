package br.com.pacto.swagger.respostas.grafico;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Map;

@ApiModel(description = "Representação da resposta de status 200 para cadastro de view de gráfico BI")
public class ExemploRespostaCadastroBIGraficoView {

    @ApiModelProperty(value = "Mapa contendo o ID da view criada",
                      example = "{\"viewId\":\"BI_GRAFICO_VIEW_123\",\"status\":\"Criado com sucesso\",\"nome\":\"Dashboard Frequência Mensal\"}")
    private Map<String, String> content;

    public Map<String, String> getContent() {
        return content;
    }

    public void setContent(Map<String, String> content) {
        this.content = content;
    }
}
