package br.com.pacto.swagger.respostas.locacao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para operações de check-in/check-out de alunos em locações do tipo play ")
public class ExemploRespostaCheckinCheckoutPlay {

    @ApiModelProperty(value = "Identificador do resultado da operação de check-in/check-out. Retorna 'checkinId' quando o aluno faz check-in ou 'checkoutId' quando faz check-out.", example = "checkinId")
    private String content;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
