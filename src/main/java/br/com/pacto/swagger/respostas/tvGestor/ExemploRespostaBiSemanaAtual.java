package br.com.pacto.swagger.respostas.tvGestor;

import br.com.pacto.controller.json.tvGestor.dto.BiTvGestorDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Map;

@ApiModel(description = "Exemplo de resposta para dados de BI da semana atual da TV Gestor encapsulada em EnvelopeRespostaDTO")
public class ExemploRespostaBiSemanaAtual {

    @ApiModelProperty(value = "Dados de BI organizados por dia da semana e hora, contendo estatísticas de comparecimento e agendamentos. " +
            "A estrutura é um mapa onde a chave externa representa o dia (formato yyyyMMdd) e a chave interna representa a hora (0-23). " +
            "Cada entrada contém dados de alunos esperados (agendados) e que compareceram para aquele dia e horário específico.",
            example = "{\"20240623\":{\"6\":{\"esperado\":8,\"compareceu\":6},\"7\":{\"esperado\":12,\"compareceu\":10},\"18\":{\"esperado\":15,\"compareceu\":13},\"19\":{\"esperado\":18,\"compareceu\":16}},\"20240624\":{\"6\":{\"esperado\":10,\"compareceu\":8},\"7\":{\"esperado\":14,\"compareceu\":12},\"18\":{\"esperado\":16,\"compareceu\":14},\"19\":{\"esperado\":20,\"compareceu\":18}}}")
    private Map<String, Map<String, BiTvGestorDTO>> content;

    public Map<String, Map<String, BiTvGestorDTO>> getContent() {
        return content;
    }

    public void setContent(Map<String, Map<String, BiTvGestorDTO>> content) {
        this.content = content;
    }

}
