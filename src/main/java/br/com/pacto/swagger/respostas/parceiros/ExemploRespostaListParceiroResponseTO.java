package br.com.pacto.swagger.respostas.parceiros;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.controller.json.parceiros.ParceiroResponseTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * Classe de exemplo para resposta da listagem de parceiros
 */
@ApiModel(description = "Exemplo de resposta para listagem de parceiros")
public class ExemploRespostaListParceiroResponseTO {

    @ApiModelProperty(value = "Lista de parceiros cadastrados no sistema")
    private List<ParceiroResponseTO> content;

    public List<ParceiroResponseTO> getContent() {
        return content;
    }

    public void setContent(List<ParceiroResponseTO> content) {
        this.content = content;
    }
}
