package br.com.pacto.swagger.respostas.variaveis;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import java.util.List;

@ApiModel(description = "Representação da resposta de status 200")
public class ExemploRespostaListInteger {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as informações solicitadas", example = "[1,2,3,4,5,6]")
    private List<Integer> content;

    public List<Integer> getContent() {
        return content;
    }

    public void setContent(List<Integer> content) {
        this.content = content;
    }
}
