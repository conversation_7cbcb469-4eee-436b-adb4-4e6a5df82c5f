package br.com.pacto.swagger.respostas.wod;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Classe de exemplo para resposta de importação de WOD do CrossFit
 */
@ApiModel(description = "Representação da resposta de status 200 para importação de WOD do CrossFit encapsulada em EnvelopeRespostaDTO")
public class ExemploRespostaStringImportacaoWod {

    @ApiModelProperty(value = "Descrição do WOD importado do site oficial do CrossFit", 
                     example = "For Time: 21-15-9 reps of Thrusters (95/65 lb) and Pull-ups. Post time to comments.")
    private String content;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
