package br.com.pacto.swagger.respostas.treino;

import br.com.pacto.bean.bi.DashboardBI;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200")
public class ExemploRespostaDashboardBI {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as informações solicitadas")
    private DashboardBI content;

    public DashboardBI getContent() {
        return content;
    }

    public void setContent(DashboardBI content) {
        this.content = content;
    }
}
