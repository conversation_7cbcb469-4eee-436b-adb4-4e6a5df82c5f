package br.com.pacto.swagger.respostas.turma;


import br.com.pacto.controller.json.agendamento.TurmaResponseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

@ApiModel(description = "Representação da resposta de status 200")
public class ExemploRespostaCalendario {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as informações solicitadas", example = "Quarta-feira Junho 04 17:00:00 BRT 2025")
    private Date content;

    public Date getContent() {
        return content;
    }

    public void setContent(Date content) {
        this.content = content;
    }
}
