package br.com.pacto.swagger.respostas.cliente;

import br.com.pacto.service.intf.cliente.RetrospectivaAnoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para retrospectiva anual do cliente")
public class ExemploRespostaRetrospectiva {

    @ApiModelProperty(value = "Dados da retrospectiva anual do cliente")
    private RetrospectivaAnoVO content;

    public RetrospectivaAnoVO getContent() {
        return content;
    }

    public void setContent(RetrospectivaAnoVO content) {
        this.content = content;
    }
}
