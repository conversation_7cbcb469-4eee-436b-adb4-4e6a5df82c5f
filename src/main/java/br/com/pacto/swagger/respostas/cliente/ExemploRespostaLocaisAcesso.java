package br.com.pacto.swagger.respostas.cliente;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import servicos.integracao.zw.json.ColetorJSON;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para consulta de locais de acesso")
public class ExemploRespostaLocaisAcesso {

    @ApiModelProperty(value = "Lista de locais de acesso (coletores) da empresa")
    private List<ColetorJSON> locaisAcesso;

    public List<ColetorJSON> getLocaisAcesso() {
        return locaisAcesso;
    }

    public void setLocaisAcesso(List<ColetorJSON> locaisAcesso) {
        this.locaisAcesso = locaisAcesso;
    }
}
