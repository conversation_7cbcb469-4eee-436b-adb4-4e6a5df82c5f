package br.com.pacto.swagger.respostas.locacao;

import br.com.pacto.controller.json.agendamento.AgendaDisponibilidadeDTO;
import br.com.pacto.swagger.respostas.ExemploPaginadorDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * Classe de exemplo para resposta de disponibilidades de locação
 */
@ApiModel(description = "Exemplo de uma resposta paginada contendo informações de uma lista de agendas de disponibilidade")
public class ExemploRespostaListDisponibilidadeDTO extends ExemploPaginadorDTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo uma lista de disponibilidades da agenda")
    List<AgendaDisponibilidadeDTO> content;

    public List<AgendaDisponibilidadeDTO> getContent() {
        return content;
    }

    public void setContent(List<AgendaDisponibilidadeDTO> content) {
        this.content = content;
    }
}
