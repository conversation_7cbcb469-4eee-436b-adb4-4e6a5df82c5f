package br.com.pacto.swagger.respostas.atividade;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Exemplo de resposta para importação de atividades via DTO")
public class ExemploRespostaImportarAtividadesDto {

    @ApiModelProperty(value = "Resultado da importação com detalhes do processo", 
                     example = "Importação via DTO concluída. 15 atividades processadas com sucesso.")
    private String statusSucesso;

    @ApiModelProperty(value = "Indica sucesso da operação", example = "true")
    private Boolean sucesso;

    @ApiModelProperty(value = "Mensagem de erro (quando aplicável)", example = "null")
    private String statusErro;
}
