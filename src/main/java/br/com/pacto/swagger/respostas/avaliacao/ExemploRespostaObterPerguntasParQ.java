package br.com.pacto.swagger.respostas.avaliacao;

import br.com.pacto.bean.anamnese.PerguntaResponseTO;
import br.com.pacto.bean.anamnese.RespostaClienteTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para obtenção das perguntas do questionário PAR-Q")
public class ExemploRespostaObterPerguntasParQ {

    @ApiModelProperty(value = "Lista das perguntas do questionário PAR-Q (Physical Activity Readiness Questionnaire) para avaliação de prontidão para atividade física")
    private List<PerguntaResponseTO> perguntasParQ;

    @ApiModelProperty(value = "Indica se deve apresentar a lei do PAR-Q na interface do usuário", example = "true")
    private Boolean apresentarLeiParq;

    @ApiModelProperty(value = "Lista das respostas já preenchidas pelo aluno no questionário PAR-Q, quando informado o código da resposta")
    private List<RespostaClienteTO> respostasClienteParQ;

    @ApiModelProperty(value = "Mensagem de erro quando ocorre algum problema na operação", example = "Erro ao consultar perguntas PAR-Q")
    private String STATUS_ERRO;

    public List<PerguntaResponseTO> getPerguntasParQ() {
        return perguntasParQ;
    }

    public void setPerguntasParQ(List<PerguntaResponseTO> perguntasParQ) {
        this.perguntasParQ = perguntasParQ;
    }

    public Boolean getApresentarLeiParq() {
        return apresentarLeiParq;
    }

    public void setApresentarLeiParq(Boolean apresentarLeiParq) {
        this.apresentarLeiParq = apresentarLeiParq;
    }

    public List<RespostaClienteTO> getRespostasClienteParQ() {
        return respostasClienteParQ;
    }

    public void setRespostasClienteParQ(List<RespostaClienteTO> respostasClienteParQ) {
        this.respostasClienteParQ = respostasClienteParQ;
    }

    public String getSTATUS_ERRO() {
        return STATUS_ERRO;
    }

    public void setSTATUS_ERRO(String STATUS_ERRO) {
        this.STATUS_ERRO = STATUS_ERRO;
    }
}
