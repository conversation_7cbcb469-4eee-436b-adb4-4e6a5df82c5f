package br.com.pacto.swagger.respostas.configuracao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para recarregamento de cache de usuário")
public class ExemploRespostaReloadUsuario {

    @ApiModelProperty(value = "Mensagem de confirmação da remoção do cache do usuário", 
                     example = "Cache usuário removido com sucesso! Key: academia123 | Instance: treino-prod-01")
    private String RETURN;

    public String getRETURN() {
        return RETURN;
    }

    public void setRETURN(String RETURN) {
        this.RETURN = RETURN;
    }
}
