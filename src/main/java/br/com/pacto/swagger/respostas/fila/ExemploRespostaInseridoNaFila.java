package br.com.pacto.swagger.respostas.fila;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200")
public class ExemploRespostaInseridoNaFila {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as informações solicitadas", example = "Inserido na fila com sucesso, codigo: 1")
    private String content;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
