package br.com.pacto.swagger.respostas.avaliacao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para verificação de produto vigente de avaliação física")
public class ExemploRespostaStatusProdutoVigenteAvaliacaoFisica {

    @ApiModelProperty(value = "Status do produto de avaliação física vigente do aluno. Indica se o aluno possui um produto ativo e se ainda há avaliações disponíveis para uso. \n\n" +
                             "<strong>Possíveis valores de retorno:</strong>\n" +
                             "- <strong>\"true\":</strong> O aluno possui produto vigente com avaliações disponíveis para uso\n" +
                             "- <strong>\"O aluno não tem um produto de Avaliação Física vigente.\":</strong> O aluno não possui nenhum produto ativo no momento\n" +
                             "- <strong>\"O produto vigente já foi usado.\":</strong> O aluno possui produto vigente, mas todas as avaliações já foram utilizadas", 
                     example = "true")
    private String content;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
