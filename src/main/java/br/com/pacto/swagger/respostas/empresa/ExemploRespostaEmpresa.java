package br.com.pacto.swagger.respostas.empresa;

import br.com.pacto.bean.empresa.Empresa;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para entidade empresa")
public class ExemploRespostaEmpresa {

    @ApiModelProperty(value = "Conteúdo da resposta contendo a entidade completa da empresa")
    private Empresa content;

    public Empresa getContent() {
        return content;
    }

    public void setContent(Empresa content) {
        this.content = content;
    }
}
