package br.com.pacto.swagger.respostas.anamnese;


import br.com.pacto.bean.anamnese.AnamneseResponseTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200")
public class ExemploRespostaListAnamneseResponseTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as informações solicitadas")
    private List<AnamneseResponseTO> content;

    public List<AnamneseResponseTO> getContent() {
        return content;
    }

    public void setContent(List<AnamneseResponseTO> content) {
        this.content = content;
    }
}
