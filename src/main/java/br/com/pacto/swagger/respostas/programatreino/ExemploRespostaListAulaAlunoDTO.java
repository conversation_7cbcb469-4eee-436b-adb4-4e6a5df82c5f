package br.com.pacto.swagger.respostas.programatreino;

import br.com.pacto.controller.json.aulaDia.AulaAlunoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para consulta de aulas agendadas por aluno encapsulada em EnvelopeRespostaDTO")
public class ExemploRespostaListAulaAlunoDTO {

    @ApiModelProperty(value = "Lista das aulas agendadas para o aluno no período especificado")
    private List<AulaAlunoDTO> content;

    public List<AulaAlunoDTO> getContent() {
        return content;
    }

    public void setContent(List<AulaAlunoDTO> content) {
        this.content = content;
    }
}
