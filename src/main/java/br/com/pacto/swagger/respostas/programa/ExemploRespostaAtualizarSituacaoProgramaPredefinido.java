package br.com.pacto.swagger.respostas.programa;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para atualização de situação de programa predefinido")
public class ExemploRespostaAtualizarSituacaoProgramaPredefinido {

    @ApiModelProperty(value = "Indicador de sucesso da operação", example = "sucesso")
    private String sucesso;

    public String getSucesso() {
        return sucesso;
    }

    public void setSucesso(String sucesso) {
        this.sucesso = sucesso;
    }
}
