package br.com.pacto.swagger.respostas.cliente;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para gravação de observação do cliente")
public class ExemploRespostaGravarObservacaoCorrigida {

    @JsonProperty("return")
    @ApiModelProperty(value = "Dados da observação cadastrada com sucesso")
    private ObservacaoMapeada returnValue;

    public static class ObservacaoMapeada {

        @ApiModelProperty(value = "Código único da observação cadastrada", example = "456")
        private Integer codigo;

        @ApiModelProperty(value = "Texto da observação cadastrada", example = "Cliente apresentou boa evolução no treino de membros superiores e demonstrou maior resistência nos exercícios cardiovasculares")
        private String observacao;

        @ApiModelProperty(value = "Data da observação formatada para exibição", example = "15/06/2024 14:30")
        private String data;

        @ApiModelProperty(value = "Data da observação em timestamp (milissegundos)", example = "1718467800000")
        private Long dataLong;

        @ApiModelProperty(value = "Indica se a observação foi marcada como importante", example = "true")
        private Boolean importante;

        @ApiModelProperty(value = "Nome do profissional que cadastrou a observação", example = "João Silva Santos")
        private String nome;

        @ApiModelProperty(value = "Nome de usuário do profissional que cadastrou a observação", example = "professor1")
        private String username;

        @ApiModelProperty(value = "URL da foto do profissional que cadastrou a observação", example = "https://exemplo.com/fotos/professor1.jpg")
        private String srcImg;

        public Integer getCodigo() {
            return codigo;
        }

        public void setCodigo(Integer codigo) {
            this.codigo = codigo;
        }

        public String getObservacao() {
            return observacao;
        }

        public void setObservacao(String observacao) {
            this.observacao = observacao;
        }

        public String getData() {
            return data;
        }

        public void setData(String data) {
            this.data = data;
        }

        public Long getDataLong() {
            return dataLong;
        }

        public void setDataLong(Long dataLong) {
            this.dataLong = dataLong;
        }

        public Boolean getImportante() {
            return importante;
        }

        public void setImportante(Boolean importante) {
            this.importante = importante;
        }

        public String getNome() {
            return nome;
        }

        public void setNome(String nome) {
            this.nome = nome;
        }

        public String getUsername() {
            return username;
        }

        public void setUsername(String username) {
            this.username = username;
        }

        public String getSrcImg() {
            return srcImg;
        }

        public void setSrcImg(String srcImg) {
            this.srcImg = srcImg;
        }
    }

    public ObservacaoMapeada getReturnValue() {
        return returnValue;
    }

    public void setReturnValue(ObservacaoMapeada returnValue) {
        this.returnValue = returnValue;
    }

}
