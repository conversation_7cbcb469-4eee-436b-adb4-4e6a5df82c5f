package br.com.pacto.swagger.respostas.alunoturma;

import br.com.pacto.controller.json.agendamento.ServicoAgendamentoPersonalDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Classe de exemplo para resposta do endpoint criarAgendamento
 * Representa a estrutura EnvelopeRespostaDTO retornada com dados do agendamento criado
 */
@ApiModel(description = "Resposta da criação de um novo agendamento personalizado")
public class ExemploRespostaCriarAgendamento {

    @ApiModelProperty(value = "Dados completos do agendamento criado incluindo ID gerado, informações do aluno, " +
                             "professor, tipo de serviço, data, horário e status. Contém todas as informações " +
                             "necessárias para confirmação e acompanhamento do agendamento.")
    private ServicoAgendamentoPersonalDTO content;

    @ApiModelProperty(value = "Indica se a operação foi realizada com sucesso", example = "true")
    private Boolean sucesso;

    @ApiModelProperty(value = "Status da operação realizada", example = "OK")
    private String statusErro;

    public ServicoAgendamentoPersonalDTO getContent() {
        return content;
    }

    public void setContent(ServicoAgendamentoPersonalDTO content) {
        this.content = content;
    }

    public Boolean getSucesso() {
        return sucesso;
    }

    public void setSucesso(Boolean sucesso) {
        this.sucesso = sucesso;
    }

    public String getStatusErro() {
        return statusErro;
    }

    public void setStatusErro(String statusErro) {
        this.statusErro = statusErro;
    }
}
