package br.com.pacto.swagger.respostas.agendamento;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Resposta de sucesso para reagendamento de agendamento")
public class ExemploRespostaReagendamento {

    @ApiModelProperty(value = "Mensagem de sucesso do reagendamento", example = "Reagendamento realizado com sucesso")
    private String status;

    @ApiModelProperty(value = "Mensagem de erro, presente apenas quando ocorre algum problema", example = "")
    private String erro;
}
