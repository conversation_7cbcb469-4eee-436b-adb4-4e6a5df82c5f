package br.com.pacto.swagger.respostas.configuracao;

import br.com.pacto.controller.json.base.ConfiguracaoIntegracoesDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para consulta de configurações de integrações")
public class ExemploRespostaConfiguracoesIntegracoesDTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as configurações de integrações do sistema")
    private ConfiguracaoIntegracoesDTO content;

    public ConfiguracaoIntegracoesDTO getContent() {
        return content;
    }

    public void setContent(ConfiguracaoIntegracoesDTO content) {
        this.content = content;
    }
}
