package br.com.pacto.swagger.respostas.configDisponibilidade;

import br.com.pacto.controller.json.agendamento.DisponibilidadeConfigDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para consulta de configurações de disponibilidade")
public class ExemploRespostaConfigDisponibilidade {

    @ApiModelProperty(value = "Lista de configurações de disponibilidade")
    private List<DisponibilidadeConfigDTO> content;

    public List<DisponibilidadeConfigDTO> getContent() {
        return content;
    }

    public void setContent(List<DisponibilidadeConfigDTO> content) {
        this.content = content;
    }
}
