package br.com.pacto.swagger.respostas.musculo;

import br.com.pacto.bean.musculo.MusculoResponseTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Exemplo de resposta para um músculo específico")
public class ExemploRespostaMusculoResponseTO {

    @ApiModelProperty(value = "Dados do músculo")
    private MusculoResponseTO content;

    public MusculoResponseTO getContent() {
        return content;
    }

    public void setContent(MusculoResponseTO content) {
        this.content = content;
    }
}
