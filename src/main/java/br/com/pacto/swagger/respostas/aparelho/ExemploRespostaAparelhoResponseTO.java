package br.com.pacto.swagger.respostas.aparelho;

import br.com.pacto.controller.json.programa.AparelhoResponseTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para consulta, inclusão ou alteração de aparelho encapsulada em EnvelopeRespostaDTO")
public class ExemploRespostaAparelhoResponseTO {

    @ApiModelProperty(value = "Dados detalhados do aparelho incluindo ajustes e atividades relacionadas")
    private AparelhoResponseTO content;

    public AparelhoResponseTO getContent() {
        return content;
    }

    public void setContent(AparelhoResponseTO content) {
        this.content = content;
    }
}
