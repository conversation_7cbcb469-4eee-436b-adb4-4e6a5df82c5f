package br.com.pacto.swagger.respostas.alunoturma;

import br.com.pacto.controller.json.aulaDia.AulaDiaJSON;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * Classe de exemplo para resposta do endpoint consultarAulasModalidadeAluno
 * Representa a estrutura ModelMap retornada com lista de aulas de modalidade do aluno
 */
@ApiModel(description = "Resposta da consulta de aulas de modalidade por aluno")
public class ExemploRespostaConsultarAulasModalidadeAluno {

    @ApiModelProperty(value = "Lista de aulas de modalidades agendadas para o aluno no período especificado")
    private List<AulaDiaJSON> aulas;
}
