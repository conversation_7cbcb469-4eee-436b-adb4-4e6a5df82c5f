package br.com.pacto.swagger.respostas.cliente.observacao;


import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.controller.json.aluno.ClienteObservacaoAnexosDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200")
public class ExemploRespostaListClienteObservacaoAnexosDTO extends PaginadorDTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as informações solicitadas")
    private List<ClienteObservacaoAnexosDTO> content;

    public List<ClienteObservacaoAnexosDTO> getContent() {
        return content;
    }

    public void setContent(List<ClienteObservacaoAnexosDTO> content) {
        this.content = content;
    }
}
