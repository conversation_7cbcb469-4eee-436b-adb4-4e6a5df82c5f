package br.com.pacto.swagger.respostas.configuracao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Map;

@ApiModel(description = "Representação da resposta de status 200 para consulta do status do sistema")
public class ExemploRespostaGetStatus {

    @ApiModelProperty(value = "Mapa contendo o status atual do sistema e suas conexões")
    private Map<String, Object> RETURN;

    public Map<String, Object> getRETURN() {
        return RETURN;
    }

    public void setRETURN(Map<String, Object> RETURN) {
        this.RETURN = RETURN;
    }
}
