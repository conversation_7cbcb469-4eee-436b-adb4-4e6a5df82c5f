package br.com.pacto.swagger.respostas.sintetico;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para sincronização de usuários")
public class ExemploRespostaSincronizacaoUsuario {

    @ApiModelProperty(value = "Resultado da operação de sincronização. Retorna 'OK' em caso de sucesso ou mensagem de erro iniciada com 'ERRO:' em caso de falha. A sincronização processa uma lista de usuários (alunos ou professores) e realiza a integração com o sistema interno, incluindo criação/atualização de dados pessoais, vinculação com empresas, tratamento de perfis e configuração de e-mails.", 
                     example = "OK")
    private String content;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
