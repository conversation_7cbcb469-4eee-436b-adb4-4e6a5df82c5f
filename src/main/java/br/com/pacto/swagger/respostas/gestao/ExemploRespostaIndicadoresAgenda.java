package br.com.pacto.swagger.respostas.gestao;

import br.com.pacto.controller.json.professor.ProfessorIndicadorResponseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Exemplo de resposta para indicadores da agenda dos professores encapsulada em EnvelopeRespostaDTO")
public class ExemploRespostaIndicadoresAgenda {

    @ApiModelProperty(value = "Lista contendo os indicadores de agenda agrupados por professor com estatísticas de agendamentos, execuções, cancelamentos e faltas")
    private List<ProfessorIndicadorResponseDTO> content;

    public List<ProfessorIndicadorResponseDTO> getContent() {
        return content;
    }

    public void setContent(List<ProfessorIndicadorResponseDTO> content) {
        this.content = content;
    }
}
