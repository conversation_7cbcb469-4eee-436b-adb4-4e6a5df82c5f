package br.com.pacto.swagger.respostas.locacao;

import br.com.pacto.controller.json.locacao.LocacaoHorarioTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Arrays;
import java.util.List;

/**
 * Classe de exemplo para resposta de horário individual de locação
 */

@ApiModel(description = "Exemplo de resposta contendo um horário de locação")
public class ExemploRespostaLocacaoHorarioTO {
    @ApiModelProperty(value = "Conteúdo das respostas contendo as informações de um horário de locação")
    LocacaoHorarioTO content;

    public LocacaoHorarioTO getContent() {
        return content;
    }

    public void setContent(LocacaoHorarioTO content) {
        this.content = content;
    }
}
