package br.com.pacto.swagger.respostas.gestao;

import br.com.pacto.bean.programa.ExecucoesTreinoTO;
import br.com.pacto.swagger.respostas.ExemploPaginadorDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para consulta paginada de execuções de treino")
public class ExemploRespostaExecucoesTreino extends ExemploPaginadorDTO {

    @ApiModelProperty(value = "Lista paginada contendo informações sobre as execuções de treino realizadas pelos alunos, incluindo matr<PERSON><PERSON>, nome do aluno, professor responsável e quantidade de execuções")
    private List<ExecucoesTreinoTO> content;

    public List<ExecucoesTreinoTO> getContent() {
        return content;
    }

    public void setContent(List<ExecucoesTreinoTO> content) {
        this.content = content;
    }
}
