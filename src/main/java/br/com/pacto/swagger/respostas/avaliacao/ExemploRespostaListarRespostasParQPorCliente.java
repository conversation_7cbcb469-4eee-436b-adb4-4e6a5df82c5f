package br.com.pacto.swagger.respostas.avaliacao;

import br.com.pacto.controller.json.avaliacao.RespostaClienteJSON;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * Classe de exemplo para resposta do endpoint listarRespostasParQPorCliente
 * Representa a estrutura ModelMap retornada com as respostas do questionário PAR-Q por cliente
 */
@ApiModel(description = "Resposta da consulta de respostas do questionário PAR-Q por cliente")
public class ExemploRespostaListarRespostasParQPorCliente {

    @ApiModelProperty(value = "Lista das respostas do aluno para o questionário PAR-Q (Physical Activity Readiness Questionnaire)")
    private List<RespostaClienteJSON> RETURN;

    @ApiModelProperty(value = "Mensagem de erro caso a consulta não seja possível", 
                     example = "Aluno não encontrado")
    private String STATUS_ERRO;

    public List<RespostaClienteJSON> getRETURN() {
        return RETURN;
    }

    public void setRETURN(List<RespostaClienteJSON> RETURN) {
        this.RETURN = RETURN;
    }

    public String getSTATUS_ERRO() {
        return STATUS_ERRO;
    }

    public void setSTATUS_ERRO(String STATUS_ERRO) {
        this.STATUS_ERRO = STATUS_ERRO;
    }
}
