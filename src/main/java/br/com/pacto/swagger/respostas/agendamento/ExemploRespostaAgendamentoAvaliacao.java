package br.com.pacto.swagger.respostas.agendamento;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Resposta contendo agendamentos de avaliação física em formato JSON")
public class ExemploRespostaAgendamentoAvaliacao {

    @ApiModelProperty(value = "Dados dos agendamentos de avaliação em formato JSON string", 
                      example = "[{\"id\":123,\"cliente\":\"<PERSON>\",\"professor\":\"<PERSON><PERSON>\",\"data\":\"15/12/2024\",\"hora\":\"14:00\",\"confirmado\":true}]")
    private String sucesso;

    @ApiModelProperty(value = "Mensagem de erro, presente apenas quando ocorre algum problema", example = "")
    private String erro;
}
