package br.com.pacto.swagger.respostas.administracao;

import java.util.ArrayList;
import java.util.List;

/**
 * Classe de exemplo para resposta do endpoint de busca de clientes por soundex
 */
public class ExemploClientesSoundex {
    
    private List<String> clientes;
    
    public ExemploClientesSoundex() {
        this.clientes = new ArrayList<>();
        this.clientes.add("<PERSON>");
        this.clientes.add("<PERSON>");
        this.clientes.add("<PERSON>");
        this.clientes.add("<PERSON>");
        this.clientes.add("<PERSON>");
    }
    
    public List<String> getClientes() {
        return clientes;
    }
    
    public void setClientes(List<String> clientes) {
        this.clientes = clientes;
    }
}
