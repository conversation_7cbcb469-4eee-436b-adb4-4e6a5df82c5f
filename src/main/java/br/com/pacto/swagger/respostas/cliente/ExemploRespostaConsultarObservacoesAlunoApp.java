package br.com.pacto.swagger.respostas.cliente;

import br.com.pacto.bean.cliente.ClienteObservacaoJSON;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para consulta de observações do aluno no aplicativo móvel")
public class ExemploRespostaConsultarObservacoesAlunoApp {

    @ApiModelProperty(value = "Lista de observações do aluno formatadas para aplicativo móvel", 
                     example = "[{\"codigo\":123,\"importante\":true,\"userName\":\"professor1\",\"nome\":\"<PERSON>\",\"dataLong\":1718467200000,\"observacao\":\"Cliente apresentou boa evolução no treino de membros superiores\",\"foto\":\"https://exemplo.com/foto.jpg\"},{\"codigo\":124,\"importante\":false,\"userName\":\"professor2\",\"nome\":\"Maria Santos\",\"dataLong\":1718380800000,\"observacao\":\"Aluno relatou dificuldade na execução do exercício de agachamento\",\"foto\":\"https://exemplo.com/foto2.jpg\"}]")
    private List<ClienteObservacaoJSON> sucesso;

    public List<ClienteObservacaoJSON> getSucesso() {
        return sucesso;
    }

    public void setSucesso(List<ClienteObservacaoJSON> sucesso) {
        this.sucesso = sucesso;
    }
}
