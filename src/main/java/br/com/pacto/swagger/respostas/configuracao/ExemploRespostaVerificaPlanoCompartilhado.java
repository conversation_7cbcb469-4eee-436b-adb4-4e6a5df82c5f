package br.com.pacto.swagger.respostas.configuracao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para verificação de plano compartilhado")
public class ExemploRespostaVerificaPlanoCompartilhado {

    @ApiModelProperty(value = "Indica se o sistema permite planos recorrentes compartilhados (dependentes)", example = "true")
    private Boolean content;

    public Boolean getContent() {
        return content;
    }

    public void setContent(Boolean content) {
        this.content = content;
    }
}
