package br.com.pacto.swagger.respostas.alunoturma;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;
import java.util.Map;

/**
 * Classe de exemplo para resposta do endpoint alunosTurma
 * Representa a estrutura ModelMap retornada com lista de alunos da turma
 */
@ApiModel(description = "Resposta da consulta de alunos de uma turma específica")
public class ExemploRespostaAlunosTurma {

    @ApiModelProperty(value = "Lista de alunos matriculados na turma com informações básicas para identificação. " +
                             "Cada item do mapa contém as chaves: 'foto' (URL da foto do aluno), 'nome' (nome completo do aluno) e 'matricula' (número da matrícula)")
    private List<Map<String, String>> alunos;
}
