package br.com.pacto.swagger.respostas.configuracao;

import br.com.pacto.controller.json.base.ConfiguracoesGestaoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para consulta de configurações de gestão")
public class ExemploRespostaConfiguracoesGestaoDTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as configurações de gestão do sistema")
    private ConfiguracoesGestaoDTO content;

    public ConfiguracoesGestaoDTO getContent() {
        return content;
    }

    public void setContent(ConfiguracoesGestaoDTO content) {
        this.content = content;
    }
}
