package br.com.pacto.swagger.respostas.termoaceite;

import br.com.pacto.controller.json.termoaceite.TermoAceiteAssinaturaDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para consulta de lista de assinaturas de termos de aceite")
public class ExemploRespostaListTermoAceiteAssinaturaDTO {

    @ApiModelProperty(value = "Lista contendo todas as assinaturas de termos de aceite")
    private List<TermoAceiteAssinaturaDTO> content;

    public List<TermoAceiteAssinaturaDTO> getContent() {
        return content;
    }

    public void setContent(List<TermoAceiteAssinaturaDTO> content) {
        this.content = content;
    }
}
