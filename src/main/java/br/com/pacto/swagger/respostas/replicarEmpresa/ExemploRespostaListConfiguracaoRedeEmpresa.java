package br.com.pacto.swagger.respostas.replicarEmpresa;

import br.com.pacto.bean.replicarEmpresa.ConfiguracaoRedeEmpresa;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Exemplo de resposta para lista de configurações de replicação de empresa")
public class ExemploRespostaListConfiguracaoRedeEmpresa {

    @ApiModelProperty(value = "Lista de configurações de replicação de empresa")
    private List<ConfiguracaoRedeEmpresa> content;

    public List<ConfiguracaoRedeEmpresa> getContent() {
        return content;
    }

    public void setContent(List<ConfiguracaoRedeEmpresa> content) {
        this.content = content;
    }
}
