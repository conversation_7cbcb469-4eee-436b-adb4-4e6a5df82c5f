package br.com.pacto.swagger.respostas.gympass;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para verificação de saúde da integração GymPass")
public class ExemploRespostaHealthGympass {

    @ApiModelProperty(value = "Status de saúde da integração GymPass", example = "Integração funcionando corretamente - Última sincronização: 2025-06-20 10:30:00")
    private String content;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
