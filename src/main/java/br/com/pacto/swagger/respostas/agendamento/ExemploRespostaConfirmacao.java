package br.com.pacto.swagger.respostas.agendamento;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Resposta de sucesso para confirmação de agendamento")
public class ExemploRespostaConfirmacao {

    @ApiModelProperty(value = "Mensagem de sucesso da confirmação", example = "Agendamento confirmado com sucesso")
    private String status;

    @ApiModelProperty(value = "Mensagem de erro, presente apenas quando ocorre algum problema", example = "")
    private String erro;
}
