package br.com.pacto.swagger.respostas.tipoEvento;

import br.com.pacto.controller.json.tipoEvento.TipoAgendamentoCadDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Exemplo de resposta para cadastro de tipo de agendamento")
public class ExemploRespostaTipoAgendamentoCadDTO {

    @ApiModelProperty(value = "Dados do tipo de agendamento cadastrado")
    private TipoAgendamentoCadDTO content;

    public TipoAgendamentoCadDTO getContent() {
        return content;
    }

    public void setContent(TipoAgendamentoCadDTO content) {
        this.content = content;
    }
}
