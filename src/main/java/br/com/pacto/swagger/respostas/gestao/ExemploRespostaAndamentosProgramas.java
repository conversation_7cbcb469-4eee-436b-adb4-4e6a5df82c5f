package br.com.pacto.swagger.respostas.gestao;

import br.com.pacto.bean.programa.ProgramaTreinoAndamentoTO;
import br.com.pacto.swagger.respostas.ExemploPaginadorDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para consulta paginada de andamentos de programas de treino")
public class ExemploRespostaAndamentosProgramas extends ExemploPaginadorDTO {

    @ApiModelProperty(value = "Lista paginada contendo informações sobre o andamento dos programas de treino dos alunos, incluindo nome do programa, aluno, execuções realizadas e porcentagem de conclusão")
    private List<ProgramaTreinoAndamentoTO> content;

    public List<ProgramaTreinoAndamentoTO> getContent() {
        return content;
    }

    public void setContent(List<ProgramaTreinoAndamentoTO> content) {
        this.content = content;
    }
}
