package br.com.pacto.swagger.respostas.aluno.app;


import br.com.pacto.controller.json.usuario.AlunoAppInfoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200")
public class ExemploRespostaAlunoAppInfoDTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as informações solicitadas")
    private AlunoAppInfoDTO content;

    public AlunoAppInfoDTO getContent() {
        return content;
    }

    public void setContent(AlunoAppInfoDTO content) {
        this.content = content;
    }
}
