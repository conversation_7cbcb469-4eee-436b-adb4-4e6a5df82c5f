package br.com.pacto.swagger.respostas.avaliacao;

import br.com.pacto.bean.avaliacao.AvaliacaoBioimpedanciaDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para extração de dados de bioimpedância")
public class ExemploRespostaBioimpedanciaDTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo os dados extraídos da bioimpedância")
    private AvaliacaoBioimpedanciaDTO content;

    public AvaliacaoBioimpedanciaDTO getContent() {
        return content;
    }

    public void setContent(AvaliacaoBioimpedanciaDTO content) {
        this.content = content;
    }
}
