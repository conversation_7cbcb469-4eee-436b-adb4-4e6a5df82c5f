package br.com.pacto.swagger.respostas.programa;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para retorno de valor string")
public class ExemploRespostaString {

    @ApiModelProperty(value = "Valor string retornado pela operação", example = "processando...")
    private String content;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
