package br.com.pacto.swagger.respostas.programatreino;

import br.com.pacto.bean.programa.ObjetivoPredefinido;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * Classe de exemplo para resposta do endpoint objPredef/sync
 * Representa a estrutura ModelMap retornada com objetivos predefinidos para sincronização
 */
@ApiModel(description = "Resposta da sincronização de objetivos predefinidos")
public class ExemploRespostaObjetivosPredefinidosSync {

    @ApiModelProperty(value = "Lista de objetivos predefinidos que foram modificados após a data base informada para sincronização")
    private List<ObjetivoPredefinido> RETURN;

    @ApiModelProperty(value = "Mensagem de erro caso a sincronização não seja possível", 
                     example = "Erro ao sincronizar objetivos predefinidos")
    private String statusErro;

    public List<ObjetivoPredefinido> getRETURN() {
        return RETURN;
    }

    public void setRETURN(List<ObjetivoPredefinido> RETURN) {
        this.RETURN = RETURN;
    }

    public String getStatusErro() {
        return statusErro;
    }

    public void setStatusErro(String statusErro) {
        this.statusErro = statusErro;
    }
}
