package br.com.pacto.swagger.respostas.locacao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para operações de remoção de aluno do horário play encapsulada em EnvelopeRespostaDTO")
public class ExemploRespostaRemoverAlunoHorarioPlay {

    @ApiModelProperty(value = "Identificador do resultado da operação de remoção do aluno. Retorna 'removidoId' quando o aluno é removido com sucesso do horário play.", example = "removidoId")
    private String content;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
