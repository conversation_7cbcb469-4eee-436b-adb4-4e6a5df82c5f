package br.com.pacto.swagger.respostas.erros;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Classe de exemplo para resposta de erro HTTP 400 - Bad Request
 * Utilizada para documentação automática do Swagger em todos os endpoints
 */
@ApiModel(description = "Exemplo de resposta para erro HTTP 400 - Bad Request")
public class ExemploRespostaErro400 {

    @ApiModelProperty(value = "Metadados da resposta HTTP contendo informações de erro")
    private Meta meta;

    @ApiModelProperty(value = "Conteúdo da resposta (null em caso de erro)")
    private Object content;

    @ApiModel(description = "Metadados da resposta HTTP para erro 400")
    public static class Meta {
        
        @ApiModelProperty(value = "Código ou tipo do erro ocorrido", 
                         example = "VALIDATION_ERROR")
        private String error;

        @ApiModelProperty(value = "Mensagem detalhada do erro", 
                         example = "Dados de entrada inválidos. Verifique os campos obrigatórios e tente novamente.")
        private String message;

        @ApiModelProperty(value = "Identificador da mensagem para internacionalização", 
                         example = "MSG_VALIDATION_001")
        private String messageID;

        @ApiModelProperty(value = "Valor da variável a ser concatenado com a mensagem", 
                         example = "Nome do aluno")
        private String messageValue;

        // Getters e Setters
        public String getError() {
            return error;
        }

        public void setError(String error) {
            this.error = error;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public String getMessageID() {
            return messageID;
        }

        public void setMessageID(String messageID) {
            this.messageID = messageID;
        }

        public String getMessageValue() {
            return messageValue;
        }

        public void setMessageValue(String messageValue) {
            this.messageValue = messageValue;
        }
    }

    // Getters e Setters
    public Meta getMeta() {
        return meta;
    }

    public void setMeta(Meta meta) {
        this.meta = meta;
    }

    public Object getContent() {
        return content;
    }

    public void setContent(Object content) {
        this.content = content;
    }
}
