package br.com.pacto.swagger.respostas.disponibilidade;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.controller.json.disponibilidade.ItemValidacaoDisponibilidadeDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para lista de itens de validação")
public class ExemploRespostaListItemValidacao extends PaginadorDTO {

    @ApiModelProperty(value = "Lista de itens de validação encontrados")
    private List<ItemValidacaoDisponibilidadeDTO> content;

    public List<ItemValidacaoDisponibilidadeDTO> getContent() {
        return content;
    }

    public void setContent(List<ItemValidacaoDisponibilidadeDTO> content) {
        this.content = content;
    }
}
