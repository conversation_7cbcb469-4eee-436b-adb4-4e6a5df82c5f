package br.com.pacto.swagger.respostas.configuracao;

import br.com.pacto.controller.json.base.ConfiguracoesAulasDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para consulta de configurações de aulas")
public class ExemploRespostaConfiguracoesAulasDTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as configurações de aulas do sistema")
    private ConfiguracoesAulasDTO content;

    public ConfiguracoesAulasDTO getContent() {
        return content;
    }

    public void setContent(ConfiguracoesAulasDTO content) {
        this.content = content;
    }
}
