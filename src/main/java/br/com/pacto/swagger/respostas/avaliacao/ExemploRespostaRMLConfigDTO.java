package br.com.pacto.swagger.respostas.avaliacao;

import br.com.pacto.service.impl.avaliacao.RMLConfigDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para consulta de tabela de valores RML")
public class ExemploRespostaRMLConfigDTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo a tabela de valores de referência para testes RML")
    private RMLConfigDTO content;

    public RMLConfigDTO getContent() {
        return content;
    }

    public void setContent(RMLConfigDTO content) {
        this.content = content;
    }
}
