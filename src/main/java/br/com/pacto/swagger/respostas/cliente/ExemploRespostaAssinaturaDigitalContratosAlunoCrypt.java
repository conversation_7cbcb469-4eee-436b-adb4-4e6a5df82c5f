package br.com.pacto.swagger.respostas.cliente;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para consulta criptografada de assinaturas digitais de contratos do aluno")
public class ExemploRespostaAssinaturaDigitalContratosAlunoCrypt {

    @ApiModelProperty(value = "Dados criptografados das assinaturas digitais dos contratos do aluno. Contém as mesmas informações do endpoint não criptografado (códigos dos contratos, datas de assinatura, status de validação e dados das assinaturas), porém em formato criptografado para maior segurança. Os dados são criptografados usando o método de criptografia padrão do sistema.",
                     example = "U2FsdGVkX1+vupppZksvRf5pq5g5XjFRIipRkwB0K1Y96Qsv2Lm+31cmzaAILwytX0dugpn1WE3wAN9AEh9jVnb+oq5gZNeCGjU=")
    private String RETURN;

    @ApiModelProperty(value = "Mensagem de erro criptografada caso ocorra algum problema na consulta das assinaturas digitais",
                     example = "U2FsdGVkX1+vupppZksvRf5pq5g5XjFRIipRkwB0K1Y96Qsv2Lm+31cmzaAILwyt")
    private String STATUS_ERRO;

    public String getRETURN() {
        return RETURN;
    }

    public void setRETURN(String RETURN) {
        this.RETURN = RETURN;
    }

    public String getSTATUS_ERRO() {
        return STATUS_ERRO;
    }

    public void setSTATUS_ERRO(String STATUS_ERRO) {
        this.STATUS_ERRO = STATUS_ERRO;
    }
}
