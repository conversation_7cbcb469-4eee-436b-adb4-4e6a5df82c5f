package br.com.pacto.swagger.respostas.configuracao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;
import java.util.Map;

@ApiModel(description = "Representação da resposta de status 200 para listagem de empresas da rede")
public class ExemploRespostaListaEmpresas {

    @ApiModelProperty(value = "Lista de empresas da rede com informações básicas")
    private List<Map<String, Object>> RETURN;

    public List<Map<String, Object>> getRETURN() {
        return RETURN;
    }

    public void setRETURN(List<Map<String, Object>> RETURN) {
        this.RETURN = RETURN;
    }
}
