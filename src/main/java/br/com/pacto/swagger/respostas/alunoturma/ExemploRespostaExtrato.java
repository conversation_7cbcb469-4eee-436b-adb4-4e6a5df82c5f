package br.com.pacto.swagger.respostas.alunoturma;

import br.com.pacto.util.json.ControleCreditoTreinoJSON;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * Classe de exemplo para resposta do endpoint extrato
 * Representa a estrutura ModelMap retornada com o extrato de créditos do aluno
 */
@ApiModel(description = "Resposta da consulta do extrato de créditos de treino do aluno")
public class ExemploRespostaExtrato {

    @ApiModelProperty(value = "Lista com o histórico de operações de créditos de treino do aluno")
    @JsonProperty("return")
    private List<ControleCreditoTreinoJSON> extrato;

    @ApiModelProperty(value = "Mensagem de erro caso a consulta não seja possível", 
                     example = "Não foi possível consultar o extrato. Aluno não encontrado.")
    private String erro;
}
