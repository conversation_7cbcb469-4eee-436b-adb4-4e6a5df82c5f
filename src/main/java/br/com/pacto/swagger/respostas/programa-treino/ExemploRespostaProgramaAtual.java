package br.com.pacto.swagger.respostas.programatreino;

import br.com.pacto.controller.json.acompanhamento.AcompanhamentoSimplesJSON;
import br.com.pacto.controller.json.programa.read.ProgramaTreinoJSON;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Classe de exemplo para resposta dos endpoints obterAtual e atualPorMatricula
 * Representa a estrutura ModelMap retornada com o programa de treino atual do aluno
 */
@ApiModel(description = "Resposta da consulta do programa de treino atual do aluno")
public class ExemploRespostaProgramaAtual {

    @ApiModelProperty(value = "Dados completos do programa de treino vigente do aluno, incluindo fichas, atividades e informações do professor responsável")
    private ProgramaTreinoJSON programa;

    @ApiModelProperty(value = "Informações de acompanhamento do programa, incluindo frequência de treinos realizados e percentual de assiduidade")
    private AcompanhamentoSimplesJSON acompanhamento;

    @ApiModelProperty(value = "Frequência semanal recomendada para o aluno (apenas em alguns casos específicos)", example = "3")
    private Integer frequenciaSemanal;

    @ApiModelProperty(value = "Mensagem de erro caso a consulta não seja possível", 
                     example = "Aluno não possui programa de treino ativo")
    private String statusErro;

    @ApiModelProperty(value = "Mensagem de erro caso a consulta não seja possível", 
                     example = "Programa de treino não encontrado")
    private String erro;

    @ApiModelProperty(value = "Status de informações sobre programa gerado por IA (quando aplicável)")
    private Object iaStatus;

    public ProgramaTreinoJSON getPrograma() {
        return programa;
    }

    public void setPrograma(ProgramaTreinoJSON programa) {
        this.programa = programa;
    }

    public AcompanhamentoSimplesJSON getAcompanhamento() {
        return acompanhamento;
    }

    public void setAcompanhamento(AcompanhamentoSimplesJSON acompanhamento) {
        this.acompanhamento = acompanhamento;
    }

    public Integer getFrequenciaSemanal() {
        return frequenciaSemanal;
    }

    public void setFrequenciaSemanal(Integer frequenciaSemanal) {
        this.frequenciaSemanal = frequenciaSemanal;
    }

    public String getStatusErro() {
        return statusErro;
    }

    public void setStatusErro(String statusErro) {
        this.statusErro = statusErro;
    }

    public String getErro() {
        return erro;
    }

    public void setErro(String erro) {
        this.erro = erro;
    }

    public Object getIaStatus() {
        return iaStatus;
    }

    public void setIaStatus(Object iaStatus) {
        this.iaStatus = iaStatus;
    }
}
