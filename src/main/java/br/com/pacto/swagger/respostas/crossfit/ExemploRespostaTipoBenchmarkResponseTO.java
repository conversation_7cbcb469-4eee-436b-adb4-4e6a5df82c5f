package br.com.pacto.swagger.respostas.crossfit;

import br.com.pacto.bean.benchmark.TipoBenchmarkResponseTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para operações com tipo de benchmark individual")
public class ExemploRespostaTipoBenchmarkResponseTO {

    @ApiModelProperty(value = "Dados do tipo de benchmark")
    private TipoBenchmarkResponseTO content;

    public TipoBenchmarkResponseTO getContent() {
        return content;
    }

    public void setContent(TipoBenchmarkResponseTO content) {
        this.content = content;
    }
}
