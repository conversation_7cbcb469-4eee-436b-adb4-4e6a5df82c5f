package br.com.pacto.swagger.respostas.locacao;

import br.com.pacto.controller.json.locacao.TipoHorarioLocacaoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * Classe de exemplo para resposta de lista de tipos de horário de locação
 */
@ApiModel(description = "Exemplo de uma resposta contendo uma lista de horários de locação")
public class ExemploRespostaListTipoHorarioLocacaoDTO {

    @ApiModelProperty(value = "Conteúdo de uma resposta contendo uma lista de tipos de horário de locação")
    List<TipoHorarioLocacaoDTO> content;

    public List<TipoHorarioLocacaoDTO> getContent() {
        return content;
    }

    public void setContent(List<TipoHorarioLocacaoDTO> content) {
        this.content = content;
    }
}
