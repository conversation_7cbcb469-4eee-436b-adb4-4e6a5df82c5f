package br.com.pacto.swagger.respostas.gympass;

import br.com.pacto.bean.gympass.LogGymPass;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Arrays;
import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para consulta de logs do GymPass")
public class ExemploRespostaListLogGymPass {

    @ApiModelProperty(value = "Lista de logs da integração GymPass")
    private List<LogGymPass> content;

    public List<LogGymPass> getContent() {
        return content;
    }

    public void setContent(List<LogGymPass> content) {
        this.content = content;
    }
}
