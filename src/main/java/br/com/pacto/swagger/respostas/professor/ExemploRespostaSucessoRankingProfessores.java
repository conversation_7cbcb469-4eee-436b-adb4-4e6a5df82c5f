package br.com.pacto.swagger.respostas.professor;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para operações de sucesso relacionadas ao ranking de professores encapsulada em EnvelopeRespostaDTO")
public class ExemploRespostaSucessoRankingProfessores {

    @ApiModelProperty(value = "Indica que a operação foi executada com sucesso", example = "true")
    private Boolean success;

    @ApiModelProperty(value = "Mensagem de confirmação da operação", example = "Operação realizada com sucesso")
    private String message;

    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
