package br.com.pacto.swagger.respostas.auladia;

import br.com.pacto.controller.json.aulaDia.AulaAlunoJSON;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * Classe de exemplo para resposta do endpoint consultarAlunosDeUmaAula
 * Representa a estrutura ModelMap retornada com lista de alunos de uma aula específica
 */
@ApiModel(description = "Resposta da consulta de alunos de uma aula específica")
public class ExemploRespostaConsultarAlunosDeUmaAula {

    @ApiModelProperty(value = "Lista de alunos matriculados na aula específica com informações detalhadas incluindo dados pessoais, situação do contrato, vínculo com a aula e dados da própria aula")
    private List<AulaAlunoJSON> alunos;

    public List<AulaAlunoJSON> getAlunos() {
        return alunos;
    }

    public void setAlunos(List<AulaAlunoJSON> alunos) {
        this.alunos = alunos;
    }
}
