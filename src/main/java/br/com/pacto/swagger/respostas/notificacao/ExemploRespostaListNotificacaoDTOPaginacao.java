package br.com.pacto.swagger.respostas.notificacao;

import br.com.pacto.dto.notificacao.NotificacaoDTO;
import br.com.pacto.swagger.respostas.ExemploPaginadorDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para consulta paginada de notificações do usuário")
public class ExemploRespostaListNotificacaoDTOPaginacao extends ExemploPaginadorDTO {

    @ApiModelProperty(value = "Lista de notificações do usuário")
    private List<NotificacaoDTO> content;

    public List<NotificacaoDTO> getContent() {
        return content;
    }

    public void setContent(List<NotificacaoDTO> content) {
        this.content = content;
    }
}
