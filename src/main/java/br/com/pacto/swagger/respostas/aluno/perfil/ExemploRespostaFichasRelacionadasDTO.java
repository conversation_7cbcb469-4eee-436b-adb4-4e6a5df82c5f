package br.com.pacto.swagger.respostas.aluno.perfil;

import br.com.pacto.service.impl.cliente.perfil.FichasRelacionadasDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para consulta das fichas relacionadas do aluno")
public class ExemploRespostaFichasRelacionadasDTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as informações das fichas relacionadas do aluno")
    private FichasRelacionadasDTO content;

    public FichasRelacionadasDTO getContent() {
        return content;
    }

    public void setContent(FichasRelacionadasDTO content) {
        this.content = content;
    }
}
