package br.com.pacto.swagger.respostas.aparelho;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para exclusão de aparelho encapsulada em EnvelopeRespostaDTO")
public class ExemploRespostaBoolean {

    @ApiModelProperty(value = "Indica se a operação foi realizada com sucesso", example = "true")
    private Boolean content;

    public Boolean getContent() {
        return content;
    }

    public void setContent(Boolean content) {
        this.content = content;
    }
}
