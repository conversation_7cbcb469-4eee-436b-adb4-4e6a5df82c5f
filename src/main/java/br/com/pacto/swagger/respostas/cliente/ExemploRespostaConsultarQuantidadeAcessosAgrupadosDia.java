package br.com.pacto.swagger.respostas.cliente;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para consulta de quantidade de acessos de clientes agrupados por dia da semana")
public class ExemploRespostaConsultarQuantidadeAcessosAgrupadosDia {

    @ApiModelProperty(value = "Dados de sucesso da consulta contendo a frequência de acessos por dia da semana")
    private FrequenciaAcessosSemana RETURN;

    @ApiModelProperty(value = "Mensagem de erro caso ocorra algum problema na consulta",
                     example = "Não foi possível obter a frequência do aluno.")
    private String STATUS_ERRO;

    public static class FrequenciaAcessosSemana {
        
        @ApiModelProperty(value = "Quantidade de acessos na segunda-feira", example = "3")
        private Integer SEG;
        
        @ApiModelProperty(value = "Quantidade de acessos na terça-feira", example = "2")
        private Integer TER;
        
        @ApiModelProperty(value = "Quantidade de acessos na quarta-feira", example = "4")
        private Integer QUA;
        
        @ApiModelProperty(value = "Quantidade de acessos na quinta-feira", example = "1")
        private Integer QUI;
        
        @ApiModelProperty(value = "Quantidade de acessos na sexta-feira", example = "3")
        private Integer SEX;
        
        @ApiModelProperty(value = "Quantidade de acessos no sábado", example = "2")
        private Integer SAB;
        
        @ApiModelProperty(value = "Quantidade de acessos no domingo", example = "1")
        private Integer DOM;

        public Integer getSEG() {
            return SEG;
        }

        public void setSEG(Integer SEG) {
            this.SEG = SEG;
        }

        public Integer getTER() {
            return TER;
        }

        public void setTER(Integer TER) {
            this.TER = TER;
        }

        public Integer getQUA() {
            return QUA;
        }

        public void setQUA(Integer QUA) {
            this.QUA = QUA;
        }

        public Integer getQUI() {
            return QUI;
        }

        public void setQUI(Integer QUI) {
            this.QUI = QUI;
        }

        public Integer getSEX() {
            return SEX;
        }

        public void setSEX(Integer SEX) {
            this.SEX = SEX;
        }

        public Integer getSAB() {
            return SAB;
        }

        public void setSAB(Integer SAB) {
            this.SAB = SAB;
        }

        public Integer getDOM() {
            return DOM;
        }

        public void setDOM(Integer DOM) {
            this.DOM = DOM;
        }
    }

    public FrequenciaAcessosSemana getRETURN() {
        return RETURN;
    }

    public void setRETURN(FrequenciaAcessosSemana RETURN) {
        this.RETURN = RETURN;
    }

    public String getSTATUS_ERRO() {
        return STATUS_ERRO;
    }

    public void setSTATUS_ERRO(String STATUS_ERRO) {
        this.STATUS_ERRO = STATUS_ERRO;
    }
}
