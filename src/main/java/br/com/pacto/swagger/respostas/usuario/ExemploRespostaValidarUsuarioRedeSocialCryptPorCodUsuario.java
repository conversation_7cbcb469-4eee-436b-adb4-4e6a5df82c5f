package br.com.pacto.swagger.respostas.usuario;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para validação de usuário de rede social por código com dados criptografados")
public class ExemploRespostaValidarUsuarioRedeSocialCryptPorCodUsuario {

    @ApiModelProperty(value = "Dados do usuário validado retornados de forma criptografada. " +
            "Contém todas as informações do usuário (dados pessoais, empresa, planos, créditos, etc.) " +
            "em formato JSON criptografado para segurança na transmissão. " +
            "Utiliza o código do usuário para identificação ao invés do e-mail.",
            example = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjb2QiOjEyMzQ1LCJjb2RVc3VhcmlvIjoxMjM0NSwiY29kVXN1YXJpb1pXIjo2Nzg5MCwibm9tZSI6Ik1hcmlhIE9saXZlaXJhIiwidXNlcm5hbWUiOiJtYXJpYS5vbGl2ZWlyYUBlbWFpbC5jb20iLCJpZGFkZSI6MjgsImVtYWlsIjoibWFyaWEub2xpdmVpcmFAZW1haWwuY29tIiwibWF0cmljdWxhIjoiMDAxMjM1Iiwibm9tZUVtcHJlc2EiOiJDcm9zc0ZpdCBFeHRyZW1lIiwic2FsZG9DcmVkaXRvcyI6MjAsInRvdGFsQ3JlZGl0b3MiOjQwLCJub21lUGxhbm8iOiJQbGFubyBUcmltZXN0cmFsIEF2YW7Dp2FkbyIsInN0YXR1c0FsdW5vIjoiQVRJVk8ifQ")
    private String RETURN;

    public String getRETURN() {
        return RETURN;
    }

    public void setRETURN(String RETURN) {
        this.RETURN = RETURN;
    }
}
