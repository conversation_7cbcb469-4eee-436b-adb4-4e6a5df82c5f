package br.com.pacto.swagger.respostas.tipoEvento;

import br.com.pacto.controller.json.tipoEvento.TipoAgendamentoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Exemplo de resposta para consulta de um tipo de agendamento específico")
public class ExemploRespostaTipoAgendamentoDTO {

    @ApiModelProperty(value = "Dados do tipo de agendamento")
    private TipoAgendamentoDTO content;

    public TipoAgendamentoDTO getContent() {
        return content;
    }

    public void setContent(TipoAgendamentoDTO content) {
        this.content = content;
    }
}
