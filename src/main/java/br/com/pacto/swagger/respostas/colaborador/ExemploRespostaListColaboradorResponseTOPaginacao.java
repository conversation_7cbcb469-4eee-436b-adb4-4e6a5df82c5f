package br.com.pacto.swagger.respostas.colaborador;

import br.com.pacto.bean.colaborador.ColaboradorResponseTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para consulta simplificada paginada de colaboradores encapsulada em EnvelopeRespostaDTO")
public class ExemploRespostaListColaboradorResponseTOPaginacao {

    @ApiModelProperty(value = "Lista paginada contendo dados simplificados dos colaboradores encontrados conforme os filtros especificados")
    private List<ColaboradorResponseTO> content;

    @ApiModelProperty(value = "Número total de elementos encontrados na consulta", example = "85")
    private Long totalElements;

    @ApiModelProperty(value = "Número total de páginas disponíveis", example = "5")
    private Long totalPages;

    @ApiModelProperty(value = "Indica se é a primeira página", example = "true")
    private Boolean first;

    @ApiModelProperty(value = "Indica se é a última página", example = "false")
    private Boolean last;

    @ApiModelProperty(value = "Número de elementos na página atual", example = "20")
    private Long numberOfElements;

    @ApiModelProperty(value = "Tamanho da página solicitada", example = "20")
    private Long size;

    @ApiModelProperty(value = "Número da página atual (baseado em zero)", example = "0")
    private Long number;

    public List<ColaboradorResponseTO> getContent() {
        return content;
    }

    public void setContent(List<ColaboradorResponseTO> content) {
        this.content = content;
    }

    public Long getTotalElements() {
        return totalElements;
    }

    public void setTotalElements(Long totalElements) {
        this.totalElements = totalElements;
    }

    public Long getTotalPages() {
        return totalPages;
    }

    public void setTotalPages(Long totalPages) {
        this.totalPages = totalPages;
    }

    public Boolean getFirst() {
        return first;
    }

    public void setFirst(Boolean first) {
        this.first = first;
    }

    public Boolean getLast() {
        return last;
    }

    public void setLast(Boolean last) {
        this.last = last;
    }

    public Long getNumberOfElements() {
        return numberOfElements;
    }

    public void setNumberOfElements(Long numberOfElements) {
        this.numberOfElements = numberOfElements;
    }

    public Long getSize() {
        return size;
    }

    public void setSize(Long size) {
        this.size = size;
    }

    public Long getNumber() {
        return number;
    }

    public void setNumber(Long number) {
        this.number = number;
    }
}
