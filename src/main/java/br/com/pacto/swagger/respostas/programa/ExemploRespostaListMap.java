package br.com.pacto.swagger.respostas.programa;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;
import java.util.Map;

@ApiModel(description = "Representação da resposta de status 200 para retorno de lista de mapas")
public class ExemploRespostaListMap {

    @ApiModelProperty(value = "Lista de mapas contendo dados dos grupos musculares trabalhados")
    private List<Map<String, Object>> content;

    public List<Map<String, Object>> getContent() {
        return content;
    }

    public void setContent(List<Map<String, Object>> content) {
        this.content = content;
    }
}
