package br.com.pacto.swagger.respostas.gympass;

import br.com.pacto.bean.gympass.ConfigGymPass;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para alteração de configuração GymPass")
public class ExemploRespostaConfigGymPassAlterado {

    @ApiModelProperty(value = "Conteúdo da resposta contendo os dados da configuração GymPass alterada")
    private ConfigGymPass content;

    public ConfigGymPass getContent() {
        return content;
    }

    public void setContent(ConfigGymPass content) {
        this.content = content;
    }
}
