package br.com.pacto.swagger.respostas.colaborador;

import br.com.pacto.dto.ColaboradorDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para consulta de colaboradores ZW (TreinoWeb) encapsulada em EnvelopeRespostaDTO")
public class ExemploRespostaListColaboradorZWDTO {

    @ApiModelProperty(value = "Lista contendo dados dos colaboradores do sistema ZW (TreinoWeb) com ID, nome e URI da imagem")
    private List<ColaboradorDTO> content;

    public List<ColaboradorDTO> getContent() {
        return content;
    }

    public void setContent(List<ColaboradorDTO> content) {
        this.content = content;
    }
}
