package br.com.pacto.swagger.respostas.aparelho;

import br.com.pacto.controller.json.programa.AparelhoResponseTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para listagem de aparelhos habilitados para reserva encapsulada em EnvelopeRespostaDTO")
public class ExemploRespostaListAparelhoResponseTO {

    @ApiModelProperty(value = "Lista contendo dados dos aparelhos habilitados para reserva de equipamento")
    private List<AparelhoResponseTO> content;

    public List<AparelhoResponseTO> getContent() {
        return content;
    }

    public void setContent(List<AparelhoResponseTO> content) {
        this.content = content;
    }
}
