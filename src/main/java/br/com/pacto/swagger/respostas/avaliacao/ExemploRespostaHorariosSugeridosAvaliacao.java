package br.com.pacto.swagger.respostas.avaliacao;

import br.com.pacto.controller.json.avaliacao.SugestaoHorarioJSON;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

public class ExemploRespostaHorariosSugeridosAvaliacao {

    @ApiModelProperty(value = "Lista de horários sugeridos para agendamento de avaliação física")
    private List<SugestaoHorarioJSON> RETURN;

    @ApiModelProperty(value = "Indica se a operação foi executada com sucesso", example = "true")
    private Boolean sucesso;

    public List<SugestaoHorarioJSON> getRETURN() {
        return RETURN;
    }

    public void setRETURN(List<SugestaoHorarioJSON> RETURN) {
        this.RETURN = RETURN;
    }

    public Boolean getSucesso() {
        return sucesso;
    }

    public void setSucesso(Boolean sucesso) {
        this.sucesso = sucesso;
    }
}
