package br.com.pacto.swagger.respostas.gympass;

import br.com.pacto.service.impl.gympass.dto.SlotsDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;
import java.util.Map;

@ApiModel(description = "Representação da resposta de status 200 para consulta de horários por empresa e dia")
public class ExemploRespostaMapHorarios {

    @ApiModelProperty(value = "Mapa de horários organizados por classe de aula. A chave é formada por: 'NomeClasse_slug_referencia(id:idClasse)' e o valor é uma lista de slots disponíveis para agendamento")
    private Map<String, List<SlotsDTO>> content;

    public Map<String, List<SlotsDTO>> getContent() {
        return content;
    }

    public void setContent(Map<String, List<SlotsDTO>> content) {
        this.content = content;
    }
}
