package br.com.pacto.swagger.respostas.musculo;

import br.com.pacto.bean.musculo.GrupoMuscularResumidoResponseTO;
import br.com.pacto.swagger.respostas.ExemploPaginadorDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Exemplo de resposta para lista paginada de grupos musculares resumidos")
public class ExemploRespostaListGrupoMuscularResumidoResponseTOPaginacao extends ExemploPaginadorDTO {

    @ApiModelProperty(value = "Lista de grupos musculares encontrados")
    private List<GrupoMuscularResumidoResponseTO> content;

    public List<GrupoMuscularResumidoResponseTO> getContent() {
        return content;
    }

    public void setContent(List<GrupoMuscularResumidoResponseTO> content) {
        this.content = content;
    }
}
