package br.com.pacto.swagger.respostas.configuracao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para sincronização de alunos MGB")
public class ExemploRespostaSincronizacaoMgbDTO {

    @ApiModelProperty(value = "Resultado da sincronização com informações do processo", example = "Sincronização realizada com sucesso. 150 alunos processados.")
    private String content;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
