package br.com.pacto.swagger.respostas.tipoEvento;

import br.com.pacto.controller.json.tipoEvento.TipoAgendamentoDTO;
import br.com.pacto.swagger.respostas.ExemploPaginadorDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Exemplo de resposta para consulta paginada de tipos de agendamento")
public class ExemploRespostaListTipoAgendamentoDTOPaginacao extends ExemploPaginadorDTO {

    @ApiModelProperty(value = "Lista de tipos de agendamento encontrados")
    private List<TipoAgendamentoDTO> content;

    public List<TipoAgendamentoDTO> getContent() {
        return content;
    }

    public void setContent(List<TipoAgendamentoDTO> content) {
        this.content = content;
    }
}
