package br.com.pacto.swagger.respostas.tvGestor;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.controller.json.tvGestor.dto.BiTvGestorDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Map;

@ApiModel(description = "Exemplo de resposta para dados de BI do período (últimos 30 dias) da TV Gestor encapsulada em EnvelopeRespostaDTO")
public class      ExemploRespostaBiPeriodo {


    @ApiModelProperty(value = "Dados de BI organizados por dia dos últimos 30 dias, contendo estatísticas de comparecimento e agendamentos. " +
            "A estrutura é um mapa onde a chave representa o dia (formato yyyyMMdd) e o valor contém dados de alunos esperados (agendados) " +
            "e que compareceram para aquele dia específico. Abrange um período de 30 dias retroativos a partir da data atual.",
            example = "{\"20240525\":{\"esperado\":45,\"compareceu\":38},\"20240526\":{\"esperado\":52,\"compareceu\":47},\"20240527\":{\"esperado\":48,\"compareceu\":41},\"20240528\":{\"esperado\":55,\"compareceu\":49},\"20240529\":{\"esperado\":62,\"compareceu\":56},\"20240530\":{\"esperado\":58,\"compareceu\":52},\"20240531\":{\"esperado\":41,\"compareceu\":35},\"20240601\":{\"esperado\":47,\"compareceu\":42},\"20240602\":{\"esperado\":53,\"compareceu\":48}}")
    private Map<String, BiTvGestorDTO> content;



    public Map<String, BiTvGestorDTO> getContent() {
        return content;
    }

    public void setContent(Map<String, BiTvGestorDTO> content) {
        this.content = content;
    }


}
