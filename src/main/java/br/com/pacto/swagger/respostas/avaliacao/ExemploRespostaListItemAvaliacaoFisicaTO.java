package br.com.pacto.swagger.respostas.avaliacao;

import br.com.pacto.service.impl.avaliacao.ItemAvaliacaoFisicaTO;
import br.com.pacto.swagger.respostas.ExemploPaginadorDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para consulta paginada de itens de avaliação física para Business Intelligence")
public class ExemploRespostaListItemAvaliacaoFisicaTO extends ExemploPaginadorDTO {

    @ApiModelProperty(value = "Lista paginada contendo os itens de avaliação física filtrados conforme os critérios de Business Intelligence especificados")
    private List<ItemAvaliacaoFisicaTO> content;

    public List<ItemAvaliacaoFisicaTO> getContent() {
        return content;
    }

    public void setContent(List<ItemAvaliacaoFisicaTO> content) {
        this.content = content;
    }
}
