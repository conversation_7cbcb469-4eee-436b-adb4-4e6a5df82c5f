package br.com.pacto.swagger.respostas.professor;

import br.com.pacto.controller.json.professor.DetalhesProfessorRankingDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para detalhes do professor no ranking encapsulada em EnvelopeRespostaDTO")
public class ExemploRespostaDetalhesProfessorRanking {

    @ApiModelProperty(value = "Detalhes completos do professor no ranking incluindo indicadores por categoria")
    private DetalhesProfessorRankingDTO content;

    public DetalhesProfessorRankingDTO getContent() {
        return content;
    }

    public void setContent(DetalhesProfessorRankingDTO content) {
        this.content = content;
    }
}
