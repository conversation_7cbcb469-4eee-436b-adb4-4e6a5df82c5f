package br.com.pacto.swagger.respostas.cliente;

import br.com.pacto.bean.cliente.AlunoCatalogoResponseTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para consulta do catálogo de alunos da academia")
public class ExemploRespostaListAlunoCatalogoResponseTO {

    @ApiModelProperty(value = "Lista contendo os alunos do catálogo conforme os filtros e tipo de consulta especificados")
    private List<AlunoCatalogoResponseTO> content;

    public List<AlunoCatalogoResponseTO> getContent() {
        return content;
    }

    public void setContent(List<AlunoCatalogoResponseTO> content) {
        this.content = content;
    }
}
