package br.com.pacto.swagger.respostas.nivel;

import br.com.pacto.bean.nivel.NivelResponseTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para listagem de todos os níveis encapsulada em EnvelopeRespostaDTO")
public class ExemploListarTodosNiveisResponseDTO {

    @ApiModelProperty(value = "Lista contendo todos os níveis cadastrados no sistema")
    private List<NivelResponseTO> content;

    public List<NivelResponseTO> getContent() {
        return content;
    }

    public void setContent(List<NivelResponseTO> content) {
        this.content = content;
    }
}
