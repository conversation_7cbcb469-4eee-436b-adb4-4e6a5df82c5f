package br.com.pacto.swagger.respostas.alunoturma;

import br.com.pacto.controller.json.agendamento.AgendamentoDisponibilidadePersonalDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;
import java.util.Map;

/**
 * Classe de exemplo para resposta do endpoint disponibilidades
 * Representa a estrutura EnvelopeRespostaDTO retornada com mapa de disponibilidades
 */
@ApiModel(description = "Resposta da consulta de disponibilidades de agendamento")
public class ExemploRespostaDisponibilidades {

    @ApiModelProperty(value = "Mapa de disponibilidades organizadas por data. " +
                             "A chave representa a data no formato 'dd/MM/yyyy' e o valor contém uma lista de " +
                             "disponibilidades de agendamento para aquela data específica. Cada disponibilidade " +
                             "inclui informações sobre ho<PERSON>, professor, tipo de serviço e status do agendamento.",
                      example = "{" +
                                "\"15/01/2024\": [" +
                                  "{" +
                                    "\"id\": 1001," +
                                    "\"dia\": \"15/01/2024\"," +
                                    "\"horarioInicial\": \"14:00\"," +
                                    "\"horarioFinal\": \"15:00\"," +
                                    "\"status\": \"AGUARDANDO_CONFIRMACAO\"," +
                                    "\"professor\": {" +
                                      "\"id\": 25," +
                                      "\"codigoColaborador\": 25," +
                                      "\"nome\": \"João Silva\"," +
                                      "\"imageUri\": \"www.pactosolucoes.com.br/imagens/joao_silva.png\"," +
                                      "\"cref\": \"012345-G/GO\"" +
                                    "}," +
                                    "\"tipoAgendamento\": {" +
                                      "\"id\": 4," +
                                      "\"nome\": \"Avaliação Física\"," +
                                      "\"comportamento\": \"Avaliação física\"," +
                                      "\"ativo\": true," +
                                      "\"cor\": \"#28a745\"" +
                                    "}," +
                                    "\"horarioDisponibilidadeCod\": 501" +
                                  "}," +
                                  "{" +
                                    "\"id\": 1002," +
                                    "\"dia\": \"15/01/2024\"," +
                                    "\"horarioInicial\": \"16:00\"," +
                                    "\"horarioFinal\": \"17:00\"," +
                                    "\"status\": \"CONFIRMADO\"," +
                                    "\"professor\": {" +
                                      "\"id\": 30," +
                                      "\"codigoColaborador\": 30," +
                                      "\"nome\": \"Ana Costa\"," +
                                      "\"imageUri\": \"www.pactosolucoes.com.br/imagens/ana_costa.png\"," +
                                      "\"cref\": \"067890-G/GO\"" +
                                    "}," +
                                    "\"tipoAgendamento\": {" +
                                      "\"id\": 1," +
                                      "\"nome\": \"Personal Training\"," +
                                      "\"comportamento\": \"Prescrição de treino\"," +
                                      "\"ativo\": true," +
                                      "\"cor\": \"#007bff\"" +
                                    "}," +
                                    "\"horarioDisponibilidadeCod\": 502" +
                                  "}" +
                                "]," +
                                "\"16/01/2024\": [" +
                                  "{" +
                                    "\"id\": 1003," +
                                    "\"dia\": \"16/01/2024\"," +
                                    "\"horarioInicial\": \"08:00\"," +
                                    "\"horarioFinal\": \"09:00\"," +
                                    "\"status\": \"AGUARDANDO_CONFIRMACAO\"," +
                                    "\"professor\": {" +
                                      "\"id\": 35," +
                                      "\"codigoColaborador\": 35," +
                                      "\"nome\": \"Carlos Lima\"," +
                                      "\"imageUri\": \"www.pactosolucoes.com.br/imagens/carlos_lima.png\"," +
                                      "\"cref\": \"054321-G/GO\"" +
                                    "}," +
                                    "\"tipoAgendamento\": {" +
                                      "\"id\": 2," +
                                      "\"nome\": \"Revisão de Treino\"," +
                                      "\"comportamento\": \"Revisão de treino\"," +
                                      "\"ativo\": true," +
                                      "\"cor\": \"#ffc107\"" +
                                    "}," +
                                    "\"horarioDisponibilidadeCod\": 503" +
                                  "}," +
                                  "{" +
                                    "\"id\": 1004," +
                                    "\"dia\": \"16/01/2024\"," +
                                    "\"horarioInicial\": \"19:00\"," +
                                    "\"horarioFinal\": \"20:00\"," +
                                    "\"status\": \"EXECUTADO\"," +
                                    "\"professor\": {" +
                                      "\"id\": 40," +
                                      "\"codigoColaborador\": 40," +
                                      "\"nome\": \"Mariana Santos\"," +
                                      "\"imageUri\": \"www.pactosolucoes.com.br/imagens/mariana_santos.png\"," +
                                      "\"cref\": \"098765-G/GO\"" +
                                    "}," +
                                    "\"tipoAgendamento\": {" +
                                      "\"id\": 3," +
                                      "\"nome\": \"Crossfit Personal\"," +
                                      "\"comportamento\": \"Contato interpessoal\"," +
                                      "\"ativo\": true," +
                                      "\"cor\": \"#dc3545\"" +
                                    "}," +
                                    "\"horarioDisponibilidadeCod\": 504" +
                                  "}" +
                                "]," +
                                "\"17/01/2024\": [" +
                                  "{" +
                                    "\"id\": 1005," +
                                    "\"dia\": \"17/01/2024\"," +
                                    "\"horarioInicial\": \"15:30\"," +
                                    "\"horarioFinal\": \"16:30\"," +
                                    "\"status\": \"CANCELADO\"," +
                                    "\"professor\": {" +
                                      "\"id\": 25," +
                                      "\"codigoColaborador\": 25," +
                                      "\"nome\": \"João Silva\"," +
                                      "\"imageUri\": \"www.pactosolucoes.com.br/imagens/joao_silva.png\"," +
                                      "\"cref\": \"012345-G/GO\"" +
                                    "}," +
                                    "\"tipoAgendamento\": {" +
                                      "\"id\": 5," +
                                      "\"nome\": \"Renovação de Treino\"," +
                                      "\"comportamento\": \"Renovar treino\"," +
                                      "\"ativo\": true," +
                                      "\"cor\": \"#6f42c1\"" +
                                    "}," +
                                    "\"horarioDisponibilidadeCod\": 505" +
                                  "}" +
                                "]" +
                              "}")
    private Object content;

    @ApiModelProperty(value = "Indica se a operação foi realizada com sucesso", example = "true")
    private Boolean sucesso;

    @ApiModelProperty(value = "Status da operação realizada", example = "OK")
    private String statusErro;

    public Object getContent() {
        return content;
    }

    public void setContent(Object content) {
        this.content = content;
    }

    public Boolean getSucesso() {
        return sucesso;
    }

    public void setSucesso(Boolean sucesso) {
        this.sucesso = sucesso;
    }

    public String getStatusErro() {
        return statusErro;
    }

    public void setStatusErro(String statusErro) {
        this.statusErro = statusErro;
    }
}
