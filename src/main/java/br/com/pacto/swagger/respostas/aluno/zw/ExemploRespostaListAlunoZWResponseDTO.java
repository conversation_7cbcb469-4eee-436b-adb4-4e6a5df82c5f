package br.com.pacto.swagger.respostas.aluno.zw;


import br.com.pacto.controller.json.aluno.AlunoZWResponseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200")
public class ExemploRespostaListAlunoZWResponseDTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as informações solicitadas")
    private List<AlunoZWResponseDTO> content;

    public List<AlunoZWResponseDTO> getContent() {
        return content;
    }

    public void setContent(List<AlunoZWResponseDTO> content) {
        this.content = content;
    }
}
