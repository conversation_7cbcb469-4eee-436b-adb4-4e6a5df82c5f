package br.com.pacto.swagger.respostas.objetivos;

import br.com.pacto.bean.programa.ObjetivoPredefinidoDTO;
import br.com.pacto.swagger.respostas.ExemploPaginadorDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para consulta paginada de objetivos predefinidos")
public class ExemploRespostaListObjetivoPredefinidoDTOPaginacao extends ExemploPaginadorDTO {

    @ApiModelProperty(value = "Lista de objetivos predefinidos com informações de paginação")
    private List<ObjetivoPredefinidoDTO> content;

    public List<ObjetivoPredefinidoDTO> getContent() {
        return content;
    }

    public void setContent(List<ObjetivoPredefinidoDTO> content) {
        this.content = content;
    }
}
