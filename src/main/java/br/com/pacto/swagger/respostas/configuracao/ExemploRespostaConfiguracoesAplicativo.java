package br.com.pacto.swagger.respostas.configuracao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para consulta de configurações do aplicativo")
public class ExemploRespostaConfiguracoesAplicativo {

    @ApiModelProperty(value = "String JSON contendo as configurações do aplicativo móvel", 
                     example = "{\"nomeAppParaEmail\":\"FitApp Academia\",\"appUrlEmail\":\"https://app.academia.com/email\"}")
    private String RETURN;

    public String getRETURN() {
        return RETURN;
    }

    public void setRETURN(String RETURN) {
        this.RETURN = RETURN;
    }
}
