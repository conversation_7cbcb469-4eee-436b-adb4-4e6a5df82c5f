package br.com.pacto.swagger.respostas.aula.edicao;


import br.com.pacto.bean.aula.EdicaoAulaTemporaria;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200")
public class ExemploRespostaEdicaoAulaTemporaria {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as informações solicitadas")
    private EdicaoAulaTemporaria content;

    public EdicaoAulaTemporaria getContent() {
        return content;
    }

    public void setContent(EdicaoAulaTemporaria content) {
        this.content = content;
    }
}
