package br.com.pacto.swagger.respostas.professor;

import br.com.pacto.bean.bi.IndicadorDashboardEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para lista de indicadores adicionados ao ranking encapsulada em EnvelopeRespostaDTO")
public class ExemploRespostaIndicadoresAdd {

    @ApiModelProperty(value = "Lista contendo os indicadores configurados e ativos para o ranking de professores")
    private List<IndicadorDashboardEnum> content;

    public List<IndicadorDashboardEnum> getContent() {
        return content;
    }

    public void setContent(List<IndicadorDashboardEnum> content) {
        this.content = content;
    }
}
