package br.com.pacto.swagger.respostas.avaliacao;

import br.com.pacto.bean.avaliacao.evolucao.EvolucaoFisicaDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para consulta de evolução física do aluno")
public class ExemploRespostaEvolucaoFisicaDTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo os dados consolidados de evolução física")
    private EvolucaoFisicaDTO content;

    public EvolucaoFisicaDTO getContent() {
        return content;
    }

    public void setContent(EvolucaoFisicaDTO content) {
        this.content = content;
    }
}
