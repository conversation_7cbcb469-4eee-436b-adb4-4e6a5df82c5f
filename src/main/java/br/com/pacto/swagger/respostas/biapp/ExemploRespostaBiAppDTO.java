package br.com.pacto.swagger.respostas.biapp;

import br.com.pacto.controller.json.biapp.BiAppDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para dashboard do BI App")
public class ExemploRespostaBiAppDTO {

    @ApiModelProperty(value = "Dados consolidados do dashboard de Business Intelligence do aplicativo")
    private BiAppDTO content;

    public BiAppDTO getContent() {
        return content;
    }

    public void setContent(BiAppDTO content) {
        this.content = content;
    }
}
