package br.com.pacto.swagger.respostas.programa;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para retorno de lista de inteiros")
public class ExemploRespostaListInteger {

    @ApiModelProperty(value = "Lista de valores inteiros retornados pela operação", example = "[123, 456, 789]")
    private List<Integer> content;

    public List<Integer> getContent() {
        return content;
    }

    public void setContent(List<Integer> content) {
        this.content = content;
    }
}
