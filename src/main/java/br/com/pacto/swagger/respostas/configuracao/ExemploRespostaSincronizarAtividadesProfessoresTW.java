package br.com.pacto.swagger.respostas.configuracao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para sincronização de atividades de professores com sistema TW")
public class ExemploRespostaSincronizarAtividadesProfessoresTW {

    @ApiModelProperty(value = "Resultado da sincronização de atividades de professores", example = "Sincronização de atividades de professores realizada com sucesso. 25 atividades sincronizadas para 8 professores.")
    private String content;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
