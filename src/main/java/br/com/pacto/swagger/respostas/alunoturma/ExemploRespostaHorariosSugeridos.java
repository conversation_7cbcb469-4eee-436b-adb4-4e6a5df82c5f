package br.com.pacto.swagger.respostas.alunoturma;

import br.com.pacto.controller.json.avaliacao.SugestaoHorarioPersonalJSON;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * Classe de exemplo para resposta do endpoint horariosSugeridos
 * Representa a estrutura ModelMap retornada com lista de horários sugeridos
 */
@ApiModel(description = "Resposta da consulta de horários sugeridos para agendamento")
public class ExemploRespostaHorariosSugeridos {

    @ApiModelProperty(value = "Lista de horários sugeridos para agendamento de serviços personalizados. " +
                             "Cada sugestão inclui informações sobre o tipo de evento, professor disponí<PERSON> e horário específico. " +
                             "Útil para apresentar opções de agendamento baseadas na disponibilidade da academia.")
    private List<SugestaoHorarioPersonalJSON> RETURN;

    @ApiModelProperty(value = "Mensagem de erro, presente apenas quando ocorre algum problema durante a consulta", 
                      example = "")
    private String statusErro;

    public List<SugestaoHorarioPersonalJSON> getRETURN() {
        return RETURN;
    }

    public void setRETURN(List<SugestaoHorarioPersonalJSON> RETURN) {
        this.RETURN = RETURN;
    }

    public String getStatusErro() {
        return statusErro;
    }

    public void setStatusErro(String statusErro) {
        this.statusErro = statusErro;
    }
}
