package br.com.pacto.swagger.respostas.programa;

import br.com.pacto.bean.cliente.PessoaPrescricaoDTO;
import br.com.pacto.swagger.respostas.ExemploPaginadorDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para consulta paginada de prescrições")
public class ExemploRespostaListPessoaPrescricaoDTOPaginacao extends ExemploPaginadorDTO {

    @ApiModelProperty(value = "Lista de prescrições encontradas")
    private List<PessoaPrescricaoDTO> content;

    public List<PessoaPrescricaoDTO> getContent() {
        return content;
    }

    public void setContent(List<PessoaPrescricaoDTO> content) {
        this.content = content;
    }
}
