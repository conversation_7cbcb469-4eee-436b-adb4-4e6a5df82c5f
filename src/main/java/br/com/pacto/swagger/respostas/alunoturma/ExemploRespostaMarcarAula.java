package br.com.pacto.swagger.respostas.alunoturma;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Classe de exemplo para resposta do endpoint marcarAula
 * Representa a estrutura ModelMap retornada após marcar uma aula
 */
@ApiModel(description = "Resposta da marcação de aula para um aluno")
public class ExemploRespostaMarcarAula {

    @ApiModelProperty(value = "Mensagem de sucesso da marcação da aula", 
                     example = "Aula marcada com sucesso! Você está agendado para Musculação no dia 15/02/2024 às 14:30.")
    private String sucesso;

    @ApiModelProperty(value = "Mensagem de erro caso a marcação não seja possível", 
                     example = "Não foi possível marcar a aula. Turma lotada.")
    private String erro;
}
