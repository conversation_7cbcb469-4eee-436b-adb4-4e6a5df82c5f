package br.com.pacto.swagger.respostas.cliente;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.controller.json.aluno.AvaliacaoProfessorVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para busca de avaliações de professor por cliente")
public class ExemploRespostaBuscaAvaliacaoPorCliente extends EnvelopeRespostaDTO {

    @ApiModelProperty(value = "Lista de avaliações de professores feitas pelo cliente específico")
    private List<AvaliacaoProfessorVO> content;

    public List<AvaliacaoProfessorVO> getContent() {
        return content;
    }

    public void setContent(List<AvaliacaoProfessorVO> content) {
        this.content = content;
    }
}
