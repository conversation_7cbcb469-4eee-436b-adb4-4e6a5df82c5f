package br.com.pacto.swagger.respostas.programatreino;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Classe de exemplo para resposta do endpoint atualizarNrTreinos
 * Representa a estrutura ModelMap retornada após atualização do número de treinos
 */
@ApiModel(description = "Resposta da atualização do número de treinos realizados")
public class ExemploRespostaAtualizarNrTreinos {

    @ApiModelProperty(value = "Dados atualizados do andamento do programa de treino do aluno, incluindo contadores recalculados", 
                     example = "Andamento atualizado com sucesso")
    private Object RETURN;

    @ApiModelProperty(value = "Mensagem de erro caso a atualização não seja possível", 
                     example = "Erro ao atualizar número de treinos")
    private String statusErro;

    public Object getRETURN() {
        return RETURN;
    }

    public void setRETURN(Object RETURN) {
        this.RETURN = RETURN;
    }

    public String getStatusErro() {
        return statusErro;
    }

    public void setStatusErro(String statusErro) {
        this.statusErro = statusErro;
    }
}
