package br.com.pacto.swagger.respostas.notificacao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para registro de resposta de notificação")
public class ExemploRespostaNotificacaoString {

    @ApiModelProperty(value = "Notificação em formato JSON com a resposta registrada", 
                      example = "{\"cod\":1001,\"tipo\":\"INICIOU_TREINO\",\"codProfessor\":25,\"data\":\"15/03/2024 14:30\",\"titulo\":\"Iniciou o treino\",\"texto\":\"João Silva iniciou seu treino de musculação\",\"codCliente\":1234,\"telAluno\":\"(62) 99999-8888\",\"gravidadeCor\":\"#70c061\",\"opcoes\":\"Confirmar|Cancelar\",\"resposta\":\"Confirmado\",\"lida\":false,\"pushEnviado\":true}")
    private String RETURN;

    public String getRETURN() {
        return RETURN;
    }

    public void setRETURN(String RETURN) {
        this.RETURN = RETURN;
    }
}
