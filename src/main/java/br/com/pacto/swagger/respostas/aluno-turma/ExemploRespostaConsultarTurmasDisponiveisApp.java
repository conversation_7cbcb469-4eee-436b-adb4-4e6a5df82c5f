package br.com.pacto.swagger.respostas.alunoturma;

import br.com.pacto.controller.json.aulaDia.AulaDiaJSON;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * Classe de exemplo para resposta do endpoint consultarTurmasDisponiveisApp
 * Representa a estrutura ModelMap retornada com lista de turmas disponíveis para o aluno
 */
@ApiModel(description = "Resposta da consulta de turmas disponíveis para agendamento no aplicativo")
public class ExemploRespostaConsultarTurmasDisponiveisApp {

    @ApiModelProperty(value = "Lista de aulas e turmas disponíveis para agendamento pelo aluno no período especificado, " +
                             "ordenadas por data. Inclui informações completas sobre modalidade, professor, hor<PERSON><PERSON><PERSON>, " +
                             "capacidade, vagas disponíveis e outras informações relevantes para o agendamento.")
    private List<AulaDiaJSON> aulas;

    public List<AulaDiaJSON> getAulas() {
        return aulas;
    }

    public void setAulas(List<AulaDiaJSON> aulas) {
        this.aulas = aulas;
    }
}
