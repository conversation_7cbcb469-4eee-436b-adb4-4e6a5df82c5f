package br.com.pacto.swagger.respostas.avaliacao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para link do WhatsApp com relatório individual de avaliação física")
public class ExemploRespostaLinkWhatsAppAvaliacaoFisica {

    @ApiModelProperty(value = "Link formatado para WhatsApp contendo o relatório individual de avaliação física. O link inclui o número de telefone informado como parâmetro e uma URL criptografada de redirecionamento para o relatório PDF da avaliação específica. Permite compartilhamento direto via WhatsApp Web com um clique, respeitando o idioma selecionado para o relatório.", 
                     example = "https://web.whatsapp.com/send?phone=5511999887766&text=https%3A//discovery.pacto.com.br/redir/ctx123/treino%3Fm%3Dimprimiravaliacao%26j%3DeyJrIjoiY3R4MTIzIiwiYSI6Nzg5LCJpIjoiMCJ9")
    private String content;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
