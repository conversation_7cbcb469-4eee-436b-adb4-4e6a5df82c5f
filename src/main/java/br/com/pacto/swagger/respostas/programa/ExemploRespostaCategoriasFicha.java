package br.com.pacto.swagger.respostas.programa;

import br.com.pacto.bean.ficha.CategoriaFichaResponseTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para categorias de fichas")
public class ExemploRespostaCategoriasFicha {

    @ApiModelProperty(value = "Lista das categorias de fichas disponíveis")
    private List<CategoriaFichaResponseTO> content;

    public List<CategoriaFichaResponseTO> getContent() {
        return content;
    }

    public void setContent(List<CategoriaFichaResponseTO> content) {
        this.content = content;
    }
}
