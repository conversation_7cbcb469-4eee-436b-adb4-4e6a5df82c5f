package br.com.pacto.swagger.respostas.avaliacao;


import br.com.pacto.bean.cliente.AvaliacaoCatalogoAlunoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200")
public class ExemploRespostaListAvaliacaoCatalogoAlunoDTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as informações solicitadas")
    private List<AvaliacaoCatalogoAlunoDTO> content;

    public List<AvaliacaoCatalogoAlunoDTO> getContent() {
        return content;
    }

    public void setContent(List<AvaliacaoCatalogoAlunoDTO> content) {
        this.content = content;
    }
}
