package br.com.pacto.swagger.respostas.avaliacaoProgresso;


import br.com.pacto.bean.atividade.CategoriaAtividadeResponseTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200")
public class ExemploRespostaPDFAvalicaoProgressoAluno {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as informações solicitadas",example = "https://arquivos.empresa.com/avaliacoes/resultadoAvaliacaoProgresso_105.pdf")
    private String content;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
