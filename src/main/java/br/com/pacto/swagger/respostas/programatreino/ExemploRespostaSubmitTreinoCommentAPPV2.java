package br.com.pacto.swagger.respostas.programatreino;

import br.com.pacto.controller.json.acompanhamento.AcompanhamentoSimplesJSON;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Classe de exemplo para resposta do endpoint submittreinoCommentAPPV2
 * Representa a estrutura ModelMap retornada após submissão de comentário de treino via aplicativo (versão 2)
 */
@ApiModel(description = "Resposta da submissão de comentário de treino via aplicativo (versão 2 com validações aprimoradas)")
public class ExemploRespostaSubmitTreinoCommentAPPV2 {

    @ApiModelProperty(value = "Dados de acompanhamento atualizados após a submissão do comentário de treino, contendo informações sobre frequência e assiduidade do aluno")
    private AcompanhamentoSimplesJSON RETURN;

    @ApiModelProperty(value = "Mensagem de erro caso ocorra algum problema durante a submissão do comentário. " +
            "Este campo só estará presente em caso de falha na operação e conterá detalhes sobre o erro ocorrido.", 
            example = "Data de execução do treino não pode ser menor que a data de início do programa")
    private String statusErro;

    // Getters e Setters
    public AcompanhamentoSimplesJSON getRETURN() {
        return RETURN;
    }

    public void setRETURN(AcompanhamentoSimplesJSON RETURN) {
        this.RETURN = RETURN;
    }

    public String getStatusErro() {
        return statusErro;
    }

    public void setStatusErro(String statusErro) {
        this.statusErro = statusErro;
    }
}
