package br.com.pacto.swagger.respostas.erros;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Classe de exemplo para resposta de erro HTTP 404 - Not Found (ModelMap)
 * Utilizada para documentação automática do Swagger em endpoints que retornam ModelMap
 */
@ApiModel(description = "Exemplo de resposta para erro HTTP 404 - Not Found (formato ModelMap)")
public class ExemploRespostaErroModelMap404 {

    @ApiModelProperty(value = "Mensagem de erro detalhada", 
                     example = "Registro não encontrado. Verifique se o ID informado está correto e tente novamente.")
    private String STATUS_ERRO;

    // Getter e Setter
    public String getSTATUS_ERRO() {
        return STATUS_ERRO;
    }

    public void setSTATUS_ERRO(String STATUS_ERRO) {
        this.STATUS_ERRO = STATUS_ERRO;
    }
}
