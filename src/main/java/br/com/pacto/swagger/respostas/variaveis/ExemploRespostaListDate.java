package br.com.pacto.swagger.respostas.variaveis;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import java.util.List;

@ApiModel(description = "Representação da resposta de status 200")
public class ExemploRespostaListDate {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as informações solicitadas", example = "[\"Wed Jun 04 00:00:00 BRT 2025\"]")
    private List<Date> content;

    public List<Date> getContent() {
        return content;
    }

    public void setContent(List<Date> content) {
        this.content = content;
    }
}
