package br.com.pacto.swagger.respostas.programatreino;

import br.com.pacto.controller.json.acompanhamento.AcompanhamentoSimplesJSON;
import br.com.pacto.controller.json.programa.read.ProgramaVersaoJSON;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Classe de exemplo para resposta do endpoint obterVersaoProgramaAtual
 * Representa a estrutura ModelMap retornada com informações de versão do programa de treino
 */
@ApiModel(description = "Resposta da consulta da versão do programa de treino atual")
public class ExemploRespostaVersaoProgramaAtual {

    @ApiModelProperty(value = "Informações de versão do programa de treino, incluindo código do programa e versões das fichas")
    private ProgramaVersaoJSON programa;

    @ApiModelProperty(value = "Informações de acompanhamento do programa, incluindo frequência de treinos realizados e percentual de assiduidade")
    private AcompanhamentoSimplesJSON acompanhamento;

    @ApiModelProperty(value = "Mensagem de erro caso a consulta não seja possível", 
                     example = "Programa de treino não encontrado")
    private String statusErro;

    public ProgramaVersaoJSON getPrograma() {
        return programa;
    }

    public void setPrograma(ProgramaVersaoJSON programa) {
        this.programa = programa;
    }

    public AcompanhamentoSimplesJSON getAcompanhamento() {
        return acompanhamento;
    }

    public void setAcompanhamento(AcompanhamentoSimplesJSON acompanhamento) {
        this.acompanhamento = acompanhamento;
    }

    public String getStatusErro() {
        return statusErro;
    }

    public void setStatusErro(String statusErro) {
        this.statusErro = statusErro;
    }
}
