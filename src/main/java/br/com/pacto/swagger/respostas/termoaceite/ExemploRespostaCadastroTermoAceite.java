package br.com.pacto.swagger.respostas.termoaceite;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para cadastro de novo termo de aceite")
public class ExemploRespostaCadastroTermoAceite {

    @ApiModelProperty(value = "Mensagem de confirmação do cadastro do termo de aceite",
            example = "Salvo com sucesso!")
    private String content;

    // Getters e Setters

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
