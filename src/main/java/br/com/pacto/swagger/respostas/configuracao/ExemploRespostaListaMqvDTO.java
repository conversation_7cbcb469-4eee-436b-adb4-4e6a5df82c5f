package br.com.pacto.swagger.respostas.configuracao;

import br.com.pacto.controller.json.mqv.MqvDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para consulta de integrações MQV")
public class ExemploRespostaListaMqvDTO {

    @ApiModelProperty(value = "Lista de configurações de integração MQV")
    private List<MqvDTO> content;

    public List<MqvDTO> getContent() {
        return content;
    }

    public void setContent(List<MqvDTO> content) {
        this.content = content;
    }
}
