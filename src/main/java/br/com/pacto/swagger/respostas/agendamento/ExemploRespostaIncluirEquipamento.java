package br.com.pacto.swagger.respostas.agendamento;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Resposta de sucesso para inclusão de equipamento no horário do aluno")
public class ExemploRespostaIncluirEquipamento {

    @ApiModelProperty(value = "Mensagem de sucesso da inclusão do equipamento", example = "Equipamento incluído com sucesso")
    private String status;

    @ApiModelProperty(value = "Mensagem de erro, presente apenas quando ocorre algum problema", example = "")
    private String erro;
}
