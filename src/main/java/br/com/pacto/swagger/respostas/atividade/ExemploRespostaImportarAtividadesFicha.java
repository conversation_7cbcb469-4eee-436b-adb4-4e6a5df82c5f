package br.com.pacto.swagger.respostas.atividade;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Exemplo de resposta para importação de atividades de ficha com séries")
public class ExemploRespostaImportarAtividadesFicha {

    @ApiModelProperty(value = "Resultado da importação com detalhes do processo", 
                     example = "Importação de atividades de ficha concluída. 8 fichas processadas, 32 atividades com séries importadas.")
    private String statusSucesso;

    @ApiModelProperty(value = "Indica sucesso da operação", example = "true")
    private Boolean sucesso;

    @ApiModelProperty(value = "Mensagem de erro (quando aplicável)", example = "null")
    private String statusErro;
}
