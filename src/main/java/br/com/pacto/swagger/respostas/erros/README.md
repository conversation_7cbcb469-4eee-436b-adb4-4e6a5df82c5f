# Configuração Global de Respostas de Erro - Swagger

## Visão Geral

Este diretório contém as classes de exemplo para respostas de erro HTTP que são aplicadas **automaticamente** a todos os endpoints documentados no projeto através da configuração global do Spring Fox 2.0.

## Funcionalidade

A configuração no `SwaggerConfig.java` utiliza o método `globalResponseMessage()` para aplicar automaticamente as seguintes respostas de erro a **TODOS** os endpoints:

### Códigos de Erro Configurados

| Código HTTP | Classe de Exemplo | Descrição |
|-------------|-------------------|-----------|
| 400 | `ExemploRespostaErro400` | Bad Request - Dados de entrada inválidos |
| 401 | `ExemploRespostaErro401` | Unauthorized - Credenciais inválidas ou ausentes |
| 403 | `ExemploRespostaErro403` | Forbidden - Sem permissão para acessar o recurso |
| 404 | `ExemploRespostaErro404` | Not Found - Registro não encontrado |
| 409 | `ExemploRespostaErro409` | Conflict - Conflito de dados ou registro duplicado |
| 500 | `ExemploRespostaErro500` | Internal Server Error - Erro interno do servidor |

### Classes para Endpoints ModelMap

Para endpoints que retornam `ModelMap` com atributo `STATUS_ERRO`:

| Código HTTP | Classe de Exemplo | Descrição |
|-------------|-------------------|-----------|
| 400 | `ExemploRespostaErroModelMap400` | Bad Request (formato ModelMap) |
| 404 | `ExemploRespostaErroModelMap404` | Not Found (formato ModelMap) |
| 500 | `ExemploRespostaErroModelMap500` | Internal Server Error (formato ModelMap) |

## Como Funciona

### 1. Configuração Automática

No `SwaggerConfig.java`, as respostas de erro são configuradas globalmente:

```java
.globalResponseMessage(RequestMethod.GET, getGlobalResponseMessages())
.globalResponseMessage(RequestMethod.POST, getGlobalResponseMessages())
.globalResponseMessage(RequestMethod.PUT, getGlobalResponseMessages())
.globalResponseMessage(RequestMethod.DELETE, getGlobalResponseMessages())
.additionalModels(
    typeResolver.resolve(ExemploRespostaErro400.class),
    typeResolver.resolve(ExemploRespostaErro401.class),
    typeResolver.resolve(ExemploRespostaErro403.class),
    typeResolver.resolve(ExemploRespostaErro404.class),
    typeResolver.resolve(ExemploRespostaErro409.class),
    typeResolver.resolve(ExemploRespostaErro500.class),
    typeResolver.resolve(ExemploRespostaErroModelMap400.class),
    typeResolver.resolve(ExemploRespostaErroModelMap404.class),
    typeResolver.resolve(ExemploRespostaErroModelMap500.class)
)
```

### 2. Aplicação Automática

**TODOS** os endpoints documentados com `@ApiOperation` automaticamente exibirão na documentação Swagger:

- As respostas de sucesso específicas do endpoint (definidas com `@ApiResponse`)
- **MAIS** as respostas de erro globais configuradas

### 3. Exemplo Prático

Um endpoint como este:

```java
@ApiOperation(value = "Realizar login", tags = "Autenticação e Login")
@ApiResponses({
    @ApiResponse(code = 200, message = "Login realizado com sucesso", response = ExemploRespostaStringLogin.class)
})
@RequestMapping(method = RequestMethod.POST)
public ResponseEntity<EnvelopeRespostaDTO> login(@RequestBody UsuarioLoginDTO usuarioDTO) {
    // implementação
}
```

Automaticamente exibirá na documentação Swagger:
- **200**: Login realizado com sucesso (definido manualmente)
- **400**: Bad Request - Dados de entrada inválidos (aplicado automaticamente)
- **401**: Unauthorized - Credenciais inválidas (aplicado automaticamente)
- **403**: Forbidden - Sem permissão (aplicado automaticamente)
- **404**: Not Found - Registro não encontrado (aplicado automaticamente)
- **409**: Conflict - Conflito de dados (aplicado automaticamente)
- **500**: Internal Server Error - Erro interno (aplicado automaticamente)

## Vantagens

1. **Consistência**: Todos os endpoints têm a mesma documentação de erro
2. **Manutenibilidade**: Alterações nas respostas de erro são feitas em um local único
3. **Produtividade**: Não é necessário documentar erros individualmente em cada endpoint
4. **Padronização**: Exemplos contextuais para o domínio de academia/crossfit

## Estrutura das Classes de Exemplo

### Para EnvelopeRespostaDTO (ResponseEntity)

```java
public class ExemploRespostaErro400 {
    private Meta meta;           // Metadados do erro
    private Object content;      // Conteúdo (null em caso de erro)
    
    public static class Meta {
        private String error;        // Código do erro
        private String message;      // Mensagem detalhada
        private String messageID;    // ID para internacionalização
        private String messageValue; // Valor da variável
    }
}
```

### Para ModelMap

```java
public class ExemploRespostaErroModelMap400 {
    private String STATUS_ERRO;  // Mensagem de erro
}
```

## Manutenção

Para adicionar novos códigos de erro ou modificar os existentes:

1. Crie/edite a classe de exemplo correspondente neste diretório
2. Adicione/modifique a configuração no método `getGlobalResponseMessages()` do `SwaggerConfig.java`
3. **IMPORTANTE**: Registre a nova classe no método `additionalModels()` usando `typeResolver.resolve(NovaClasse.class)`
4. A alteração será aplicada automaticamente a todos os endpoints

### Requisitos Técnicos

- **TypeResolver**: As classes de exemplo devem ser registradas usando `@Autowired TypeResolver` e `typeResolver.resolve()`
- **additionalModels()**: Essencial para que o Spring Fox 2.0 reconheça as classes customizadas como modelos válidos
- **ModelRef**: Usa o nome simples da classe (`getSimpleName()`) para referenciar os modelos registrados

## Observações Importantes

- As respostas de erro globais **NÃO** substituem as respostas de sucesso específicas de cada endpoint
- As respostas são **adicionadas** às respostas já documentadas individualmente
- Os exemplos utilizam contexto realista do domínio de academia/crossfit
- A configuração segue os padrões estabelecidos no projeto para `EnvelopeRespostaDTO` e `ModelMap`
