package br.com.pacto.swagger.respostas.fila;


import br.com.pacto.controller.json.aulaDia.FilaDeEsperaDTO;
import br.com.pacto.swagger.respostas.ExemploPaginadorDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200")
public class ExemploRespostaListFilaDeEsperaDTOPaginacao extends ExemploPaginadorDTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as informações solicitadas")
    private List<FilaDeEsperaDTO> content;

    public List<FilaDeEsperaDTO> getContent() {
        return content;
    }

    public void setContent(List<FilaDeEsperaDTO> content) {
        this.content = content;
    }
}
