package br.com.pacto.swagger.respostas.cliente.observacao;


import br.com.pacto.controller.json.aluno.ClienteObservacaoAnexosDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200")
public class ExemploRespostaClienteObservacaoAnexosDTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as informações solicitadas")
    private ClienteObservacaoAnexosDTO content;

    public ClienteObservacaoAnexosDTO getContent() {
        return content;
    }

    public void setContent(ClienteObservacaoAnexosDTO content) {
        this.content = content;
    }
}
