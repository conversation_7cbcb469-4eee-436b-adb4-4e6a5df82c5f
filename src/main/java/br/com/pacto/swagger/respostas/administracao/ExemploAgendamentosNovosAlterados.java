package br.com.pacto.swagger.respostas.administracao;

import java.util.ArrayList;
import java.util.List;

/**
 * Classe de exemplo para resposta do endpoint de agendamentos novos ou alterados
 */
public class ExemploAgendamentosNovosAlterados {
    
    private List<AgendamentoExemplo> agendamentos;
    
    public ExemploAgendamentosNovosAlterados() {
        this.agendamentos = new ArrayList<>();
        
        AgendamentoExemplo agendamento1 = new AgendamentoExemplo();
        agendamento1.setId(2001);
        agendamento1.setData("16/12/2024 09:00:00");
        agendamento1.setHora("09:00");
        agendamento1.setHoraFinal("10:00");
        agendamento1.setNome("Prescrição de Treino com Prof. Carlos Lima");
        agendamento1.setStatus("Novo");
        agendamento1.setNomeProfessor("Carlos Lima");
        agendamento1.setStatusCor("#ffc107");
        agendamento1.setDataLong(1734340800000L);
        
        AgendamentoExemplo agendamento2 = new AgendamentoExemplo();
        agendamento2.setId(2002);
        agendamento2.setData("16/12/2024 15:30:00");
        agendamento2.setHora("15:30");
        agendamento2.setHoraFinal("16:30");
        agendamento2.setNome("Revisão de Treino com Prof. Ana Costa");
        agendamento2.setStatus("Alterado");
        agendamento2.setNomeProfessor("Ana Costa");
        agendamento2.setStatusCor("#fd7e14");
        agendamento2.setDataLong(1734364200000L);
        
        this.agendamentos.add(agendamento1);
        this.agendamentos.add(agendamento2);
    }
    
    public List<AgendamentoExemplo> getAgendamentos() {
        return agendamentos;
    }
    
    public void setAgendamentos(List<AgendamentoExemplo> agendamentos) {
        this.agendamentos = agendamentos;
    }
    
    public static class AgendamentoExemplo {
        private Integer id;
        private String data;
        private String hora;
        private String horaFinal;
        private String nome;
        private String status;
        private String nomeProfessor;
        private String statusCor;
        private Long dataLong;
        
        // Getters e Setters
        public Integer getId() { return id; }
        public void setId(Integer id) { this.id = id; }
        
        public String getData() { return data; }
        public void setData(String data) { this.data = data; }
        
        public String getHora() { return hora; }
        public void setHora(String hora) { this.hora = hora; }
        
        public String getHoraFinal() { return horaFinal; }
        public void setHoraFinal(String horaFinal) { this.horaFinal = horaFinal; }
        
        public String getNome() { return nome; }
        public void setNome(String nome) { this.nome = nome; }
        
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        
        public String getNomeProfessor() { return nomeProfessor; }
        public void setNomeProfessor(String nomeProfessor) { this.nomeProfessor = nomeProfessor; }
        
        public String getStatusCor() { return statusCor; }
        public void setStatusCor(String statusCor) { this.statusCor = statusCor; }
        
        public Long getDataLong() { return dataLong; }
        public void setDataLong(Long dataLong) { this.dataLong = dataLong; }
    }
}
