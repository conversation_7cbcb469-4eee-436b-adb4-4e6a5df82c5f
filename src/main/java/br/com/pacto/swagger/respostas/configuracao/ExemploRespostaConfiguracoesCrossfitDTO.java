package br.com.pacto.swagger.respostas.configuracao;

import br.com.pacto.controller.json.base.ConfiguracoesCrossfitDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para consulta de configurações de CrossFit")
public class ExemploRespostaConfiguracoesCrossfitDTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as configurações de CrossFit do sistema")
    private ConfiguracoesCrossfitDTO content;

    public ConfiguracoesCrossfitDTO getContent() {
        return content;
    }

    public void setContent(ConfiguracoesCrossfitDTO content) {
        this.content = content;
    }
}
