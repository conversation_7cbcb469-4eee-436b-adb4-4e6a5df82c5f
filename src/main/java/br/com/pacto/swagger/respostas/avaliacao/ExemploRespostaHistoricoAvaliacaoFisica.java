package br.com.pacto.swagger.respostas.avaliacao;

import br.com.pacto.controller.json.avaliacao.AvaliacaoFisicaJSON;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Resposta do histórico de avaliações físicas do aluno")
public class ExemploRespostaHistoricoAvaliacaoFisica {

    @ApiModelProperty(value = "URL criptografada para acesso à evolução automática das avaliações físicas do aluno", 
                     example = "/evolucaoauto/avtwABC123XYZ789DEF456GHI012JKL345MNO678PQR901STU234VWX567YZ")
    private String url;

    @ApiModelProperty(value = "Lista com o histórico completo de avaliações físicas do aluno, ordenadas da mais recente para a mais antiga")
    private List<AvaliacaoFisicaJSON> RETURN;

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public List<AvaliacaoFisicaJSON> getRETURN() {
        return RETURN;
    }

    public void setRETURN(List<AvaliacaoFisicaJSON> RETURN) {
        this.RETURN = RETURN;
    }
}
