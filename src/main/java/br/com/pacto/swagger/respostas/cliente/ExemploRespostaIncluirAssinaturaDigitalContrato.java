package br.com.pacto.swagger.respostas.cliente;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para inclusão de assinatura digital no contrato do aluno")
public class ExemploRespostaIncluirAssinaturaDigitalContrato {

    @ApiModelProperty(value = "Resultado da operação de inclusão da assinatura digital. Retorna uma mensagem de confirmação quando a assinatura é incluída com sucesso no contrato, ou informações sobre o status da operação. Pode retornar dados em formato JSON com detalhes da assinatura registrada.",
                     example = "{\"status\":\"sucesso\",\"mensagem\":\"Assinatura digital incluída com sucesso no contrato CONT2024001\",\"codigoContrato\":\"CONT2024001\",\"dataInclusao\":\"2024-06-24T15:30:00Z\",\"hashAssinatura\":\"a1b2c3d4e5f6789012345678901234567890abcdef\",\"validacao\":\"PENDENTE\"}")
    private String RETURN;

    @ApiModelProperty(value = "Mensagem de erro caso ocorra algum problema na inclusão da assinatura digital no contrato",
                     example = "Ocorreu um erro ao incluir assinatura no contrato: CONT123")
    private String STATUS_ERRO;

    public String getRETURN() {
        return RETURN;
    }

    public void setRETURN(String RETURN) {
        this.RETURN = RETURN;
    }

    public String getSTATUS_ERRO() {
        return STATUS_ERRO;
    }

    public void setSTATUS_ERRO(String STATUS_ERRO) {
        this.STATUS_ERRO = STATUS_ERRO;
    }
}
