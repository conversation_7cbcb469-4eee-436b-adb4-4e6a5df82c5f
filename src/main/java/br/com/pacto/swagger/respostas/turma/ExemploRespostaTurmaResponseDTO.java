package br.com.pacto.swagger.respostas.turma;


import br.com.pacto.controller.json.agendamento.AdicionarAlunoTurmaResponseDTO;
import br.com.pacto.controller.json.agendamento.TurmaResponseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200")
public class ExemploRespostaTurmaResponseDTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as informações solicitadas")
    private TurmaResponseDTO content;

    public TurmaResponseDTO getContent() {
        return content;
    }

    public void setContent(TurmaResponseDTO content) {
        this.content = content;
    }
}
