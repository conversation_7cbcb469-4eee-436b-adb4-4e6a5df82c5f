package br.com.pacto.swagger.respostas.aluno.perfil;

import br.com.pacto.service.impl.cliente.perfil.DiasQueTreinouProgramaAtualDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para consulta dos dias que o aluno treinou no programa atual")
public class ExemploRespostaDiasQueTreinouProgramaAtualDTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as estatísticas dos dias que o aluno treinou no programa atual")
    private DiasQueTreinouProgramaAtualDTO content;

    public DiasQueTreinouProgramaAtualDTO getContent() {
        return content;
    }

    public void setContent(DiasQueTreinouProgramaAtualDTO content) {
        this.content = content;
    }
}
