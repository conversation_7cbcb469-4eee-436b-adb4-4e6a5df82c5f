package br.com.pacto.swagger.respostas.perfil;

import br.com.pacto.bean.perfil.PerfilDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para operações detalhadas com perfil de acesso")
public class ExemploRespostaPerfilDTO {

    @ApiModelProperty(value = "Dados detalhados do perfil de acesso incluindo recursos e funcionalidades")
    private PerfilDTO content;

    public PerfilDTO getContent() {
        return content;
    }

    public void setContent(PerfilDTO content) {
        this.content = content;
    }
}
