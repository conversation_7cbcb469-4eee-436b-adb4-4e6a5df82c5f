package br.com.pacto.swagger.respostas.avaliacao;

import br.com.pacto.swagger.respostas.ExemploPaginadorDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para listagem paginada de questionários PAR-Q")
public class ExemploRespostaListParQDTO extends ExemploPaginadorDTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo os dados dos questionários PAR-Q do aluno")
    private ExemploConteudoParQDTO content;

    // Getters e Setters
    public ExemploConteudoParQDTO getContent() {
        return content;
    }

    public void setContent(ExemploConteudoParQDTO content) {
        this.content = content;
    }

}
