package br.com.pacto.swagger.respostas.avaliacao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para exportação de lista de avaliações físicas BI")
public class ExemploRespostaExportacaoAvaliacoesBI {

    @ApiModelProperty(value = "URL completa do arquivo exportado contendo a lista de avaliações físicas filtradas conforme os critérios de Business Intelligence especificados. O arquivo pode estar nos formatos Excel, PDF ou CSV e contém dados como código do cliente, nome do aluno, data da avaliação, avaliador, professor, observações e matrícula.",
                     example = "https://treino.pacto.com.br/exports/avaliacoes-fisicas/BI_AvaliacoesFisicas_20240620_143052.xlsx")
    private String content;

    // Getters e Setters

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

}
