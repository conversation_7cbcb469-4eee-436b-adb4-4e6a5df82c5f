package br.com.pacto.swagger.respostas.aluno.perfil;

import br.com.pacto.service.impl.cliente.perfil.HorariosQueTreinouProgramaAtual;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para consulta dos horários que o aluno treinou no programa atual")
public class ExemploRespostaHorariosQueTreinouProgramaAtual {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as estatísticas dos horários que o aluno treinou no programa atual")
    private HorariosQueTreinouProgramaAtual content;

    public HorariosQueTreinouProgramaAtual getContent() {
        return content;
    }

    public void setContent(HorariosQueTreinouProgramaAtual content) {
        this.content = content;
    }
}
