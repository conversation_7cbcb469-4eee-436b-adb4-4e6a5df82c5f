package br.com.pacto.swagger.respostas.log;


import br.com.pacto.bean.gympass.DetalheLogGympassTO;
import br.com.pacto.bean.gympass.LogGymPassTO;
import br.com.pacto.swagger.respostas.ExemploPaginadorDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200")
public class ExemploRespostaDetalheLogGymPassTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as informações solicitadas")
    private DetalheLogGympassTO content;

    public DetalheLogGympassTO getContent() {
        return content;
    }

    public void setContent(DetalheLogGympassTO content) {
        this.content = content;
    }
}
