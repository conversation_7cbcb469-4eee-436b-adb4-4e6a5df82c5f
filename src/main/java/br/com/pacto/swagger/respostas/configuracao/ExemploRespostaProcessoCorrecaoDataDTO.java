package br.com.pacto.swagger.respostas.configuracao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para processo de correção de datas")
public class ExemploRespostaProcessoCorrecaoDataDTO {

    @ApiModelProperty(value = "Resultado do processo de correção de datas", example = "Datas corrigidas com sucesso para a matrícula 12345")
    private String content;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
