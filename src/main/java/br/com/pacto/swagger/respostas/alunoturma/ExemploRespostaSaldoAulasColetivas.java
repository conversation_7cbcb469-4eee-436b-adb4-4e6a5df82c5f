package br.com.pacto.swagger.respostas.alunoturma;

import br.com.pacto.controller.json.aulaDia.SaldoAulaColetivaVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Classe de exemplo para resposta do endpoint consultarSaldoAulasColetivas
 * Representa a estrutura ModelMap retornada com saldo de aulas coletivas do aluno
 */
@ApiModel(description = "Resposta da consulta de saldo de aulas coletivas do aluno")
public class ExemploRespostaSaldoAulasColetivas {

    @ApiModelProperty(value = "Dados detalhados do saldo de aulas coletivas disponíveis para o aluno. " +
                             "Inclui informações sobre créditos de aulas em grupo, modalidades disponíveis, " +
                             "restrições de uso, validade dos créditos e outras informações relevantes " +
                             "para validação de agendamentos em turmas coletivas.")
    private SaldoAulaColetivaVO sucesso;

    @ApiModelProperty(value = "Mensagem de erro, presente apenas quando ocorre algum problema durante a consulta", 
                      example = "")
    private String statusErro;

    public SaldoAulaColetivaVO getSucesso() {
        return sucesso;
    }

    public void setSucesso(SaldoAulaColetivaVO sucesso) {
        this.sucesso = sucesso;
    }

    public String getStatusErro() {
        return statusErro;
    }

    public void setStatusErro(String statusErro) {
        this.statusErro = statusErro;
    }
}
