package br.com.pacto.swagger.respostas.configuracao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para processo de alteração PAR-Q")
public class ExemploRespostaProcessoParqDTO {

    @ApiModelProperty(value = "Resultado do processo de alteração do PAR-Q", example = "PAR-Q definido como falso para o aluno matrícula 12345")
    private String content;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
