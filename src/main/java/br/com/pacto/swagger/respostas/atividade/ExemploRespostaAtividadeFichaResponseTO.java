package br.com.pacto.swagger.respostas.atividade;

import br.com.pacto.bean.atividade.MetodoExecucaoEnum;
import br.com.pacto.bean.atividade.TipoAtividadeEndpointEnum;
import br.com.pacto.bean.ficha.AtividadeFichaResponseTO;
import br.com.pacto.bean.ficha.AtividadeResponseTO;
import br.com.pacto.bean.ficha.SerieResponseTO;
import br.com.pacto.bean.ficha.SetAtividadeFichaResponseDTO;
import br.com.pacto.bean.musculo.GrupoMuscularResponseTO;

import java.util.ArrayList;
import java.util.List;

/**
 * Classe de exemplo para resposta de AtividadeFichaResponseTO
 */
public class ExemploRespostaAtividadeFichaResponseTO {

    public AtividadeFichaResponseTO content;

    public AtividadeFichaResponseTO getContent() {
        return content;
    }

    public void setContent(AtividadeFichaResponseTO content) {
        this.content = content;
    }
}
