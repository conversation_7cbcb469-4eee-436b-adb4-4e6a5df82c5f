package br.com.pacto.swagger.respostas.aula;


import br.com.pacto.controller.json.aulaDia.AulaResponseDTO;
import br.com.pacto.swagger.respostas.ExemploPaginadorDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200")
public class ExemploRespostaListAulaResponseDTOPaginacao extends ExemploPaginadorDTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as informações solicitadas")
    private List<AulaResponseDTO> content;

    public List<AulaResponseDTO> getContent() {
        return content;
    }

    public void setContent(List<AulaResponseDTO> content) {
        this.content = content;
    }
}
