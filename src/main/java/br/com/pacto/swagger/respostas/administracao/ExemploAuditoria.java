package br.com.pacto.swagger.respostas.administracao;

import java.util.ArrayList;
import java.util.List;

/**
 * Classe de exemplo para resposta do endpoint de auditoria (implementação vazia)
 */
public class ExemploAuditoria {
    
    private List<Object> auditoria;
    
    public ExemploAuditoria() {
        this.auditoria = new ArrayList<>();
        // Lista vazia pois a implementação está vazia
    }
    
    public List<Object> getAuditoria() {
        return auditoria;
    }
    
    public void setAuditoria(List<Object> auditoria) {
        this.auditoria = auditoria;
    }
}
