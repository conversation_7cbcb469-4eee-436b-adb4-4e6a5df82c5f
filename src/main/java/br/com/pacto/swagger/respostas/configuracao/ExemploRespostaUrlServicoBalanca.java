package br.com.pacto.swagger.respostas.configuracao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para consulta da URL do serviço de balança")
public class ExemploRespostaUrlServicoBalanca {

    @ApiModelProperty(value = "URL do serviço de balança de bioimpedância", 
                     example = "https://socket.academia.com:8080")
    private String url;

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }
}
