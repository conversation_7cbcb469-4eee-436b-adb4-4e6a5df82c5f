package br.com.pacto.swagger.respostas.turma;

import br.com.pacto.controller.json.turma.TurmaResponseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Exemplo de resposta para turma individual")
public class ExemploRespostaTurmaResponseDTOIndividual {

    @ApiModelProperty(value = "Dados completos da turma")
    private TurmaResponseDTO content;

    public TurmaResponseDTO getContent() {
        return content;
    }

    public void setContent(TurmaResponseDTO content) {
        this.content = content;
    }
}
