package br.com.pacto.swagger.respostas.wod;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Classe de exemplo para resposta de importação de WODs da franqueadora
 */
@ApiModel(description = "Representação da resposta de status 200 para importação de WODs da franqueadora encapsulada em EnvelopeRespostaDTO")
public class ExemploRespostaStringImportacaoFranqueadora {

    @ApiModelProperty(value = "Mensagem de confirmação da importação dos WODs da franqueadora", 
                     example = "WODs importados com sucesso da franqueadora. Total de 15 WODs importados.")
    private String content;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
