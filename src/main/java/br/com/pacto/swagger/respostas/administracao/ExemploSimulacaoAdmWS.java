package br.com.pacto.swagger.respostas.administracao;

import java.util.ArrayList;
import java.util.List;

/**
 * Classe de exemplo para resposta do endpoint de simulação do web service administrativo
 */
public class ExemploSimulacaoAdmWS {
    
    private List<EmpresaWSExemplo> empresas;
    
    public ExemploSimulacaoAdmWS() {
        this.empresas = new ArrayList<>();
        
        EmpresaWSExemplo empresa1 = new EmpresaWSExemplo();
        empresa1.setCodigo(1);
        empresa1.setNome("Academia Fitness Plus");
        empresa1.setCnpj("12.345.678/0001-90");
        empresa1.setEndereco("Rua das Flores, 123");
        empresa1.setCidade("São Paulo");
        empresa1.setEstado("SP");
        empresa1.setTelefone("(11) 1234-5678");
        empresa1.setEmail("<EMAIL>");
        empresa1.setEmpresaSuspensa(false);
        
        EmpresaWSExemplo empresa2 = new EmpresaWSExemplo();
        empresa2.setCodigo(2);
        empresa2.setNome("Crossfit Iron Box");
        empresa2.setCnpj("98.765.432/0001-10");
        empresa2.setEndereco("Av. Principal, 456");
        empresa2.setCidade("Rio de Janeiro");
        empresa2.setEstado("RJ");
        empresa2.setTelefone("(21) 9876-5432");
        empresa2.setEmail("<EMAIL>");
        empresa2.setEmpresaSuspensa(false);
        
        this.empresas.add(empresa1);
        this.empresas.add(empresa2);
    }
    
    public List<EmpresaWSExemplo> getEmpresas() {
        return empresas;
    }
    
    public void setEmpresas(List<EmpresaWSExemplo> empresas) {
        this.empresas = empresas;
    }
    
    public static class EmpresaWSExemplo {
        private Integer codigo;
        private String nome;
        private String cnpj;
        private String endereco;
        private String cidade;
        private String estado;
        private String telefone;
        private String email;
        private Boolean empresaSuspensa;
        
        // Getters e Setters
        public Integer getCodigo() { return codigo; }
        public void setCodigo(Integer codigo) { this.codigo = codigo; }
        
        public String getNome() { return nome; }
        public void setNome(String nome) { this.nome = nome; }
        
        public String getCnpj() { return cnpj; }
        public void setCnpj(String cnpj) { this.cnpj = cnpj; }
        
        public String getEndereco() { return endereco; }
        public void setEndereco(String endereco) { this.endereco = endereco; }
        
        public String getCidade() { return cidade; }
        public void setCidade(String cidade) { this.cidade = cidade; }
        
        public String getEstado() { return estado; }
        public void setEstado(String estado) { this.estado = estado; }
        
        public String getTelefone() { return telefone; }
        public void setTelefone(String telefone) { this.telefone = telefone; }
        
        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }
        
        public Boolean getEmpresaSuspensa() { return empresaSuspensa; }
        public void setEmpresaSuspensa(Boolean empresaSuspensa) { this.empresaSuspensa = empresaSuspensa; }
    }
}
