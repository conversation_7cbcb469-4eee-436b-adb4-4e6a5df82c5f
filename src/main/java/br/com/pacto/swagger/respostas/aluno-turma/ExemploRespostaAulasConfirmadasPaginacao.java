package br.com.pacto.swagger.respostas.alunoturma;

import br.com.pacto.controller.json.aulaDia.AulaConfirmadaVO;
import br.com.pacto.swagger.respostas.ExemploPaginadorDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * Classe de exemplo para resposta do endpoint aulasConfirmadasPorAluno
 * Representa a estrutura ModelMap retornada com lista paginada de aulas confirmadas
 */
@ApiModel(description = "Resposta da consulta de aulas confirmadas pelo aluno com paginação")
public class ExemploRespostaAulasConfirmadasPaginacao extends ExemploPaginadorDTO {

    @ApiModelProperty(value = "Lista de aulas confirmadas pelo aluno")
    private List<AulaConfirmadaVO> sucesso;

    @ApiModelProperty(value = "Mensagem de erro caso a consulta não seja possível", 
                     example = "Erro ao consultar aulas confirmadas. Matrícula não encontrada.")
    private String erro;

    public List<AulaConfirmadaVO> getSucesso() {
        return sucesso;
    }

    public void setSucesso(List<AulaConfirmadaVO> sucesso) {
        this.sucesso = sucesso;
    }

    public String getErro() {
        return erro;
    }

    public void setErro(String erro) {
        this.erro = erro;
    }
}
