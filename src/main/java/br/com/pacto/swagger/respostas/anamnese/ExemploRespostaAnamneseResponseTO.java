package br.com.pacto.swagger.respostas.anamnese;


import br.com.pacto.bean.anamnese.AnamneseResponseTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200")
public class ExemploRespostaAnamneseResponseTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as informações solicitadas")
    private AnamneseResponseTO content;

    public AnamneseResponseTO getContent() {
        return content;
    }

    public void setContent(AnamneseResponseTO content) {
        this.content = content;
    }
}
