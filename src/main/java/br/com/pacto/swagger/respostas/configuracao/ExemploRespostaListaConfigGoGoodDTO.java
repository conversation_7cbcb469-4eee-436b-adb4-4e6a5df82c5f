package br.com.pacto.swagger.respostas.configuracao;

import br.com.pacto.controller.json.gogood.ConfigGoGoodDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para consulta de integrações GoGood")
public class ExemploRespostaListaConfigGoGoodDTO {

    @ApiModelProperty(value = "Lista de configurações de integração GoGood")
    private List<ConfigGoGoodDTO> content;

    public List<ConfigGoGoodDTO> getContent() {
        return content;
    }

    public void setContent(List<ConfigGoGoodDTO> content) {
        this.content = content;
    }
}
