package br.com.pacto.swagger.respostas.configuracao;

import br.com.pacto.controller.json.base.ConfiguracoesIaDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para consulta de configurações de IA")
public class ExemploRespostaConfiguracoesIaDTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as configurações de Inteligência Artificial")
    private ConfiguracoesIaDTO content;

    public ConfiguracoesIaDTO getContent() {
        return content;
    }

    public void setContent(ConfiguracoesIaDTO content) {
        this.content = content;
    }
}
