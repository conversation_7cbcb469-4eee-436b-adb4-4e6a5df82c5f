package br.com.pacto.swagger.respostas.treino;

import br.com.pacto.controller.json.aluno.AlunoSimplesDTO;
import br.com.pacto.swagger.respostas.ExemploPaginadorDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200")
public class ExemploRespostaListAlunoSimplesDTOPaginacao extends ExemploPaginadorDTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as informações solicitadas")
    private List<AlunoSimplesDTO> content;

    public List<AlunoSimplesDTO> getContent() {
        return content;
    }

    public void setContent(List<AlunoSimplesDTO> content) {
        this.content = content;
    }
}
