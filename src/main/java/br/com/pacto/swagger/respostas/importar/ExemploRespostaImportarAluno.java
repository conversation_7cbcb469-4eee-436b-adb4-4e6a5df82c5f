package br.com.pacto.swagger.respostas.importar;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200")
public class ExemploRespostaImportarAluno {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as informações solicitadas", example = "Processo iniciado, aguarde e confie")
    private String content;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
