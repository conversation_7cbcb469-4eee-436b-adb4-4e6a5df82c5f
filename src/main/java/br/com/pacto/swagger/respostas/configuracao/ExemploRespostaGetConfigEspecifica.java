package br.com.pacto.swagger.respostas.configuracao;

import br.com.pacto.controller.json.configuracao.ConfiguracaoSistemaJSON;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para consulta de configuração específica")
public class ExemploRespostaGetConfigEspecifica {

    @ApiModelProperty(value = "Objeto contendo a configuração específica solicitada")
    private ConfiguracaoSistemaJSON RETURN;

    public ConfiguracaoSistemaJSON getRETURN() {
        return RETURN;
    }

    public void setRETURN(ConfiguracaoSistemaJSON RETURN) {
        this.RETURN = RETURN;
    }
}
