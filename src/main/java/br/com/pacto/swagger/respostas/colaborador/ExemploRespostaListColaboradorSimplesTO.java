package br.com.pacto.swagger.respostas.colaborador;

import br.com.pacto.bean.colaborador.ColaboradorSimplesTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para consulta simplificada de colaboradores encapsulada em EnvelopeRespostaDTO")
public class ExemploRespostaListColaboradorSimplesTO {

    @ApiModelProperty(value = "Lista contendo dados simplificados dos colaboradores (ID, nome, imagem)")
    private List<ColaboradorSimplesTO> content;

    public List<ColaboradorSimplesTO> getContent() {
        return content;
    }

    public void setContent(List<ColaboradorSimplesTO> content) {
        this.content = content;
    }
}
