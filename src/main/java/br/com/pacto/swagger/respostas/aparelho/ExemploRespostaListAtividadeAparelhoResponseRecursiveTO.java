package br.com.pacto.swagger.respostas.aparelho;

import br.com.pacto.controller.json.programa.AtividadeAparelhoResponseRecursiveTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para listagem de todos os aparelhos encapsulada em EnvelopeRespostaDTO")
public class ExemploRespostaListAtividadeAparelhoResponseRecursiveTO {

    @ApiModelProperty(value = "Lista contendo informações básicas de todos os aparelhos filtrados por tipo")
    private List<AtividadeAparelhoResponseRecursiveTO> content;

    public List<AtividadeAparelhoResponseRecursiveTO> getContent() {
        return content;
    }

    public void setContent(List<AtividadeAparelhoResponseRecursiveTO> content) {
        this.content = content;
    }
}
