package br.com.pacto.swagger.respostas.aluno.perfil;

import br.com.pacto.service.impl.cliente.perfil.FichaDoDiaDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para consulta da ficha do dia do aluno")
public class ExemploRespostaFichaDoDiaDTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as informações da ficha do dia do aluno")
    private FichaDoDiaDTO content;

    public FichaDoDiaDTO getContent() {
        return content;
    }

    public void setContent(FichaDoDiaDTO content) {
        this.content = content;
    }
}
