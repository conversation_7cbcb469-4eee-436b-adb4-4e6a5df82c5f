package br.com.pacto.swagger.respostas.configuracao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para limpeza do cache Memcached")
public class ExemploRespostaClearMemcached {

    // Esta classe representa uma resposta vazia, pois o endpoint clear() não retorna conteúdo específico
    // O ModelMap é retornado vazio em caso de sucesso
}
