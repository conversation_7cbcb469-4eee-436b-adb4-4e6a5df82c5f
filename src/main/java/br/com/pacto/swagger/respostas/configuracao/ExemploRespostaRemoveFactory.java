package br.com.pacto.swagger.respostas.configuracao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para remoção de factory de entidade")
public class ExemploRespostaRemoveFactory {

    @ApiModelProperty(value = "Mensagem de confirmação da remoção da factory", 
                     example = "Factory academia123 removido com sucesso!")
    private String sucesso;

    public String getSucesso() {
        return sucesso;
    }

    public void setSucesso(String sucesso) {
        this.sucesso = sucesso;
    }
}
