package br.com.pacto.swagger.respostas.notificacao;


import br.com.pacto.dto.notificacao.NotificacaoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200")
public class ExemploRespostaNotificacaoDTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as informações solicitadas")
    private NotificacaoDTO content;

    public NotificacaoDTO getContent() {
        return content;
    }

    public void setContent(NotificacaoDTO content) {
        this.content = content;
    }
}
