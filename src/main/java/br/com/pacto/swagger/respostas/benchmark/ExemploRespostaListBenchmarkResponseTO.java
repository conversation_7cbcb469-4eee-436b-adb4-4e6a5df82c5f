package br.com.pacto.swagger.respostas.benchmark;

import br.com.pacto.controller.json.benchmark.BenchmarkResponseTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200")
public class ExemploRespostaListBenchmarkResponseTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as informações solicitadas")
    private List<BenchmarkResponseTO> content;

    public List<BenchmarkResponseTO> getContent() {
        return content;
    }

    public void setContent(List<BenchmarkResponseTO> content) {
        this.content = content;
    }
}
