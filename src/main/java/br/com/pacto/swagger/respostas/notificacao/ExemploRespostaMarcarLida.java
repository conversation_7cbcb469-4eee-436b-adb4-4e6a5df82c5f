package br.com.pacto.swagger.respostas.notificacao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para marcar notificações como lidas")
public class ExemploRespostaMarcarLida {

    @ApiModelProperty(value = "Mensagem de confirmação com a quantidade de notificações marcadas como lidas", 
                      example = "Notificaçoes marcadas como lida. Total 3")
    private String RETURN;

    public String getRETURN() {
        return RETURN;
    }

    public void setRETURN(String RETURN) {
        this.RETURN = RETURN;
    }
}
