package br.com.pacto.swagger.respostas.tvGestor;

import br.com.pacto.controller.json.tvGestor.dto.TvGestorFiltroResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Exemplo de resposta para filtros da TV Gestor")
public class ExemploRespostaTvGestorFiltroResponse {

    @ApiModelProperty(value = "Dados dos filtros disponíveis para a TV Gestor")
    private TvGestorFiltroResponse content;

    public TvGestorFiltroResponse getContent() {
        return content;
    }

    public void setContent(TvGestorFiltroResponse content) {
        this.content = content;
    }
}
