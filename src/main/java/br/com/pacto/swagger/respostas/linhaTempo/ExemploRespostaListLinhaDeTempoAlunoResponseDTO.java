package br.com.pacto.swagger.respostas.linhaTempo;


import br.com.pacto.controller.json.aluno.LinhaDeTempoAlunoResponseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200")
public class ExemploRespostaListLinhaDeTempoAlunoResponseDTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as informações solicitadas")
    private List<LinhaDeTempoAlunoResponseDTO> content;

    public List<LinhaDeTempoAlunoResponseDTO> getContent() {
        return content;
    }

    public void setContent(List<LinhaDeTempoAlunoResponseDTO> content) {
        this.content = content;
    }
}
