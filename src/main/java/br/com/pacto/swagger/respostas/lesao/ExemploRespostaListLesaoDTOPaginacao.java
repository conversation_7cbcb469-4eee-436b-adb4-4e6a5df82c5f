package br.com.pacto.swagger.respostas.lesao;

import br.com.pacto.bean.lesao.LesaoDTO;
import br.com.pacto.swagger.respostas.ExemploPaginadorDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representacao da resposta de status 200 para listagem paginada de lesoes")
public class ExemploRespostaListLesaoDTOPaginacao extends ExemploPaginadorDTO {

    @ApiModelProperty(value = "Lista de lesoes com paginacao")
    private List<LesaoDTO> content;

    public List<LesaoDTO> getContent() {
        return content;
    }

    public void setContent(List<LesaoDTO> content) {
        this.content = content;
    }
}
