package br.com.pacto.swagger.respostas.notificacao;

import br.com.pacto.controller.json.notificacao.NotificacaoJSON;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para consulta do histórico de notificações do cliente")
public class ExemploRespostaHistoricoNotificacoes {

    @ApiModelProperty(value = "Lista paginada de notificações do histórico do cliente")
    private List<NotificacaoJSON> RETURN;

    public List<NotificacaoJSON> getRETURN() {
        return RETURN;
    }

    public void setRETURN(List<NotificacaoJSON> RETURN) {
        this.RETURN = RETURN;
    }
}
