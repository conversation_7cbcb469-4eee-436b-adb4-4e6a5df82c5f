package br.com.pacto.swagger.respostas.objetivos;

import br.com.pacto.bean.programa.ObjetivoAlunoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para operações com objetivo individual do aluno")
public class ExemploRespostaObjetivoAlunoVO {

    @ApiModelProperty(value = "Dados do objetivo do aluno")
    private ObjetivoAlunoVO content;

    public ObjetivoAlunoVO getContent() {
        return content;
    }

    public void setContent(ObjetivoAlunoVO content) {
        this.content = content;
    }
}
