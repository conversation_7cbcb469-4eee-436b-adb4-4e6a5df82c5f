package br.com.pacto.swagger.respostas.parceiros;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.controller.json.parceiros.ParceiroResponseTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * Classe de exemplo para resposta do cadastro/atualização de parceiros
 */
@ApiModel(description = "Exemplo de resposta para cadastro/atualização de parceiros")
public class ExemploRespostaListParceiroResponseTOCadastro {

    @ApiModelProperty(value = "Lista de parceiros processados (criados ou atualizados)")
    private List<ParceiroResponseTO> content;

    public List<ParceiroResponseTO> getContent() {
        return content;
    }

    public void setContent(List<ParceiroResponseTO> content) {
        this.content = content;
    }
}
