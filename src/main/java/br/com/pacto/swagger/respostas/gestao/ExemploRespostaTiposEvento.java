package br.com.pacto.swagger.respostas.gestao;

import br.com.pacto.controller.json.gestao.TipoDeEventosDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para consulta de tipos de evento")
public class ExemploRespostaTiposEvento {

    @ApiModelProperty(value = "Conteúdo da resposta contendo a lista de tipos de evento disponíveis para o usuário")
    private List<TipoDeEventosDTO> content;

    public List<TipoDeEventosDTO> getContent() {
        return content;
    }

    public void setContent(List<TipoDeEventosDTO> content) {
        this.content = content;
    }
}
