package br.com.pacto.swagger.respostas.colaborador;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.colaborador.ColaboradorResponseTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para consulta paginada de colaboradores encapsulada em EnvelopeRespostaDTO")
public class ExemploRespostaListColaboradorResponseTO extends PaginadorDTO {

    @ApiModelProperty(value = "Lista paginada contendo os colaboradores encontrados conforme os filtros especificados")
    private List<ColaboradorResponseTO> content;

    public List<ColaboradorResponseTO> getContent() {
        return content;
    }

    public void setContent(List<ColaboradorResponseTO> content) {
        this.content = content;
    }

}
