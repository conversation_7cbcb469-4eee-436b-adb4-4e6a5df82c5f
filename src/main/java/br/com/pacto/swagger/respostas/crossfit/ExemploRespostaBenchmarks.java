package br.com.pacto.swagger.respostas.crossfit;

import br.com.pacto.bean.benchmark.Benchmark;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para consulta de benchmarks")
public class ExemploRespostaBenchmarks {

    @ApiModelProperty(value = "Lista de benchmarks de CrossFit")
    private List<Benchmark> content;

    public List<Benchmark> getContent() {
        return content;
    }

    public void setContent(List<Benchmark> content) {
        this.content = content;
    }
}
