package br.com.pacto.swagger.respostas.manutencao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Exemplo de resposta para sincronização de foto entre ZW e Treino")
public class ExemploRespostaSincronizacaoFotoKey {

    @ApiModelProperty(value = "Resultado do processo de sincronização de fotos",
            example = "Processo concluído! - Total pessoas zw verificado: 150 - Total sucesso: 45 - Total fotokey igual entre zw e treino: 105")
    private String content;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
