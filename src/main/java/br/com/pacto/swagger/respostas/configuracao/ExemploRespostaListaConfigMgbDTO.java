package br.com.pacto.swagger.respostas.configuracao;

import br.com.pacto.controller.json.mgb.ConfigMgb;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para consulta de lista de configurações MGB")
public class ExemploRespostaListaConfigMgbDTO {

    @ApiModelProperty(value = "Lista de configurações MGB")
    private List<ConfigMgb> content;

    public List<ConfigMgb> getContent() {
        return content;
    }

    public void setContent(List<ConfigMgb> content) {
        this.content = content;
    }
}
