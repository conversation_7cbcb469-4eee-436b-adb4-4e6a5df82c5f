package br.com.pacto.swagger.respostas.usuario;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para validação de usuário com dados criptografados")
public class ExemploRespostaValidarUsuarioCrypt {

    @ApiModelProperty(value = "Dados completos do usuário validado retornados de forma criptografada. " +
            "Contém todas as informações do usuário (dados pessoais, empresa, planos, créditos, modalidades, etc.) " +
            "em formato JSON criptografado para segurança na transmissão. " +
            "Os dados incluem informações como código do usuário, nome, e-mail, matrícula, empresa, " +
            "saldo de créditos, planos ativos, modalidades disponíveis e URLs de redirecionamento.",
            example = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjb2QiOjEyMzQ1LCJjb2RVc3VhcmlvIjoxMjM0NSwiY29kVXN1YXJpb1pXIjo2Nzg5MCwibm9tZSI6IkNhcmxvcyBFZHVhcmRvIFNpbHZhIiwidXNlcm5hbWUiOiJjYXJsb3Muc2lsdmFAZW1haWwuY29tIiwiaWRhZGUiOjMyLCJlbWFpbCI6ImNhcmxvcy5zaWx2YUBlbWFpbC5jb20iLCJtYXRyaWN1bGEiOiIwMDEyMzQiLCJub21lRW1wcmVzYSI6IkNyb3NzRml0IEV4dHJlbWUiLCJzYWxkb0NyZWRpdG9zIjoyNSwidG90YWxDcmVkaXRvcyI6NTAsIm5vbWVQbGFubyI6IlBsYW5vIE1lbnNhbCBBdmFuw6dhZG8iLCJzdGF0dXNBbHVubyI6IkFUSVZPIiwibW9kYWxpZGFkZSI6Ik11c2N1bGHDp8OjbywgRnVuY2lvbmFsLCBDcm9zc2ZpdCIsInByb2Zlc3NvciI6IkFuYSBDb3N0YSIsInRlbGVmb25lIjoiKDExKSA5OTk5OS05OTk5In0")
    private String RETURN;

    public String getRETURN() {
        return RETURN;
    }

    public void setRETURN(String RETURN) {
        this.RETURN = RETURN;
    }
}
