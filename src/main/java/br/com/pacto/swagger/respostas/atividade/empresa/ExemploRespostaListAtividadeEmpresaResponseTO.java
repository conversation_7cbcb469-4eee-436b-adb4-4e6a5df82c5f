package br.com.pacto.swagger.respostas.atividade.empresa;


import br.com.pacto.bean.atividade.AtividadeEmpresaResponseTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200")
public class ExemploRespostaListAtividadeEmpresaResponseTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as informações solicitadas")
    private List<AtividadeEmpresaResponseTO> content;

    public List<AtividadeEmpresaResponseTO> getContent() {
        return content;
    }

    public void setContent(List<AtividadeEmpresaResponseTO> content) {
        this.content = content;
    }
}
