package br.com.pacto.swagger.respostas.treino;


import br.com.pacto.controller.json.aluno.ProgramaTreinoAlunoResponseDTO;
import br.com.pacto.swagger.respostas.ExemploPaginadorDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200")
public class ExemploRespostaListProgramaTreinoAlunoResponseDTOPaginacao extends ExemploPaginadorDTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as informações solicitadas")
    private ProgramaTreinoAlunoResponseDTO content;

    public ProgramaTreinoAlunoResponseDTO getContent() {
        return content;
    }

    public void setContent(ProgramaTreinoAlunoResponseDTO content) {
        this.content = content;
    }
}
