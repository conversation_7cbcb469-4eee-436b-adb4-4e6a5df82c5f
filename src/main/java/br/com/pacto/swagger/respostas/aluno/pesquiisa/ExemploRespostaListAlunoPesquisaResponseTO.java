package br.com.pacto.swagger.respostas.aluno.pesquiisa;


import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.controller.json.aluno.AlunoPesquisaResponseTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200")
public class ExemploRespostaListAlunoPesquisaResponseTO extends PaginadorDTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as informações solicitadas")
    private List<AlunoPesquisaResponseTO> content;

    public List<AlunoPesquisaResponseTO> getContent() {
        return content;
    }

    public void setContent(List<AlunoPesquisaResponseTO> content) {
        this.content = content;
    }
}
