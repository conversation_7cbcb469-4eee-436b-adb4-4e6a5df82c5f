package br.com.pacto.swagger.respostas.token;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para validação de token com criptografia")
public class ExemploRespostaStringTokenCriptografado {

    @ApiModelProperty(value = "String contendo os dados do usuário, empresas e permissões em formato criptografado para maior segurança", 
                     example = "ZXlKaGJHY2lPaUpJVXpJMU5pSXNJblI1Y0NJNklrcFhWQ0o5LmV5SnpkV0lpT2lJeE1qTTBOVFkzT0RreU1DSXNJbTVoYldVaU9pSktZV2x5YjI0Z1JHOWxJaXdpYVdGMElqb3hOVEUyTWpNNU1ESXlmUS5TZmxLeHdSSlNNZUtLRjJRVDRmd3BNZUpGMzZQT2s2eUpWX2FkUXNzdzVj")
    private String content;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
