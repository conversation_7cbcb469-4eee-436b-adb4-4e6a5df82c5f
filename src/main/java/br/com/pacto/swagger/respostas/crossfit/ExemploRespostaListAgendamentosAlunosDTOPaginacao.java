package br.com.pacto.swagger.respostas.crossfit;

import br.com.pacto.service.impl.crossfit.AgendamentosAlunosDTO;
import br.com.pacto.swagger.respostas.ExemploPaginadorDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para consulta paginada de agendamentos de alunos do Crossfit")
public class ExemploRespostaListAgendamentosAlunosDTOPaginacao extends ExemploPaginadorDTO {

    @ApiModelProperty(value = "Lista de agendamentos de alunos do Crossfit")
    private List<AgendamentosAlunosDTO> content;

    public List<AgendamentosAlunosDTO> getContent() {
        return content;
    }

    public void setContent(List<AgendamentosAlunosDTO> content) {
        this.content = content;
    }
}
