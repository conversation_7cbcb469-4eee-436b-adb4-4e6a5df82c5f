package br.com.pacto.swagger.respostas.atestado;


import br.com.pacto.controller.json.aluno.AtestadoResponseTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200")
public class ExemploRespostaListAtestadoResponseTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as informações solicitadas")
    private List<AtestadoResponseTO> content;

    public List<AtestadoResponseTO> getContent() {
        return content;
    }

    public void setContent(List<AtestadoResponseTO> content) {
        this.content = content;
    }
}
