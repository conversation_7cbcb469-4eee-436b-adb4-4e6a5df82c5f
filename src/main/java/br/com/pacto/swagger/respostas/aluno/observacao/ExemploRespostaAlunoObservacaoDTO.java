package br.com.pacto.swagger.respostas.aluno.observacao;


import br.com.pacto.controller.json.aluno.AlunoObservacaoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200")
public class ExemploRespostaAlunoObservacaoDTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as informações solicitadas")
    private AlunoObservacaoDTO content;

    public AlunoObservacaoDTO getContent() {
        return content;
    }

    public void setContent(AlunoObservacaoDTO content) {
        this.content = content;
    }
}
