package br.com.pacto.swagger.respostas.modalidade;

import br.com.pacto.controller.json.modalidade.ModalidadeResponseTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Exemplo de resposta para lista de modalidades")
public class ExemploRespostaListModalidadeResponseTO {

    @ApiModelProperty(value = "Lista de modalidades")
    private List<ModalidadeResponseTO> content;

    public List<ModalidadeResponseTO> getContent() {
        return content;
    }

    public void setContent(List<ModalidadeResponseTO> content) {
        this.content = content;
    }
}
