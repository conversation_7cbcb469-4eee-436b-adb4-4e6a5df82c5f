package br.com.pacto.swagger.respostas.administracao;

import java.util.ArrayList;
import java.util.List;

/**
 * Classe de exemplo para resposta do endpoint de execução de migração
 */
public class ExemploMigracao {
    
    private List<String> resultados;
    
    public ExemploMigracao() {
        this.resultados = new ArrayList<>();
        this.resultados.add("empresa1 sucesso");
        this.resultados.add("empresa2 sucesso");
        this.resultados.add("demo sucesso");
        this.resultados.add("teste sucesso");
    }
    
    public List<String> getResultados() {
        return resultados;
    }
    
    public void setResultados(List<String> resultados) {
        this.resultados = resultados;
    }
}
