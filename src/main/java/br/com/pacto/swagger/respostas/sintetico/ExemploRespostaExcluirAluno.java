package br.com.pacto.swagger.respostas.sintetico;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para exclusão de aluno")
public class ExemploRespostaExcluirAluno {

    @ApiModelProperty(value = "Resultado da operação de exclusão do aluno. Retorna 'OK' em caso de sucesso ou mensagem de erro iniciada com 'ERRO:' em caso de falha. A operação remove completamente o aluno do sistema com base no código do cliente fornecido.", 
                     example = "OK")
    private String content;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
