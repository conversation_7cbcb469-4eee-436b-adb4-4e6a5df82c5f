package br.com.pacto.swagger.respostas.aula;


import br.com.pacto.controller.json.aulaDia.AulaColetivaResponseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200")
public class ExemploRespostaAulaColetivaResponseDTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as informações solicitadas")
    private AulaColetivaResponseDTO content;

    public AulaColetivaResponseDTO getContent() {
        return content;
    }

    public void setContent(AulaColetivaResponseDTO content) {
        this.content = content;
    }
}
