package br.com.pacto.swagger.respostas.horario.turma;


import br.com.pacto.controller.json.turma.HorarioTurmaResponseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200")
public class ExemploRespostaListHorarioTurmaResponseDTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as informações solicitadas")
    private List<HorarioTurmaResponseDTO> content;

    public List<HorarioTurmaResponseDTO> getContent() {
        return content;
    }

    public void setContent(List<HorarioTurmaResponseDTO> content) {
        this.content = content;
    }
}
