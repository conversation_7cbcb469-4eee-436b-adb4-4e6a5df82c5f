package br.com.pacto.swagger.respostas.nivel;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para obtenção do nível do aluno encapsulada em EnvelopeRespostaDTO")
public class ExemploNivelAlunoResponseDTO {

    @ApiModelProperty(value = "Código identificador do nível associado ao aluno", example = "2")
    private Integer content;

    public Integer getContent() {
        return content;
    }

    public void setContent(Integer content) {
        this.content = content;
    }
}
