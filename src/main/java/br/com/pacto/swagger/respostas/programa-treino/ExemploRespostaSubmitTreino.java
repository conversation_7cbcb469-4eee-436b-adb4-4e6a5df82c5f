package br.com.pacto.swagger.respostas.programatreino;

import br.com.pacto.controller.json.acompanhamento.AcompanhamentoSimplesJSON;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Classe de exemplo para resposta do endpoint submitTreino
 * Representa a estrutura ModelMap retornada após submissão de treino completo
 */
@ApiModel(description = "Resposta da submissão de treino realizado completo")
public class ExemploRespostaSubmitTreino {

    @ApiModelProperty(value = "Dados de acompanhamento atualizados após a submissão do treino, contendo informações sobre frequência e assiduidade do aluno")
    private AcompanhamentoSimplesJSON RETURN;

    @ApiModelProperty(value = "Mensagem de erro caso a submissão não seja possível", 
                     example = "Erro ao registrar treino realizado")
    private String statusErro;

    public AcompanhamentoSimplesJSON getRETURN() {
        return RETURN;
    }

    public void setRETURN(AcompanhamentoSimplesJSON RETURN) {
        this.RETURN = RETURN;
    }

    public String getStatusErro() {
        return statusErro;
    }

    public void setStatusErro(String statusErro) {
        this.statusErro = statusErro;
    }
}
