package br.com.pacto.swagger.respostas.tipoEvento;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Exemplo de resposta para remoção de tipo de evento")
public class ExemploRespostaRemocaoTipoEvento {

    @ApiModelProperty(value = "Indica se a remoção foi realizada com sucesso", example = "true")
    private Boolean content;

    public Boolean getContent() {
        return content;
    }

    public void setContent(Boolean content) {
        this.content = content;
    }
}
