package br.com.pacto.swagger.respostas.wod;

import br.com.pacto.bean.wod.WodResponseTO;
import br.com.pacto.swagger.respostas.ExemploPaginadorDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * Classe de exemplo para resposta paginada de lista de WODs
 */
@ApiModel(description = "Representação da resposta de status 200 para listagem paginada de WODs")
public class ExemploRespostaListWodResponseTOPaginacao extends ExemploPaginadorDTO {

    @ApiModelProperty(value = "Lista de WODs encontrados")
    private List<WodResponseTO> content;

    public List<WodResponseTO> getContent() {
        return content;
    }

    public void setContent(List<WodResponseTO> content) {
        this.content = content;
    }
}
