package br.com.pacto.swagger.respostas.aluno.observacao;


import br.com.pacto.controller.json.aluno.AlunoObservacaoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200")
public class ExemploRespostaListAlunoObservacaoDTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as informações solicitadas")
    private List<AlunoObservacaoDTO> content;

    public List<AlunoObservacaoDTO> getContent() {
        return content;
    }

    public void setContent(List<AlunoObservacaoDTO> content) {
        this.content = content;
    }
}
