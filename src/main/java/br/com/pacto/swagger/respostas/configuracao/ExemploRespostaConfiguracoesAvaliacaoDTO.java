package br.com.pacto.swagger.respostas.configuracao;

import br.com.pacto.controller.json.base.ConfiguracoesAvaliacaoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para consulta de configurações de avaliação física")
public class ExemploRespostaConfiguracoesAvaliacaoDTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as configurações de avaliação física do sistema")
    private ConfiguracoesAvaliacaoDTO content;

    public ConfiguracoesAvaliacaoDTO getContent() {
        return content;
    }

    public void setContent(ConfiguracoesAvaliacaoDTO content) {
        this.content = content;
    }
}
