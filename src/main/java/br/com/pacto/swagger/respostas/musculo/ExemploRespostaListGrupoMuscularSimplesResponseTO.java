package br.com.pacto.swagger.respostas.musculo;

import br.com.pacto.controller.json.musculo.GrupoMuscularSimplesResponseTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Exemplo de resposta para lista de grupos musculares simplificados")
public class ExemploRespostaListGrupoMuscularSimplesResponseTO {

    @ApiModelProperty(value = "Lista de grupos musculares simplificados")
    private List<GrupoMuscularSimplesResponseTO> content;

    public List<GrupoMuscularSimplesResponseTO> getContent() {
        return content;
    }

    public void setContent(List<GrupoMuscularSimplesResponseTO> content) {
        this.content = content;
    }
}
