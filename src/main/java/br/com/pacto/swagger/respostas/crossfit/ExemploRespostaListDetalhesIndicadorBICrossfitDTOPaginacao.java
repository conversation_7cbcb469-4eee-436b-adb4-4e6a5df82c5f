package br.com.pacto.swagger.respostas.crossfit;

import br.com.pacto.service.impl.crossfit.DetalhesIndicadorBICrossfitDTO;
import br.com.pacto.swagger.respostas.ExemploPaginadorDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para consulta paginada de detalhes de indicadores de BI do Crossfit")
public class ExemploRespostaListDetalhesIndicadorBICrossfitDTOPaginacao extends ExemploPaginadorDTO {

    @ApiModelProperty(value = "Lista de detalhes de indicadores de BI do Crossfit")
    private List<DetalhesIndicadorBICrossfitDTO> content;

    public List<DetalhesIndicadorBICrossfitDTO> getContent() {
        return content;
    }

    public void setContent(List<DetalhesIndicadorBICrossfitDTO> content) {
        this.content = content;
    }
}
