package br.com.pacto.swagger.respostas.gympass;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para sincronização de turma com GymPass")
public class ExemploRespostaSincronizarTurma {

    @ApiModelProperty(value = "Resultado da sincronização da turma", example = "Turma sincronizada com sucesso - ID: 123")
    private String content;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
