package br.com.pacto.swagger.respostas.alunoturma;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Classe de exemplo para resposta do endpoint desmarcarEuQueroHorario
 * Representa a estrutura ModelMap retornada com status da solicitação de remoção de interesse
 */
@ApiModel(description = "Resposta da solicitação de remoção de interesse em horário específico de aula")
public class ExemploRespostaDesmarcarEuQueroHorario {

    @ApiModelProperty(value = "Mensagem de sucesso quando a remoção do interesse é processada corretamente", 
                     example = "Solicitação enviada.")
    private String sucesso;

    @ApiModelProperty(value = "Mensagem de erro caso a remoção do interesse não seja possível", 
                     example = "Erro ao processar solicitação. Cliente não encontrado.")
    private String erro;

    public String getSucesso() {
        return sucesso;
    }

    public void setSucesso(String sucesso) {
        this.sucesso = sucesso;
    }

    public String getErro() {
        return erro;
    }

    public void setErro(String erro) {
        this.erro = erro;
    }
}
