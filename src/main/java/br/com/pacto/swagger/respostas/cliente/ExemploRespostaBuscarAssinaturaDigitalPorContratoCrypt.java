package br.com.pacto.swagger.respostas.cliente;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para consulta criptografada de assinatura digital por contrato específico")
public class ExemploRespostaBuscarAssinaturaDigitalPorContratoCrypt {

    @ApiModelProperty(value = "Dados criptografados da assinatura digital do contrato específico. Contém as mesmas informações do endpoint não criptografado (código do contrato, data de assinatura, status de validação, dados da assinatura digital, informações do aluno e detalhes do contrato), porém em formato criptografado para maior segurança. Os dados são criptografados usando o método de criptografia padrão do sistema. Retorna array vazio criptografado se o contrato não possuir assinatura digital ou se a funcionalidade estiver desabilitada.",
                     example = "U2FsdGVkX1+vupppZksvRf5pq5g5XjFRIipRkwB0K1Y96Qsv2Lm+31cmzaAILwytX0dugpn1WE3wAN9AEh9jVnb+oq5gZNeCGjUkL8mN3xF2vQ8pR1tK9sL4nM7bC2eR")
    private String RETURN;

    @ApiModelProperty(value = "Mensagem de erro criptografada caso ocorra algum problema na consulta da assinatura digital do contrato",
                     example = "U2FsdGVkX1+vupppZksvRf5pq5g5XjFRIipRkwB0K1Y96Qsv2Lm+31cmzaAILwyt")
    private String STATUS_ERRO;

    public String getRETURN() {
        return RETURN;
    }

    public void setRETURN(String RETURN) {
        this.RETURN = RETURN;
    }

    public String getSTATUS_ERRO() {
        return STATUS_ERRO;
    }

    public void setSTATUS_ERRO(String STATUS_ERRO) {
        this.STATUS_ERRO = STATUS_ERRO;
    }
}
