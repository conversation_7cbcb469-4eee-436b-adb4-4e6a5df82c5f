package br.com.pacto.swagger.respostas.usuario;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para validação de usuário de rede social com dados criptografados")
public class ExemploRespostaValidarUsuarioRedeSocialCrypt {

    @ApiModelProperty(value = "Dados do usuário validado retornados de forma criptografada. " +
            "Contém todas as informações do usuário (dados pessoais, empresa, planos, créditos, etc.) " +
            "em formato JSON criptografado para segurança na transmissão.",
            example = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************")
    private String RETURN;

    public String getRETURN() {
        return RETURN;
    }

    public void setRETURN(String RETURN) {
        this.RETURN = RETURN;
    }
}
