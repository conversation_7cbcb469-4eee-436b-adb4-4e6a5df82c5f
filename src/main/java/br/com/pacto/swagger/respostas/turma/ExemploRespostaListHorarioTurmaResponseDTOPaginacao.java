package br.com.pacto.swagger.respostas.turma;

import br.com.pacto.controller.json.turma.HorarioTurmaResponseDTO;
import br.com.pacto.swagger.respostas.ExemploPaginadorDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Exemplo de resposta para lista paginada de horários de turma")
public class ExemploRespostaListHorarioTurmaResponseDTOPaginacao extends ExemploPaginadorDTO {

    @ApiModelProperty(value = "Lista de horários da turma encontrados")
    private List<HorarioTurmaResponseDTO> content;

    public List<HorarioTurmaResponseDTO> getContent() {
        return content;
    }

    public void setContent(List<HorarioTurmaResponseDTO> content) {
        this.content = content;
    }
}
