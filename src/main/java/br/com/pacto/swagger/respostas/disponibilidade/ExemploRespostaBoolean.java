package br.com.pacto.swagger.respostas.disponibilidade;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para valores booleanos")
public class ExemploRespostaBoolean {

    @ApiModelProperty(value = "Conteúdo da resposta contendo um valor booleano", example = "true")
    private Boolean content;

    public Boolean getContent() {
        return content;
    }

    public void setContent(Boolean content) {
        this.content = content;
    }
}
