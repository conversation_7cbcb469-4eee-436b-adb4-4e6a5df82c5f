package br.com.pacto.swagger.respostas.atividade;

import br.com.pacto.controller.json.atividade.read.AtividadeJSON;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Exemplo de resposta para consulta de atividades para aplicativo")
public class ExemploRespostaAtividadesApp {

    @ApiModelProperty(value = "Lista de atividades otimizadas para aplicativo móvel")
    private List<AtividadeJSON> statusSucesso;

    @ApiModelProperty(value = "Indica sucesso da operação", example = "true")
    private Boolean sucesso;

    @ApiModelProperty(value = "Mensagem de erro (quando aplicável)", example = "null")
    private String statusErro;
}
