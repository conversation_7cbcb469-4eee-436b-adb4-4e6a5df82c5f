package br.com.pacto.swagger.respostas.empresa;

import br.com.pacto.controller.json.empresa.ResponseActiveEmpresaDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para ativação de empresa")
public class ExemploRespostaResponseActiveEmpresaDTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo token e ID da empresa ativada")
    private ResponseActiveEmpresaDTO content;

    public ResponseActiveEmpresaDTO getContent() {
        return content;
    }

    public void setContent(ResponseActiveEmpresaDTO content) {
        this.content = content;
    }
}
