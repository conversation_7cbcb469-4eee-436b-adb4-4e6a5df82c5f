package br.com.pacto.swagger.respostas.erros;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Classe de exemplo para resposta de erro HTTP 400 - Bad Request (ModelMap)
 * Utilizada para documentação automática do Swagger em endpoints que retornam ModelMap
 */
@ApiModel(description = "Exemplo de resposta para erro HTTP 400 - Bad Request (formato ModelMap)")
public class ExemploRespostaErroModelMap400 {

    @ApiModelProperty(value = "Mensagem de erro detalhada", 
                     example = "Dados de entrada inválidos. Verifique os campos obrigatórios e tente novamente.")
    private String STATUS_ERRO;

    // Getter e Setter
    public String getSTATUS_ERRO() {
        return STATUS_ERRO;
    }

    public void setSTATUS_ERRO(String STATUS_ERRO) {
        this.STATUS_ERRO = STATUS_ERRO;
    }
}
