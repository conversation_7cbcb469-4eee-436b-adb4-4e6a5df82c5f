package br.com.pacto.swagger.respostas.replicarEmpresa;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Exemplo de resposta para operações de replicação de empresa")
public class ExemploRespostaStringReplicacao {

    @ApiModelProperty(value = "Resultado da operação de replicação", example = "sucesso")
    private String content;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

}
