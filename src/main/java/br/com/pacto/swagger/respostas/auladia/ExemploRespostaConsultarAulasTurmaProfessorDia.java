package br.com.pacto.swagger.respostas.auladia;

import br.com.pacto.controller.json.aulaDia.AulaDiaJSON;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para consulta de aulas de turma por professor e dia")
public class ExemploRespostaConsultarAulasTurmaProfessorDia {

    @ApiModelProperty(value = "Lista de aulas do professor no dia especificado")
    private List<AulaDiaJSON> aulas;

    @ApiModelProperty(value = "Mensagem de erro caso ocorra algum problema durante a consulta", 
                      example = "Usuário não encontrado")
    private String statusErro;

    public List<AulaDiaJSON> getAulas() {
        return aulas;
    }

    public void setAulas(List<AulaDiaJSON> aulas) {
        this.aulas = aulas;
    }

    public String getStatusErro() {
        return statusErro;
    }

    public void setStatusErro(String statusErro) {
        this.statusErro = statusErro;
    }
}
