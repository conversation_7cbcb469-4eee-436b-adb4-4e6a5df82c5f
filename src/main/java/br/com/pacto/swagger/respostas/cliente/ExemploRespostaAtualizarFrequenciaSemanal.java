package br.com.pacto.swagger.respostas.cliente;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Classe de exemplo para resposta do endpoint de atualização de frequência semanal de clientes.
 * Representa a estrutura de retorno do ModelMap utilizado pelo endpoint atualizarFrequenciaSemanal.
 */
@ApiModel(description = "Exemplo de resposta para atualização de frequência semanal de clientes")
public class ExemploRespostaAtualizarFrequenciaSemanal {

    @ApiModelProperty(value = "Mensagem de retorno em caso de sucesso na atualização da frequência semanal dos clientes. " +
            "Este atributo é preenchido quando a operação é executada com sucesso.",
            example = "Sucesso")
    private String RETURN;

    // Getters e Setters
    public String getReturnValue() {
        return RETURN;
    }

    public void setReturnValue(String returnValue) {
        this.RETURN = RETURN;
    }
}
