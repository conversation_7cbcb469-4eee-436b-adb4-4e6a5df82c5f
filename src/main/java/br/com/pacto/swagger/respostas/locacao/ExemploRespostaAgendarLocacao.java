package br.com.pacto.swagger.respostas.locacao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para operações de agendamento de locação encapsulada em EnvelopeRespostaDTO")
public class ExemploRespostaAgendarLocacao {

    @ApiModelProperty(value = "Código identificador único do agendamento de locação criado com sucesso.", example = "123")
    private Integer content;

    public Integer getContent() {
        return content;
    }

    public void setContent(Integer content) {
        this.content = content;
    }
}
