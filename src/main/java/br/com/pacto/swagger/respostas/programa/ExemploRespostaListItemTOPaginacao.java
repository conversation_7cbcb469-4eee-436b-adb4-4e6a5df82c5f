package br.com.pacto.swagger.respostas.programa;

import br.com.pacto.util.bean.ItemTO;
import br.com.pacto.swagger.respostas.ExemploPaginadorDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para consulta paginada de itens de filtro")
public class ExemploRespostaListItemTOPaginacao extends ExemploPaginadorDTO {

    @ApiModelProperty(value = "Lista de itens de filtro encontrados")
    private List<ItemTO> content;

    public List<ItemTO> getContent() {
        return content;
    }

    public void setContent(List<ItemTO> content) {
        this.content = content;
    }
}
