package br.com.pacto.swagger.respostas.avaliacao;

import br.com.pacto.controller.json.avaliacao.ConfirmacaoAgendamentoJSON;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Exemplo de resposta para inserção de agendamento de avaliação física")
public class ExemploRespostaInserirAgendamentoAvaliacao {

    @ApiModelProperty(value = "Dados de confirmação do agendamento de avaliação física criado", 
                     example = "Objeto ConfirmacaoAgendamentoJSON com detalhes do agendamento")
    private ConfirmacaoAgendamentoJSON RETURN;

    @ApiModelProperty(value = "Mensagem de erro caso ocorra algum problema durante o agendamento", 
                     example = "")
    private String statusErro;

    public ConfirmacaoAgendamentoJSON getRETURN() {
        return RETURN;
    }

    public void setRETURN(ConfirmacaoAgendamentoJSON RETURN) {
        this.RETURN = RETURN;
    }

    public String getStatusErro() {
        return statusErro;
    }

    public void setStatusErro(String statusErro) {
        this.statusErro = statusErro;
    }
}
