package br.com.pacto.swagger.respostas.professor;


import br.com.pacto.controller.json.professor.RankingProfessoresResponseDTO;
import br.com.pacto.swagger.respostas.ExemploPaginadorDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para ranking de professores encapsulada em EnvelopeRespostaDTO")
public class ExemploRespostaRankingProfessores extends ExemploPaginadorDTO {

    @ApiModelProperty(value = "Lista contendo o ranking dos professores com suas pontuações e indicadores")
    private List<RankingProfessoresResponseDTO> content;

    public List<RankingProfessoresResponseDTO> getContent() {
        return content;
    }

    public void setContent(List<RankingProfessoresResponseDTO> content) {
        this.content = content;
    }
}
