package br.com.pacto.swagger.respostas.programatreino;

import br.com.pacto.controller.json.acompanhamento.AcompanhamentoSimplesJSON;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Classe de exemplo para resposta do endpoint submitTreinoComment
 * Representa a estrutura ModelMap retornada após submissão de comentário de treino
 */
@ApiModel(description = "Resposta da submissão de comentário de treino")
public class ExemploRespostaSubmitTreinoComment {

    @ApiModelProperty(value = "Dados de acompanhamento atualizados após a submissão do comentário de treino, contendo informações sobre frequência e assiduidade do aluno")
    private AcompanhamentoSimplesJSON RETURN;

    @ApiModelProperty(value = "Mensagem de erro caso ocorra algum problema durante a submissão do comentário. " +
            "Este campo só estará presente em caso de falha na operação e conterá detalhes sobre o erro ocorrido.", 
            example = "Programa de treino não encontrado")
    private String statusErro;

    // Getters e Setters
    public AcompanhamentoSimplesJSON getRETURN() {
        return RETURN;
    }

    public void setRETURN(AcompanhamentoSimplesJSON RETURN) {
        this.RETURN = RETURN;
    }

    public String getStatusErro() {
        return statusErro;
    }

    public void setStatusErro(String statusErro) {
        this.statusErro = statusErro;
    }
}
