package br.com.pacto.swagger.respostas.gympass;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para exclusão de horários GymPass")
public class ExemploRespostaExcluirHorarios {

    @ApiModelProperty(value = "Resultado da exclusão dos horários", example = "Horários excluídos com sucesso - Total: 5 horários removidos")
    private String content;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
