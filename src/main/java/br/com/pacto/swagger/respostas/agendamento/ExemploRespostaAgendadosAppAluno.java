package br.com.pacto.swagger.respostas.agendamento;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Resposta contendo dias e horários disponíveis para reagendamento nos próximos 30 dias")
public class ExemploRespostaAgendadosAppAluno {

    @ApiModelProperty(value = "Lista de dias com horários disponíveis para reagendamento")
    private List<DiaComHorarios> retorno;

    @ApiModelProperty(value = "Mensagem de erro, presente apenas quando ocorre algum problema", example = "")
    private String erro;

    @ApiModel(description = "Dia com lista de horários disponíveis")
    public static class DiaComHorarios {
        
        @ApiModelProperty(value = "Data do dia (formato dd/MM/yyyy)", example = "15/12/2024")
        private String dia;
        
        @ApiModelProperty(value = "Lista de horários disponíveis no formato 'HH:mm - HH:mm'", example = "[\"14:00 - 15:00\", \"15:00 - 16:00\", \"16:00 - 17:00\"]")
        private List<String> horarios;
    }
}
