package br.com.pacto.swagger.respostas.locacao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Arrays;
import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para validação de reagendamento de locação encapsulada em EnvelopeRespostaDTO")
public class ExemploRespostaValidarReagendamento {

    @ApiModelProperty(value = "Lista de horários disponíveis para reagendamento")
    private List<ExemploLocacaoHorarioTO> dados = Arrays.asList(
            new ExemploLocacaoHorarioTO(1, "Segunda-feira", "08:00", "09:00", "Quadra Principal", "Maria Santos"),
            new ExemploLocacaoHorarioTO(2, "Segunda-feira", "14:00", "15:00", "Quadra Secundária", "<PERSON> Silva"),
            new ExemploLocacaoHorarioTO(3, "Terça-feira", "08:00", "09:00", "Quadra Principal", "<PERSON>")
    );

    public List<ExemploLocacaoHorarioTO> getDados() {
        return dados;
    }

    public void setDados(List<ExemploLocacaoHorarioTO> dados) {
        this.dados = dados;
    }

    public static class ExemploLocacaoHorarioTO {
        @ApiModelProperty(value = "Código do horário da locação", example = "1")
        private Integer codigo;

        @ApiModelProperty(value = "Dia da semana", example = "Segunda-feira")
        private String diaSemana;

        @ApiModelProperty(value = "Horário de início", example = "08:00")
        private String horaInicio;

        @ApiModelProperty(value = "Horário de fim", example = "09:00")
        private String horaFim;

        @ApiModelProperty(value = "Nome do ambiente", example = "Quadra Principal")
        private String nomeAmbiente;

        @ApiModelProperty(value = "Nome do responsável", example = "Maria Santos")
        private String nomeResponsavel;

        @ApiModelProperty(value = "Valor por hora", example = "50.0")
        private Double valorHora;

        @ApiModelProperty(value = "Indica se o horário está disponível", example = "true")
        private Boolean disponivel;

        public ExemploLocacaoHorarioTO() {}

        public ExemploLocacaoHorarioTO(Integer codigo, String diaSemana, String horaInicio, String horaFim, String nomeAmbiente, String nomeResponsavel) {
            this.codigo = codigo;
            this.diaSemana = diaSemana;
            this.horaInicio = horaInicio;
            this.horaFim = horaFim;
            this.nomeAmbiente = nomeAmbiente;
            this.nomeResponsavel = nomeResponsavel;
            this.valorHora = 50.0;
            this.disponivel = true;
        }

        // Getters e Setters
        public Integer getCodigo() {
            return codigo;
        }

        public void setCodigo(Integer codigo) {
            this.codigo = codigo;
        }

        public String getDiaSemana() {
            return diaSemana;
        }

        public void setDiaSemana(String diaSemana) {
            this.diaSemana = diaSemana;
        }

        public String getHoraInicio() {
            return horaInicio;
        }

        public void setHoraInicio(String horaInicio) {
            this.horaInicio = horaInicio;
        }

        public String getHoraFim() {
            return horaFim;
        }

        public void setHoraFim(String horaFim) {
            this.horaFim = horaFim;
        }

        public String getNomeAmbiente() {
            return nomeAmbiente;
        }

        public void setNomeAmbiente(String nomeAmbiente) {
            this.nomeAmbiente = nomeAmbiente;
        }

        public String getNomeResponsavel() {
            return nomeResponsavel;
        }

        public void setNomeResponsavel(String nomeResponsavel) {
            this.nomeResponsavel = nomeResponsavel;
        }

        public Double getValorHora() {
            return valorHora;
        }

        public void setValorHora(Double valorHora) {
            this.valorHora = valorHora;
        }

        public Boolean getDisponivel() {
            return disponivel;
        }

        public void setDisponivel(Boolean disponivel) {
            this.disponivel = disponivel;
        }
    }
}
