package br.com.pacto.swagger.respostas.colaborador;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para operações que retornam apenas confirmação de sucesso encapsulada em EnvelopeRespostaDTO")
public class ExemploRespostaSucesso {

    @ApiModelProperty(value = "Conteúdo vazio ou null para operações de sucesso sem retorno de dados")
    private Object content;

    public Object getContent() {
        return content;
    }

    public void setContent(Object content) {
        this.content = content;
    }
}
