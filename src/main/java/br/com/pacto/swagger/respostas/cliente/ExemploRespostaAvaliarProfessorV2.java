package br.com.pacto.swagger.respostas.cliente;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.controller.json.aluno.AvaliacaoProfessorVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Exemplo de resposta para avaliação de professor versão 2 (App Treino)")
public class ExemploRespostaAvaliarProfessorV2 extends EnvelopeRespostaDTO {

    @ApiModelProperty(value = "Dados da avaliação do professor processada com informações adicionais")
    private AvaliacaoProfessorVO content;

    public AvaliacaoProfessorVO getContent() {
        return content;
    }

    public void setContent(AvaliacaoProfessorVO content) {
        this.content = (AvaliacaoProfessorVO) content;
    }
}
