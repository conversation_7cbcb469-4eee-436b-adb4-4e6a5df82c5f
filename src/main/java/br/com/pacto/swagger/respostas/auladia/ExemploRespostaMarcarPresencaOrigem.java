package br.com.pacto.swagger.respostas.auladia;

import br.com.pacto.controller.json.aulaDia.AulaAlunoJSON;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Classe de exemplo para resposta do endpoint marcarPresencaOrigem
 * Representa a estrutura de retorno quando um aluno marca presença em uma aula
 */
@ApiModel(description = "Resposta do sistema ao marcar presença de um aluno em uma aula específica")
public class ExemploRespostaMarcarPresencaOrigem {

    @ApiModelProperty(value = "Informações detalhadas do aluno e da aula após marcação de presença bem-sucedida")
    private AulaAlunoJSON sucesso;

    @ApiModelProperty(value = "Mensagem de erro caso ocorra algum problema durante a marcação de presença", 
                      example = "Não foi possível agendar a aula do dia 20/01/2024 pois ela foi excluída")
    private String erro;

    @ApiModelProperty(value = "Informações sobre aula experimental quando aplicável", 
                      example = "Você possui 2 aulas experimentais disponíveis")
    private String aulaExperimental;

    public AulaAlunoJSON getSucesso() {
        return sucesso;
    }

    public void setSucesso(AulaAlunoJSON sucesso) {
        this.sucesso = sucesso;
    }

    public String getErro() {
        return erro;
    }

    public void setErro(String erro) {
        this.erro = erro;
    }

    public String getAulaExperimental() {
        return aulaExperimental;
    }

    public void setAulaExperimental(String aulaExperimental) {
        this.aulaExperimental = aulaExperimental;
    }
}
