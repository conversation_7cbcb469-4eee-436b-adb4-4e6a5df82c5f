package br.com.pacto.swagger.respostas;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para operações que retornam texto ou URL")
public class ExemploRespostaString {

    @ApiModelProperty(value = "Metadados da resposta. Será null para este tipo de resposta.")
    private Object meta;

    @ApiModelProperty(value = "Conteúdo textual da resposta, podendo ser uma URL, link, mensagem de status ou resultado de operação",
                     example = "Operação realizada com sucesso")
    private String content;

    @ApiModelProperty(value = "Número total de elementos. Será null para este tipo de resposta.")
    private Long totalElements;

    @ApiModelProperty(value = "Número total de páginas. Será null para este tipo de resposta.")
    private Long totalPages;

    @ApiModelProperty(value = "Indica se é a primeira página. Será null para este tipo de resposta.")
    private Boolean first;

    @ApiModelProperty(value = "Indica se é a última página. Será null para este tipo de resposta.")
    private Boolean last;

    @ApiModelProperty(value = "Número de elementos na página atual. Será null para este tipo de resposta.")
    private Long numberOfElements;

    @ApiModelProperty(value = "Tamanho da página. Será null para este tipo de resposta.")
    private Long size;

    @ApiModelProperty(value = "Número da página atual. Será null para este tipo de resposta.")
    private Long number;

    // Getters e Setters
    public Object getMeta() {
        return meta;
    }

    public void setMeta(Object meta) {
        this.meta = meta;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Long getTotalElements() {
        return totalElements;
    }

    public void setTotalElements(Long totalElements) {
        this.totalElements = totalElements;
    }

    public Long getTotalPages() {
        return totalPages;
    }

    public void setTotalPages(Long totalPages) {
        this.totalPages = totalPages;
    }

    public Boolean getFirst() {
        return first;
    }

    public void setFirst(Boolean first) {
        this.first = first;
    }

    public Boolean getLast() {
        return last;
    }

    public void setLast(Boolean last) {
        this.last = last;
    }

    public Long getNumberOfElements() {
        return numberOfElements;
    }

    public void setNumberOfElements(Long numberOfElements) {
        this.numberOfElements = numberOfElements;
    }

    public Long getSize() {
        return size;
    }

    public void setSize(Long size) {
        this.size = size;
    }

    public Long getNumber() {
        return number;
    }

    public void setNumber(Long number) {
        this.number = number;
    }
}
