package br.com.pacto.swagger.respostas.grafico;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para remoção de view de gráfico BI")
public class ExemploRespostaRemocaoBIGraficoView {

    @ApiModelProperty(value = "Indica se a remoção foi realizada com sucesso", example = "true")
    private Boolean content;

    public Boolean getContent() {
        return content;
    }

    public void setContent(Boolean content) {
        this.content = content;
    }
}
