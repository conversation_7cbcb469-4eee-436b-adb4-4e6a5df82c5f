package br.com.pacto.swagger.respostas.locacao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para finalização de agendamento de locação")
public class ExemploRespostaFinalizarAgendamentoLocacao {

    @ApiModelProperty(value = "Indica se a finalização do agendamento requer redirecionamento para tela de venda", example = "false")
    private Boolean dados;

    public Boolean getDados() {
        return dados;
    }

    public void setDados(Boolean dados) {
        this.dados = dados;
    }
}
