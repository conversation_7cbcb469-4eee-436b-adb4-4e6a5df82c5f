package br.com.pacto.swagger.respostas.categoria.atividade;


import br.com.pacto.bean.atividade.CategoriaAtividadeResponseTO;
import br.com.pacto.swagger.respostas.ExemploPaginadorDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200")
public class ExemploRespostaListCategoriaAtividadeResponseTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as informações solicitadas")
    private List<CategoriaAtividadeResponseTO> content;

    public List<CategoriaAtividadeResponseTO> getContent() {
        return content;
    }

    public void setContent(List<CategoriaAtividadeResponseTO> content) {
        this.content = content;
    }
}
