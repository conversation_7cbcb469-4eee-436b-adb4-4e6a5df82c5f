package br.com.pacto.swagger.respostas.professor;

import br.com.pacto.controller.json.professor.ProfessorIndicadorResponseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para indicadores de atividade dos professores encapsulada em EnvelopeRespostaDTO")
public class ExemploRespostaIndicadoresAtividadeProfessores {

    @ApiModelProperty(value = "Lista contendo os indicadores de atividade dos professores")
    private List<ProfessorIndicadorResponseDTO> content;

    public List<ProfessorIndicadorResponseDTO> getContent() {
        return content;
    }

    public void setContent(List<ProfessorIndicadorResponseDTO> content) {
        this.content = content;
    }
}
