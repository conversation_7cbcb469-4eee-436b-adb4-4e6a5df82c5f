package br.com.pacto.swagger.respostas.programa;

import br.com.pacto.bean.ficha.FichaResponseTO;
import br.com.pacto.swagger.respostas.ExemploPaginadorDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para consulta de fichas pré-definidas")
public class ExemploRespostaFichasPreDefinidas extends ExemploPaginadorDTO {

    @ApiModelProperty(value = "Lista de fichas pré-definidas encontradas")
    private List<FichaResponseTO> content;

    public List<FichaResponseTO> getContent() {
        return content;
    }

    public void setContent(List<FichaResponseTO> content) {
        this.content = content;
    }
}
