package br.com.pacto.swagger.respostas.cliente;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import servicos.integracao.zw.json.ColetorJSON;

@ApiModel(description = "Representação da resposta de status 200 para consulta de local de acesso por NFC")
public class ExemploRespostaConsultarLocalAcessoPorNFC {

    @ApiModelProperty(value = "Informações do coletor de acesso encontrado através do código NFC")
    private ColetorJSON coletorJSON;


    public ColetorJSON getColetorJSON() {
        return coletorJSON;
    }

    public void setColetorJSON(ColetorJSON coletorJSON) {
        this.coletorJSON = coletorJSON;
    }

}
