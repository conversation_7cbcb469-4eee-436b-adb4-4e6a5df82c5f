package br.com.pacto.swagger.respostas.termoaceite;

import br.com.pacto.controller.json.termoaceite.TermoAceiteDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para consulta de um termo de aceite específico")
public class ExemploRespostaTermoAceiteDTO {
    @ApiModelProperty(value = "Dados do termo de aceite encontrado")
    private TermoAceiteDTO content;

    public TermoAceiteDTO getContent() {
        return content;
    }

    public void setContent(TermoAceiteDTO content) {
        this.content = content;
    }
}
