package br.com.pacto.swagger.respostas.locacao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para cancelamento de locação horário play encapsulada em EnvelopeRespostaDTO")
public class ExemploRespostaCancelarLocacaoHorarioPlay {

    @ApiModelProperty(value = "Resultado do cancelamento da locação play. Indica o status da operação realizada: " +
            "'locacaoHorarioCanceladoId' quando a locação é cancelada com sucesso, " +
            "'locacaoHorarioFinalizadaId' quando a locação é finalizada, ou " +
            "'locacaoHorarioNaoCancelado' quando já existe um cancelamento anterior para este horário.",
            example = "locacaoHorarioCanceladoId")
    private String content;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
