package br.com.pacto.swagger.respostas.turma;

import br.com.pacto.controller.json.turma.HorarioTurmaResponseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Exemplo de resposta para horário de turma individual")
public class ExemploRespostaHorarioTurmaResponseDTO {

    @ApiModelProperty(value = "Dados do horário da turma")
    private HorarioTurmaResponseDTO content;

    public HorarioTurmaResponseDTO getContent() {
        return content;
    }

    public void setContent(HorarioTurmaResponseDTO content) {
        this.content = content;
    }
}
