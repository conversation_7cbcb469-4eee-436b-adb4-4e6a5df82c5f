package br.com.pacto.swagger.respostas.log;


import br.com.pacto.bean.gympass.LogGymPassTO;
import br.com.pacto.swagger.respostas.ExemploPaginadorDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200")
public class ExemploRespostaListLogGymPassTO extends ExemploPaginadorDTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as informações solicitadas")
    private List<LogGymPassTO> content;

    public List<LogGymPassTO> getContent() {
        return content;
    }

    public void setContent(List<LogGymPassTO> content) {
        this.content = content;
    }
}
