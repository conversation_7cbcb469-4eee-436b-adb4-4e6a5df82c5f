package br.com.pacto.swagger.respostas.configuracao;

import br.com.pacto.controller.json.base.ConfiguracoesNotificacaoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para consulta de configurações de notificação")
public class ExemploRespostaConfiguracoesNotificacaoDTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as configurações de notificação do sistema")
    private ConfiguracoesNotificacaoDTO content;

    public ConfiguracoesNotificacaoDTO getContent() {
        return content;
    }

    public void setContent(ConfiguracoesNotificacaoDTO content) {
        this.content = content;
    }
}
