package br.com.pacto.swagger.respostas.gympass;

import br.com.pacto.controller.json.gympass.ConfigGymPassDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para consulta de configurações GymPass - pode retornar configuração individual ou lista de configurações")
public class ExemploRespostaListConfigGymPassDTO {

    @ApiModelProperty(value = "Conteúdo da resposta - pode ser um objeto ConfigGymPassDTO individual (quando parâmetro empresa é informado) ou uma lista de ConfigGymPassDTO (quando parâmetro empresa não é informado)")
    private List<ConfigGymPassDTO> content;

    public List<ConfigGymPassDTO> getContent() {
        return content;
    }

    public void setContent(List<ConfigGymPassDTO> content) {
        this.content = content;
    }
}
