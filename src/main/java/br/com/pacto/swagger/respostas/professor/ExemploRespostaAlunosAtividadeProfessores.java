package br.com.pacto.swagger.respostas.professor;

import br.com.pacto.controller.json.aluno.AlunoSimplesDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para alunos por indicadores de atividade dos professores encapsulada em EnvelopeRespostaDTO")
public class ExemploRespostaAlunosAtividadeProfessores {

    @ApiModelProperty(value = "Lista contendo os alunos relacionados aos indicadores de atividade dos professores")
    private List<AlunoSimplesDTO> content;

    public List<AlunoSimplesDTO> getContent() {
        return content;
    }

    public void setContent(List<AlunoSimplesDTO> content) {
        this.content = content;
    }
}
