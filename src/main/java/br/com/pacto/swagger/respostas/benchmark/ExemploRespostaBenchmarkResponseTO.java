package br.com.pacto.swagger.respostas.benchmark;

import br.com.pacto.controller.json.benchmark.BenchmarkResponseTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200")
public class ExemploRespostaBenchmarkResponseTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as informações solicitadas")
    private BenchmarkResponseTO content;

    public BenchmarkResponseTO getContent() {
        return content;
    }

    public void setContent(BenchmarkResponseTO content) {
        this.content = content;
    }
}
