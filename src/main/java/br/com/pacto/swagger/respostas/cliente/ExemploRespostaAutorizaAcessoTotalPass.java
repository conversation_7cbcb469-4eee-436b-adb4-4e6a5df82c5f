package br.com.pacto.swagger.respostas.cliente;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para autorização de acesso TotalPass")
public class ExemploRespostaAutorizaAcessoTotalPass extends EnvelopeRespostaDTO {

    @ApiModelProperty(value = "Mensagem de confirmação da autorização TotalPass", 
                     example = "Total Pass lançado com sucesso!")
    private String content;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
