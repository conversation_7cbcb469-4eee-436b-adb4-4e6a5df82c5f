package br.com.pacto.swagger.respostas.objetivos;

import br.com.pacto.bean.programa.ObjetivoPredefinidoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para operações com objetivo predefinido individual")
public class ExemploRespostaObjetivoPredefinidoDTO {

    @ApiModelProperty(value = "Dados do objetivo predefinido")
    private ObjetivoPredefinidoDTO content;

    public ObjetivoPredefinidoDTO getContent() {
        return content;
    }

    public void setContent(ObjetivoPredefinidoDTO content) {
        this.content = content;
    }
}
