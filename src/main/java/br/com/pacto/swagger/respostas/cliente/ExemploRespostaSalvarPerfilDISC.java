package br.com.pacto.swagger.respostas.cliente;

import br.com.pacto.controller.json.agendamento.PerfilDISCVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para salvamento do perfil DISC")
public class ExemploRespostaSalvarPerfilDISC {

    @ApiModelProperty(value = "Dados do perfil DISC salvo")
    private PerfilDISCVO content;

    public PerfilDISCVO getContent() {
        return content;
    }

    public void setContent(PerfilDISCVO content) {
        this.content = content;
    }
}
