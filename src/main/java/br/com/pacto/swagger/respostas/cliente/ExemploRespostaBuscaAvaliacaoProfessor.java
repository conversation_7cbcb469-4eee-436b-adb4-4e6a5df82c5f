package br.com.pacto.swagger.respostas.cliente;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.controller.json.aluno.AvaliacaoProfessorDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para busca de avaliações de professores")
public class ExemploRespostaBuscaAvaliacaoProfessor extends EnvelopeRespostaDTO {

    @ApiModelProperty(value = "Lista de avaliações de professores conforme os códigos especificados")
    private List<AvaliacaoProfessorDTO> content;

    public List<AvaliacaoProfessorDTO> getContent() {
        return content;
    }

    public void setContent(List<AvaliacaoProfessorDTO> content) {
        this.content = content;
    }
}
