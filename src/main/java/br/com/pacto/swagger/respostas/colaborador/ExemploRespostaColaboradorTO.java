package br.com.pacto.swagger.respostas.colaborador;

import br.com.pacto.bean.colaborador.ColaboradorResponseTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para operações de cadastro e atualização de colaborador encapsulada em EnvelopeRespostaDTO")
public class ExemploRespostaColaboradorTO {

    @ApiModelProperty(value = "Dados do colaborador após operação de cadastro ou atualização")
    private ColaboradorResponseTO content;

    public ColaboradorResponseTO getContent() {
        return content;
    }

    public void setContent(ColaboradorResponseTO content) {
        this.content = content;
    }
}
