package br.com.pacto.swagger.respostas.horario.turma;


import br.com.pacto.controller.json.turma.HorarioTurmaResponseDTO;
import br.com.pacto.swagger.respostas.ExemploPaginadorDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200")
public class ExemploRespostaRemoverHorarioTurma extends ExemploPaginadorDTO {

    @ApiModelProperty(value = "Conteú<PERSON> da resposta contendo as informações solicitadas", example = "sucesso")
    private String content;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
