package br.com.pacto.swagger.respostas.alunoturma;

import br.com.pacto.controller.json.agendamento.AgendamentoDisponibilidadePersonalDTO;
import br.com.pacto.enumerador.agenda.TipoAgendamentoEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;
import java.util.Map;

/**
 * Classe de exemplo para resposta do endpoint disponibilidadesAgrupadasPorComportamento
 * Representa a estrutura EnvelopeRespostaDTO retornada com mapa hierárquico de disponibilidades
 */
@ApiModel(description = "Resposta da consulta de disponibilidades agrupadas por comportamento")
public class ExemploRespostaDisponibilidadesAgrupadasPorComportamento {

    @ApiModelProperty(value = "Mapa hierárquico de disponibilidades organizadas primeiro por data e depois por tipo de agendamento. " +
                             "A estrutura é: data -> tipo de agendamento -> lista de disponibilidades. " +
                             "A chave principal representa a data no formato 'dd/MM/yyyy', o segundo nível agrupa por " +
                             "comportamento do serviço (CONTATO_INTERPESSOAL, PRESCRICAO_TREINO, REVISAO_TREINO, etc.) " +
                             "e o valor final contém as disponibilidades específicas para cada categoria.",
                      example = "{" +
                                "\"15/01/2024\": {" +
                                  "\"CONTATO_INTERPESSOAL\": [" +
                                    "{" +
                                      "\"id\": 1001," +
                                      "\"dia\": \"15/01/2024\"," +
                                      "\"horarioInicial\": \"14:00\"," +
                                      "\"horarioFinal\": \"15:00\"," +
                                      "\"status\": \"DISPONIVEL\"," +
                                      "\"professor\": {" +
                                        "\"id\": 25," +
                                        "\"codigoColaborador\": 25," +
                                        "\"nome\": \"João Silva\"," +
                                        "\"imageUri\": \"www.pactosolucoes.com.br/imagens/joao_silva.png\"," +
                                        "\"cref\": \"012345-G/GO\"" +
                                      "}," +
                                      "\"tipoAgendamento\": {" +
                                        "\"id\": 3," +
                                        "\"nome\": \"Contato Interpessoal\"," +
                                        "\"comportamento\": \"CONTATO_INTERPESSOAL\"," +
                                        "\"ativo\": true," +
                                        "\"cor\": \"#28a745\"" +
                                      "}," +
                                      "\"horarioDisponibilidadeCod\": 501" +
                                    "}" +
                                  "]," +
                                  "\"PRESCRICAO_TREINO\": [" +
                                    "{" +
                                      "\"id\": 1002," +
                                      "\"dia\": \"15/01/2024\"," +
                                      "\"horarioInicial\": \"16:00\"," +
                                      "\"horarioFinal\": \"17:00\"," +
                                      "\"status\": \"DISPONIVEL\"," +
                                      "\"professor\": {" +
                                        "\"id\": 26," +
                                        "\"codigoColaborador\": 26," +
                                        "\"nome\": \"Maria Santos\"," +
                                        "\"imageUri\": \"www.pactosolucoes.com.br/imagens/maria_santos.png\"," +
                                        "\"cref\": \"054321-G/GO\"" +
                                      "}," +
                                      "\"tipoAgendamento\": {" +
                                        "\"id\": 1," +
                                        "\"nome\": \"Prescrição de Treino\"," +
                                        "\"comportamento\": \"PRESCRICAO_TREINO\"," +
                                        "\"ativo\": true," +
                                        "\"cor\": \"#007bff\"" +
                                      "}," +
                                      "\"horarioDisponibilidadeCod\": 502" +
                                    "}" +
                                  "]" +
                                "}," +
                                "\"16/01/2024\": {" +
                                  "\"REVISAO_TREINO\": [" +
                                    "{" +
                                      "\"id\": 1003," +
                                      "\"dia\": \"16/01/2024\"," +
                                      "\"horarioInicial\": \"10:00\"," +
                                      "\"horarioFinal\": \"11:00\"," +
                                      "\"status\": \"DISPONIVEL\"," +
                                      "\"professor\": {" +
                                        "\"id\": 27," +
                                        "\"codigoColaborador\": 27," +
                                        "\"nome\": \"Carlos Oliveira\"," +
                                        "\"imageUri\": \"www.pactosolucoes.com.br/imagens/carlos_oliveira.png\"," +
                                        "\"cref\": \"098765-G/GO\"" +
                                      "}," +
                                      "\"tipoAgendamento\": {" +
                                        "\"id\": 2," +
                                        "\"nome\": \"Revisão de Treino\"," +
                                        "\"comportamento\": \"REVISAO_TREINO\"," +
                                        "\"ativo\": true," +
                                        "\"cor\": \"#fd7e14\"" +
                                      "}," +
                                      "\"horarioDisponibilidadeCod\": 503" +
                                    "}" +
                                  "]" +
                                "}" +
                              "}")
    private Object content;

    @ApiModelProperty(value = "Indica se a operação foi realizada com sucesso", example = "true")
    private Boolean sucesso;

    @ApiModelProperty(value = "Status da operação realizada", example = "OK")
    private String statusErro;

    public Object getContent() {
        return content;
    }

    public void setContent(Object content) {
        this.content = content;
    }

    public Boolean getSucesso() {
        return sucesso;
    }

    public void setSucesso(Boolean sucesso) {
        this.sucesso = sucesso;
    }

    public String getStatusErro() {
        return statusErro;
    }

    public void setStatusErro(String statusErro) {
        this.statusErro = statusErro;
    }
}
