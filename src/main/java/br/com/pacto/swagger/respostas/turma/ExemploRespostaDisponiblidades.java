package br.com.pacto.swagger.respostas.turma;


import br.com.pacto.controller.json.agendamento.ServicoAgendamentoDisponibilidadeDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;
import java.util.Map;

@ApiModel(description = "Representação da resposta de status 200")
public class ExemploRespostaDisponiblidades {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as informações solicitadas",
            example = "{\"2025-06-05_19:00-20:00\":[{\"id\":354,\"dia\":\"20250605\",\"horarioInicial\":\"19:00\",\"horarioFinal\":\"20:00\",\"professor\":{\"id\":1,\"codigoColaborador\":1,\"nome\":\"<PERSON>\",\"imageUri\":\"www.pactosolucoes.com.br/imagens/professor.png\",\"cref\":\"000000-G/GO\"},\"tipoAgendamento\":{\"id\":1,\"nome\":\"Avaliação Física\"},\"agendamentosConflitantes\":[]},{\"id\":355,\"dia\":\"20250605\",\"horarioInicial\":\"19:00\",\"horarioFinal\":\"20:00\",\"professor\":{\"id\":2,\"codigoColaborador\":2,\"nome\":\"Maria Santos\",\"imageUri\":\"www.pactosolucoes.com.br/imagens/professora.png\",\"cref\":\"111111-G/GO\"},\"tipoAgendamento\":{\"id\":2,\"nome\":\"Personal Training\"},\"agendamentosConflitantes\":[]}],\"2025-06-05_20:00-21:00\":[{\"id\":356,\"dia\":\"20250605\",\"horarioInicial\":\"20:00\",\"horarioFinal\":\"21:00\",\"professor\":{\"id\":3,\"codigoColaborador\":3,\"nome\":\"Carlos Oliveira\",\"imageUri\":\"www.pactosolucoes.com.br/imagens/professor2.png\",\"cref\":\"222222-G/GO\"},\"tipoAgendamento\":{\"id\":3,\"nome\":\"Crossfit WOD\"},\"agendamentosConflitantes\":[]},{\"id\":357,\"dia\":\"20250605\",\"horarioInicial\":\"20:00\",\"horarioFinal\":\"21:00\",\"professor\":{\"id\":4,\"codigoColaborador\":4,\"nome\":\"Ana Costa\",\"imageUri\":\"www.pactosolucoes.com.br/imagens/professora2.png\",\"cref\":\"333333-G/GO\"},\"tipoAgendamento\":{\"id\":4,\"nome\":\"Musculação\"},\"agendamentosConflitantes\":[]}],\"2025-06-06_07:00-08:00\":[{\"id\":358,\"dia\":\"20250606\",\"horarioInicial\":\"07:00\",\"horarioFinal\":\"08:00\",\"professor\":{\"id\":5,\"codigoColaborador\":5,\"nome\":\"Pedro Almeida\",\"imageUri\":\"www.pactosolucoes.com.br/imagens/professor3.png\",\"cref\":\"444444-G/GO\"},\"tipoAgendamento\":{\"id\":5,\"nome\":\"Funcional\"},\"agendamentosConflitantes\":[]},{\"id\":359,\"dia\":\"20250606\",\"horarioInicial\":\"07:00\",\"horarioFinal\":\"08:00\",\"professor\":{\"id\":6,\"codigoColaborador\":6,\"nome\":\"Lucia Ferreira\",\"imageUri\":\"www.pactosolucoes.com.br/imagens/professora3.png\",\"cref\":\"555555-G/GO\"},\"tipoAgendamento\":{\"id\":6,\"nome\":\"Yoga\"},\"agendamentosConflitantes\":[]}]}")
    private Map<String, List<ServicoAgendamentoDisponibilidadeDTO>> content;

    public Map<String, List<ServicoAgendamentoDisponibilidadeDTO>> getContent() {
        return content;
    }

    public void setContent(Map<String, List<ServicoAgendamentoDisponibilidadeDTO>> content) {
        this.content = content;
    }
}
