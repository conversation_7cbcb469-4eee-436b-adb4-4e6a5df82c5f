package br.com.pacto.swagger.respostas.empresa;

import br.com.pacto.bean.atividade.EmpresaBasicaResponseTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para lista de empresas básicas")
public class ExemploRespostaListEmpresaBasicaResponseTO {

    @ApiModelProperty(value = "Lista de empresas encontradas")
    private List<EmpresaBasicaResponseTO> content;

    public List<EmpresaBasicaResponseTO> getContent() {
        return content;
    }

    public void setContent(List<EmpresaBasicaResponseTO> content) {
        this.content = content;
    }
}
