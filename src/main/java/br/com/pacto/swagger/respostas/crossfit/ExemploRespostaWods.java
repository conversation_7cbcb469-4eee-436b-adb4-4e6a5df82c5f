package br.com.pacto.swagger.respostas.crossfit;

import br.com.pacto.bean.wod.Wod;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para consulta de WODs")
public class ExemploRespostaWods {

    @ApiModelProperty(value = "Lista de WODs (Workout of the Day)")
    private List<Wod> content;

    public List<Wod> getContent() {
        return content;
    }

    public void setContent(List<Wod> content) {
        this.content = content;
    }
}
