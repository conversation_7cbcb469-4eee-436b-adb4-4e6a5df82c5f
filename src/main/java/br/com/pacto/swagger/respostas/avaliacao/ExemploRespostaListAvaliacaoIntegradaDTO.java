package br.com.pacto.swagger.respostas.avaliacao;

import br.com.pacto.bean.avaliacao.AvaliacaoIntegradaDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para consulta de lista de avaliações integradas")
public class ExemploRespostaListAvaliacaoIntegradaDTO {

    @ApiModelProperty(value = "Lista contendo todas as avaliações integradas do aluno")
    private List<AvaliacaoIntegradaDTO> content;

    public List<AvaliacaoIntegradaDTO> getContent() {
        return content;
    }

    public void setContent(List<AvaliacaoIntegradaDTO> content) {
        this.content = content;
    }
}
