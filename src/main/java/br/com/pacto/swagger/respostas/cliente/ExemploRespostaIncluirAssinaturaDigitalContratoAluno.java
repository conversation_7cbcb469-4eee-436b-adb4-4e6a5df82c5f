package br.com.pacto.swagger.respostas.cliente;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para inclusão de assinatura digital em contrato do aluno")
public class ExemploRespostaIncluirAssinaturaDigitalContratoAluno {

    @ApiModelProperty(value = "Resultado da operação de inclusão da assinatura digital no contrato. Retorna uma mensagem de confirmação indicando o sucesso da operação, incluindo informações sobre o contrato assinado e dados de validação da assinatura digital.",
                     example = "Assinatura digital incluída com sucesso no contrato 12345. Data de assinatura: 2024-01-15 14:30:00. Hash de validação: a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6")
    private String RETURN;

    @ApiModelProperty(value = "Mensagem de erro caso ocorra algum problema durante a inclusão da assinatura digital no contrato",
                     example = "Erro ao processar assinatura digital: contrato não encontrado")
    private String STATUS_ERRO;

    public String getRETURN() {
        return RETURN;
    }

    public void setRETURN(String RETURN) {
        this.RETURN = RETURN;
    }

    public String getSTATUS_ERRO() {
        return STATUS_ERRO;
    }

    public void setSTATUS_ERRO(String STATUS_ERRO) {
        this.STATUS_ERRO = STATUS_ERRO;
    }
}
