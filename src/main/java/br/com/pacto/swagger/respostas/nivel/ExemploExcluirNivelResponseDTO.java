package br.com.pacto.swagger.respostas.nivel;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para exclusão de nível encapsulada em EnvelopeRespostaDTO")
public class ExemploExcluirNivelResponseDTO {

    @ApiModelProperty(value = "Indica se a exclusão foi realizada com sucesso", example = "true")
    private Boolean content;

    public Boolean getContent() {
        return content;
    }

    public void setContent(Boolean content) {
        this.content = content;
    }
}
