package br.com.pacto.swagger.respostas.configuracao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para atualização de dados de avaliação dos alunos")
public class ExemploRespostaAtualizarAlunosAvaliacao {

    @ApiModelProperty(value = "Status do processo de atualização dos dados de avaliação", 
                     example = "atualizado")
    private String processo;

    public String getProcesso() {
        return processo;
    }

    public void setProcesso(String processo) {
        this.processo = processo;
    }
}
