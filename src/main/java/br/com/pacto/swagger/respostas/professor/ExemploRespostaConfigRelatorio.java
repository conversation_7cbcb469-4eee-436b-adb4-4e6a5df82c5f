package br.com.pacto.swagger.respostas.professor;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para configuração de relatório encapsulada em EnvelopeRespostaDTO")
public class ExemploRespostaConfigRelatorio {

    @ApiModelProperty(value = "Configuração do relatório em formato JSON string contendo definições de colunas, filtros e ordenação", 
                      example = "{\"columns\":[\"nome\",\"email\",\"telefone\"], \"filters\":{\"ativo\":true}, \"sorting\":{\"field\":\"nome\",\"order\":\"asc\"}}")
    private String content;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
