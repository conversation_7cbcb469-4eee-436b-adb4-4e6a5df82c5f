package br.com.pacto.swagger.respostas.programa;

import br.com.pacto.bean.programa.ProgramaTreinoResponseTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para consulta de programa de treino individual")
public class ExemploRespostaProgramaTreinoResponseTO {

    @ApiModelProperty(value = "Dados do programa de treino consultado")
    private ProgramaTreinoResponseTO content;

    public ProgramaTreinoResponseTO getContent() {
        return content;
    }

    public void setContent(ProgramaTreinoResponseTO content) {
        this.content = content;
    }
}
