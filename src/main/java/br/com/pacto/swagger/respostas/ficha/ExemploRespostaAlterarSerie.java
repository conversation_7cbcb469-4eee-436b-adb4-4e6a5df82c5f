package br.com.pacto.swagger.respostas.ficha;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Classe de exemplo para resposta do endpoint alterarSerie
 * Representa a estrutura ModelMap retornada após alteração de uma série de exercício
 */
@ApiModel(description = "Resposta da alteração de série de exercício")
public class ExemploRespostaAlterarSerie {

    @ApiModelProperty(value = "Mensagem de confirmação da alteração da série de exercício", 
                     example = "Serie alterada com sucesso.")
    private String RETURN;

    @ApiModelProperty(value = "Mensagem de erro caso a alteração não seja possível", 
                     example = "Erro ao alterar série: dados inválidos")
    private String erro;

    public String getRETURN() {
        return RETURN;
    }

    public void setRETURN(String RETURN) {
        this.RETURN = RETURN;
    }

    public String getErro() {
        return erro;
    }

    public void setErro(String erro) {
        this.erro = erro;
    }
}
