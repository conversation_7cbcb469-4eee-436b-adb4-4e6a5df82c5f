package br.com.pacto.swagger.respostas.atividade;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Exemplo de resposta para recarregamento de cache")
public class ExemploRespostaReloadCache {

    @ApiModelProperty(value = "Mensagem de confirmação do recarregamento", example = "Cache reloaded!")
    private String statusSucesso;

    @ApiModelProperty(value = "Indica sucesso da operação", example = "true")
    private Boolean sucesso;

    @ApiModelProperty(value = "Mensagem de erro (quando aplicável)", example = "null")
    private String statusErro;
}
