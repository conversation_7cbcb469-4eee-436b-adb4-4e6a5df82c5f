package br.com.pacto.swagger.respostas.agendamento;

import br.com.pacto.controller.json.agendamento.AgendamentoJSON;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Resposta contendo agendamentos abertos e concluídos do aluno")
public class ExemploRespostaAgendamentosTodos {

    @ApiModelProperty(value = "Lista de agendamentos em status aberto (aguardando confirmação, confirmados ou reagendados)")
    private List<AgendamentoJSON> agendamentosAbertos;

    @ApiModelProperty(value = "Lista de agendamentos concluídos (executados, cancelados ou faltou)")
    private List<AgendamentoJSON> agendamentosConcluidos;

    @ApiModelProperty(value = "Mensagem de erro, presente apenas quando ocorre algum problema", example = "")
    private String erro;
}
