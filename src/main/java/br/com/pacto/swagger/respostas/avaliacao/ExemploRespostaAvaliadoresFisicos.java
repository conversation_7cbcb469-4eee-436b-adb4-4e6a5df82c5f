package br.com.pacto.swagger.respostas.avaliacao;

import br.com.pacto.controller.json.avaliacao.AvaliadorFisicoJSON;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * Classe de exemplo para resposta do endpoint avaliadoresFisicos
 * Representa a estrutura ModelMap retornada com a lista de avaliadores físicos disponíveis
 */
@ApiModel(description = "Resposta da consulta de avaliadores físicos disponíveis")
public class ExemploRespostaAvaliadoresFisicos {

    @ApiModelProperty(value = "Lista dos avaliadores físicos disponíveis para agendamento de avaliações na data informada. " +
                             "Cada avaliador contém informações básicas como código, nome, status ativo, empresa, avatar e " +
                             "a data do primeiro dia disponível para agendamento.")
    private List<AvaliadorFisicoJSON> RETURN;

    @ApiModelProperty(value = "Mensagem de erro caso ocorra algum problema durante a consulta dos avaliadores físicos. " +
                             "Este campo só estará presente em caso de falha na operação.",
                      example = "Erro ao consultar avaliadores físicos: Data inválida")
    private String STATUS_ERRO;

    // Getters e Setters
    public List<AvaliadorFisicoJSON> getRETURN() {
        return RETURN;
    }

    public void setRETURN(List<AvaliadorFisicoJSON> RETURN) {
        this.RETURN = RETURN;
    }

    public String getSTATUS_ERRO() {
        return STATUS_ERRO;
    }

    public void setSTATUS_ERRO(String STATUS_ERRO) {
        this.STATUS_ERRO = STATUS_ERRO;
    }
}
