package br.com.pacto.swagger.respostas.grafico;

import br.com.pacto.controller.json.gestao.BIGraficoResponseListaDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para listagem de views de gráfico BI disponíveis")
public class ExemploRespostaListaBIGraficoViews {

    @ApiModelProperty(value = "Lista de views de gráfico BI disponíveis, incluindo views padrão e personalizadas")
    private List<BIGraficoResponseListaDTO> content;

    public List<BIGraficoResponseListaDTO> getContent() {
        return content;
    }

    public void setContent(List<BIGraficoResponseListaDTO> content) {
        this.content = content;
    }
}
