package br.com.pacto.swagger.respostas.turma;


import br.com.pacto.controller.json.agendamento.AdicionarAlunoTurmaResponseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200")
public class ExemploRespostaAdicionarAlunoTurmaResponseDTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as informações solicitadas")
    private AdicionarAlunoTurmaResponseDTO content;

    public AdicionarAlunoTurmaResponseDTO getContent() {
        return content;
    }

    public void setContent(AdicionarAlunoTurmaResponseDTO content) {
        this.content = content;
    }
}
