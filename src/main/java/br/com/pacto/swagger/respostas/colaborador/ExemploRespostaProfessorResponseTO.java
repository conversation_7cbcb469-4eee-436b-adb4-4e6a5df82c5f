package br.com.pacto.swagger.respostas.colaborador;

import br.com.pacto.bean.programa.ProfessorResponseTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para consulta de colaborador por ID de usuário encapsulada em EnvelopeRespostaDTO")
public class ExemploRespostaProfessorResponseTO {

    @ApiModelProperty(value = "Dados do professor/colaborador consultado através do ID do usuário")
    private ProfessorResponseTO content;

    public ProfessorResponseTO getContent() {
        return content;
    }

    public void setContent(ProfessorResponseTO content) {
        this.content = content;
    }
}
