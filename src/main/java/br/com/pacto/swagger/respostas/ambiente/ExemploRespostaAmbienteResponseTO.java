package br.com.pacto.swagger.respostas.ambiente;


import br.com.pacto.controller.json.aluno.NivelAlunoResponseTO;
import br.com.pacto.controller.json.ambiente.AmbienteResponseTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200")
public class ExemploRespostaAmbienteResponseTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as informações solicitadas")
    private AmbienteResponseTO content;

    public AmbienteResponseTO getContent() {
        return content;
    }

    public void setContent(AmbienteResponseTO content) {
        this.content = content;
    }
}
