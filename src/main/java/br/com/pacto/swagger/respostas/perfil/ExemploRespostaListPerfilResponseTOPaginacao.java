package br.com.pacto.swagger.respostas.perfil;

import br.com.pacto.bean.perfil.PerfilResponseTO;
import br.com.pacto.swagger.respostas.ExemploPaginadorDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para listagem paginada de perfis de acesso")
public class ExemploRespostaListPerfilResponseTOPaginacao extends ExemploPaginadorDTO {

    @ApiModelProperty(value = "Lista contendo os perfis de acesso encontrados na página atual")
    private List<PerfilResponseTO> content;

    public List<PerfilResponseTO> getContent() {
        return content;
    }

    public void setContent(List<PerfilResponseTO> content) {
        this.content = content;
    }
}
