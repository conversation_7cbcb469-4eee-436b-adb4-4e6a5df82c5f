package br.com.pacto.swagger.respostas.aula.tv;


import br.com.pacto.controller.json.aulaDia.TvAulaDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200")
public class ExemploRespostaTVAulaDTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as informações solicitadas")
    private TvAulaDTO content;

    public TvAulaDTO getContent() {
        return content;
    }

    public void setContent(TvAulaDTO content) {
        this.content = content;
    }
}
