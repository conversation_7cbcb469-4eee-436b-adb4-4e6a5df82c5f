package br.com.pacto.swagger.respostas.niveis;


import br.com.pacto.controller.json.ambiente.NivelTurmaResponseTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200")
public class ExemploRespostaListNivelTurmaResponseTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as informações solicitadas")
    private List<NivelTurmaResponseTO> content;

    public List<NivelTurmaResponseTO> getContent() {
        return content;
    }

    public void setContent(List<NivelTurmaResponseTO> content) {
        this.content = content;
    }
}
