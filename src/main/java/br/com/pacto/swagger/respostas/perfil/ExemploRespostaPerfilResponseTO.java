package br.com.pacto.swagger.respostas.perfil;

import br.com.pacto.bean.perfil.PerfilResponseTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para operações com perfil de acesso encapsulada em EnvelopeRespostaDTO")
public class ExemploRespostaPerfilResponseTO {

    @ApiModelProperty(value = "Dados do perfil de acesso criado ou consultado")
    private PerfilResponseTO content;

    public PerfilResponseTO getContent() {
        return content;
    }

    public void setContent(PerfilResponseTO content) {
        this.content = content;
    }
}
