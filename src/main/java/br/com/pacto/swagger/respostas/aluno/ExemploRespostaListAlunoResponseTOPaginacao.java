package br.com.pacto.swagger.respostas.aluno;


import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.controller.json.aluno.AlunoResponseTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200")
public class ExemploRespostaListAlunoResponseTOPaginacao extends PaginadorDTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as informações solicitadas")
    private List<AlunoResponseTO> content;

    public List<AlunoResponseTO> getContent() {
        return content;
    }

    public void setContent(List<AlunoResponseTO> content) {
        this.content = content;
    }
}
