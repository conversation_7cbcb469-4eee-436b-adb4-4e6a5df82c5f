package br.com.pacto.swagger.respostas.sintetico;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para incremento de versão do cliente")
public class ExemploRespostaIncrementarVersao {

    @ApiModelProperty(value = "Resultado da operação de incremento de versão. Retorna 'OK' em caso de sucesso ou mensagem de erro iniciada com 'ERRO:' em caso de falha. A operação incrementa a versão do cliente no sistema e, opcionalmente, atualiza a foto do aluno no aplicativo se fornecida uma chave de foto válida.", 
                     example = "OK")
    private String content;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
