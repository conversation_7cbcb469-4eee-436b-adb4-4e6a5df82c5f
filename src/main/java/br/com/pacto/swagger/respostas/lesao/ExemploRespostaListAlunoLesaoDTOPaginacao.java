package br.com.pacto.swagger.respostas.lesao;

import br.com.pacto.bean.lesao.AlunoLesaoDTO;
import br.com.pacto.swagger.respostas.ExemploPaginadorDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representacao da resposta de status 200 para listagem paginada de lesoes de alunos para BI")
public class ExemploRespostaListAlunoLesaoDTOPaginacao extends ExemploPaginadorDTO {

    @ApiModelProperty(value = "Lista de lesoes de alunos para analise de Business Intelligence com paginacao")
    private List<AlunoLesaoDTO> content;

    public List<AlunoLesaoDTO> getContent() {
        return content;
    }

    public void setContent(List<AlunoLesaoDTO> content) {
        this.content = content;
    }
}
