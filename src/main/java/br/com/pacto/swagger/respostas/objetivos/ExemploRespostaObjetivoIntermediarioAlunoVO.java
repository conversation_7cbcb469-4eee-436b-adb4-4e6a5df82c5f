package br.com.pacto.swagger.respostas.objetivos;

import br.com.pacto.bean.programa.ObjetivoIntermediarioAlunoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para operações com objetivo intermediário individual do aluno encapsulada em EnvelopeRespostaDTO")
public class ExemploRespostaObjetivoIntermediarioAlunoVO {

    @ApiModelProperty(value = "Dados do objetivo intermediário do aluno")
    private ObjetivoIntermediarioAlunoVO content;

    public ObjetivoIntermediarioAlunoVO getContent() {
        return content;
    }

    public void setContent(ObjetivoIntermediarioAlunoVO content) {
        this.content = content;
    }
}
