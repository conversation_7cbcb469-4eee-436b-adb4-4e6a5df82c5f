package br.com.pacto.swagger.respostas.professor;

import br.com.pacto.controller.json.professor.InfoProfessorRankingDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para compartilhamento de detalhes do ranking de professor encapsulada em EnvelopeRespostaDTO")
public class ExemploRespostaCompartilharRankingProfessor {

    @ApiModelProperty(value = "Lista contendo os indicadores detalhados de desempenho do professor no ranking")
    private List<InfoProfessorRankingDTO> content;

    public List<InfoProfessorRankingDTO> getContent() {
        return content;
    }

    public void setContent(List<InfoProfessorRankingDTO> content) {
        this.content = content;
    }
}
