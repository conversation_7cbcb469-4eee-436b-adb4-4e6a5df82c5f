package br.com.pacto.swagger.respostas.configuracao;

import br.com.pacto.service.impl.log.LogTO;
import br.com.pacto.swagger.respostas.ExemploPaginadorDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para consulta de logs de configuração")
public class ExemploRespostaLogConfiguracao extends ExemploPaginadorDTO {

    @ApiModelProperty(value = "Lista de logs de configuração encontrados")
    private List<LogTO> content;

    public List<LogTO> getContent() {
        return content;
    }

    public void setContent(List<LogTO> content) {
        this.content = content;
    }
}
