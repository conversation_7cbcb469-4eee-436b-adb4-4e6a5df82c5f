package br.com.pacto.swagger.respostas.turma;

import br.com.pacto.controller.json.turma.TurmaResponseDTO;
import br.com.pacto.swagger.respostas.ExemploPaginadorDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Exemplo de resposta para lista paginada de turmas")
public class ExemploRespostaListTurmaResponseDTOPaginacao extends ExemploPaginadorDTO {

    @ApiModelProperty(value = "Lista de turmas encontradas")
    private List<TurmaResponseDTO> content;

    public List<TurmaResponseDTO> getContent() {
        return content;
    }

    public void setContent(List<TurmaResponseDTO> content) {
        this.content = content;
    }
}
