package br.com.pacto.swagger.respostas.atividade;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Exemplo de resposta para inserção de atividades padrão")
public class ExemploRespostaInserirAtividades {

    @ApiModelProperty(value = "Identificador da academia onde as atividades foram inseridas", example = "academia123")
    private String statusSucesso;

    @ApiModelProperty(value = "Indica sucesso da operação", example = "true")
    private Boolean sucesso;

    @ApiModelProperty(value = "Mensagem de erro (quando aplicável)", example = "null")
    private String statusErro;
}
