package br.com.pacto.swagger.respostas.programatreino;

import br.com.pacto.bean.programa.ProgramaTreinoResponseTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para consulta de programa de treino da franqueadora encapsulada em EnvelopeRespostaDTO")
public class ExemploRespostaProgramaTreinoResponseTO {

    @ApiModelProperty(value = "Dados completos do programa de treino consultado")
    private ProgramaTreinoResponseTO content;

    public ProgramaTreinoResponseTO getContent() {
        return content;
    }

    public void setContent(ProgramaTreinoResponseTO content) {
        this.content = content;
    }
}
