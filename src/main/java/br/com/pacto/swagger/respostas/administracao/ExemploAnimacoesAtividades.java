package br.com.pacto.swagger.respostas.administracao;

import java.util.ArrayList;
import java.util.List;

/**
 * Classe de exemplo para resposta do endpoint de animações de atividades
 */
public class ExemploAnimacoesAtividades {
    
    private List<AnimacaoExemplo> animacoes;
    
    public ExemploAnimacoesAtividades() {
        this.animacoes = new ArrayList<>();
        
        AnimacaoExemplo animacao1 = new AnimacaoExemplo();
        animacao1.setUrl("supino_reto.gif");
        animacao1.setAtividade("Supino Reto");
        
        AnimacaoExemplo animacao2 = new AnimacaoExemplo();
        animacao2.setUrl("agachamento_livre.gif");
        animacao2.setAtividade("Agachamento Livre");
        
        AnimacaoExemplo animacao3 = new AnimacaoExemplo();
        animacao3.setUrl("rosca_direta.gif");
        animacao3.setAtividade("Rosca Direta");
        
        AnimacaoExemplo animacao4 = new AnimacaoExemplo();
        animacao4.setUrl("leg_press.gif");
        animacao4.setAtividade("Leg Press");
        
        AnimacaoExemplo animacao5 = new AnimacaoExemplo();
        animacao5.setUrl("puxada_frontal.gif");
        animacao5.setAtividade("Puxada Frontal");
        
        this.animacoes.add(animacao1);
        this.animacoes.add(animacao2);
        this.animacoes.add(animacao3);
        this.animacoes.add(animacao4);
        this.animacoes.add(animacao5);
    }
    
    public List<AnimacaoExemplo> getAnimacoes() {
        return animacoes;
    }
    
    public void setAnimacoes(List<AnimacaoExemplo> animacoes) {
        this.animacoes = animacoes;
    }
    
    public static class AnimacaoExemplo {
        private String url;
        private String atividade;
        
        public String getUrl() {
            return url;
        }
        
        public void setUrl(String url) {
            this.url = url;
        }
        
        public String getAtividade() {
            return atividade;
        }
        
        public void setAtividade(String atividade) {
            this.atividade = atividade;
        }
    }
}
