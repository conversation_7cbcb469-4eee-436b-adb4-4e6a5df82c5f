package br.com.pacto.swagger.respostas.configuracao;

import br.com.pacto.controller.json.configuracao.ConfiguracaoSistemaJSON;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para consulta de configurações do sistema")
public class ExemploRespostaGetConfigs {

    @ApiModelProperty(value = "Objeto contendo todas as configurações do sistema para a empresa")
    private ConfiguracaoSistemaJSON RETURN;

    public ConfiguracaoSistemaJSON getRETURN() {
        return RETURN;
    }

    public void setRETURN(ConfiguracaoSistemaJSON RETURN) {
        this.RETURN = RETURN;
    }
}
