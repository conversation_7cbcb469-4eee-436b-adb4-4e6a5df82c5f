package br.com.pacto.swagger.respostas.configuracao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para atualização de instâncias")
public class ExemploRespostaUpdateInstances {

    @ApiModelProperty(value = "Mensagem de confirmação da atualização das instâncias", 
                     example = "Instâncias atualizadas com sucesso do cloud")
    private String sucesso;

    public String getSucesso() {
        return sucesso;
    }

    public void setSucesso(String sucesso) {
        this.sucesso = sucesso;
    }
}
