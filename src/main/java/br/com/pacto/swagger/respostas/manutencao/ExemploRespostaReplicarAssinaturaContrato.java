package br.com.pacto.swagger.respostas.manutencao;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Exemplo de resposta para replicação de assinatura de contrato para PAR-Q")
public class ExemploRespostaReplicarAssinaturaContrato extends EnvelopeRespostaDTO<List<String>> {

    @ApiModelProperty(value = "Lista de logs do processo de replicação de assinatura",
            example = "[\"#### ALUNOS COM ASSINATURA PARQ REPETIDA ###############################\", " +
                    "\"João <PERSON> - MAT: 12345 - SITUACAO: AT\", " +
                    "\"Maria <PERSON> - MAT: 12346 - SITUACAO: AT\", " +
                    "\"#### TOTAL: 2\", " +
                    "\"###################################################################################################\", " +
                    "\"ASSINATURA REPLICADA COM SUCESSO! - João Silva - MAT: 12345\", " +
                    "\"ASSINATURA REPLICADA COM SUCESSO! - Maria Santos - MAT: 12346\", " +
                    "\"#### TOTAL: 2\"]")
    private List<String> content;

    @Override
    public List<String> getContent() {
        return content;
    }

    @Override
    public void setContent(List<String> content) {
        this.content = content;
    }
}
