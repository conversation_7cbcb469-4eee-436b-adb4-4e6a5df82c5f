package br.com.pacto.swagger.respostas.coletor;


import br.com.pacto.controller.json.ambiente.ColetorResponseTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200")
public class ExemploRespostaListColetorResponseTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as informações solicitadas")
    private List<ColetorResponseTO> content;

    public List<ColetorResponseTO> getContent() {
        return content;
    }

    public void setContent(List<ColetorResponseTO> content) {
        this.content = content;
    }
}
