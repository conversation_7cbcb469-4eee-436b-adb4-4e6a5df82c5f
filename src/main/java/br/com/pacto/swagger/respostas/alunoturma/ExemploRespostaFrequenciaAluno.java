package br.com.pacto.swagger.respostas.alunoturma;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Classe de exemplo para resposta do endpoint consultarFrequenciaAluno
 * Representa a estrutura EnvelopeRespostaDTO retornada com dados de frequência de um aluno específico
 */
@ApiModel(description = "Resposta da consulta de frequência de treino e atendimento de um aluno específico")
public class ExemploRespostaFrequenciaAluno {

    @ApiModelProperty(value = "Dados detalhados de frequência e atendimento do aluno no período especificado. " +
                             "Inclui estatísticas de presença, faltas, agendamentos realizados, cancelamentos, " +
                             "percentual de assiduidade, média de treinos por semana e outras métricas de " +
                             "engajamento individual do aluno.")
    private Object content;

    @ApiModelProperty(value = "Indica se a operação foi realizada com sucesso", example = "true")
    private Boolean sucesso;

    @ApiModelProperty(value = "Status da operação realizada", example = "OK")
    private String statusErro;

    public Object getContent() {
        return content;
    }

    public void setContent(Object content) {
        this.content = content;
    }

    public Boolean getSucesso() {
        return sucesso;
    }

    public void setSucesso(Boolean sucesso) {
        this.sucesso = sucesso;
    }

    public String getStatusErro() {
        return statusErro;
    }

    public void setStatusErro(String statusErro) {
        this.statusErro = statusErro;
    }
}
