package br.com.pacto.swagger.respostas.eventoUsuario;

import br.com.pacto.bean.eventoUsuario.EventoUsuario;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para atualização de evento de usuário")
public class ExemploRespostaEventoUsuario {

    @ApiModelProperty(value = "Evento de usuário atualizado")
    private EventoUsuario content;

    public ExemploRespostaEventoUsuario() {
        this.content = new EventoUsuario("EVT001", "Evento de Treinamento Especial Atualizado");
    }

    public EventoUsuario getContent() {
        return content;
    }

    public void setContent(EventoUsuario content) {
        this.content = content;
    }
}
