package br.com.pacto.swagger.respostas.gympass;

import br.com.pacto.controller.json.gympass.ConfigGymPassDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para consulta de configuração GymPass individual")
public class ExemploRespostaConfigGymPassDTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo os dados da configuração GymPass")
    private ConfigGymPassDTO content;

    public ConfigGymPassDTO getContent() {
        return content;
    }

    public void setContent(ConfigGymPassDTO content) {
        this.content = content;
    }
}
