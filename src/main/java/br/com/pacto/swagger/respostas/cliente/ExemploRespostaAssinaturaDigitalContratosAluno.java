package br.com.pacto.swagger.respostas.cliente;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para consulta de assinaturas digitais de contratos do aluno")
public class ExemploRespostaAssinaturaDigitalContratosAluno {

    @ApiModelProperty(value = "Dados das assinaturas digitais dos contratos do aluno em formato JSON. Contém informações sobre os contratos assinados digitalmente, incluindo códigos dos contratos, datas de assinatura, status de validação e dados das assinaturas. Retorna array vazio '[]' se não houver contratos com assinatura digital ou se a funcionalidade estiver desabilitada.",
                     example = "[{\"codigoContrato\":\"CONT2024001\",\"dataAssinatura\":\"2024-01-15T10:30:00Z\",\"statusValidacao\":\"VALIDA\",\"tipoContrato\":\"MATRICULA\",\"assinaturaBase64\":\"iVBORw0KGgoAAAANSUhEUgAA...\",\"nomeAluno\":\"João Silva Santos\",\"cpfAluno\":\"123.456.789-00\"},{\"codigoContrato\":\"CONT2024002\",\"dataAssinatura\":\"2024-06-10T14:20:00Z\",\"statusValidacao\":\"VALIDA\",\"tipoContrato\":\"RENOVACAO\",\"assinaturaBase64\":\"iVBORw0KGgoAAAANSUhEUgBB...\",\"nomeAluno\":\"João Silva Santos\",\"cpfAluno\":\"123.456.789-00\"}]")
    private String RETURN;

    @ApiModelProperty(value = "Mensagem de erro caso ocorra algum problema na consulta das assinaturas digitais",
                     example = "Ocorreu um erro ao buscar contratos da matricula: 12345")
    private String STATUS_ERRO;

    public String getRETURN() {
        return RETURN;
    }

    public void setRETURN(String RETURN) {
        this.RETURN = RETURN;
    }

    public String getSTATUS_ERRO() {
        return STATUS_ERRO;
    }

    public void setSTATUS_ERRO(String STATUS_ERRO) {
        this.STATUS_ERRO = STATUS_ERRO;
    }
}
