package br.com.pacto.swagger.respostas.nivel;

import br.com.pacto.bean.nivel.NivelResponseTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para alteração de nível encapsulada em EnvelopeRespostaDTO")
public class ExemploAlterarNivelResponseDTO {

    @ApiModelProperty(value = "Dados do nível alterado com sucesso")
    private NivelResponseTO content;

    public NivelResponseTO getContent() {
        return content;
    }

    public void setContent(NivelResponseTO content) {
        this.content = content;
    }
}
