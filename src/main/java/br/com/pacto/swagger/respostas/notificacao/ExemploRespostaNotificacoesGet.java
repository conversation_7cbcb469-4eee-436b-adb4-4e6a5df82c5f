package br.com.pacto.swagger.respostas.notificacao;

import br.com.pacto.controller.json.notificacao.NotificacaoJSON;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para consulta de notificações do usuário")
public class ExemploRespostaNotificacoesGet {

    @ApiModelProperty(value = "Lista de notificações da academia direcionadas ao aluno")
    private List<NotificacaoJSON> academia;

    @ApiModelProperty(value = "Lista de notificações do professor direcionadas ao aluno (sempre vazia neste endpoint)")
    private List<NotificacaoJSON> professor;

    public List<NotificacaoJSON> getAcademia() {
        return academia;
    }

    public void setAcademia(List<NotificacaoJSON> academia) {
        this.academia = academia;
    }

    public List<NotificacaoJSON> getProfessor() {
        return professor;
    }

    public void setProfessor(List<NotificacaoJSON> professor) {
        this.professor = professor;
    }
}
