package br.com.pacto.swagger.respostas.avaliacao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;

@ApiModel(description = "Conteúdo específico da resposta contendo os dados dos questionários PAR-Q")
public class ExemploConteudoParQDTO {

    @ApiModelProperty(value = "Lista de questionários PAR-Q do aluno")
    private List<ExemploParQAlunoDTO> alunos;

    @ApiModelProperty(value = "Número total de elementos encontrados na consulta", example = "25")
    private Long totalElementos;

    @ApiModelProperty(value = "Tamanho da página solicitada", example = "10")
    private Long size;

    @ApiModelProperty(value = "Número da página atual (baseado em zero)", example = "0")
    private Long page;

    @ApiModelProperty(value = "Status da operação realizada", example = "sucesso")
    private String status;

    // Getters e Setters
    public List<ExemploParQAlunoDTO> getAlunos() {
        return alunos;
    }

    public void setAlunos(List<ExemploParQAlunoDTO> alunos) {
        this.alunos = alunos;
    }

    public Long getTotalElementos() {
        return totalElementos;
    }

    public void setTotalElementos(Long totalElementos) {
        this.totalElementos = totalElementos;
    }

    public Long getSize() {
        return size;
    }

    public void setSize(Long size) {
        this.size = size;
    }

    public Long getPage() {
        return page;
    }

    public void setPage(Long page) {
        this.page = page;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
