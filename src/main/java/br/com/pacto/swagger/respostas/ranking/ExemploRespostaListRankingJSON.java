package br.com.pacto.swagger.respostas.ranking;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.controller.json.crossfit.RankingJSON;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * Classe de exemplo para resposta do endpoint de ranking de alunos por WOD
 */
@ApiModel(description = "Exemplo de resposta para consulta de ranking de alunos por WOD")
public class ExemploRespostaListRankingJSON {

    @ApiModelProperty(value = "Lista com o ranking dos alunos no WOD")
    private List<RankingJSON> content;

    public List<RankingJSON> getContent() {
        return content;
    }

    public void setContent(List<RankingJSON> content) {
        this.content = content;
    }
}
