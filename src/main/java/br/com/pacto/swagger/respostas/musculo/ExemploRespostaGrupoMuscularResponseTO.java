package br.com.pacto.swagger.respostas.musculo;

import br.com.pacto.bean.musculo.GrupoMuscularResponseTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Exemplo de resposta para um grupo muscular específico")
public class ExemploRespostaGrupoMuscularResponseTO {

    @ApiModelProperty(value = "Dados do grupo muscular")
    private GrupoMuscularResponseTO content;

    public GrupoMuscularResponseTO getContent() {
        return content;
    }

    public void setContent(GrupoMuscularResponseTO content) {
        this.content = content;
    }
}
