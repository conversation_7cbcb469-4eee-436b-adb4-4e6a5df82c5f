package br.com.pacto.swagger.respostas.disponibilidade;

import br.com.pacto.controller.json.disponibilidade.DisponibilidadeDTO;
import br.com.pacto.swagger.respostas.ExemploPaginadorDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para lista de disponibilidades")
public class ExemploRespostaListaDisponibilidades extends ExemploPaginadorDTO {

    @ApiModelProperty(value = "Lista de disponibilidades encontradas")
    private List<DisponibilidadeDTO> content;

    public List<DisponibilidadeDTO> getContent() {
        return content;
    }

    public void setContent(List<DisponibilidadeDTO> content) {
        this.content = content;
    }

}
