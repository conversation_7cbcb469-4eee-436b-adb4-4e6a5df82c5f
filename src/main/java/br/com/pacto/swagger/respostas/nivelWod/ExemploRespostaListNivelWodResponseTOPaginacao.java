package br.com.pacto.swagger.respostas.nivelWod;

import br.com.pacto.bean.wod.NivelWodResponseTO;
import br.com.pacto.swagger.respostas.ExemploPaginadorDTO;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * Classe de exemplo para resposta paginada de lista de níveis WOD
 */
public class ExemploRespostaListNivelWodResponseTOPaginacao extends ExemploPaginadorDTO {

    @ApiModelProperty(value = "Lista de níveis WOD")
    private List<NivelWodResponseTO> content;

    public List<NivelWodResponseTO> getContent() {
        return content;
    }

    public void setContent(List<NivelWodResponseTO> content) {
        this.content = content;
    }
}
