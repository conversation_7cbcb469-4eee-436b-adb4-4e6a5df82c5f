package br.com.pacto.swagger.respostas.aluno.perfil;

import br.com.pacto.service.impl.cliente.perfil.ProgramaAtualDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para consulta do programa atual do aluno")
public class ExemploRespostaProgramaAtualDTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as informações do programa atual do aluno")
    private ProgramaAtualDTO content;

    public ProgramaAtualDTO getContent() {
        return content;
    }

    public void setContent(ProgramaAtualDTO content) {
        this.content = content;
    }
}
