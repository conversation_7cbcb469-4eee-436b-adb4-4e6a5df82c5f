package br.com.pacto.swagger.respostas.gympass;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@ApiModel(description = "Representação da resposta de status 200 para consulta de últimas sincronizações")
public class ExemploRespostaListHorariosSincronizados {

    @ApiModelProperty(value = "Lista de horários sincronizados recentemente")
    private List<Map<String, Object>> content;

    public List<Map<String, Object>> getContent() {
        return content;
    }

    public void setContent(List<Map<String, Object>> content) {
        this.content = content;
    }
}
