package br.com.pacto.swagger.respostas.cliente;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para sincronização de matrículas de clientes sintéticos")
public class ExemploRespostaSincronizarMatriculaClienteSintetico {

    @ApiModelProperty(value = "Resultado da operação de sincronização de matrículas. Retorna uma mensagem informando quantos alunos foram verificados e quantos precisaram ter suas matrículas sincronizadas. Se todas as matrículas já estiverem corretas, retorna mensagem informativa.",
                     example = "150 alunos verificados e 3 alunos que precisaram sincronizar a matrícula!")
    private String RETURN;

    @ApiModelProperty(value = "Mensagem de erro caso ocorra algum problema na sincronização das matrículas",
                     example = "Erro ao sincronizar matrícula dos alunos!")
    private String STATUS_ERRO;

    public String getRETURN() {
        return RETURN;
    }

    public void setRETURN(String RETURN) {
        this.RETURN = RETURN;
    }

    public String getSTATUS_ERRO() {
        return STATUS_ERRO;
    }

    public void setSTATUS_ERRO(String STATUS_ERRO) {
        this.STATUS_ERRO = STATUS_ERRO;
    }
}
