package br.com.pacto.swagger.respostas.musculo;

import br.com.pacto.bean.musculo.MusculoResponseTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Exemplo de resposta para lista de músculos")
public class ExemploRespostaListMusculoResponseTO {

    @ApiModelProperty(value = "Lista de músculos cadastrados no sistema")
    private List<MusculoResponseTO> content;

    public List<MusculoResponseTO> getContent() {
        return content;
    }

    public void setContent(List<MusculoResponseTO> content) {
        this.content = content;
    }
}
