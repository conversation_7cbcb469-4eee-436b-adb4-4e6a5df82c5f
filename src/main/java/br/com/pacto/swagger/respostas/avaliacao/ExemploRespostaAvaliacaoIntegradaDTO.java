package br.com.pacto.swagger.respostas.avaliacao;

import br.com.pacto.bean.avaliacao.AvaliacaoIntegradaDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para consulta de avaliação integrada")
public class ExemploRespostaAvaliacaoIntegradaDTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo os dados completos da avaliação integrada")
    private AvaliacaoIntegradaDTO content;

    public AvaliacaoIntegradaDTO getContent() {
        return content;
    }

    public void setContent(AvaliacaoIntegradaDTO content) {
        this.content = content;
    }
}
