package br.com.pacto.swagger.respostas.niveis;


import br.com.pacto.controller.json.aluno.NivelAlunoResponseTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200")
public class ExemploRespostaListNivelAlunoResponseDTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as informações solicitadas")
    private List<NivelAlunoResponseTO> content;

    public List<NivelAlunoResponseTO> getContent() {
        return content;
    }

    public void setContent(List<NivelAlunoResponseTO> content) {
        this.content = content;
    }
}
