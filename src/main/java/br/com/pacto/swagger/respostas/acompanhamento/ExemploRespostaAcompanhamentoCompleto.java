package br.com.pacto.swagger.respostas.acompanhamento;

import br.com.pacto.controller.json.acompanhamento.AcompanhamentoCompletoJSON;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Classe de exemplo para resposta do endpoint de acompanhamento completo de treino
 * Utilizada para documentação automática do Swagger
 */
@ApiModel(description = "Exemplo de resposta para consulta de acompanhamento completo de treino do aluno")
public class ExemploRespostaAcompanhamentoCompleto {

    @ApiModelProperty(value = "Dados completos do acompanhamento de treino do aluno com estatísticas e desempenho")
    private AcompanhamentoCompletoJSON acompanhamento;

    @ApiModelProperty(value = "Mensagem de erro caso ocorra algum problema durante a consulta. " +
            "Este campo só estará presente em caso de falha na operação e conterá detalhes sobre o erro ocorrido.", 
            example = "Usuário não encontrado")
    private String STATUS_ERRO;

    // Getters e Setters
    public AcompanhamentoCompletoJSON getAcompanhamento() {
        return acompanhamento;
    }

    public void setAcompanhamento(AcompanhamentoCompletoJSON acompanhamento) {
        this.acompanhamento = acompanhamento;
    }

    public String getSTATUS_ERRO() {
        return STATUS_ERRO;
    }

    public void setSTATUS_ERRO(String STATUS_ERRO) {
        this.STATUS_ERRO = STATUS_ERRO;
    }
}
