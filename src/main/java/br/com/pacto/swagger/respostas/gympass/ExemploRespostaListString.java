package br.com.pacto.swagger.respostas.gympass;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Arrays;
import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para obtenção de aluno por token GymPass")
public class ExemploRespostaListString {

    @ApiModelProperty(value = "Lista de informações do aluno obtido pelo token GymPass")
    private List<String> content;

    public List<String> getContent() {
        return content;
    }

    public void setContent(List<String> content) {
        this.content = content;
    }
}
