package br.com.pacto.swagger.respostas.programa;

import br.com.pacto.bean.ficha.CategoriaFichaResponseTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200")
public class ExemploRespostaCategoriaFichaResponseTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as informações da categoria de ficha")
    private CategoriaFichaResponseTO content;

    public CategoriaFichaResponseTO getContent() {
        return content;
    }

    public void setContent(CategoriaFichaResponseTO content) {
        this.content = content;
    }
}
