package br.com.pacto.swagger.respostas.administracao;

import java.util.ArrayList;
import java.util.List;

/**
 * Classe de exemplo para resposta do endpoint de fichas predefinidas
 */
public class ExemploFichasPredefinidas {
    
    private List<FichaExemplo> fichas;
    private List<AtividadeExemplo> atividades;
    
    public ExemploFichasPredefinidas() {
        this.fichas = new ArrayList<>();
        this.atividades = new ArrayList<>();
        
        // Fichas de exemplo
        FichaExemplo ficha1 = new FichaExemplo();
        ficha1.setCod(1);
        ficha1.setVersao("1.0");
        ficha1.setNome("Ficha Iniciante - Membros Superiores");
        ficha1.setCodPrograma("PROG001");
        ficha1.setMensagemAluno("Ficha focada no desenvolvimento dos membros superiores para iniciantes");
        ficha1.setCategoria("Musculação");
        ficha1.setTipoExecucao(1);
        
        FichaExemplo ficha2 = new FichaExemplo();
        ficha2.setCod(2);
        ficha2.setVersao("1.0");
        ficha2.setNome("Ficha Intermediário - Membros Inferiores");
        ficha2.setCodPrograma("PROG002");
        ficha2.setMensagemAluno("Ficha para desenvolvimento dos membros inferiores - nível intermediário");
        ficha2.setCategoria("Musculação");
        ficha2.setTipoExecucao(2);
        
        this.fichas.add(ficha1);
        this.fichas.add(ficha2);
        
        // Atividades de exemplo
        AtividadeExemplo atividade1 = new AtividadeExemplo();
        atividade1.setCod(101);
        atividade1.setNome("Supino Reto");
        atividade1.setDescricao("Exercício para desenvolvimento do peitoral");
        atividade1.setTipo(1);
        atividade1.setAtivo(true);
        
        AtividadeExemplo atividade2 = new AtividadeExemplo();
        atividade2.setCod(102);
        atividade2.setNome("Agachamento Livre");
        atividade2.setDescricao("Exercício para desenvolvimento das pernas");
        atividade2.setTipo(1);
        atividade2.setAtivo(true);
        
        this.atividades.add(atividade1);
        this.atividades.add(atividade2);
    }
    
    public List<FichaExemplo> getFichas() {
        return fichas;
    }
    
    public void setFichas(List<FichaExemplo> fichas) {
        this.fichas = fichas;
    }
    
    public List<AtividadeExemplo> getAtividades() {
        return atividades;
    }
    
    public void setAtividades(List<AtividadeExemplo> atividades) {
        this.atividades = atividades;
    }
    
    public static class FichaExemplo {
        private Integer cod;
        private String versao;
        private String nome;
        private String codPrograma;
        private String mensagemAluno;
        private String categoria;
        private Integer tipoExecucao;
        
        // Getters e Setters
        public Integer getCod() { return cod; }
        public void setCod(Integer cod) { this.cod = cod; }
        
        public String getVersao() { return versao; }
        public void setVersao(String versao) { this.versao = versao; }
        
        public String getNome() { return nome; }
        public void setNome(String nome) { this.nome = nome; }
        
        public String getCodPrograma() { return codPrograma; }
        public void setCodPrograma(String codPrograma) { this.codPrograma = codPrograma; }
        
        public String getMensagemAluno() { return mensagemAluno; }
        public void setMensagemAluno(String mensagemAluno) { this.mensagemAluno = mensagemAluno; }
        
        public String getCategoria() { return categoria; }
        public void setCategoria(String categoria) { this.categoria = categoria; }
        
        public Integer getTipoExecucao() { return tipoExecucao; }
        public void setTipoExecucao(Integer tipoExecucao) { this.tipoExecucao = tipoExecucao; }
    }
    
    public static class AtividadeExemplo {
        private Integer cod;
        private String nome;
        private String descricao;
        private Integer tipo;
        private Boolean ativo;
        
        // Getters e Setters
        public Integer getCod() { return cod; }
        public void setCod(Integer cod) { this.cod = cod; }
        
        public String getNome() { return nome; }
        public void setNome(String nome) { this.nome = nome; }
        
        public String getDescricao() { return descricao; }
        public void setDescricao(String descricao) { this.descricao = descricao; }
        
        public Integer getTipo() { return tipo; }
        public void setTipo(Integer tipo) { this.tipo = tipo; }
        
        public Boolean getAtivo() { return ativo; }
        public void setAtivo(Boolean ativo) { this.ativo = ativo; }
    }
}
