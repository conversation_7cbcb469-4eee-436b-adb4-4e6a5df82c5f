package br.com.pacto.swagger.respostas.treino;

import br.com.pacto.controller.json.gestao.BITreinoTreinamentoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200")
public class ExemploRespostaBITreinoTreinamentoDTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as informações solicitadas")
    private BITreinoTreinamentoDTO content;

    public BITreinoTreinamentoDTO getContent() {
        return content;
    }

    public void setContent(BITreinoTreinamentoDTO content) {
        this.content = content;
    }
}
