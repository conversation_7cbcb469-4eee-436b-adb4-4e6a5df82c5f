package br.com.pacto.swagger.respostas.configuracao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para operações de configuração bem-sucedidas")
public class ExemploRespostaConfiguracaoSucesso {

    @ApiModelProperty(value = "Mensagem de sucesso da operação", example = "Salvo com sucesso!")
    private String content;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
