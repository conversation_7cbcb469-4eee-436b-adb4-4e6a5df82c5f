package br.com.pacto.swagger.respostas.atividade.crossfit;


import br.com.pacto.controller.json.atividade.AtividadeCrossfitResponseDTO;
import br.com.pacto.swagger.respostas.ExemploPaginadorDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200")
public class ExemploRespostaListAtividadeCrossfitResponseDTO extends ExemploPaginadorDTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as informações solicitadas")
    private List<AtividadeCrossfitResponseDTO> content;

    public List<AtividadeCrossfitResponseDTO> getContent() {
        return content;
    }

    public void setContent(List<AtividadeCrossfitResponseDTO> content) {
        this.content = content;
    }
}
