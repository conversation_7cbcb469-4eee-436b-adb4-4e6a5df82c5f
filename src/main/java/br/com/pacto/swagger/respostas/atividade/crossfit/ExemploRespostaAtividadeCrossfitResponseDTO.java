package br.com.pacto.swagger.respostas.atividade.crossfit;


import br.com.pacto.controller.json.atividade.AtividadeCrossfitResponseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200")
public class ExemploRespostaAtividadeCrossfitResponseDTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as informações solicitadas")
    private AtividadeCrossfitResponseDTO content;

    public AtividadeCrossfitResponseDTO getContent() {
        return content;
    }

    public void setContent(AtividadeCrossfitResponseDTO content) {
        this.content = content;
    }
}
