package br.com.pacto.swagger.respostas.colaborador;

import br.com.pacto.bean.programa.ProfessorResponseTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Arrays;
import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para consulta de lista de professores encapsulada")
public class ExemploRespostaListProfessorResponseTO {

    @ApiModelProperty(value = "Lista contendo dados dos professores da empresa com informações completas")
    private List<ProfessorResponseTO> content;

    public List<ProfessorResponseTO> getContent() {
        return content;
    }

    public void setContent(List<ProfessorResponseTO> content) {
        this.content = content;
    }
}
