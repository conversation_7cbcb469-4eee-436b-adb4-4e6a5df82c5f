package br.com.pacto.swagger.respostas.configuracao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para sincronização de professores com sistema TW")
public class ExemploRespostaSincronizarProfessorTW {

    @ApiModelProperty(value = "Resultado da sincronização de professores", example = "Sincronização de professores realizada com sucesso. 15 professores sincronizados.")
    private String content;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
