package br.com.pacto.swagger.respostas.aluno.codigo;


import br.com.pacto.base.dto.PaginadorDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200")
public class ExemploRespostaCodigoAluno extends PaginadorDTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as informações solicitadas")
    private ExemploCodigoAluno content;

    public ExemploCodigoAluno getContent() {
        return content;
    }

    public void setContent(ExemploCodigoAluno content) {
        this.content = content;
    }
}
