package br.com.pacto.swagger.respostas.administracao;

import java.util.HashMap;
import java.util.Map;

/**
 * Classe de exemplo para resposta do endpoint de listagem de endpoints mapeados
 */
public class ExemploEndpointsMapeados {
    
    private Map<String, String> endpoints;
    
    public ExemploEndpointsMapeados() {
        this.endpoints = new HashMap<>();
        this.endpoints.put("public org.springframework.ui.ModelMap br.com.pacto.controller.json.base.EndpointDocController.mostrar()", "[/EndpointControl/show]");
        this.endpoints.put("public org.springframework.ui.ModelMap br.com.pacto.controller.json.base.EndpointDocController.empresas()", "[/EndpointControl/empresas]");
        this.endpoints.put("public org.springframework.ui.ModelMap br.com.pacto.controller.json.base.EndpointDocController.soundex(java.lang.String,java.lang.String)", "[/EndpointControl/{ctx}/soundex]");
        this.endpoints.put("public org.springframework.ui.ModelMap br.com.pacto.controller.json.cliente.ClienteJSONControle.obterAlunos(java.lang.String,org.json.JSONObject,br.com.pacto.util.PaginadorDTO)", "[/Cliente/{ctx}/obterAlunos]");
        this.endpoints.put("public org.springframework.ui.ModelMap br.com.pacto.controller.json.login.LoginController.autenticar(java.lang.String,java.lang.String,java.lang.String)", "[/Login/{ctx}/autenticar]");
    }
    
    public Map<String, String> getEndpoints() {
        return endpoints;
    }
    
    public void setEndpoints(Map<String, String> endpoints) {
        this.endpoints = endpoints;
    }
}
