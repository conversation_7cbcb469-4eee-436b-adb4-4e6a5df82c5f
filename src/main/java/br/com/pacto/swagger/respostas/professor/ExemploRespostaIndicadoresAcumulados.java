package br.com.pacto.swagger.respostas.professor;

import br.com.pacto.bean.professor.IndicadorAtividadeProfessorAcumuladoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para indicadores acumulados de atividade dos professores encapsulada em EnvelopeRespostaDTO")
public class ExemploRespostaIndicadoresAcumulados {

    @ApiModelProperty(value = "Dados dos indicadores acumulados de atividade dos professores")
    private IndicadorAtividadeProfessorAcumuladoVO content;

    public IndicadorAtividadeProfessorAcumuladoVO getContent() {
        return content;
    }

    public void setContent(IndicadorAtividadeProfessorAcumuladoVO content) {
        this.content = content;
    }
}
