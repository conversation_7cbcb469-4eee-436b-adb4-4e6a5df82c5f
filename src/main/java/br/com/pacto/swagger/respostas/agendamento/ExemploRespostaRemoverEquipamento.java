package br.com.pacto.swagger.respostas.agendamento;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Resposta de sucesso para remoção de equipamento do horário do aluno")
public class ExemploRespostaRemoverEquipamento {

    @ApiModelProperty(value = "Mensagem de sucesso da remoção do equipamento", example = "Equipamento removido com sucesso")
    private String status;

    @ApiModelProperty(value = "Mensagem de erro, presente apenas quando ocorre algum problema", example = "")
    private String erro;
}
