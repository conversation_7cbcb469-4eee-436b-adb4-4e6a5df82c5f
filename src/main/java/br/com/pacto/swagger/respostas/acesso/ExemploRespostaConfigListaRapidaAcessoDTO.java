package br.com.pacto.swagger.respostas.acesso;


import br.com.pacto.controller.json.aluno.ConfigListaRapidaAcessoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200")
public class ExemploRespostaConfigListaRapidaAcessoDTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as informações solicitadas")
    private List<ConfigListaRapidaAcessoDTO> content;

    public List<ConfigListaRapidaAcessoDTO> getContent() {
        return content;
    }

    public void setContent(List<ConfigListaRapidaAcessoDTO> content) {
        this.content = content;
    }
}
