package br.com.pacto.swagger.respostas.turma;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Exemplo de resposta para sincronização com MGB")
public class ExemploRespostaSincronizacaoMGB {

    @ApiModelProperty(value = "Mensagem de confirmação da sincronização", 
                     example = "A sincronização foi iniciada e pode levar alguns minutos, dependendo da quantidade de horários da turma. Acompanhe as turmas sincronizadas na plataforma do MGB.")
    private String content;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
