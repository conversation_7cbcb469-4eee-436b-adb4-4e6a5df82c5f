package br.com.pacto.swagger.respostas.treino;

import br.com.pacto.controller.json.gestao.BITreinoResponseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200")
public class ExemploRespostaBITreinoResponseDTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as informações solicitadas")
    private BITreinoResponseDTO content;

    public BITreinoResponseDTO getContent() {
        return content;
    }

    public void setContent(BITreinoResponseDTO content) {
        this.content = content;
    }
}
