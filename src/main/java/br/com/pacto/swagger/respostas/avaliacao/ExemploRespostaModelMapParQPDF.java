package br.com.pacto.swagger.respostas.avaliacao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para geração de PDF do questionário PAR-Q com assinatura digital")
public class ExemploRespostaModelMapParQPDF {

    @ApiModelProperty(value = "URL do arquivo PDF gerado contendo o questionário PAR-Q preenchido pelo aluno. " +
            "Esta URL permite o download direto do documento PDF que inclui todas as perguntas, respostas e " +
            "informações do questionário de prontidão para atividade física.", 
            example = "https://relatorio-ms.treino.com.br/temp/parq_aluno_12345_20240620_143022.pdf")
    private String returnValue;

    @ApiModelProperty(value = "URL da assinatura digital do aluno no questionário PAR-Q. " +
            "Contém o link para a imagem da assinatura eletrônica capturada durante o preenchimento do questionário. " +
            "Pode ser uma string vazia se o aluno não assinou digitalmente o questionário.", 
            example = "https://storage.treino.com.br/assinaturas/parq_aluno_12345_assinatura_20240620.png")
    private String assinatura;

    @ApiModelProperty(value = "Mensagem de erro caso ocorra algum problema durante a geração do PDF. " +
            "Este campo só estará presente em caso de falha na operação e conterá detalhes sobre o erro ocorrido.", 
            example = "Erro ao gerar PDF: Cliente não encontrado")
    private String erro;

    // Getters e Setters
    public String getReturnValue() {
        return returnValue;
    }

    public void setReturnValue(String returnValue) {
        this.returnValue = returnValue;
    }

    public String getAssinatura() {
        return assinatura;
    }

    public void setAssinatura(String assinatura) {
        this.assinatura = assinatura;
    }

    public String getErro() {
        return erro;
    }

    public void setErro(String erro) {
        this.erro = erro;
    }
}
