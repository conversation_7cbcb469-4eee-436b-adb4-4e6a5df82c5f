package br.com.pacto.swagger.respostas.usuario;

import br.com.pacto.bean.usuario.UsuarioColaboradorResponseTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para operações com usuário colaborador encapsulada em EnvelopeRespostaDTO")
public class ExemploRespostaUsuarioColaboradorResponseTO {

    @ApiModelProperty(value = "Dados completos do usuário colaborador")
    private UsuarioColaboradorResponseTO content;

    public UsuarioColaboradorResponseTO getContent() {
        return content;
    }

    public void setContent(UsuarioColaboradorResponseTO content) {
        this.content = content;
    }
}
