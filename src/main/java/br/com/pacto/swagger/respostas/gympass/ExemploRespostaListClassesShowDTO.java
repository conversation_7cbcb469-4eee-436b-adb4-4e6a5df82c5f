package br.com.pacto.swagger.respostas.gympass;

import br.com.pacto.service.impl.gympass.dto.ClassesShowDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para listagem de aulas disponíveis")
public class ExemploRespostaListClassesShowDTO {

    @ApiModelProperty(value = "Lista de aulas disponíveis no GymPass")
    private List<ClassesShowDTO> content;

    public List<ClassesShowDTO> getContent() {
        return content;
    }

    public void setContent(List<ClassesShowDTO> content) {
        this.content = content;
    }
}
