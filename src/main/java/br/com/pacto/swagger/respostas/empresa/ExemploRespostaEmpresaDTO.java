package br.com.pacto.swagger.respostas.empresa;

import br.com.pacto.controller.json.empresa.EmpresaDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para empresa DTO")
public class ExemploRespostaEmpresaDTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo os dados essenciais da empresa")
    private EmpresaDTO content;

    public EmpresaDTO getContent() {
        return content;
    }

    public void setContent(EmpresaDTO content) {
        this.content = content;
    }
}
