package br.com.pacto.swagger.respostas.biapp;

import br.com.pacto.swagger.respostas.ExemploPaginadorDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;
import java.util.Map;

@ApiModel(description = "Representação da resposta de status 200 para consulta paginada de usuários do Business Intelligence App")
public class ExemploRespostaUsuariosBiAppPaginacao extends ExemploPaginadorDTO {

    @ApiModelProperty(value = "Lista de usuários com informações básicas para análise de uso do aplicativo móvel.<br/>" +
            "<strong>Estrutura de cada usuário:</strong><br/>" +
            "<ul>" +
            "<li><strong>matricula:</strong> Número da matrícula do aluno na academia</li>" +
            "<li><strong>nomeAbreviado:</strong> Nome abreviado do aluno (primeiro nome + sobrenome)</li>" +
            "<li><strong>nomeProfessor:</strong> Nome abreviado do professor responsável pelo aluno</li>" +
            "</ul>",
            example = "[{\"matricula\":\"12345\",\"nomeAbreviado\":\"João Silva\",\"nomeProfessor\":\"Carlos Santos\"},{\"matricula\":\"67890\",\"nomeAbreviado\":\"Maria Oliveira\",\"nomeProfessor\":\"Ana Costa\"},{\"matricula\":\"11223\",\"nomeAbreviado\":\"Pedro Souza\",\"nomeProfessor\":\"Lucas Ferreira\"}]")
    private List<Map<String, String>> content;

    public List<Map<String, String>> getContent() {
        return content;
    }

    public void setContent(List<Map<String, String>> content) {
        this.content = content;
    }
}
