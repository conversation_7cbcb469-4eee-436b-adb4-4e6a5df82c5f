package br.com.pacto.swagger.respostas.alunoturma;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Classe de exemplo para resposta do endpoint buscaFrequenciaAlunoPorCarteiraProfessor
 * Representa a estrutura EnvelopeRespostaDTO retornada com dados de frequência dos alunos
 */
@ApiModel(description = "Resposta da consulta de frequência de treinos dos alunos por carteira do professor")
public class ExemploRespostaBuscaFrequenciaAluno {

    @ApiModelProperty(value = "Dados estatísticos de frequência dos alunos da carteira do professor. " +
                             "Inclui métricas como média de frequência, número de alunos ativos, " +
                             "percentual de assiduidade e outras informações relevantes para análise " +
                             "de performance da carteira de clientes do professor.")
    private Object content;

    @ApiModelProperty(value = "Indica se a operação foi realizada com sucesso", example = "true")
    private Boolean sucesso;

    @ApiModelProperty(value = "Status da operação realizada", example = "OK")
    private String statusErro;

    public Object getContent() {
        return content;
    }

    public void setContent(Object content) {
        this.content = content;
    }

    public Boolean getSucesso() {
        return sucesso;
    }

    public void setSucesso(Boolean sucesso) {
        this.sucesso = sucesso;
    }

    public String getStatusErro() {
        return statusErro;
    }

    public void setStatusErro(String statusErro) {
        this.statusErro = statusErro;
    }
}
