package br.com.pacto.swagger.respostas.cliente;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;
import java.util.Map;

@ApiModel(description = "Representação da resposta de status 200 para consulta de observações do cliente")
public class ExemploRespostaConsultarObservacoesCliente {

    @JsonProperty("return")
    @ApiModelProperty(value = "Lista de observações do cliente com dados do profissional que as cadastrou",
            example = "[{\"codigo\":123,\"observacao\":\"Cliente apresentou excelente evolução no treino de membros superiores, demonstrando maior resistência e técnica aprimorada nos exercícios de musculação\",\"data\":\"15/06/2024 14:30\",\"dataLong\":1718467800000,\"importante\":true,\"nome\":\"<PERSON>\",\"username\":\"professor1\",\"srcImg\":\"https://exemplo.com/fotos/professor1.jpg\"},{\"codigo\":124,\"observacao\":\"Aluno relatou leve desconforto no joelho direito durante exercícios de agachamento. Recomendado ajuste na carga e acompanhamento fisioterápico\",\"data\":\"14/06/2024 09:15\",\"dataLong\":1718362500000,\"importante\":false,\"nome\":\"Maria Santos Silva\",\"username\":\"professor2\",\"srcImg\":\"https://exemplo.com/fotos/professor2.jpg\"},{\"codigo\":125,\"observacao\":\"Cliente atingiu meta de perda de peso estabelecida no início do programa. Parabenizado pelo comprometimento e dedicação aos treinos\",\"data\":\"13/06/2024 16:45\",\"dataLong\":1718298300000,\"importante\":true,\"nome\":\"Carlos Eduardo Lima\",\"username\":\"professor3\",\"srcImg\":\"https://exemplo.com/fotos/professor3.jpg\"}]")
    private List<Map<String, Object>> returnValue;

    public List<Map<String, Object>> getReturnValue() {
        return returnValue;
    }

    public void setReturnValue(List<Map<String, Object>> returnValue) {
        this.returnValue = returnValue;
    }

}
