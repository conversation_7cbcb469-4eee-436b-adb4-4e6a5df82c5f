package br.com.pacto.swagger.respostas.atividade;


import br.com.pacto.bean.atividade.AtividadeCompletaResponseTO;
import br.com.pacto.swagger.respostas.ExemploPaginadorDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200")
public class ExemploRespostaListAtividadeCompletaResponseTOPaginacao extends ExemploPaginadorDTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as informações solicitadas")
    private List<AtividadeCompletaResponseTO> content;

    public List<AtividadeCompletaResponseTO> getContent() {
        return content;
    }

    public void setContent(List<AtividadeCompletaResponseTO> content) {
        this.content = content;
    }
}
