package br.com.pacto.swagger.respostas.notificacao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para consulta da quantidade de notificações não lidas")
public class ExemploRespostaQuantidadeNaoLidas {

    @ApiModelProperty(value = "Quantidade total de notificações não lidas do usuário", example = "5")
    private Long content;

    public Long getContent() {
        return content;
    }

    public void setContent(Long content) {
        this.content = content;
    }
}
