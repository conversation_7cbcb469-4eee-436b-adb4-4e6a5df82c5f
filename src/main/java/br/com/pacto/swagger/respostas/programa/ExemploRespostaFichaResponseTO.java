package br.com.pacto.swagger.respostas.programa;

import br.com.pacto.bean.ficha.FichaResponseTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para operações com ficha")
public class ExemploRespostaFichaResponseTO {

    @ApiModelProperty(value = "Dados da ficha criada ou atualizada")
    private FichaResponseTO content;

    public FichaResponseTO getContent() {
        return content;
    }

    public void setContent(FichaResponseTO content) {
        this.content = content;
    }
}
