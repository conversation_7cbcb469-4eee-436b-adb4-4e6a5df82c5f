package br.com.pacto.swagger.respostas.programatreino;

import br.com.pacto.bean.avaliacao.evolucao.EvolucaoFisicaDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Classe de exemplo para resposta do endpoint consultarGruposTrabalhadosPeriodo
 * Representa a estrutura EnvelopeRespostaDTO retornada com dados de evolução física e grupos musculares trabalhados
 */
@ApiModel(description = "Resposta da consulta de grupos musculares trabalhados em um período específico")
public class ExemploRespostaGruposTrabalhadosPeriodo {

    @ApiModelProperty(value = "Dados completos da evolução física do aluno no período especificado, incluindo " +
                              "informações sobre grupos musculares trabalhados, estatísticas de treino, " +
                              "dados de composição corporal e análises de desempenho")
    private EvolucaoFisicaDTO content;

    public EvolucaoFisicaDTO getContent() {
        return content;
    }

    public void setContent(EvolucaoFisicaDTO content) {
        this.content = content;
    }
}
