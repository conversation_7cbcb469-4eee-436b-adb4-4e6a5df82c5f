package br.com.pacto.swagger.respostas.configuracao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para recarregamento de indicadores de BI")
public class ExemploRespostaReloadIndicadores {

    @ApiModelProperty(value = "Status do processo de recarregamento dos indicadores", 
                     example = "Indicadores recarregados com sucesso")
    private String processo;

    public String getProcesso() {
        return processo;
    }

    public void setProcesso(String processo) {
        this.processo = processo;
    }
}
