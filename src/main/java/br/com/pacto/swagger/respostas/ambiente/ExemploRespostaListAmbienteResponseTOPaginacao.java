package br.com.pacto.swagger.respostas.ambiente;


import br.com.pacto.controller.json.ambiente.AmbienteResponseTO;
import br.com.pacto.swagger.respostas.ExemploPaginadorDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200")
public class ExemploRespostaListAmbienteResponseTOPaginacao extends ExemploPaginadorDTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as informações solicitadas")
    private List<AmbienteResponseTO> content;

    public List<AmbienteResponseTO> getContent() {
        return content;
    }

    public void setContent(List<AmbienteResponseTO> content) {
        this.content = content;
    }
}
