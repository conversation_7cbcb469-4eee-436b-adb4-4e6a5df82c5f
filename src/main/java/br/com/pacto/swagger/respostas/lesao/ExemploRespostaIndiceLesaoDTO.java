package br.com.pacto.swagger.respostas.lesao;

import br.com.pacto.bean.lesao.IndiceLesaoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representacao da resposta de status 200 para indice de lesoes")
public class ExemploRespostaIndiceLesaoDTO {

    @ApiModelProperty(value = "Conteudo da resposta contendo o indice de lesoes por gravidade")
    private IndiceLesaoDTO content;

    public IndiceLesaoDTO getContent() {
        return content;
    }

    public void setContent(IndiceLesaoDTO content) {
        this.content = content;
    }
}
