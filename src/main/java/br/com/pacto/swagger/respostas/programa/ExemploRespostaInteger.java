package br.com.pacto.swagger.respostas.programa;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para retorno de valor inteiro")
public class ExemploRespostaInteger {

    @ApiModelProperty(value = "Valor inteiro retornado pela operação", example = "123")
    private Integer content;

    public Integer getContent() {
        return content;
    }

    public void setContent(Integer content) {
        this.content = content;
    }
}
