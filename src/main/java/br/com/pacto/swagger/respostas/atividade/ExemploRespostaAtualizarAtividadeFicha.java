package br.com.pacto.swagger.respostas.atividade;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Exemplo de resposta para atualização de atividade por ficha")
public class ExemploRespostaAtualizarAtividadeFicha {

    @ApiModelProperty(value = "Resultado da atualização da atividade na ficha", example = "Atividade atualizada com sucesso na ficha 123")
    private String RETURN;

    @ApiModelProperty(value = "Indica sucesso da operação", example = "true")
    private Boolean sucesso;

    @ApiModelProperty(value = "Mensagem de erro (quando aplicável)", example = "null")
    private String statusErro;
}
