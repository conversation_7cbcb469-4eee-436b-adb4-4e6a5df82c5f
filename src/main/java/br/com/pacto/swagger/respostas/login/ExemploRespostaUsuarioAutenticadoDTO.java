package br.com.pacto.swagger.respostas.login;

import br.com.pacto.security.service.UsuarioAutenticadoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para dados do usuário autenticado")
public class ExemploRespostaUsuarioAutenticadoDTO {

    @ApiModelProperty(value = "Dados do usuário autenticado no sistema")
    private UsuarioAutenticadoDTO content;

    public UsuarioAutenticadoDTO getContent() {
        return content;
    }

    public void setContent(UsuarioAutenticadoDTO content) {
        this.content = content;
    }


}
