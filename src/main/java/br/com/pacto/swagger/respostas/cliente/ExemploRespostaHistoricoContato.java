package br.com.pacto.swagger.respostas.cliente;

import br.com.pacto.controller.json.aluno.HistoricoContatoAlunoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para consulta do histórico de contatos")
public class ExemploRespostaHistoricoContato {

    @ApiModelProperty(value = "Lista do histórico de contatos e observações do cliente")
    private List<HistoricoContatoAlunoVO> content;

    public List<HistoricoContatoAlunoVO> getContent() {
        return content;
    }

    public void setContent(List<HistoricoContatoAlunoVO> content) {
        this.content = content;
    }
}
