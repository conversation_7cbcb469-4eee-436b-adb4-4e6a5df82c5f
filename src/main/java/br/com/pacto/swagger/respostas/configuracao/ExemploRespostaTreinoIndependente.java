package br.com.pacto.swagger.respostas.configuracao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para verificação de treino independente")
public class ExemploRespostaTreinoIndependente {

    @ApiModelProperty(value = "Indica se o sistema está configurado para funcionar de forma independente", 
                     example = "true")
    private Boolean treinoindependente;

    public Boolean getTreinoindependente() {
        return treinoindependente;
    }

    public void setTreinoindependente(Boolean treinoindependente) {
        this.treinoindependente = treinoindependente;
    }
}
