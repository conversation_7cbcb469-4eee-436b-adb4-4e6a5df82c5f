package br.com.pacto.swagger.respostas.termoaceite;

import br.com.pacto.controller.json.termoaceite.TermoAceiteDTO;
import flex.messaging.services.remoting.PageableRowSetCache;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para consulta de lista de termos de aceite")
public class ExemploRespostaListTermoAceiteDTO  {

    @ApiModelProperty(value = "Lista contendo todos os termos de aceite cadastrados")
    private List<TermoAceiteDTO> content;

    public List<TermoAceiteDTO> getContent() {
        return content;
    }

    public void setContent(List<TermoAceiteDTO> content) {
        this.content = content;
    }
}
