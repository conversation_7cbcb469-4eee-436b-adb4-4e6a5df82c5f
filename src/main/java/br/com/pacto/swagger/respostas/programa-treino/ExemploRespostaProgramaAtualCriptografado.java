package br.com.pacto.swagger.respostas.programatreino;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Classe de exemplo para resposta do endpoint atualPorMatriculaCrypt
 * Representa a estrutura ModelMap retornada com dados criptografados do programa de treino
 */
@ApiModel(description = "Resposta da consulta do programa de treino atual com dados criptografados")
public class ExemploRespostaProgramaAtualCriptografado {

    @ApiModelProperty(value = "Dados completos do programa de treino criptografados em formato string. " +
                             "Contém todas as informações do programa, fichas, acompanhamento e dados do professor em formato criptografado",
                     example = "eyJwcm9ncmFtYSI6eyJjb2QiOjEwMDEsIm5vbWUiOiJQcm9ncmFtYSBkZSBNdXNjdWxhw6fDo28gSW5pY2lhbnRlIiwiZGF0YUluaWNpbyI6IjIwMjQtMDEtMTVUMDA6MDA6MDAuMDAwWiJ9fQ==")
    private String RETURN;

    @ApiModelProperty(value = "Mensagem de erro criptografada caso a consulta não seja possível", 
                     example = "ZXJyby5wcm9ncmFtYS5uYW8uZW5jb250cmFkbw==")
    private String statusErro;

    public String getRETURN() {
        return RETURN;
    }

    public void setRETURN(String RETURN) {
        this.RETURN = RETURN;
    }

    public String getStatusErro() {
        return statusErro;
    }

    public void setStatusErro(String statusErro) {
        this.statusErro = statusErro;
    }
}
