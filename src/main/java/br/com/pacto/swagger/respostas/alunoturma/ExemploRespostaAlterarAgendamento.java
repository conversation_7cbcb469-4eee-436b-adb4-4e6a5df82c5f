package br.com.pacto.swagger.respostas.alunoturma;

import br.com.pacto.controller.json.agendamento.ServicoAgendamentoPersonalDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Classe de exemplo para resposta do endpoint alterarAgendamento
 * Representa a estrutura EnvelopeRespostaDTO retornada com dados do agendamento alterado
 */
@ApiModel(description = "Resposta da alteração de um agendamento existente")
public class ExemploRespostaAlterarAgendamento {

    @ApiModelProperty(value = "Dados completos do agendamento alterado incluindo as novas informações atualizadas. " +
                             "Contém ID do agendamento, dados do aluno, professor, tipo de serviço, nova data, " +
                             "novo horário e status atualizado após as modificações realizadas.")
    private ServicoAgendamentoPersonalDTO content;

    @ApiModelProperty(value = "Indica se a operação foi realizada com sucesso", example = "true")
    private Boolean sucesso;

    @ApiModelProperty(value = "Status da operação realizada", example = "OK")
    private String statusErro;

    public ServicoAgendamentoPersonalDTO getContent() {
        return content;
    }

    public void setContent(ServicoAgendamentoPersonalDTO content) {
        this.content = content;
    }

    public Boolean getSucesso() {
        return sucesso;
    }

    public void setSucesso(Boolean sucesso) {
        this.sucesso = sucesso;
    }

    public String getStatusErro() {
        return statusErro;
    }

    public void setStatusErro(String statusErro) {
        this.statusErro = statusErro;
    }
}
