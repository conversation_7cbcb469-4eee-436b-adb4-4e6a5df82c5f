package br.com.pacto.swagger.respostas.login;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para dados do usuário em formato JSON para aplicativo")
public class ExemploRespostaUsuarioJSON {

    @ApiModelProperty(value = "Dados do usuário formatados para aplicativo móvel")
    private UsuarioJSONExemplo content;

    public UsuarioJSONExemplo getContent() {
        return content;
    }

    public void setContent(UsuarioJSONExemplo content) {
        this.content = content;
    }

    public static class UsuarioJSONExemplo {
        
        @ApiModelProperty(value = "Código do usuário", example = "12345")
        private Integer cod;
        
        @ApiModelProperty(value = "Código do usuário no sistema", example = "12345")
        private Integer codUsuario;
        
        @ApiModelProperty(value = "Código do usuário ZW", example = "67890")
        private Integer codUsuarioZW;
        
        @ApiModelProperty(value = "Idade do usuário", example = "30")
        private Integer idade;
        
        @ApiModelProperty(value = "Nome completo do usuário", example = "João Silva Santos")
        private String nome;
        
        @ApiModelProperty(value = "Nome de usuário", example = "joao.silva")
        private String username;
        
        @ApiModelProperty(value = "URL da imagem do usuário", example = "https://exemplo.com/foto.jpg")
        private String srcImg;
        
        @ApiModelProperty(value = "Nível do usuário", example = "Intermediário")
        private String nivel;
        
        @ApiModelProperty(value = "Email do usuário", example = "<EMAIL>")
        private String email;
        
        @ApiModelProperty(value = "Nome do professor responsável", example = "Maria Santos")
        private String professor;
        
        @ApiModelProperty(value = "Token de autenticação", example = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9")
        private String token;
        
        @ApiModelProperty(value = "Versão dos dados", example = "1")
        private Integer versao;
        
        @ApiModelProperty(value = "Avatar da empresa", example = "https://exemplo.com/logo.png")
        private String avatarEmpresa;
        
        @ApiModelProperty(value = "URL do site da empresa", example = "https://academia.com.br")
        private String urlSiteEmpresa;
        
        @ApiModelProperty(value = "Nome da empresa", example = "Academia Exemplo")
        private String nomeEmpresa;
        
        @ApiModelProperty(value = "Código da pessoa", example = "98765")
        private Integer codigoPessoa;
        
        @ApiModelProperty(value = "Código do professor", example = "54321")
        private Integer codigoProfessor;
        
        @ApiModelProperty(value = "Código do colaborador", example = "11111")
        private Integer codigoColaborador;
        
        @ApiModelProperty(value = "Data de vencimento do plano", example = "2024-12-31")
        private String vencPlano;
        
        @ApiModelProperty(value = "Código da empresa", example = "1")
        private Integer codEmpresa;
        
        @ApiModelProperty(value = "Matrícula do usuário", example = "MAT001")
        private String matricula;
        
        @ApiModelProperty(value = "Status do usuário", example = "ATIVO")
        private String status;
        
        @ApiModelProperty(value = "Lista de empresas disponíveis para o usuário")
        private List<EmpresaExemplo> empresas;

        // Getters e Setters
        public Integer getCod() { return cod; }
        public void setCod(Integer cod) { this.cod = cod; }
        
        public Integer getCodUsuario() { return codUsuario; }
        public void setCodUsuario(Integer codUsuario) { this.codUsuario = codUsuario; }
        
        public Integer getCodUsuarioZW() { return codUsuarioZW; }
        public void setCodUsuarioZW(Integer codUsuarioZW) { this.codUsuarioZW = codUsuarioZW; }
        
        public Integer getIdade() { return idade; }
        public void setIdade(Integer idade) { this.idade = idade; }
        
        public String getNome() { return nome; }
        public void setNome(String nome) { this.nome = nome; }
        
        public String getUsername() { return username; }
        public void setUsername(String username) { this.username = username; }
        
        public String getSrcImg() { return srcImg; }
        public void setSrcImg(String srcImg) { this.srcImg = srcImg; }
        
        public String getNivel() { return nivel; }
        public void setNivel(String nivel) { this.nivel = nivel; }
        
        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }
        
        public String getProfessor() { return professor; }
        public void setProfessor(String professor) { this.professor = professor; }
        
        public String getToken() { return token; }
        public void setToken(String token) { this.token = token; }
        
        public Integer getVersao() { return versao; }
        public void setVersao(Integer versao) { this.versao = versao; }
        
        public String getAvatarEmpresa() { return avatarEmpresa; }
        public void setAvatarEmpresa(String avatarEmpresa) { this.avatarEmpresa = avatarEmpresa; }
        
        public String getUrlSiteEmpresa() { return urlSiteEmpresa; }
        public void setUrlSiteEmpresa(String urlSiteEmpresa) { this.urlSiteEmpresa = urlSiteEmpresa; }
        
        public String getNomeEmpresa() { return nomeEmpresa; }
        public void setNomeEmpresa(String nomeEmpresa) { this.nomeEmpresa = nomeEmpresa; }
        
        public Integer getCodigoPessoa() { return codigoPessoa; }
        public void setCodigoPessoa(Integer codigoPessoa) { this.codigoPessoa = codigoPessoa; }
        
        public Integer getCodigoProfessor() { return codigoProfessor; }
        public void setCodigoProfessor(Integer codigoProfessor) { this.codigoProfessor = codigoProfessor; }
        
        public Integer getCodigoColaborador() { return codigoColaborador; }
        public void setCodigoColaborador(Integer codigoColaborador) { this.codigoColaborador = codigoColaborador; }
        
        public String getVencPlano() { return vencPlano; }
        public void setVencPlano(String vencPlano) { this.vencPlano = vencPlano; }
        
        public Integer getCodEmpresa() { return codEmpresa; }
        public void setCodEmpresa(Integer codEmpresa) { this.codEmpresa = codEmpresa; }
        
        public String getMatricula() { return matricula; }
        public void setMatricula(String matricula) { this.matricula = matricula; }
        
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        
        public List<EmpresaExemplo> getEmpresas() { return empresas; }
        public void setEmpresas(List<EmpresaExemplo> empresas) { this.empresas = empresas; }
    }

    public static class EmpresaExemplo {
        
        @ApiModelProperty(value = "Código da empresa", example = "1")
        private Integer codigo;
        
        @ApiModelProperty(value = "Nome da empresa", example = "Academia Exemplo")
        private String nome;

        public Integer getCodigo() { return codigo; }
        public void setCodigo(Integer codigo) { this.codigo = codigo; }
        
        public String getNome() { return nome; }
        public void setNome(String nome) { this.nome = nome; }
    }
}
