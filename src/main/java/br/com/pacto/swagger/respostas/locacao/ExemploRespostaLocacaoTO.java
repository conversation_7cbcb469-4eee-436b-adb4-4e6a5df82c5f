package br.com.pacto.swagger.respostas.locacao;

import br.com.pacto.controller.json.locacao.LocacaoTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Classe de exemplo para resposta de consulta de locação por código
 */
@ApiModel(description = "Representação da resposta de status 200 para consulta de locação por código")
public class ExemploRespostaLocacaoTO {

    @ApiModelProperty(value = "Dados da locação consultada")
    private LocacaoTO content;

    public LocacaoTO getContent() {
        return content;
    }

    public void setContent(LocacaoTO content) {
        this.content = content;
    }
}
