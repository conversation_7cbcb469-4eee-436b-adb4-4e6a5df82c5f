package br.com.pacto.swagger.respostas.empresa;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para URL de redirecionamento do ZillionWeb")
public class ExemploRespostaUrlZW {

    @ApiModelProperty(
            value = "URL completa para redirecionamento ao sistema ZillionWeb (ZW) com parâmetros de autenticação encriptados. " +
                    "A URL contém informações de login, empresa, usuário e token necessários para acesso automático ao ZW.",
            example = "https://zw.pactosolucoes.com.br/oid?lgn=eyJsb2dpbiI6InR..."
    )
    private String content;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
