package br.com.pacto.swagger.respostas.alunoturma;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Classe de exemplo para resposta do endpoint removerAgendamento
 * Representa a estrutura EnvelopeRespostaDTO retornada com confirmação da remoção
 */
@ApiModel(description = "Resposta da remoção de um agendamento existente")
public class ExemploRespostaRemoverAgendamento {

    @ApiModelProperty(value = "Mensagem de confirmação da remoção do agendamento", 
                      example = "sucesso")
    private String content;

    @ApiModelProperty(value = "Indica se a operação foi realizada com sucesso", example = "true")
    private Boolean sucesso;

    @ApiModelProperty(value = "Status da operação realizada", example = "OK")
    private String statusErro;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Boolean getSucesso() {
        return sucesso;
    }

    public void setSucesso(Boolean sucesso) {
        this.sucesso = sucesso;
    }

    public String getStatusErro() {
        return statusErro;
    }

    public void setStatusErro(String statusErro) {
        this.statusErro = statusErro;
    }
}
