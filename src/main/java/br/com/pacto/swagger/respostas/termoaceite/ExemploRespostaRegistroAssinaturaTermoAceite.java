package br.com.pacto.swagger.respostas.termoaceite;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para registro de assinatura de termo de aceite")
public class ExemploRespostaRegistroAssinaturaTermoAceite {

    @ApiModelProperty(value = "Mensagem de confirmação do registro da assinatura do termo de aceite",
            example = "Salvo com sucesso!")
    private String content;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
