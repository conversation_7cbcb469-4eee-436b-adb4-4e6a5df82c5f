package br.com.pacto.swagger.respostas.avaliacao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para geração de relatório PDF em grupo de avaliações integradas")
public class ExemploRespostaPDFAvaliacaoIntegradaGrupo {

    @ApiModelProperty(value = "URL do relatório PDF comparativo gerado contendo múltiplas avaliações integradas. O relatório inclui análise comparativa detalhada entre as avaliações, evolução temporal dos resultados de mobilidade (cadeia anterior, posterior, lateral, rotacional) e estabilidade (controle, fechamento, abertura), progressão das classificações de qualidade de movimento e vida, gráficos evolutivos, tendências de melhoria e recomendações baseadas na evolução do aluno ao longo do tempo.", example = "https://treino.pacto.com.br/avaliacaointegrada?isComparativo=true&j=Y29tcGFyYXRpdm9fYXZhbGlhY2FvX2ludGVncmFkYQ==")
    private String content;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
