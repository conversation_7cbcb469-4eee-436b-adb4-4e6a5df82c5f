package br.com.pacto.swagger.respostas.administracao;

import java.util.ArrayList;
import java.util.List;

/**
 * Classe de exemplo para resposta do endpoint de agendamentos próximos
 */
public class ExemploAgendamentosProximos {
    
    private List<AgendamentoExemplo> agendamentos;
    
    public ExemploAgendamentosProximos() {
        this.agendamentos = new ArrayList<>();
        
        AgendamentoExemplo agendamento1 = new AgendamentoExemplo();
        agendamento1.setId(1001);
        agendamento1.setData("15/12/2024 14:30:00");
        agendamento1.setHora("14:30");
        agendamento1.setHoraFinal("15:30");
        agendamento1.setNome("Avaliação Física com Prof. <PERSON>");
        agendamento1.setStatus("Confirmado");
        agendamento1.setNomeProfessor("<PERSON>");
        agendamento1.setStatusCor("#28a745");
        agendamento1.setDataLong(1734274200000L);
        
        AgendamentoExemplo agendamento2 = new AgendamentoExemplo();
        agendamento2.setId(1002);
        agendamento2.setData("15/12/2024 16:00:00");
        agendamento2.setHora("16:00");
        agendamento2.setHoraFinal("17:00");
        agendamento2.setNome("Personal Training com Prof. Maria Santos");
        agendamento2.setStatus("Agendado");
        agendamento2.setNomeProfessor("Maria Santos");
        agendamento2.setStatusCor("#007bff");
        agendamento2.setDataLong(1734279600000L);
        
        this.agendamentos.add(agendamento1);
        this.agendamentos.add(agendamento2);
    }
    
    public List<AgendamentoExemplo> getAgendamentos() {
        return agendamentos;
    }
    
    public void setAgendamentos(List<AgendamentoExemplo> agendamentos) {
        this.agendamentos = agendamentos;
    }
    
    public static class AgendamentoExemplo {
        private Integer id;
        private String data;
        private String hora;
        private String horaFinal;
        private String nome;
        private String status;
        private String nomeProfessor;
        private String statusCor;
        private Long dataLong;
        
        // Getters e Setters
        public Integer getId() { return id; }
        public void setId(Integer id) { this.id = id; }
        
        public String getData() { return data; }
        public void setData(String data) { this.data = data; }
        
        public String getHora() { return hora; }
        public void setHora(String hora) { this.hora = hora; }
        
        public String getHoraFinal() { return horaFinal; }
        public void setHoraFinal(String horaFinal) { this.horaFinal = horaFinal; }
        
        public String getNome() { return nome; }
        public void setNome(String nome) { this.nome = nome; }
        
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        
        public String getNomeProfessor() { return nomeProfessor; }
        public void setNomeProfessor(String nomeProfessor) { this.nomeProfessor = nomeProfessor; }
        
        public String getStatusCor() { return statusCor; }
        public void setStatusCor(String statusCor) { this.statusCor = statusCor; }
        
        public Long getDataLong() { return dataLong; }
        public void setDataLong(Long dataLong) { this.dataLong = dataLong; }
    }
}
