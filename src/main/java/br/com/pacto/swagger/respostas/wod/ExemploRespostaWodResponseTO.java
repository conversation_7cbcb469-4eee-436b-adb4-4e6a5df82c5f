package br.com.pacto.swagger.respostas.wod;

import br.com.pacto.bean.wod.WodResponseTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Classe de exemplo para resposta individual de WOD
 */
@ApiModel(description = "Representação da resposta de status 200 para WOD individual encapsulada em EnvelopeRespostaDTO")
public class ExemploRespostaWodResponseTO {

    @ApiModelProperty(value = "Dados completos do WOD")
    private WodResponseTO content;

    public WodResponseTO getContent() {
        return content;
    }

    public void setContent(WodResponseTO content) {
        this.content = content;
    }
}
