package br.com.pacto.swagger.respostas.locacao;

import br.com.pacto.controller.json.locacao.LocacaoTO;
import br.com.pacto.swagger.respostas.ExemploPaginadorDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * Classe de exemplo para resposta de consulta paginada de locações
 */
@ApiModel(description = "Representação da resposta de status 200 para consulta paginada de locações")
public class ExemploRespostaListLocacaoTOPaginacao extends ExemploPaginadorDTO {

    @ApiModelProperty(value = "Lista de locações encontradas")
    private List<LocacaoTO> content;

    public List<LocacaoTO> getContent() {
        return content;
    }

    public void setContent(List<LocacaoTO> content) {
        this.content = content;
    }
}
