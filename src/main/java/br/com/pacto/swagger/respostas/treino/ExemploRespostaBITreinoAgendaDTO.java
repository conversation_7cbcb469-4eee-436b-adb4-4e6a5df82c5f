package br.com.pacto.swagger.respostas.treino;

import br.com.pacto.controller.json.gestao.BITreinoAgendaDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200")
public class ExemploRespostaBITreinoAgendaDTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as informações solicitadas")
    private BITreinoAgendaDTO content;

    public BITreinoAgendaDTO getContent() {
        return content;
    }

    public void setContent(BITreinoAgendaDTO content) {
        this.content = content;
    }
}
