package br.com.pacto.swagger.respostas.avaliacao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para URL de relatório PDF comparativo de múltiplas avaliações físicas")
public class ExemploRespostaUrlPdfComparativoAvaliacaoFisica {

    @ApiModelProperty(value = "URL completa do relatório PDF comparativo de avaliações físicas gerado. O arquivo contém análise evolutiva entre múltiplas avaliações do mesmo aluno, incluindo gráficos comparativos de composição corporal, perímetros, dobras cutâneas, capacidade cardiorrespiratória e análise da progressão dos resultados ao longo do tempo.", 
                     example = "https://treino.pacto.com.br/relatorios/avaliacao-fisica/Comparativo_AF_2024_<PERSON>_<PERSON>_789_456_123_20240620.pdf")
    private String content;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
