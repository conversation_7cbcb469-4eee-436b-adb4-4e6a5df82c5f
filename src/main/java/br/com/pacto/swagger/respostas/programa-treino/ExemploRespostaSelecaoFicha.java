package br.com.pacto.swagger.respostas.programatreino;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Classe de exemplo para resposta do endpoint selecaoFicha
 * Representa a estrutura ModelMap retornada após marcação de treino em ficha avulsa
 */
@ApiModel(description = "Resposta da marcação de treino realizado em ficha avulsa")
public class ExemploRespostaSelecaoFicha {

    @ApiModelProperty(value = "Confirmação do registro da execução da ficha avulsa", 
                     example = "Treino em ficha avulsa registrado com sucesso")
    private Object RETURN;

    @ApiModelProperty(value = "Mensagem de erro caso o registro não seja possível", 
                     example = "Erro ao registrar treino em ficha avulsa")
    private String statusErro;

    public Object getRETURN() {
        return RETURN;
    }

    public void setRETURN(Object RETURN) {
        this.RETURN = RETURN;
    }

    public String getStatusErro() {
        return statusErro;
    }

    public void setStatusErro(String statusErro) {
        this.statusErro = statusErro;
    }
}
