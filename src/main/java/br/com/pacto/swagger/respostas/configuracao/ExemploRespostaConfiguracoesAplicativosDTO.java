package br.com.pacto.swagger.respostas.configuracao;

import br.com.pacto.controller.json.base.ConfiguracoesAplicativosDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para consulta de configurações de aplicativos")
public class ExemploRespostaConfiguracoesAplicativosDTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as configurações de aplicativos do sistema")
    private ConfiguracoesAplicativosDTO content;

    public ConfiguracoesAplicativosDTO getContent() {
        return content;
    }

    public void setContent(ConfiguracoesAplicativosDTO content) {
        this.content = content;
    }
}
