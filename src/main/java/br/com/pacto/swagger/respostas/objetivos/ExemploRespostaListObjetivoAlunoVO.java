package br.com.pacto.swagger.respostas.objetivos;

import br.com.pacto.bean.programa.ObjetivoAlunoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para consulta de objetivos do aluno")
public class ExemploRespostaListObjetivoAlunoVO {

    @ApiModelProperty(value = "Lista de objetivos do aluno")
    private List<ObjetivoAlunoVO> content;

    public List<ObjetivoAlunoVO> getContent() {
        return content;
    }

    public void setContent(List<ObjetivoAlunoVO> content) {
        this.content = content;
    }
}
