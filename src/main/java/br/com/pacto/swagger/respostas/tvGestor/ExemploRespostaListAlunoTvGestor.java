package br.com.pacto.swagger.respostas.tvGestor;

import br.com.pacto.controller.json.tvGestor.dto.AlunoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Exemplo de resposta para lista de alunos da TV Gestor")
public class ExemploRespostaListAlunoTvGestor {

    @ApiModelProperty(value = "Lista de alunos que acessaram a academia")
    private List<AlunoDTO> content;

    public List<AlunoDTO> getContent() {
        return content;
    }

    public void setContent(List<AlunoDTO> content) {
        this.content = content;
    }
}
