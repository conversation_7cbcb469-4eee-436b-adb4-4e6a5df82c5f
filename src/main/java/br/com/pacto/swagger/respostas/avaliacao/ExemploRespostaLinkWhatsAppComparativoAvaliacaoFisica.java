package br.com.pacto.swagger.respostas.avaliacao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para link do WhatsApp com relatório comparativo de avaliações físicas")
public class ExemploRespostaLinkWhatsAppComparativoAvaliacaoFisica {

    @ApiModelProperty(value = "Link formatado para WhatsApp contendo o relatório comparativo de avaliações físicas. O link inclui o número de telefone do aluno (obtido automaticamente do cadastro) e a URL do relatório PDF comparativo como mensagem. Permite compartilhamento direto via WhatsApp Web com um clique.", 
                     example = "https://web.whatsapp.com/send?phone=5511999887766&text=https%3A//treino.pacto.com.br/relatorios/avaliacao-fisica/Comparativo_AF_2024_<PERSON>_<PERSON>_789_456_123_20240620.pdf")
    private String content;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
