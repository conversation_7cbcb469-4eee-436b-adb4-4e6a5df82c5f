package br.com.pacto.swagger.respostas.disponibilidade;

import br.com.pacto.controller.json.disponibilidade.DisponibilidadeDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para uma disponibilidade")
public class ExemploRespostaDisponibilidade {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as informações da disponibilidade")
    private DisponibilidadeDTO content;

    public DisponibilidadeDTO getContent() {
        return content;
    }

    public void setContent(DisponibilidadeDTO content) {
        this.content = content;
    }
}
