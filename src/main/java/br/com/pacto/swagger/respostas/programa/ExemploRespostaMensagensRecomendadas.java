package br.com.pacto.swagger.respostas.programa;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Arrays;
import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para mensagens recomendadas")
public class ExemploRespostaMensagensRecomendadas {

    @ApiModelProperty(value = "Lista de mensagens recomendadas para fichas", 
                     example = "[\"Mantenha a postura correta durante todo o exercício\", \"Controle a respiração em cada movimento\", \"Execute o movimento de forma lenta e controlada\", \"Foque na contração muscular\", \"Mantenha a concentração durante toda a série\"]")
    private List<String> content;

    public List<String> getContent() {
        return content;
    }

    public void setContent(List<String> content) {
        this.content = content;
    }
}
