package br.com.pacto.swagger.respostas.tvGestor;

import br.com.pacto.controller.json.tvGestor.dto.AlunoDTO;
import br.com.pacto.swagger.respostas.ExemploPaginadorDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Exemplo de resposta paginada para lista de alunos da TV Gestor")
public class ExemploRespostaListAlunoTvGestorPaginacao extends ExemploPaginadorDTO {

    @ApiModelProperty(value = "Lista de alunos com informações para exibição na TV Gestor")
    private List<AlunoDTO> content;

    public List<AlunoDTO> getContent() {
        return content;
    }

    public void setContent(List<AlunoDTO> content) {
        this.content = content;
    }
}
