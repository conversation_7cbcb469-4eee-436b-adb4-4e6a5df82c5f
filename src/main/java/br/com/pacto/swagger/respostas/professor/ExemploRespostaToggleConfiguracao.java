package br.com.pacto.swagger.respostas.professor;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para toggle de configuração de ranking encapsulada em EnvelopeRespostaDTO")
public class ExemploRespostaToggleConfiguracao {

    @ApiModelProperty(value = "Resultado da operação de toggle", example = "true")
    private Boolean content;

    public Boolean getContent() {
        return content;
    }

    public void setContent(Boolean content) {
        this.content = content;
    }
}
