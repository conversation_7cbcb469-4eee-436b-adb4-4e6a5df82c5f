package br.com.pacto.swagger.respostas.auladia;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Classe de exemplo para resposta do endpoint inserirNaFila
 * Representa a estrutura ModelMap retornada quando um aluno é inserido na fila de espera de uma aula
 */
@ApiModel(description = "Resposta da inserção de aluno na fila de espera de uma aula")
public class ExemploRespostaInserirFilaEspera {

    @ApiModelProperty(value = "Mensagem de confirmação da inserção na fila de espera. " +
                              "Contém informações sobre o sucesso da operação e posição na fila quando aplicável.",
                      example = "Aluno inserido na fila de espera com sucesso. Posição: 3")
    private String Fila;

    @ApiModelProperty(value = "Mensagem de erro caso ocorra algum problema durante a inserção na fila de espera. " +
                              "Pode incluir erros como fila de espera desabilitada, aluno já na fila, ou problemas de sistema.",
                      example = "A fila de espera está desabilitada")
    private String erro;

    public String getFila() {
        return Fila;
    }

    public void setFila(String fila) {
        Fila = fila;
    }

    public String getErro() {
        return erro;
    }

    public void setErro(String erro) {
        this.erro = erro;
    }
}
