package br.com.pacto.swagger.respostas.programa;

import br.com.pacto.bean.avaliacao.evolucao.EvolucaoFisicaDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para consulta de grupos musculares trabalhados por período")
public class ExemploRespostaGruposTrabalhadosPeriodo {

    @ApiModelProperty(value = "Dados da evolução física do aluno, incluindo grupos musculares trabalhados no período consultado.", example = "Dados completos da evolução física")
    private EvolucaoFisicaDTO content;


    public EvolucaoFisicaDTO getContent() {
        return content;
    }

    public void setContent(EvolucaoFisicaDTO content) {
        this.content = content;
    }

}
