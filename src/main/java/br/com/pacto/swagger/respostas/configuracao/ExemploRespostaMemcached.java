package br.com.pacto.swagger.respostas.configuracao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para configurações do Memcached")
public class ExemploRespostaMemcached {

    @ApiModelProperty(value = "Endereço IP dos servidores Memcached", 
                     example = "*************:11211")
    private String ip;

    @ApiModelProperty(value = "Indica se o serviço Memcached está ativo", 
                     example = "true")
    private Boolean on;

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public Boolean getOn() {
        return on;
    }

    public void setOn(Boolean on) {
        this.on = on;
    }
}
