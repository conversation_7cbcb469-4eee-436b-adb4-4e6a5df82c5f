package br.com.pacto.swagger.respostas.avaliacao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para geração de relatório PDF individual de avaliação integrada")
public class ExemploRespostaPDFAvaliacaoIntegradaIndividual {

    @ApiModelProperty(value = "URL do relatório PDF gerado contendo os resultados completos da avaliação integrada individual. O relatório inclui análise detalhada de mobilidade (cadeia anterior, posterior, lateral, rotacional), estabilidade (controle, fechamento, abertura), classificações de qualidade de movimento e vida, gráficos comparativos, recomendações personalizadas e dados da anamnese específica.", example = "https://treino.pacto.com.br/avaliacaointegrada?isComparativo=false&j=aGVsbG93b3JsZA==")
    private String content;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
