package br.com.pacto.swagger.respostas.objetivos;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para remoção de objetivo intermediário encapsulada em EnvelopeRespostaDTO")
public class ExemploRespostaRemoverObjetivoIntermediario {

    @ApiModelProperty(value = "Conteúdo vazio ou null para operações de remoção bem-sucedidas")
    private Object content;

    public Object getContent() {
        return content;
    }

    public void setContent(Object content) {
        this.content = content;
    }
}
