package br.com.pacto.swagger.respostas.treino;


import br.com.pacto.controller.json.aluno.DetalheTreinoAlunoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200")
public class ExemploRespostaDetalheTreinoAlunoDTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as informações solicitadas")
    private DetalheTreinoAlunoDTO content;

    public DetalheTreinoAlunoDTO getContent() {
        return content;
    }

    public void setContent(DetalheTreinoAlunoDTO content) {
        this.content = content;
    }
}
