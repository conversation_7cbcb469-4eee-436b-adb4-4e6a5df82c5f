package br.com.pacto.swagger.respostas.colaborador;

import br.com.pacto.bean.programa.ProfessorBIResponseTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para consulta de Business Intelligence de professores com vínculos encapsulada em EnvelopeRespostaDTO")
public class ExemploRespostaListProfessorBIResponseTO {

    @ApiModelProperty(value = "Lista contendo dados de Business Intelligence dos professores com vínculos ativos, incluindo estatísticas de treinamento")
    private List<ProfessorBIResponseTO> content;

    public List<ProfessorBIResponseTO> getContent() {
        return content;
    }

    public void setContent(List<ProfessorBIResponseTO> content) {
        this.content = content;
    }
}
