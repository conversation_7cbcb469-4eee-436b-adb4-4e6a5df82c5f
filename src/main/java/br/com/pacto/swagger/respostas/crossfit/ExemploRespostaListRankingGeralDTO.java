package br.com.pacto.swagger.respostas.crossfit;

import br.com.pacto.controller.json.gestao.RankingGeralDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para consulta de ranking geral de alunos do Crossfit")
public class ExemploRespostaListRankingGeralDTO {

    @ApiModelProperty(value = "Lista do ranking geral de alunos do Crossfit")
    private List<RankingGeralDTO> content;

    public List<RankingGeralDTO> getContent() {
        return content;
    }

    public void setContent(List<RankingGeralDTO> content) {
        this.content = content;
    }
}
