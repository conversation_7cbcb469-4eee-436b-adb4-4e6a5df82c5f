package br.com.pacto.swagger.respostas.turma;


import br.com.pacto.controller.json.agendamento.AdicionarAlunoTurmaResponseDTO;
import br.com.pacto.controller.json.agendamento.ServicoAgendamentoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200")
public class ExemploRespostaServicoAgendamentoDTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as informações solicitadas")
    private ServicoAgendamentoDTO content;

    public ServicoAgendamentoDTO getContent() {
        return content;
    }

    public void setContent(ServicoAgendamentoDTO content) {
        this.content = content;
    }
}
