package br.com.pacto.swagger.respostas.turma;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Exemplo de resposta para remoção de horário")
public class ExemploRespostaRemocaoHorario {

    @ApiModelProperty(value = "Mensagem de confirmação da remoção", 
                     example = "Horário removido com sucesso")
    private String content;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
