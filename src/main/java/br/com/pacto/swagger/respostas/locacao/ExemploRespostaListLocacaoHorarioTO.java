package br.com.pacto.swagger.respostas.locacao;

import br.com.pacto.controller.json.locacao.LocacaoHorarioTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Exemplo de uma resposta contendo uma lista de horários de locação")
public class ExemploRespostaListLocacaoHorarioTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as informações de uma lista de Horários de Locação")
    List<LocacaoHorarioTO> content;

    public List<LocacaoHorarioTO> getContent() {
        return content;
    }

    public void setContent(List<LocacaoHorarioTO> content) {
        this.content = content;
    }
}
