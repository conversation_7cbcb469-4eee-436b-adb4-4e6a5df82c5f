package br.com.pacto.swagger.respostas.avaliacao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação de um questionário PAR-Q individual do aluno")
public class ExemploParQAlunoDTO {

    @ApiModelProperty(value = "Código único da resposta PAR-Q no sistema", example = "1234")
    private Integer codigo;

    @ApiModelProperty(value = "Data em que o questionário PAR-Q foi respondido pelo aluno", example = "2024-01-15")
    private String dataresposta;

    @ApiModelProperty(value = "URL da assinatura digital do aluno no questionário PAR-Q", example = "https://storage.example.com/assinaturas/parq_1234_assinatura.png")
    private String urlassinatura;

    @ApiModelProperty(value = "Código do cliente/aluno que respondeu o questionário", example = "5678")
    private Integer cliente_codigo;

    @ApiModelProperty(value = "Código do usuário/profissional que aplicou o questionário PAR-Q", example = "9012")
    private Integer usuario_codigo;

    @ApiModelProperty(value = "Nome do usuário/profissional que aplicou o questionário PAR-Q", example = "Dr. João Silva")
    private String usuario_nome;

    @ApiModelProperty(value = "Data de vencimento do questionário PAR-Q ou '-' se não há vencimento configurado", example = "2024-07-15")
    private String diasparavencimentoparq;

    @ApiModelProperty(value = "Status do resultado do questionário PAR-Q. \n\n" +
            "<strong>Valores disponíveis</strong>\n" +
            "- Positivo (Aluno apresenta alguma condição que requer atenção médica antes da atividade física)\n" +
            "- Negativo (Aluno está apto para atividade física sem restrições)\n" +
            "- Vencido (Questionário PAR-Q expirou e precisa ser renovado)\n" +
            "- Inativo (Questionário PAR-Q foi desativado)\n" +
            "- Assinado (Questionário foi assinado digitalmente)", example = "Negativo")
    private String parQPositivo;

    // Getters e Setters
    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDataresposta() {
        return dataresposta;
    }

    public void setDataresposta(String dataresposta) {
        this.dataresposta = dataresposta;
    }

    public String getUrlassinatura() {
        return urlassinatura;
    }

    public void setUrlassinatura(String urlassinatura) {
        this.urlassinatura = urlassinatura;
    }

    public Integer getCliente_codigo() {
        return cliente_codigo;
    }

    public void setCliente_codigo(Integer cliente_codigo) {
        this.cliente_codigo = cliente_codigo;
    }

    public Integer getUsuario_codigo() {
        return usuario_codigo;
    }

    public void setUsuario_codigo(Integer usuario_codigo) {
        this.usuario_codigo = usuario_codigo;
    }

    public String getUsuario_nome() {
        return usuario_nome;
    }

    public void setUsuario_nome(String usuario_nome) {
        this.usuario_nome = usuario_nome;
    }

    public String getDiasparavencimentoparq() {
        return diasparavencimentoparq;
    }

    public void setDiasparavencimentoparq(String diasparavencimentoparq) {
        this.diasparavencimentoparq = diasparavencimentoparq;
    }

    public String getParQPositivo() {
        return parQPositivo;
    }

    public void setParQPositivo(String parQPositivo) {
        this.parQPositivo = parQPositivo;
    }
}
