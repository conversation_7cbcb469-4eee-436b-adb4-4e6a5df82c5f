package br.com.pacto.swagger.respostas.gestao;

import br.com.pacto.controller.json.gestao.IndicadorAgendaResponseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para indicadores de agenda por alunos encapsulada em EnvelopeRespostaDTO")
public class ExemploRespostaIndicadoresAgendaAlunos {

    @ApiModelProperty(value = "Lista de indicadores de agenda por aluno contendo informações sobre agendamentos específicos")
    private List<IndicadorAgendaResponseDTO> content;

    public List<IndicadorAgendaResponseDTO> getContent() {
        return content;
    }

    public void setContent(List<IndicadorAgendaResponseDTO> content) {
        this.content = content;
    }
}
