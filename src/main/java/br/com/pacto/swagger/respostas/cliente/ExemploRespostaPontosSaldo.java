package br.com.pacto.swagger.respostas.cliente;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para consulta de pontos e saldo do cliente")
public class ExemploRespostaPontosSaldo {

    @ApiModelProperty(value = "Saldo atual de pontos do cliente no programa de fidelidade", example = "1250")
    private Integer content;

    public Integer getContent() {
        return content;
    }

    public void setContent(Integer content) {
        this.content = content;
    }
}
