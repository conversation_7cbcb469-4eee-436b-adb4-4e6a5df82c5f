package br.com.pacto.swagger.respostas.turma;


import br.com.pacto.controller.json.agendamento.DisponibilidadeDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200")
public class ExemploRespostaDisponibilidadeDTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as informações solicitadas")
    private DisponibilidadeDTO content;

    public DisponibilidadeDTO getContent() {
        return content;
    }

    public void setContent(DisponibilidadeDTO content) {
        this.content = content;
    }
}
