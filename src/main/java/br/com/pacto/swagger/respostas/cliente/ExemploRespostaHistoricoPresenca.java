package br.com.pacto.swagger.respostas.cliente;

import br.com.pacto.service.intf.cliente.HistoricoPresencasVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para consulta do histórico de presença")
public class ExemploRespostaHistoricoPresenca {

    @ApiModelProperty(value = "Dados consolidados do histórico de presença do cliente")
    private HistoricoPresencasVO content;

    public HistoricoPresencasVO getContent() {
        return content;
    }

    public void setContent(HistoricoPresencasVO content) {
        this.content = content;
    }
}
