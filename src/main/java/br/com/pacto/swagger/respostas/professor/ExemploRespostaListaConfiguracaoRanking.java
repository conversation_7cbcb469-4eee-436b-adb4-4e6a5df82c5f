package br.com.pacto.swagger.respostas.professor;

import br.com.pacto.bean.bi.AgrupamentoIndicadorDashboardEnum;
import br.com.pacto.controller.json.professor.ConfiguracaoRankingDTO;
import br.com.pacto.swagger.respostas.ExemploPaginadorDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;
import java.util.Map;

@ApiModel(description = "Representação da resposta de status 200 para lista de configurações de ranking de professores encapsulada em EnvelopeRespostaDTO")
public class ExemploRespostaListaConfiguracaoRanking extends ExemploPaginadorDTO {

    @ApiModelProperty(value = "Mapa contendo as configurações de ranking agrupadas por categoria",
            example = "{\"ALUNOS\":[{\"id\":1,\"indicador\":\"ATIVOS\",\"operacao\":true,\"ativa\":true,\"peso\":2.5,\"pontuacao\":\"92.3\"},{\"id\":2,\"indicador\":\"TOTAL_ALUNOS\",\"operacao\":true,\"ativa\":false,\"peso\":1.0,\"pontuacao\":\"78.5\"}],\"TREINO\":[{\"id\":3,\"indicador\":\"ATIVOS_COM_TREINO\",\"operacao\":true,\"ativa\":true,\"peso\":3.0,\"pontuacao\":\"85.7\"},{\"id\":4,\"indicador\":\"EM_DIA\",\"operacao\":true,\"ativa\":true,\"peso\":2.0,\"pontuacao\":\"91.2\"}],\"AGENDA\":[{\"id\":5,\"indicador\":\"COM_AVALIACAO_FISICA\",\"operacao\":true,\"ativa\":false,\"peso\":1.5,\"pontuacao\":\"67.8\"},{\"id\":6,\"indicador\":\"AGENDAMENTOS\",\"operacao\":true,\"ativa\":true,\"peso\":2.2,\"pontuacao\":\"88.4\"}]}")
    private Map<AgrupamentoIndicadorDashboardEnum, List<ConfiguracaoRankingDTO>> content;

    public Map<AgrupamentoIndicadorDashboardEnum, List<ConfiguracaoRankingDTO>> getContent() {
        return content;
    }

    public void setContent(Map<AgrupamentoIndicadorDashboardEnum, List<ConfiguracaoRankingDTO>> content) {
        this.content = content;
    }
}
