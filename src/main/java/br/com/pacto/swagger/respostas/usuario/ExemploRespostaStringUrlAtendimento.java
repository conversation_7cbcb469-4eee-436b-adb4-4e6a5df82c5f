package br.com.pacto.swagger.respostas.usuario;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para URL de solicitação de atendimento encapsulada em EnvelopeRespostaDTO")
public class ExemploRespostaStringUrlAtendimento {

    @ApiModelProperty(value = "URL para abertura de chamado de suporte", example = "https://suporte.exemplo.com/abrir-chamado?token=abc123")
    private String content;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
