package br.com.pacto.swagger.respostas.programatreino;

import br.com.pacto.controller.json.acompanhamento.AcompanhamentoSimplesJSON;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Classe de exemplo para resposta do endpoint executarFichaRetorno
 * Representa a estrutura ModelMap retornada após execução de uma ficha de treino
 */
@ApiModel(description = "Resposta da execução de ficha de treino")
public class ExemploRespostaExecutarFicha {

    @ApiModelProperty(value = "Dados de acompanhamento atualizados após a execução da ficha, contendo informações sobre frequência e assiduidade do aluno")
    private AcompanhamentoSimplesJSON RETURN;

    @ApiModelProperty(value = "Mensagem de erro caso a execução não seja possível", 
                     example = "J<PERSON> possui um programa sendo processado. Por favor aguarde...")
    private String statusErro;

    public AcompanhamentoSimplesJSON getRETURN() {
        return RETURN;
    }

    public void setRETURN(AcompanhamentoSimplesJSON RETURN) {
        this.RETURN = RETURN;
    }

    public String getStatusErro() {
        return statusErro;
    }

    public void setStatusErro(String statusErro) {
        this.statusErro = statusErro;
    }
}
