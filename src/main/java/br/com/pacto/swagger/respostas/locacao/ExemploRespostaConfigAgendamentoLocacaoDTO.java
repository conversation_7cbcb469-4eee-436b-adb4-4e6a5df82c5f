package br.com.pacto.swagger.respostas.locacao;

import br.com.pacto.controller.json.locacao.ConfigAgendamentoLocacaoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Classe de exemplo para resposta de configuração de agendamento de locação
 */
@ApiModel(description = "Exemplo de uma resposta contendo uma configuração de agendamento de locação")
public class ExemploRespostaConfigAgendamentoLocacaoDTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo uma configuração de agendamento de locação")
    ConfigAgendamentoLocacaoDTO content;

    public ConfigAgendamentoLocacaoDTO getContent() {
        return content;
    }

    public void setContent(ConfigAgendamentoLocacaoDTO content) {
        this.content = content;
    }
}
