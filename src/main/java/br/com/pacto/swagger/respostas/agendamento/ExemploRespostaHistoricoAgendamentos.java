package br.com.pacto.swagger.respostas.agendamento;

import br.com.pacto.controller.json.agendamento.AgendamentoJSON;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Resposta contendo o histórico de agendamentos do cliente com paginação")
public class ExemploRespostaHistoricoAgendamentos {

    @ApiModelProperty(value = "Lista de agendamentos do histórico do cliente, ordenados por data de início decrescente")
    private List<AgendamentoJSON> retorno;

    @ApiModelProperty(value = "Mensagem de erro, presente apenas quando ocorre algum problema", example = "")
    private String erro;
}
