package br.com.pacto.swagger.respostas.alunoturma;

import br.com.pacto.controller.json.aulaDia.AulaDiaJSON;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * Classe de exemplo para resposta do endpoint consultarTurmasAluno
 * Representa a estrutura ModelMap retornada com lista de turmas do aluno
 */
@ApiModel(description = "Resposta da consulta de turmas de um aluno")
public class ExemploRespostaConsultarTurmasAluno {

    @ApiModelProperty(value = "Lista de turmas nas quais o aluno está matriculado, convertidas para formato de aulas")
    private List<AulaDiaJSON> aulas;
}
