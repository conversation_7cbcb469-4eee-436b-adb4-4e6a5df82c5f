package br.com.pacto.swagger.respostas.avaliacao;

import br.com.pacto.bean.anamnese.PerguntaResponseTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para consulta de perguntas PAR-Q")
public class ExemploRespostaListPerguntaResponseTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo a lista de perguntas do questionário PAR-Q")
    private List<PerguntaResponseTO> content;

    public List<PerguntaResponseTO> getContent() {
        return content;
    }

    public void setContent(List<PerguntaResponseTO> content) {
        this.content = content;
    }
}
