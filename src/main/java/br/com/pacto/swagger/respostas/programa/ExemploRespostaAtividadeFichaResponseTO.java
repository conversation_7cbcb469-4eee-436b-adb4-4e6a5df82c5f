package br.com.pacto.swagger.respostas.programa;

import br.com.pacto.bean.ficha.AtividadeFichaResponseTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para operações com atividades da ficha")
public class ExemploRespostaAtividadeFichaResponseTO {

    @ApiModelProperty(value = "Lista das atividades da ficha reordenadas")
    private List<AtividadeFichaResponseTO> content;

    public List<AtividadeFichaResponseTO> getContent() {
        return content;
    }

    public void setContent(List<AtividadeFichaResponseTO> content) {
        this.content = content;
    }
}
