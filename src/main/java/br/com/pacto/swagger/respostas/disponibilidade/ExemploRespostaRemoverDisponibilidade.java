package br.com.pacto.swagger.respostas.disponibilidade;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para remoção de disponibilidade")
public class ExemploRespostaRemoverDisponibilidade {

    @ApiModelProperty(value = "Resultado da operação de remoção da disponibilidade. Retorna 'OK' quando a disponibilidade é removida com sucesso.", 
                     example = "OK")
    private String content;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
