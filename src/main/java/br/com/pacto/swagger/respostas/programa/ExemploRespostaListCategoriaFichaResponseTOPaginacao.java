package br.com.pacto.swagger.respostas.programa;

import br.com.pacto.bean.ficha.CategoriaFichaResponseTO;
import br.com.pacto.swagger.respostas.ExemploPaginadorDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200")
public class ExemploRespostaListCategoriaFichaResponseTOPaginacao extends ExemploPaginadorDTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo a lista de categorias de ficha")
    private List<CategoriaFichaResponseTO> content;

    public List<CategoriaFichaResponseTO> getContent() {
        return content;
    }

    public void setContent(List<CategoriaFichaResponseTO> content) {
        this.content = content;
    }
}
