package br.com.pacto.swagger.respostas.aula.excluida;


import br.com.pacto.controller.json.aulaExcluida.AulaExcluidaDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200")
public class ExemploRespostaAulaExcluidaDTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as informações solicitadas")
    private AulaExcluidaDTO content;

    public AulaExcluidaDTO getContent() {
        return content;
    }

    public void setContent(AulaExcluidaDTO content) {
        this.content = content;
    }
}
