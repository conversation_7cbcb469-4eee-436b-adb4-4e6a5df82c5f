package br.com.pacto.swagger.respostas.atividade;

import br.com.pacto.controller.json.atividade.read.MetodoExecucaoJSON;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Exemplo de resposta para busca de métodos de execução")
public class ExemploRespostaMetodosExecucao {

    @ApiModelProperty(value = "Lista de métodos de execução disponíveis")
    private List<MetodoExecucaoJSON> RETURN;

    @ApiModelProperty(value = "Indica sucesso da operação", example = "true")
    private Boolean sucesso;

    @ApiModelProperty(value = "Mensagem de erro (quando aplicável)", example = "null")
    private String statusErro;
}
