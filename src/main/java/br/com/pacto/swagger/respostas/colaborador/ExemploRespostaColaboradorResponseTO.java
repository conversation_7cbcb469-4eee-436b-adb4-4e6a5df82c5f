package br.com.pacto.swagger.respostas.colaborador;

import br.com.pacto.bean.colaborador.ColaboradorResponseTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para consulta de detalhes de um colaborador específico encapsulada em EnvelopeRespostaDTO")
public class ExemploRespostaColaboradorResponseTO {

    @ApiModelProperty(value = "Dados completos do colaborador consultado")
    private ColaboradorResponseTO content;

    public ColaboradorResponseTO getContent() {
        return content;
    }

    public void setContent(ColaboradorResponseTO content) {
        this.content = content;
    }
}
