package br.com.pacto.swagger.respostas.cliente;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para sincronização de dados do cliente sintético")
public class ExemploRespostaSincronizarClienteSintetico {

    @ApiModelProperty(value = "Resultado da operação de sincronização. Retorna uma mensagem informando quantos alunos foram sincronizados e atualizados, incluindo detalhes dos alunos processados (matrícula e nome). Se todos os alunos já estiverem atualizados, retorna mensagem informativa.",
                     example = "<br/><br/>3 Alunos sincronizados e atualizados com sucesso!<br/><br/><br/> MAT: 12345 Nome: <PERSON><br/> MAT: 12346 Nome: <PERSON><br/> MAT: 12347 Nome: <PERSON><br/>")
    private String RETURN;

    @ApiModelProperty(value = "Mensagem de erro caso ocorra algum problema na sincronização",
                     example = "Erro ao sincronizar os alunos!")
    private String STATUS_ERRO;

    public String getRETURN() {
        return RETURN;
    }

    public void setRETURN(String RETURN) {
        this.RETURN = RETURN;
    }

    public String getSTATUS_ERRO() {
        return STATUS_ERRO;
    }

    public void setSTATUS_ERRO(String STATUS_ERRO) {
        this.STATUS_ERRO = STATUS_ERRO;
    }
}
