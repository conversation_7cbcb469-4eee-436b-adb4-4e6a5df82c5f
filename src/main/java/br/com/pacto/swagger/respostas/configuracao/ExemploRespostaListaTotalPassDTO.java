package br.com.pacto.swagger.respostas.configuracao;

import br.com.pacto.controller.json.totalpass.TotalPassDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para consulta de integrações TotalPass")
public class ExemploRespostaListaTotalPassDTO {

    @ApiModelProperty(value = "Lista de configurações de integração TotalPass")
    private List<TotalPassDTO> content;

    public List<TotalPassDTO> getContent() {
        return content;
    }

    public void setContent(List<TotalPassDTO> content) {
        this.content = content;
    }
}
