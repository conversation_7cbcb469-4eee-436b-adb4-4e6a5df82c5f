package br.com.pacto.swagger.respostas.configuracao;

import br.com.pacto.controller.json.selfloops.SelfloopsDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para consulta de integrações Selfloops")
public class ExemploRespostaListaSelfloopsDTO {

    @ApiModelProperty(value = "Lista de configurações de integração Selfloops")
    private List<SelfloopsDTO> content;

    public List<SelfloopsDTO> getContent() {
        return content;
    }

    public void setContent(List<SelfloopsDTO> content) {
        this.content = content;
    }
}
