package br.com.pacto.swagger.respostas.configuracao;

import br.com.pacto.controller.json.base.ConfiguracoesTreinoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para consulta de configurações de treino")
public class ExemploRespostaConfiguracoesTreinoDTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as configurações de treino do sistema")
    private ConfiguracoesTreinoDTO content;

    public ConfiguracoesTreinoDTO getContent() {
        return content;
    }

    public void setContent(ConfiguracoesTreinoDTO content) {
        this.content = content;
    }
}
