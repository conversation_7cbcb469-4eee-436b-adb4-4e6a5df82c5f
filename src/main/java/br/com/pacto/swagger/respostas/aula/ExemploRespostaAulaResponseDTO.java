package br.com.pacto.swagger.respostas.aula;


import br.com.pacto.controller.json.aulaDia.AulaResponseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200")
public class ExemploRespostaAulaResponseDTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as informações solicitadas")
    private AulaResponseDTO content;

    public AulaResponseDTO getContent() {
        return content;
    }

    public void setContent(AulaResponseDTO content) {
        this.content = content;
    }
}
