package br.com.pacto.swagger.respostas.token;

import br.com.pacto.controller.json.token.TokenResponseTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para validação de token")
public class ExemploRespostaTokenResponseTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo dados completos do usuário autenticado, empresas vinculadas e permissões")
    private TokenResponseTO content;

    public TokenResponseTO getContent() {
        return content;
    }

    public void setContent(TokenResponseTO content) {
        this.content = content;
    }
}
