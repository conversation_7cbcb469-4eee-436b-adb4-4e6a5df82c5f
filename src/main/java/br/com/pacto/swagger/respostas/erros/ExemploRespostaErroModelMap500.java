package br.com.pacto.swagger.respostas.erros;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Classe de exemplo para resposta de erro HTTP 500 - Internal Server Error (ModelMap)
 * Utilizada para documentação automática do Swagger em endpoints que retornam ModelMap
 */
@ApiModel(description = "Exemplo de resposta para erro HTTP 500 - Internal Server Error (formato ModelMap)")
public class ExemploRespostaErroModelMap500 {

    @ApiModelProperty(value = "Mensagem de erro detalhada", 
                     example = "Ocorreu um erro interno no servidor. Tente novamente em alguns instantes ou contate o suporte técnico.")
    private String STATUS_ERRO;

    // Getter e Setter
    public String getSTATUS_ERRO() {
        return STATUS_ERRO;
    }

    public void setSTATUS_ERRO(String STATUS_ERRO) {
        this.STATUS_ERRO = STATUS_ERRO;
    }
}
