package br.com.pacto.swagger;

import br.com.pacto.swagger.respostas.erros.*;
import com.fasterxml.classmate.TypeResolver;
import com.google.common.base.Predicate;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.bind.annotation.RequestMethod;
import springfox.documentation.RequestHandler;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.ParameterBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.builders.ResponseMessageBuilder;
import springfox.documentation.schema.ModelRef;
import springfox.documentation.service.ApiKey;
import springfox.documentation.service.AuthorizationScope;
import springfox.documentation.service.Parameter;
import springfox.documentation.service.ResponseMessage;
import springfox.documentation.service.SecurityReference;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spi.service.contexts.SecurityContext;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;
import springfox.documentation.service.ApiKey;
import springfox.documentation.service.SecurityReference;
import springfox.documentation.service.SecurityScheme;
import springfox.documentation.spi.service.contexts.SecurityContext;
import springfox.documentation.service.Parameter;
import springfox.documentation.builders.ParameterBuilder;
import springfox.documentation.schema.ModelRef;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@Configuration
@EnableSwagger2
public abstract SwaggerConfig {

    @Autowired
    private TypeResolver typeResolver;

    @Bean
    public Docket api() {
        List<Parameter> globalHeaders = Arrays.asList(
                new ParameterBuilder()
                        .name("chave")
                        .description("Chave da empresa")
                        .modelRef(new ModelRef("string"))
                        .parameterType("header")
                        .required(true)
                        .build(),
                new ParameterBuilder()
                        .name("empresaId")
                        .description("Código da empresa")
                        .modelRef(new ModelRef("string"))
                        .parameterType("header")
                        .required(true)
                        .build()
        );
        Predicate<RequestHandler> documentedOnly = input -> {
            return input.isAnnotatedWith(ApiOperation.class);
        };
        return new Docket(DocumentationType.SWAGGER_2)
                .securitySchemes(Collections.singletonList(apiKey()))
                .securityContexts(Collections.singletonList(securityContext()))
                .globalResponseMessage(RequestMethod.GET, getGlobalResponseMessages())
                .globalResponseMessage(RequestMethod.POST, getGlobalResponseMessages())
                .globalResponseMessage(RequestMethod.PUT, getGlobalResponseMessages())
                .globalResponseMessage(RequestMethod.DELETE, getGlobalResponseMessages())
                .additionalModels(
                        typeResolver.resolve(ExemploRespostaErro400.class),
                        typeResolver.resolve(ExemploRespostaErro401.class),
                        typeResolver.resolve(ExemploRespostaErro403.class),
                        typeResolver.resolve(ExemploRespostaErro404.class),
                        typeResolver.resolve(ExemploRespostaErro409.class),
                        typeResolver.resolve(ExemploRespostaErro500.class),
                        typeResolver.resolve(ExemploRespostaErroModelMap400.class),
                        typeResolver.resolve(ExemploRespostaErroModelMap404.class),
                        typeResolver.resolve(ExemploRespostaErroModelMap500.class)
                )
                .select()
                .apis(RequestHandlerSelectors.basePackage("br.com.pacto.controller.json"))
//                .paths( Predicates.or(
//                        PathSelectors.ant("/psec/alunos/**"),
//                        PathSelectors.ant("/psec/perfil/**"),
//                        PathSelectors.ant("/psec/agenda-cards/**"),
//                        PathSelectors.ant("/notificacoes/**"),
//                        PathSelectors.ant("/psec/programas/**")
//                ))
                .apis(documentedOnly)
                .paths(PathSelectors.any())
                .build()
                .apiInfo(new ApiInfoBuilder()
                        .title("TreinoWeb")
                        .description("Documentação da API do Treino")
                        .version("1.0")
                        .build())
                .globalOperationParameters(globalHeaders);
    }


    private ApiKey apiKey() {
        return new ApiKey("Authorization", "Authorization", "header");
    }

    private SecurityContext securityContext() {
        return SecurityContext.builder()
                .securityReferences(defaultAuth())
                .forPaths(PathSelectors.any())
                .build();
    }

    private List<SecurityReference> defaultAuth() {
        AuthorizationScope authorizationScope =
                new AuthorizationScope("global", "Acesso total à API");
        AuthorizationScope[] authorizationScopes = new AuthorizationScope[]{authorizationScope};
        return Arrays.asList(new SecurityReference("Authorization", authorizationScopes));
    }

    /**
     * Configura as mensagens de resposta globais para códigos de erro HTTP comuns
     * Aplicadas automaticamente a todos os endpoints documentados
     */
    private List<ResponseMessage> getGlobalResponseMessages() {
        return Arrays.asList(
                new ResponseMessageBuilder()
                        .code(400)
                        .message("Bad Request - Dados de entrada inválidos")
                        .responseModel(new ModelRef(ExemploRespostaErro400.class.getSimpleName()))
                        .build(),
                new ResponseMessageBuilder()
                        .code(401)
                        .message("Unauthorized - Credenciais de autenticação inválidas ou ausentes")
                        .responseModel(new ModelRef(ExemploRespostaErro401.class.getSimpleName()))
                        .build(),
                new ResponseMessageBuilder()
                        .code(403)
                        .message("Forbidden - Sem permissão para acessar este recurso")
                        .responseModel(new ModelRef(ExemploRespostaErro403.class.getSimpleName()))
                        .build(),
                new ResponseMessageBuilder()
                        .code(404)
                        .message("Not Found - Registro não encontrado")
                        .responseModel(new ModelRef(ExemploRespostaErro404.class.getSimpleName()))
                        .build(),
                new ResponseMessageBuilder()
                        .code(409)
                        .message("Conflict - Conflito de dados ou registro duplicado")
                        .responseModel(new ModelRef(ExemploRespostaErro409.class.getSimpleName()))
                        .build(),
                new ResponseMessageBuilder()
                        .code(500)
                        .message("Internal Server Error - Erro interno do servidor")
                        .responseModel(new ModelRef(ExemploRespostaErro500.class.getSimpleName()))
                        .build()
        );
    }
}