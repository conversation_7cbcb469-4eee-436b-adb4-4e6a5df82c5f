/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.util.json;

import br.com.pacto.controller.json.base.SuperJSON;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Classe que representa um item do extrato de créditos de treino do aluno
 * Contém informações sobre operações de crédito, débito e saldo de aulas
 *
 * <AUTHOR>
 */
@ApiModel(description = "Item do extrato de créditos de treino do aluno")
public class ControleCreditoTreinoJSON extends SuperJSON{

    @ApiModelProperty(value = "Data da operação no extrato de créditos", example = "15/02/2024 14:30:00")
    private String data;

    @ApiModelProperty(value = "Tipo de operação realizada no extrato de créditos", example = "CREDITO")
    private String operacao;

    @ApiModelProperty(value = "Quantidade de créditos envolvidos na operação (positivo para crédito, negativo para débito)", example = "1")
    private Integer quantidade;

    @ApiModelProperty(value = "Saldo atual de créditos após a operação", example = "5")
    private Integer saldo;

    @ApiModelProperty(value = "Código identificador único da operação no sistema", example = "12345")
    private Integer codigo;

    @ApiModelProperty(value = "Descrição da aula que foi desmarcada (quando aplicável)", example = "Musculação - 15/02/2024 14:30")
    private String aulaDesmarcada;

    @ApiModelProperty(value = "Descrição da aula que foi marcada (quando aplicável)", example = "Crossfit - 16/02/2024 08:00")
    private String aulaMarcada;

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getOperacao() {
        return operacao;
    }

    public void setOperacao(String operacao) {
        this.operacao = operacao;
    }

    public Integer getQuantidade() {
        return quantidade;
    }

    public void setQuantidade(Integer quantidade) {
        this.quantidade = quantidade;
    }

    public Integer getSaldo() {
        return saldo;
    }

    public void setSaldo(Integer saldo) {
        this.saldo = saldo;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getAulaDesmarcada() {
        return aulaDesmarcada;
    }

    public void setAulaDesmarcada(String aulaDesmarcada) {
        this.aulaDesmarcada = aulaDesmarcada;
    }

    public String getAulaMarcada() {
        return aulaMarcada;
    }

    public void setAulaMarcada(String aulaMarcada) {
        this.aulaMarcada = aulaMarcada;
    }
}
